diff --git a/node_modules/@tiptap/core/dist/index.js b/node_modules/@tiptap/core/dist/index.js
index 407d2e0..033a66f 100644
--- a/node_modules/@tiptap/core/dist/index.js
+++ b/node_modules/@tiptap/core/dist/index.js
@@ -330,12 +330,32 @@ function mergeAttributes(...objects) {
                 const existingStyles = mergedAttributes[key] ? mergedAttributes[key].split(';').map((style) => style.trim()).filter(Boolean) : [];
                 const styleMap = new Map();
                 existingStyles.forEach(style => {
-                    const [property, val] = style.split(':').map(part => part.trim());
-                    styleMap.set(property, val);
+                    // const [property, val] = style.split(':').map(part => part.trim());
+                    // styleMap.set(property, val);
+                    // 特判url
+                    if (style.includes('http')){
+                        let first_index = style.indexOf(':');
+                        const property = style.slice(0, first_index).trim();
+                        const val = style.slice(first_index + 1).trim();
+                        styleMap.set(property, val);
+                    } else {
+                        const [property, val] = style.split(':').map(part => part.trim());
+                        styleMap.set(property, val);
+                    }
                 });
                 newStyles.forEach(style => {
-                    const [property, val] = style.split(':').map(part => part.trim());
-                    styleMap.set(property, val);
+                    // const [property, val] = style.split(':').map(part => part.trim());
+                    // styleMap.set(property, val);
+                    // 特判url
+                    if (style.includes('http')){
+                        let first_index = style.indexOf(':');
+                        const property = style.slice(0, first_index).trim();
+                        const val = style.slice(first_index + 1).trim();
+                        styleMap.set(property, val);
+                    } else {
+                        const [property, val] = style.split(':').map(part => part.trim());
+                       styleMap.set(property, val);
+                    }
                 });
                 mergedAttributes[key] = Array.from(styleMap.entries()).map(([property, val]) => `${property}: ${val}`).join('; ');
             }
@@ -3372,6 +3392,33 @@ const splitBlock = ({ keepMarks = true } = {}) => ({ tr, state, dispatch, editor
         return false;
     }
     const atEnd = $to.parentOffset === $to.parent.content.size;
+    // 如果节点是段落或者h标签的话 删除style中的属性
+    let typesOfDelAttrs = ['heading', 'paragraph']
+    if (atEnd && typesOfDelAttrs.includes($from.node().type.name)) {
+        // 分栏
+        delete newAttributes.columnCount
+        // fontSize
+        delete newAttributes.fontSize
+        // letterSpacing
+        delete newAttributes.letterSpacing
+        // line-height
+        delete newAttributes.lineHeight
+        // margin
+        delete newAttributes.margin
+        // nodeAlign
+        delete newAttributes.nodeAlign
+        // textAlign
+        delete newAttributes.textAlign
+        // 背景颜色
+        delete newAttributes.containerColor
+        // 背景图片
+        delete newAttributes.backgroundImage
+        // 背景图片样式
+        delete newAttributes.backgroundOption
+        // 边框
+        delete newAttributes.backgroundBorder
+    }
+    
     const deflt = $from.depth === 0
         ? undefined
         : defaultBlockAt($from.node(-1).contentMatchAt($from.indexAfter(-1)));
