import request from '@/request'

let prefix = '/book/ai/history'

// 查询ai请求记录列表
export const queryHistoryByChapterId = (params) =>
  request({
    url: prefix + '/queryHistoryByChapterId',
    method: 'get',
    params,
  })

// 根据promptHistoryId查询ai请求记录
export const queryHistoryByPromptHistoryId = (params) =>
  request({
    url: prefix + '/queryHistoryByPromptHistoryId',
    method: 'get',
    params,
  })

// 删除ai请求记录
export const deleteHistoryByPromptHistoryId = (params) =>
  request({
    url: prefix + '/delete',
    method: 'delete',
    params,
  })
