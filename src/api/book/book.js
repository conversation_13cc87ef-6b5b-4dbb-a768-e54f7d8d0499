import request from '@/request'

// 查询数字教材列表
export function listBook(query) {
  return request({
    url: '/book/book/listOfAuthorAndEditor',
    method: 'get',
    params: query,
  })
}

export function listForResource(query) {
  return request({
    url: '/book/book/listForResource',
    method: 'get',
    params: query,
  })
}

// 查询数字教材
export function searchBookOneByBookNo(query) {
  return request({
    url: '/book/book/searchOne',
    method: 'get',
    params: query,
  })
}

// 查询数字教材题注样式
export function queryCaptionStyle(query) {
  return request({
    url: '/book/book/queryCaptionStyle',
    method: 'get',
    params: query,
  })
}

// 查询数字教材简介
export function getProfile(query) {
  return request({
    url: '/book/book/queryProfile',
    method: 'get',
    params: query,
  })
}

// 查询数字教材详细
export function getBook(bookId) {
  return request({
    url: `/book/book/${bookId}`,
    method: 'get',
  })
}

// 更新数字教材封面
export function updateCover(data) {
  return request({
    url: '/book/book/updateCover',
    method: 'put',
    data,
  })
}

// 修改数字教材
export function updateBook(data) {
  return request({
    url: '/book/book',
    method: 'put',
    data,
  })
}

// 教材定稿提交
export function bookFinalizedSubmit(data) {
  return request({
    url: '/book/book/finalizedSubmit',
    method: 'put',
    data,
  })
}

// 修改数字教材题注
export function editCaptionStyle(data) {
  return request({
    url: '/book/book/editCaptionStyle',
    method: 'put',
    data,
  })
}

// 修正数字教材
export function amendBook(data) {
  return request({
    url: '/book/book/amend',
    method: 'put',
    data,
  })
}

// 新增数字教材
export function addBook(data) {
  return request({
    url: '/book/book',
    method: 'post',
    data,
  })
}

// 复制数字教材
export function copyBook(data) {
  return request({
    url: '/book/book/copy',
    method: 'put',
    data,
  })
}

export function editCaptionFree(data) {
  return request({
    url: '/book/book/editCaptionFree',
    method: 'put',
    data,
  })
}

// 教材模板
export function getBookTemplate(query) {
  return request({
    url: `/book/template/getInfoByChapterId/${query}`,
    method: 'get',
  })
}

export function getBookTemplateList(query) {
  return request({
    url: '/book/template/list',
    method: 'get',
    params: query,
  })
}

export function setBookTemplate(query) {
  return request({
    url: '/book/chapter/updateChapterTemplate',
    method: 'put',
    data: query,
  })
}

export function getBookTemplateListHasUse(query) {
  return request({
    url: '/book/template/templateListByBookId',
    method: 'get',
    params: query,
  })
}

export function updateBookTemplate(data) {
  return request({
    url: '/book/template/updateBookTemplate',
    method: 'put',
    data,
  })
}

// 教材导出
export function exportBookList(data) {
  return request({
    url: '/book/book/export',
    method: 'post',
    data,
  })
}

export function getFormulaImg(data) {
  return request({
    url: '/book/formula/getFormulaImg',
    method: 'post',
    data: {
      langData: data.langData,
      providerName: data.providerName,
    },
  })
}
