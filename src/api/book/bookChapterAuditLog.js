import request from '@/request'

// 获取数字教材章节目录驳回详细信息
export function queryRejectedReasonInfo(chapterId) {
  return request({
    url: '/book/bookChapterAuditLog/rejectedReason/' + chapterId,
    method: 'get',
  })
}
// 获取数字教材章节目录审核列表
export function chapterAuditLogList(query) {
  return request({
    url: '/book/bookChapterAuditLog/list/',
    method: 'get',
    params: query,
  })
}
// 获取章节目录审核记录列表
export function chapterAuditHistoryList(chapterId) {
  return request({
    url: '/book/bookChapterAuditLog/chapterAuditHistoryList/' + chapterId,
    method: 'get',
  })
}
// 撤销章节申请
export function revokedChapterApply(data) {
  return request({
    url: '/book/bookChapterAuditLog/',
    method: 'post',
    data,
  })
}

// 审核章节目录
export function auditChapter(data) {
  return request({
    url: '/book/bookChapterAuditLog/',
    method: 'put',
    data,
  })
}
