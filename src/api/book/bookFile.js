import request from '@/request'

// 查询教材资源列表
export function listBookFile(query) {
  return request({
    url: '/book/bookFile/list',
    method: 'get',
    params: query
  })
}

// 查询教材资源详细
export function getBookFile(bookFileId) {
  return request({
    url: '/book/bookFile/' + bookFileId,
    method: 'get'
  })
}

// 新增教材资源
export function addBookFile(data) {
  return request({
    url: '/book/bookFile',
    method: 'post',
    data: data
  })
}

// 修改教材资源
export function updateBookFile(data) {
  return request({
    url: '/book/bookFile',
    method: 'put',
    data: data
  })
}
// 修改教材资源
export function batchUpdateBookFile(data) {
    return request({
      url: '/book/bookFile/batch',
      method: 'put',
      data: data
    })
  }

// 删除教材资源
export function delBookFile(bookFileId) {
  return request({
    url: '/book/bookFile/' + bookFileId,
    method: 'delete'
  })
}
