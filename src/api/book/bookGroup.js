import request from '@/request'

// 查询数字教材作者编辑团队详细
export function getBookGroup(groupId) {
  return request({
    url: '/book/bookGroup/' + groupId,
    method: 'get',
  })
}

// 更新数字教材作者编辑团队
export function updateBookGroup(data) {
  return request({
    url: '/book/bookGroup',
    method: 'put',
    data: data,
  })
}

// 获取数字教材作者编辑团队成员 用于下拉
export function groupUserList(bookId) {
  return request({
    url: '/book/bookGroup/groupUserList/' + bookId,
    method: 'get',
  })
}

// 获取当前用户对于此教材的权限
export function getPermissionsInfo(bookId) {
  return request({
    url: '/book/bookGroup/permissions/' + bookId,
    method: 'get',
  })
}
