import request from '@/request'

// 查询DUTP-DTB_009数字教材习题列表
export function listQuestion(query) {
  return request({
    url: '/book/bookQuestion/list',
    method: 'get',
    params: query
  })
}

// 查询DUTP-DTB_009数字教材习题列表(带选项)
export function listBookQuestionWithOptions(query) {
  return request({
    url: '/book/bookQuestion/listWithOptions',
    method: 'get',
    params: query
  })
}


// 查询DUTP-DTB_009数字教材习题详细
export function getQuestion(questionId) {
  return request({
    url: '/book/bookQuestion/' + questionId,
    method: 'get'
  })
}

// 新增DUTP-DTB_009数字教材习题
export function addQuestion(data) {
  return request({
    url: '/book/bookQuestion',
    method: 'post',
    data: data
  })
}

export function addQuestionBatch(data) {
  return request({
    url: '/book/bookQuestion/addBatch',
    method: 'post',
    data: data
  })
}

// 修改DUTP-DTB_009数字教材习题
export function updateQuestion(data) {
  return request({
    url: '/book/bookQuestion',
    method: 'put',
    data: data
  })
}

// 删除DUTP-DTB_009数字教材习题
export function delQuestion(questionId) {
  return request({
    url: '/book/bookQuestion/' + questionId,
    method: 'delete'
  })
}

// 批量导入DUTP-DTB_009数字教材习题
export function importQuestions(data) {
  return request({
    url: '/book/bookQuestion/import',
    method: 'post',
    data: data
  })
}

// 将数字教材习题移入回收站
export function moveToRecycleBin(bookQuestionIds) {
  return request({
    url: '/book/bookQuestion/recycle/' + bookQuestionIds,
    method: 'put'
  })
}

// 从回收站恢复数字教材习题
export function restoreFromRecycleBin(bookQuestionIds) {
  return request({
    url: '/book/bookQuestion/restore/' + bookQuestionIds,
    method: 'put'
  })
}

// 查询回收站中的数字教材习题列表
export function recycleBinList(query) {
  return request({
    url: '/book/bookQuestion/recycleBin/list',
    method: 'get',
    params: query
  })
}


