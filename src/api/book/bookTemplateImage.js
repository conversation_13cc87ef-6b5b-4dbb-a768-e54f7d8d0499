import request from '@/request'

// 查询教材模板图片列表
export function listBookTemplateImage(query) {
  return request({
    url: '/book/bookTemplateImage/listForAuthor',
    method: 'get',
    params: query,
  })
}

// 新增教材模板图片
export function addBookTemplateImage(data) {
  return request({
    url: '/book/bookTemplateImage',
    method: 'post',
    data: data,
  })
}

// 删除教材模板图片
export function delBookTemplateImage(imageId) {
  return request({
    url: '/book/bookTemplateImage/' + imageId,
    method: 'delete',
  })
}
