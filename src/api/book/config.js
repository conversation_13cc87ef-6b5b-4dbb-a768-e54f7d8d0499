import request from '@/request'

// 查询平台整体设置列表
export function listConfig(query) {
  return request({
    url: '/book/config/list',
    method: 'get',
    params: query
  })
}

// 查询平台整体设置详细
export function getConfig(configId) {
  return request({
    url: '/book/config/' + configId,
    method: 'get'
  })
}

// 新增平台整体设置
export function addConfig(data) {
  return request({
    url: '/book/config',
    method: 'post',
    data: data
  })
}

// 修改平台整体设置
export function updateConfig(data) {
  return request({
    url: '/book/config',
    method: 'put',
    data: data
  })
}

// 删除平台整体设置
export function delConfig(configId) {
  return request({
    url: '/book/config/' + configId,
    method: 'delete'
  })
}
