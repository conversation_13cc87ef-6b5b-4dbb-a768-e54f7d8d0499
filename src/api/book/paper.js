import request from '@/request/index.js'

// 查询试卷列表
export function listPaper(query) {
  return request({
    url: '/book/paper/list',
    method: 'get',
    params: query
  })
}

// 查询试卷详细
export function getPaper(paperId) {
  return request({
    url: '/book/paper/' + paperId,
    method: 'get'
  })
}

// 新增试卷
export function addPaper(data) {
  return request({
    url: '/book/paper',
    method: 'post',
    data: data
  })
}

// 修改试卷
export function updatePaper(data) {
  return request({
    url: '/book/paper',
    method: 'put',
    data: data
  })
}

// 删除试卷
export function delPaper(paperId) {
  return request({
    url: '/book/paper/' + paperId,
    method: 'delete'
  })
}

// 获取试卷的题目组和题目信息
export function getTestPaperQuestions(paperId) {
  return request({
    url: '/book/paper/questions/' + paperId,
    method: 'get'
  })
}

export function moveToRecycleBin(paperIds) {
  return request({
    url: '/book/paper/recycle/' + paperIds,
    method: 'put'
  })
}

export function recycleList(query) {
  return request({
    url: '/book/paper/recycle/list',
    method: 'get',
    params: query
  })
}

export function restoreFromRecycleBin(paperIds) {
  return request({
    url: '/book/paper/recycle/restore/' + paperIds,
    method: 'put'
  })
}

// 复制试卷
export function copyPaper(paperId) {
  return request({
    url: '/book/paper/copy/' + paperId,
    method: 'post'
  })
}

// 添加检测编辑前的接口
export function checkBeforeEdit(paperId) {
  return request({
    url: '/book/paper/checkBeforeEdit/' + paperId,
    method: 'get'
  })
}



