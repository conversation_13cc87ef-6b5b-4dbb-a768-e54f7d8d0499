import request from '@/request/index.js'

let prefix = '/book/process'

// 查询章节内容
export function list(query) {
  return request({
    url: '/book/process/list',
    method: 'get',
    params: query
  })
}

export function addProcess(data) {
  return request({
    url: '/book/process',
    method: 'post',
    data
  })
}

export function processDataList(query){
  return request({
    url: '/book/process/processDataList',
    method: 'get',
    params: query
  })
}

export function getPrevProcessInfo(query){
  return request({
    url: '/book/process/getPrevProcessInfo',
    method: 'get',
    params: query
  })
}

export function addProcessAddition(data){
  return request({
    url: '/book/process/addProcessAddition',
    method: 'post',
    data
  })
}

export function editProcessAddition(data){
  return request({
    url: '/book/process/editProcessAddition',
    method: 'post',
    data
  })
}

export function getProcessAddition(query){
  return request({
    url: '/book/process/getProcessAddition',
    method: 'get',
    params: query
  })
}

export function getProcessInfoLink(processId) {
  return request({
    url: '/book/process/getProcessInfoLink/' + processId,
    method: 'get'
  })
}

export function updateChapterBackup(data){
  return request({
    url: '/book/process/updateChapterBackup',
    method: 'post',
    data
  })
}

export function processChapters(processId) {
  return request({
    url: '/book/process/getProcessChapters/' + processId,
    method: 'get',
  })
}