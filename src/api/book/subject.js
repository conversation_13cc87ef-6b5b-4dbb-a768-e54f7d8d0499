import request from '@/request'

// 查询学科信息列表
export function listSubject(query) {
  return request({
    url: '/basic/subject/list',
    method: 'get',
    params: query
  })
}

// 查询学科信息列表 为了下拉
export function listSubjectNotPage() {
  return request({
    url: '/basic/subject/listNotPage',
    method: 'get'
  })
}

// 查询学科信息详细
export function getSubject(subjectId) {
  return request({
    url: '/basic/subject/' + subjectId,
    method: 'get'
  })
}

// 新增学科信息
export function addSubject(data) {
  return request({
    url: '/basic/subject',
    method: 'post',
    data: data
  })
}

// 修改学科信息
export function updateSubject(data) {
  return request({
    url: '/basic/subject',
    method: 'put',
    data: data
  })
}

// 删除学科信息
export function delSubject(subjectId) {
  return request({
    url: '/basic/subject/' + subjectId,
    method: 'delete'
  })
}

// 查询教育类型列表（一级学科） 无分页，用于下拉列表
export function listForSelect(query) {
  return request({
    url: '/basic/subject/listForSelect',
    method: 'get',
    params: query
  })
}


export function listTreeEducation() {
  return request({
    url: '/basic/subject/listTreeEducation',
    method: 'get'
  })
}
