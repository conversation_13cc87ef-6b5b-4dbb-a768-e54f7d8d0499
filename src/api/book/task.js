import request from '@/request/index.js'

let prefix = '/book/task'

// 查询中图分类列表
export function listTask(query) {
  return request({
    url: '/book/task/list',
    method: 'get',
    params: query,
  })
}

// 下载
export function downloadTask(taskId) {
  return request({
    url: '/book/task/download/' + taskId,
    method: 'get',
    responseType: 'blob',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
}

// 更新题注
export function editCaptionStyle(data) {
  return request({
    url: '/book/task/editCaptionStyle',
    method: 'post',
    data,
  })
}
