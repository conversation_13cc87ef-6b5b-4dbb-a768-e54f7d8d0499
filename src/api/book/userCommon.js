import request from '@/request'

// 编辑器备注列表
export function listUserCommon(query) {
  return request({
    url: '/book/chapterRemark/list',
    method: 'get',
    params: query,
  })
}

// 新增编辑器备注
export function addUserCommon(data) {
  return request({
    url: '/book/chapterRemark',
    method: 'post',
    data,
  })
}

// 修改编辑器备注
export function updateUserCommon(data) {
  return request({
    url: '/book/chapterRemark',
    method: 'put',
    data,
  })
}

// 删除编辑器备注
export function delUserCommon(commonId) {
  return request({
    url: `/book/chapterRemark/${commonId}`,
    method: 'delete',
  })
}
//审核备注
export function examineUserCommon(data) {
  return request({
    url: '/book/chapterRemark/handleState',
    method: 'put',
    data,
  })
}
