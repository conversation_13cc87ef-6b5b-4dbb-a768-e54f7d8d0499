import request from '@/request'

// 查询数字教材习题列表
export function listUserQuestion(query) {
  return request({
    url: '/book/userQuestion/list',
    method: 'get',
    params: query
  })
}

// 查询数字教材习题列表(包含选项)
export function listUserQuestionWithOptions(query) {
  return request({
    url: '/book/userQuestion/listWithOptions',
    method: 'get',
    params: query
  })
}

// 查询数字教材习题详细
export function getUserQuestion(questionId) {
  return request({
    url: '/book/userQuestion/' + questionId,
    method: 'get'
  })
}

// 新增数字教材习题
export function addUserQuestion(data) {
  return request({
    url: '/book/userQuestion',
    method: 'post',
    data: data
  })
}

// 修改数字教材习题
export function updateUserQuestion(data) {
  return request({
    url: '/book/userQuestion',
    method: 'put',
    data: data
  })
}

// 删除数字教材习题
export function delUserQuestion(questionId) {
  return request({
    url: '/book/userQuestion/' + questionId,
    method: 'delete'
  })
}

// 批量导入DUTP-DTB_009数字教材习题
export function importQuestions(data) {
  return request({
    url: '/book/userQuestion/import',
    method: 'post',
    data: data
  })
}

// 移入回收站
export function moveToRecycleBin(questionIds) {
  return request({
    url: '/book/userQuestion/recycle/' + questionIds,
    method: 'put'
  })
}

// 从回收站还原
export function restoreFromRecycleBin(questionIds) {
  return request({
    url: '/book/userQuestion/restore/' + questionIds,
    method: 'put'
  })
}

// 获取回收站列表
export function recycleBinList(query) {
  return request({
    url: '/book/userQuestion/recycleBin/list',
    method: 'get',
    params: query
  })
}

// 检查题目是否被试卷占用
export function checkPaperReference(questionIds) {
  return request({
    url: '/book/userQuestion/checkPaperReference',
    method: 'post',
    data: questionIds
  })
}
