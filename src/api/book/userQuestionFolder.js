import request from '@/request'

// 查询题库目录列表
export function listFolder(query) {
  return request({
    url: '/book/userQuestionFolder/list',
    method: 'get',
    params: query
  })
}

// 查询题库目录详细
export function getFolder(folderId) {
  return request({
    url: '/book/userQuestionFolder/' + folderId,
    method: 'get'
  })
}

// 新增题库目录
export function addFolder(data) {
  return request({
    url: '/book/userQuestionFolder',
    method: 'post',
    data: data
  })
}

// 修改题库目录
export function updateFolder(data) {
  return request({
    url: '/book/userQuestionFolder',
    method: 'put',
    data: data
  })
}

// 删除题库目录
export function delFolder(folderId) {
  return request({
    url: '/book/userQuestionFolder/' + folderId,
    method: 'delete'
  })
}
