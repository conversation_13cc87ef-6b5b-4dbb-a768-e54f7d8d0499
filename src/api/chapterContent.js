import request from '@/request'

let prefix = '/book/chapterContent'

// 查询章节内容
export const getChapterContentInfo = (chapterId) =>
  request({
    url: prefix + '/' + chapterId,
    method: 'get',
  })

// 更新教材章节内容
export const updateChapterContentInfo = (data) =>
  request({
    url: prefix + '/updateChapterContentInfo',
    method: 'post',
    data: data,
  })

// 获取章节目录
export const getChapterCatalogue = (chapterId) =>
  request({
    url: '/book/chapter/queryBookChapterList/',
    method: 'get',
    params: {
      chapterId
    }
  })
