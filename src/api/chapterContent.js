import request from '@/request'

let prefix = '/book/chapterContent'

// 查询章节内容
export const getChapterContentInfo = (chapterId) =>
  request({
    url: prefix + '/' + chapterId,
    method: 'get',
  })

// 更新教材章节内容
export const updateChapterContentInfo = (data) =>
  request({
    url: prefix + '/updateChapterContentInfo',
    method: 'post',
    data: data,
  })

// 获取章节目录
export const getChapterCatalogue = (chapterId) =>
  request({
    url: '/book/chapter/queryBookChapterList/',
    method: 'get',
    params: {
      chapterId,
    },
  })

// 检测章节是否有内容
export const checkIsHasContent = (chapterId) =>
  request({
    url: prefix + '/checkIsHasContent/' + chapterId,
    method: 'get',
  })

// 清空章节内容
export const clearChapterContent = (chapterId) =>
  request({
    url: prefix + '/clearChapterContent/' + chapterId,
    method: 'get',
  })

// 获取当前章节当前用户的保存版本历史 --最近十条
export const queryCurUserSaveVersionHistory = (chapterId) =>
  request({
    url: prefix + '/queryCurUserSaveVersionHistory/' + chapterId,
    method: 'get',
  })

// 切换教材章节版本内容 --根据mongodb的id
export const changeChapterContentInfo = (data) =>
  request({
    url: prefix + '/changeChapterContentInfo',
    method: 'post',
    data: data,
  })
