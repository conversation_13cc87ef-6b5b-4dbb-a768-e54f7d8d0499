import request from '@/request'

let prefix = '/file'

// 获取sts临时凭证
export const getStsToken = () =>
  request({
    url: prefix + '/getStsToken',
    method: 'get',
  })

// 获取sts临时凭证
export const uploadFile = (formData) =>
  request({
    url: prefix + '/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
      repeatSubmit: false,
    },
  })
