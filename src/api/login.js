import request from '@/request'

// 登录方法
export const login = (username, password, code, uuid) =>
  request({
    url: '/auth/autLogin',
    headers: {
      isToken: false,
      repeatSubmit: false,
      Language: localStorage.getItem('Language'),
    },
    method: 'post',
    data: { username, password, code, uuid },
  })

// 获取用户详细信息
export const getInfo = () =>
  request({
    url: '/system/user/getInfo',
    headers: {
      Language: localStorage.getItem('Language'),
    },
    method: 'get',
  })

// 退出方法
export const logout = () =>
  request({
    url: '/auth/logout',
    headers: {
      Language: localStorage.getItem('Language'),
    },
    method: 'delete',
  })

// 获取验证码
export const getCodeImg = () =>
  request({
    url: '/code',
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language'),
    },
    method: 'get',
    timeout: 20000,
  })

// 获取手机验证码
export const getLoginSmsCode = (data) =>
  request({
    url: `/system/user/getLoginSmsCode/${data}`,
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language'),
    },
    method: 'get',
  })

// 登录方法
export const codeLogin = (data) =>
  request({
    url: '/auth/codeLogin',
    headers: {
      isToken: false,
      repeatSubmit: false,
      Language: localStorage.getItem('Language'),
    },
    method: 'post',
    data,
  })

// 获取验证码
export const getSmsCode = (data) =>
  request({
    url: `/system/user/getSmsCode/${data}`,
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language'),
    },
    method: 'get',
  })

// 忘记密码重置密码
export function forgetPwd(data) {
  return request({
    url: '/system/user/forgetPwd',
    headers: {
      isToken: false,
      Language: localStorage.getItem('Language'),
    },
    method: 'put',
    data,
  })
}
