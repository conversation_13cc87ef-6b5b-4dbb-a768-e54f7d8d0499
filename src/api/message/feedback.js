import request from '@/request'

// 查询读者反馈列表
export function listForAdmin(query) {
  return request({
    url: '/message/feedback/listForAdmin',
    method: 'get',
    params: query,
  })
}

// 查询读者反馈详细
export function getFeedback(feedBackId) {
  return request({
    url: '/message/feedback/admin/' + feedBackId,
    method: 'get',
  })
}

// 修改读者反馈
export function updateFeedback(data) {
  return request({
    url: '/message/feedback/handle',
    method: 'put',
    data: data,
  })
}

// 检测是否有未读反馈
export function checkHasFeedback() {
  return request({
    url: '/message/feedback/checkHasFeedback',
    method: 'get',
  })
}
