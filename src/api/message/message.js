import request from '@/request'

// 查询推送教材列表
export function pushBookList(query) {
  return request({
    url: '/message/message/pushBookList',
    method: 'get',
    params: query,
  })
}

// 推送教材
export function pushBook(data) {
  return request({
    url: '/message/message/pushBook',
    method: 'post',
    data,
  })
}

// 获取消息列表
export function listMessage(query) {
  return request({
    url: '/message/message/myList',
    method: 'get',
    params: query,
  })
}

// 设置教材已读
export function updateMessage(data) {
  return request({
    url: '/message/message',
    method: 'put',
    data,
  })
}

// 设置教材已读
export function updateAllMessage(data) {
  return request({
    url: '/message/message/allRead',
    method: 'put',
    data,
  })
}
