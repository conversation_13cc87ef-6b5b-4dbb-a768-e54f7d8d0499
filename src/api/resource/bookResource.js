import request from '@/request'

// 查询教材资源列表
export function listBookResource(query) {
  return request({
    url: '/book/bookResource/list',
    method: 'get',
    params: query
  })
}

// 查询教材资源详细
export function getBookResource(bookResourceId) {
  return request({
    url: '/book/bookResource/' + bookResourceId,
    method: 'get'
  })
}

// 新增教材资源
export function addBookResource(data) {
  return request({
    url: '/book/bookResource',
    method: 'post',
    data: data
  })
}

// 修改教材资源
export function updateBookResource(data) {
  return request({
    url: '/book/bookResource',
    method: 'put',
    data: data
  })
}

// 删除教材资源
export function delBookResource(bookResourceId) {
  return request({
    url: '/book/bookResource/' + bookResourceId,
    method: 'delete'
  })
}
