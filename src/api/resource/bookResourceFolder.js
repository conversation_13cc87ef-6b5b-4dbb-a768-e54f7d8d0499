import request from '@/request'

// 查询教材资源文件夹列表
export function listBookResourcefolder(query) {
  return request({
    url: '/book/bookResourcefolder/list',
    method: 'get',
    params: query
  })
}

export function listBookResourcefolderResource(query) {
  return request({
    url: '/book/bookResourcefolder/listBookResourcefolderResource',
    method: 'get',
    params: query
  })
}


export function listBookResourceFolderAll(query) {
  return request({
    url: '/book/bookResourcefolder/listAll',
    method: 'get',
    params: query
  })
}

// 查询教材资源文件夹详细
export function getBookResourcefolder(folderId) {
  return request({
    url: '/book/bookResourcefolder/' + folderId,
    method: 'get'
  })
}

// 新增教材资源文件夹
export function addBookResourcefolder(data) {
  return request({
    url: '/book/bookResourcefolder',
    method: 'post',
    data: data
  })
}

// 修改教材资源文件夹
export function updateBookResourcefolder(data) {
  return request({
    url: '/book/bookResourcefolder',
    method: 'put',
    data: data
  })
}

// 删除教材资源文件夹
export function delBookResourcefolder(folderId) {
  return request({
    url: '/book/bookResourcefolder/' + folderId,
    method: 'delete'
  })
}
