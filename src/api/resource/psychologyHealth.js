import request from '@/request'

// 查询心理健康量表list
export function listPhychologyHealth(query) {
  return request({
    url: '/book/moocPsychologyHealth/list',
    method: 'get',
    params: query
  })
}

// 删除心理健康量表
export function deletePhychologyHealth(bookResourceId) {
  return request({
    url: '/book/moocPsychologyHealth/' + bookResourceId,
    method: 'delete'
  })
}

// 查询量表测试结果列表
export function listPhychologyResult(query) {
  return request({
    url: '/book/moocPsychologyHealth/resultList',
    method: 'get',
    params: query
  })
}

// 查询教材资源详细
export function getPhychologyResult(resultId) {
  return request({
    url: '/book/moocPsychologyHealth/' + resultId,
    method: 'get'
  })
}

// 查询编辑器测试量表列表
export function listPsychologyForEditor(query) {
  return request({
    url: '/book/moocPsychologyHealth/listPsychologyForEditor',
    method: 'get',
    params: query
  })
}

// 编辑器插入心理测试量表
export function addPsychologyHealth(data) {
  return request({
    url: '/book/psychologyHealthChapter',
    method: 'post',
    data: data
  })
}

// 编辑器预览心理测试量表
export function getPhychology(scaleId) {
  return request({
    url: '/book/moocPsychologyHealth/getPsychology/' + scaleId,
    method: 'get'
  })
}

//新增心理健康表
export function addHealth(data) {
  return request({
    url: '/book/moocPsychologyHealth/addPsychologyHealth',
    method: 'post',
    data: data
  })
}

//修改心理健康表
export function editHealth(data) {
  return request({
    url: '/book/moocPsychologyHealth/editPsychologyHealth',
    method: 'put',
    data: data
  })
}

//修改心理健康表
export function copyPsychologyHealth(data) {
  return request({
    url: '/book/moocPsychologyHealth/copyPsychologyHealth',
    method: 'post',
    data: data
  })
}


