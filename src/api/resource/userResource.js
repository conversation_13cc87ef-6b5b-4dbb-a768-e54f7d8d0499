import request from '@/request'

// 查询用户资源库列表
export function listUserResource(query) {
  return request({
    url: '/book/userResource/list',
    method: 'get',
    params: query
  })
}

// 查询回收站列表
export function listRecycleUserResource(query) {
  return request({
    url: '/book/userResource/recycle/list',
    method: 'get',
    params: query
  })
}

// 查询用户资源库详细
export function getUserResource(resourceId) {
  return request({
    url: '/book/userResource/' + resourceId,
    method: 'get'
  })
}

// 新增用户资源库
export function addUserResource(data) {
  return request({
    url: '/book/userResource',
    method: 'post',
    data: data
  })
}

// 修改用户资源库
export function updateUserResource(data) {
  return request({
    url: '/book/userResource',
    method: 'put',
    data: data
  })
}

// 删除用户资源库
export function delUserResource(resourceId) {
  return request({
    url: '/book/userResource/' + resourceId,
    method: 'delete'
  })
}

// 删除用户资源库
export function permanentUserResource(resourceId) {
  return request({
    url: '/book/userResource/permanent/' + resourceId,
    method: 'delete'
  })
}



// 放入回收站
export function recycleUserResource(resourceIds) {
  return request({
    url: '/book/userResource/recycle/' + resourceIds,
    method: 'put'
  })
}

// 从回收站恢复
export function restoreUserResource(resourceIds) {
  return request({
    url: '/book/userResource/restore/' + resourceIds,
    method: 'put'
  })
}


// 判断用户资源库是否存在当前文件
export function checkResourceIsExists(data) {
  return request({
    url: '/book/userResource/checkResourceIsExists',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
      repeatSubmit: false,
    },
  })
}
