import request from '@/request'

// 查询个人资源文件夹列表
export function listUserResourceFolder(query) {
  return request({
    url: '/book/userResourceFolder/list',
    method: 'get',
    params: query
  })
}


export function listUserResourceFolderResource(query) {
  return request({
    url: '/book/userResourceFolder/resourceList',
    method: 'get',
    params: query
  })
}


// 查询个人资源文件夹列表
export function listUserResourceFolderAll(query) {
  return request({
    url: '/book/userResourceFolder/listAll',
    method: 'get',
    params: query
  })
}

// 查询个人资源文件夹详细
export function getUserResourceFolder(userFolderId) {
  return request({
    url: '/book/userResourceFolder/' + userFolderId,
    method: 'get'
  })
}

// 新增个人资源文件夹
export function addUserResourceFolder(data) {
  return request({
    url: '/book/userResourceFolder',
    method: 'post',
    data: data
  })
}

// 修改个人资源文件夹
export function updateUserResourceFolder(data) {
  return request({
    url: '/book/userResourceFolder',
    method: 'put',
    data: data
  })
}

// 删除个人资源文件夹
export function delUserResourceFolder(userFolderId) {
  return request({
    url: '/book/userResourceFolder/' + userFolderId,
    method: 'delete'
  })
}
