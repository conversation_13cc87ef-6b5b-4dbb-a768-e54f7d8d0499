<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 1933">
<g id="Group 1930" opacity="0.6">
<circle id="Ellipse 3486" cx="10" cy="10" r="10" fill="url(#paint0_linear_2254_4728)"/>
<g id="Mask group">
<mask id="mask0_2254_4728" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
<circle id="Ellipse 3487" cx="10" cy="10" r="10" fill="url(#paint1_linear_2254_4728)"/>
</mask>
<g mask="url(#mask0_2254_4728)">
<g id="Ellipse 3488" filter="url(#filter0_f_2254_4728)">
<circle cx="17.5" cy="17.5" r="12.5" fill="#F7B3FF" fill-opacity="0.8"/>
</g>
<g id="Ellipse 3489" filter="url(#filter1_f_2254_4728)">
<circle cx="-3.5" cy="10.5" r="12.5" fill="#E5FFC5" fill-opacity="0.5"/>
</g>
</g>
</g>
</g>
<g id="Group 1931">
<path id="Vector" d="M13.0169 7.13843C13.0171 7.13843 13.0172 7.13854 13.0173 7.1387L13.4796 8.07111C13.5914 8.29658 13.9143 8.29251 14.0204 8.0643L14.4126 7.2204C14.4375 7.16694 14.4775 7.12199 14.5278 7.09115L15.3826 6.56648C15.5717 6.45046 15.5738 6.17651 15.3866 6.05757L14.5677 5.53735C14.5215 5.50796 14.4841 5.46647 14.4598 5.41738L14.0259 4.54213C13.9156 4.3198 13.5985 4.3198 13.4883 4.54213L13.0555 5.41513C13.0304 5.46564 12.9917 5.50807 12.9436 5.53756L12.0924 6.05995C11.9017 6.17704 11.9017 6.45427 12.0925 6.57134L13.0166 7.13836C13.0167 7.13841 13.0168 7.13843 13.0169 7.13843Z" fill="#4DDEC4"/>
<g id="Vector_2">
<path d="M12.496 14V9.19795H15V14H12.496Z" fill="url(#paint2_linear_2254_4728)"/>
<path d="M4 14L6.10511 6H9.69489L11.8997 14H9.18523L8.89716 12.4651H6.925L6.60369 14H4ZM7.15767 11.0349H8.63125L7.91108 7.53488L7.15767 11.0349Z" fill="url(#paint3_linear_2254_4728)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_f_2254_4728" x="-3" y="-3" width="41" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_2254_4728"/>
</filter>
<filter id="filter1_f_2254_4728" x="-24" y="-10" width="41" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_2254_4728"/>
</filter>
<linearGradient id="paint0_linear_2254_4728" x1="-2.89083e-07" y1="12" x2="37" y2="2" gradientUnits="userSpaceOnUse">
<stop stop-color="#9FFFD4"/>
<stop offset="0.601259" stop-color="#6AF0FF" stop-opacity="0.5"/>
<stop offset="1" stop-color="#F0CDFF"/>
</linearGradient>
<linearGradient id="paint1_linear_2254_4728" x1="-2.89083e-07" y1="12" x2="37" y2="2" gradientUnits="userSpaceOnUse">
<stop stop-color="#9FFFD4"/>
<stop offset="0.601259" stop-color="#6AF0FF" stop-opacity="0.5"/>
<stop offset="1" stop-color="#F0CDFF"/>
</linearGradient>
<linearGradient id="paint2_linear_2254_4728" x1="15.5468" y1="8.87207" x2="9.49997" y2="14" gradientUnits="userSpaceOnUse">
<stop stop-color="#4DDEC4"/>
<stop offset="1" stop-color="#7D8DFF"/>
</linearGradient>
<linearGradient id="paint3_linear_2254_4728" x1="15.5468" y1="8.87207" x2="9.49997" y2="14" gradientUnits="userSpaceOnUse">
<stop stop-color="#4DDEC4"/>
<stop offset="1" stop-color="#7D8DFF"/>
</linearGradient>
</defs>
</svg>
