.umo-scrollbar() {
  overflow: auto;
  &::-webkit-scrollbar {
    width: var(--umo-scrollbar-size) !important;
    height: var(--umo-scrollbar-size) !important;
    position: absolute;
  }
  &::-webkit-scrollbar-track {
    width: var(--umo-scrollbar-size) !important;
    background: transparent;
  }
  &:hover::-webkit-scrollbar-thumb {
    background-color: var(--umo-scrollbar-thumb-color);
    background-clip: padding-box;
    border-radius: 1em;
    transition: background-color 0.3s;
    cursor: pointer;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: var(--umo-scrollbar-thumb-hover-color);
  }
}
.umo-page-divider() {
  display: block;
  white-space: nowrap;
  letter-spacing: 100vw;
  overflow: hidden;
  width: 100%;
  user-select: none;
  border: none;
  height: unset;
  &::before {
    display: block;
    content: '\2000';
    overflow: hidden;
    text-decoration-line: line-through;
    text-decoration-style: solid;
    text-decoration-thickness: 0.1em;
  }
  &::after {
    overflow: hidden;
  }
  &[data-type='signle'] {
    &::before {
      text-decoration-style: solid;
    }
  }
  &[data-type='signle-bold'] {
    &::before {
      text-decoration-style: solid;
      text-decoration-thickness: 0.3em;
    }
  }
  &[data-type='double'] {
    &::before {
      text-decoration-style: double;
    }
  }
  &[data-type='dotted'] {
    &::before {
      text-decoration-style: dotted;
    }
  }
  &[data-type='dashed'] {
    &::before {
      text-decoration-style: dashed;
    }
  }
  &[data-type='dashed-double'] {
    &::before {
      text-decoration-style: dashed;
      text-decoration-thickness: 0.1em;
      margin-top: 0.5em;
      line-height: 0.4em;
    }
    &::after {
      display: block;
      content: '\2000';
      text-decoration-line: line-through;
      text-decoration-thickness: 0.1em;
      text-decoration-style: dashed;
      line-height: 0.4em;
      margin-bottom: 0.5em;
    }
  }
  &[data-type='double-bold-top'] {
    &::before {
      text-decoration-style: solid;
      text-decoration-thickness: 0.3em;
      margin-top: 0.5em;
      line-height: 0.4em;
    }
    &::after {
      display: block;
      content: '\2000';
      text-decoration-line: line-through;
      text-decoration-thickness: 0.05em;
      text-decoration-style: solid;
      line-height: 0.4em;
      margin-bottom: 0.5em;
    }
  }
  &[data-type='double-bold-bottom'] {
    &::before {
      text-decoration-style: solid;
      text-decoration-thickness: 0.05em;
      margin-top: 0.5em;
      line-height: 0.4em;
    }
    &::after {
      display: block;
      content: '\2000';
      text-decoration-line: line-through;
      text-decoration-thickness: 0.3em;
      text-decoration-style: solid;
      line-height: 0.4em;
      margin-bottom: 0.5em;
    }
  }
  &[data-type='wavy'] {
    &::before {
      text-decoration-style: wavy;
      text-decoration-thickness: 0.12em;
    }
  }
}
