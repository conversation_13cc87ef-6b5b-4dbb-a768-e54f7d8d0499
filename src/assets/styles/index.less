@import './_variables.less';
@import './_mixins.less';
@import './tdesign.less';

* {
  margin: 0;
  padding: 0;
}


ul, ol {
  list-style: initial; /* 重置所有列表样式 */
  
}

.umo-scrollbar {
  .umo-scrollbar();
}
.__ellipsis {
  white-space: nowrap;      /* 确保文本在一行内显示 */
  overflow: hidden;         /* 超出容器部分的文本隐藏 */
  text-overflow: ellipsis;  /* 使用省略号表示被截断的文本 */
}

.fount-bold {
  width: 55px;
  height: 55px;
  margin: 5px;
  border-radius: 5px;
  text-align: center;
  padding-top: 5px;
  &:hover {
    background-color: var(--umo-button-hover-background);
  }
  .fount-bold-text {
    font-size: 12px;
    text-align: center;
    cursor: pointer;
  }
}
.chectbold {
  background-color: var(--umo-button-hover-background);
}
.required {
  color: red;
}