<template>
  <t-dialog v-model:visible="dialogVisible" header="删除记录" :on-confirm="handleConfirm" :on-cancel="handleClose"
    :on-close="handleClose" width="500" attach="body">
    <t-input v-model="password" style="width: 240px" type="password" placeholder="请输入登录密码" />
  </t-dialog>
</template>

<script setup>
import { checkMatches } from "@/api/system/user";
import { MessagePlugin } from 'tdesign-vue-next';
const props = defineProps({
  modelValue: Boolean,
  title: {
    type: String,
    default: "确认要删除吗？",
  },
});

const { proxy } = getCurrentInstance();
const dialogVisible = ref(false);
const emit = defineEmits(["confirm", "update:modelValue"]);
const password = ref("");

watch(
  () => props.modelValue,
  (val) => {

    dialogVisible.value = val;
    if (val) {
      password.value = "";
    }
  }
);
// 关闭
function handleClose() {
  emit("update:modelValue", false);
}

// 确认
async function handleConfirm() {
  try {
    let res = await checkMatches({
      password: password.value,
    });
    if (res.data == true) {
      emit("confirm", (re) => {
        if (re) {
          handleClose();
        }
      });
    } else {
      MessagePlugin.error("密码错误");
      // proxy.$message.error("密码错误");
    }
  } catch (error) {
    console.error(error);
  }
}
</script>

<style scoped lang="less">
.footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;

  button {
    margin-left: 10px;
  }
}
</style>