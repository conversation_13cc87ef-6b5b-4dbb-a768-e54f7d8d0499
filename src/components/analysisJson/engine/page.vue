<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-22 11:19:16
 * @LastEditTime: 2024-11-30 15:28:35
 * @FilePath: \dutp-editor\src\components\analysisJson\engine\page.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<template>
  <div class="is_page">
    <div
      class="analysisJson-page-header"
      :style="`background:url('${headerUrl}') no-repeat; width:100%; background-size:100% 100%;height:${footerUrl ? pageHeaderCss(checkTabs) : ''}`"
    ></div>
    <div
      class="analysisJson-page"
      :class="`${checkTabs != 'phone' ? 'analysisJson-page-tablet' : ''}`"
    >
      <!--  -->

      <!-- 这是他妈的一段 -->
      <analysisJson-engine-paragraph
        v-for="(ele, i) in pageData.content"
        :key="i"
        :paragraph-data="ele"
        :check-tabs="checkTabs"
      />
    </div>
    <div
      class="analysisJson-page-footer"
      :style="`background:url('${footerUrl}') no-repeat;background-size:100% 100%;height:${footerUrl ? pageHeaderCss(checkTabs) : ''}; width:100%;margin-bottom:10px`"
    ></div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
const { pageTemplateId, getPageTemplate } = useTemplate()
const props = defineProps({
  pageData: {
    type: Object,
    default: () => ({}),
  },
  checkTabs: String,
})
let obj = $ref(null)
const route = useRoute()
onMounted(() => {
  if (route.query.chapterId) {
    getPageTemplate(route.query.chapterId)
  }
  obj = pageTemplateId.value
  localStorage.setItem('checkTabs', props.checkTabs)
})

const headerUrl = $computed(() => {
  return obj ? obj.headerUrl : ''
})

const footerUrl = $computed(() => {
  return obj ? obj.footerUrl : ''
})

const pageHeaderCss = $computed(() => {
  return function (item) {
    switch (item) {
      case 'phone':
        return '71px'
      case 'tablet':
        return '86px'
      case 'desktop':
        return '86px'
      default:
        return '71px'
    }
  }
})

watch(
  () => pageTemplateId.value,
  (val) => {
    obj = val
  },
)
</script>

<style lang="less" scoped>
.analysisJson-page {
  background-color: #fff;
  padding: 10px;
  min-height: 500px;
  line-height: 1.5;

  .umo-menu .umo-submenu {
    list-style: none;
  }
  ::v-deep(table) {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    margin: 0;
    overflow: hidden;
    page-break-inside: auto;

    tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }

    // thead {
    //   display: table-header-group;
    // }
    td,
    th {
      min-width: 1em;
      border: 1px solid var(--umo-content-table-border-color);
      padding: 3px 5px;
      vertical-align: middle;
      box-sizing: border-box;
      position: relative;

      > * {
        margin-bottom: 0;
      }

      &[data-align='left-top'] {
        vertical-align: top;
        text-align: left;
      }

      &[data-align='center-top'] {
        vertical-align: top;
        text-align: center;
      }

      &[data-align='right-top'] {
        vertical-align: top;
        text-align: right;
      }

      &[data-align='justify-top'] {
        vertical-align: middle;
        text-align: justify;
      }

      &[data-align='left-middle'] {
        vertical-align: middle;
        text-align: left;
      }

      &[data-align='center-middle'] {
        vertical-align: middle;
        text-align: center;
      }

      &[data-align='right-middle'] {
        vertical-align: middle;
        text-align: right;
      }

      &[data-align='justify-middle'] {
        vertical-align: middle;
        text-align: justify;
      }

      &[data-align='left-bottom'] {
        vertical-align: bottom;
        text-align: left;
      }

      &[data-align='center-bottom'] {
        vertical-align: middle;
        text-align: center;
      }

      &[data-align='right-bottom'] {
        vertical-align: bottom;
        text-align: right;
      }

      &[data-align='justify-bottom'] {
        vertical-align: bottom;
        text-align: justify;
      }
    }

    th {
      font-weight: bold;
      text-align: left;
      background-color: var(--umo-content-table-thead-background);
    }

    .selectedCell:after {
      z-index: 2;
      position: absolute;
      content: '';
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: var(--umo-content-table-selected-background);
      pointer-events: none;
    }

    .column-resize-handle {
      position: absolute;
      right: -1px;
      top: 0;
      bottom: -1px;
      width: 3px;
      background-color: var(--umo-primary-color);
      pointer-events: none;
    }

    p {
      margin: 0;
    }
  }
}

.analysisJson-page-footer {
  &:last-child {
    margin-bottom: 0;
  }
}

.analysisJson-page-tablet {
  width: 790px;
  margin: 0 auto;
  background-color: #fff;
}
</style>
