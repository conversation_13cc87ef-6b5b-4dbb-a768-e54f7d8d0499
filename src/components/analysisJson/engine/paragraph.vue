<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-22 11:28:01
 * @LastEditTime: 2024-11-25 11:42:47
 * @FilePath: \dutp-editor\src\components\analysisJson\engine\paragraph.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<script>
import { defineComponent, toRaw } from 'vue'
import { getParagraph } from './paragraphTool.js'

export default defineComponent({
  props: {
    paragraphData: {
      type: Object,
      default: () => ({}),
    },
    checkTabs: String,
  },
  setup(props) {
    return () => getParagraph(props.paragraphData, props.checkTabs)
  },
})
</script>
