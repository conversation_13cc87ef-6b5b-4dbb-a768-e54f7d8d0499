/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-22 11:41:13
 * @LastEditTime: 2024-12-11 15:01:30
 * @FilePath: \dutp-editor\src\components\analysisJson\engine\paragraphTool.js
 * @Description:
 *
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import { h, toRaw } from 'vue'

import module from '../module/index.module'
//需要吧attrs的属性注入行内的标签
const addAttribute = ['iframe']
const isAddAttribute = (name) => addAttribute.includes(name)
const isModule = (name) => !!module[name]
let checkTabs = null
/**获取样式*/
const getStyle = (data, type) => {
  if (type === 'mark') {
    const { marks } = data
    if (!marks?.length) return {}
    const styles = {}
    Object.keys(data?.attrs?.margin || {}).forEach((key) => {
      const item = margin[key]
      if (item) {
        styles[`margin-${key}`] = `${item}px`
      }
    })

    marks.forEach((ele) => {
      switch (ele.type) {
        case 'lineHeight':
          styles.lineHeight = ele.attrs.lineHeight
          break
        case 'highlight': //高亮
          styles.backgroundColor = ele.attrs.color
          break

        case 'textStyle': //文字样式
          for (const key in ele.attrs) {
            const element = ele.attrs[key]
            if (element) {
              styles[key] = element
            }
          }
          break

        case 'textShadow': //文字阴影
          styles.textShadow = '2px 2px 2px'
          break

        case 'textGlow': //文字效果--光
          styles.textShadow =
            ' 0 0 5px rgba(255, 255, 0, 0.8),0 0 10px rgba(255, 255, 0, 0.8),0 0 20px rgba(255, 255, 0, 0.8),0 0 30px rgba(255, 255, 0, 1),0 0 40px rgba(255, 255, 0, 1),0 0 50px rgba(255, 255, 0, 1)'
          break
        case 'textBorder': //文字轮廓
          styles.TextBorder =
            'text-shadow:-1px -1px 0 #000,1px -1px 0 #000,-1px 1px 0 #000,1px 1px 0 #000;' /* 边框宽度和颜色 */
          break
        case 'textStroke': //文字轮廓
          styles.TextStroke = '1px'
          styles.textFillColor = 'transparent'
          break

        case 'italic': //文字倾斜
          styles.fontStyle = 'italic'
          break

        case 'bold': //文字加粗
          styles.fontWeight = 'bold'
          break

        case 'underline': //下划线
          const textDecoration = styles.textDecoration || ''
          styles.textDecoration = `underline${textDecoration}`
          break

        case 'strike': //中划线
          const textStyle = styles.textDecoration || ''
          styles.textDecoration = `${textStyle}line-through`
          break

        case 'subscript': //下标
          styles.verticalAlign = 'sub'
          break

        case 'superscript': //上标
          styles.verticalAlign = 'super'
          break

        default:
          break
      }
    })

    for (const key in styles) {
      const element = styles[key]
      if (!element) {
        delete styles[key]
      }
    }
    return styles
  } else {
    let obj = {}

    try {
      const {
        lineHeight,
        textAlign,
        listType,
        margin,
        backgroundBorder,
        backgroundRepeat,
      } = data?.attrs
      if (
        backgroundBorder.borderColor &&
        backgroundBorder.borderWidth &&
        backgroundBorder.borderStyle
      ) {
        obj.border = `${backgroundBorder.borderWidth}px ${backgroundBorder.borderStyle} ${backgroundBorder.borderColor}`
      }
      if (backgroundBorder.borderRadius) {
        obj.borderRadius = `${backgroundBorder.borderRadius}px`
      }
      if (backgroundBorder.borderPadding) {
        obj.padding = `${backgroundBorder.borderPadding}px`
      }
      obj = { ...obj, lineHeight, textAlign, listStyleType: listType }
      Object.keys(margin).forEach((key) => {
        const item = margin[key]
        if (item) {
          obj[`margin-${key}`] = `${item}px`
        }
      })
    } catch (error) {}

    return obj
  }
}

/**获取标签名称*/
const getLableName = (data) => {
  const { type, attrs } = data
  switch (type) {
    case 'heading':
      return `h${attrs.level}`

    case 'paragraph':
      return `p`

    case 'text':
      return `span`

    case 'orderedList': //有序列表
      return `ol`

    case 'bulletList': //有序列表
      return `ul`

    case 'listItem': //有序列表
      return `li`

    case 'blockquote':
      return `blockquote`

    case 'link':
      return `a`

    case 'table':
      return `table`

    case 'tableRow':
      return `tr`

    case 'tableHeader':
      return `th`

    case 'tableCell':
      return `td`

    default:
      if (isModule(data.type)) {
        return module[data.type]
      }
      break
  }
}

/**获取标签样式 或 给自定义组件传递参数*/
const getProps = (data, type, parentType) => {
  if (!isModule(data.type) && !isAddAttribute(type)) {
    if (data.type === 'tableHeader' || data.type === 'tableCell') {
      const props = { ...toRaw(data.attrs) }
      delete props.align
      delete props.backgroundColor
      props['data-align'] = data.attrs.align
      props.style = {
        ...getStyle(data, type),
        backgroundColor: data.attrs.backgroundColor,
        border:
          (data.attrs.borderStyle || '1px solid') +
          (data.attrs.borderColor || '#f1f3f5'),
      }

      return props
    }

    if (type === 'mark') {
      // 此处有个bug，如果mark的type为textBorder，则直接返回textBorder的属性
      if (data.marks?.[0]?.type === 'textBorder') {
        const props = { ...toRaw(data.marks[0]?.attrs) }
        props.style = props.textBorder
        return props
      }
      return { style: getStyle(data, type) }
    } else {
      const obj = {
        id: data?.attrs?.id,
        style: {
          ...getStyle(data),
          marginTop: 'var(--umo-content-node-bottom)',
        },
      }

      if (data.type == 'heading') {
        obj['data-heading-id'] = data.attrs.id
      }
      if (parentType === 'tableHeader' || parentType === 'tableCell') {
        delete obj.style.marginTop
        delete obj.style.textAlign
      }
      if (data.type === 'orderedList') {
        if (data.attrs.start) {
          obj.start = data.attrs.start
        }
      }

      if (data?.attrs) {
        const {
          containerColor,
          backgroundImage,
          backgroundSize,
          indent,
          backgroundRepeat,
        } = data?.attrs

        if (containerColor) {
          obj.style.backgroundColor = containerColor
        }

        if (backgroundImage) {
          obj.style.backgroundImage = `url(${backgroundImage})`
        }
        if (backgroundRepeat) {
          obj.style.backgroundRepeat = backgroundRepeat
        }

        if (backgroundSize) {
          obj.style.backgroundSize = backgroundSize
        }
        if (indent >= 1 && indent <= 5) {
          obj.style.textIndent = `${indent * 2}em`
        }
      }
      return obj
    }
  } else {
    const props = { ...toRaw(data.attrs), checkTabs }

    // 行内节点也可以添加mark，目前只能特殊判断，然后解析成style,传入节点
    if (data.type === 'links') {
      props.style = {
        ...getStyle(data, 'mark'),
      }
    }

    return props
  }
}

const getChildren = (data, type = 'mark') => {
  const { content } = data
  if (!content?.length) return h('br', {}, '')
  const childrenList = []

  content.forEach((ele) => {
    if (ele.text) {
      for (const i in ele.text) {
        let item = ele.text[i]
        if (item == ' ') {
          item = '\u00A0'
        }
        childrenList.push(h(getLableName(ele), getProps(ele, type), item))
      }
    } else {
      const lableName = getLableName(ele)
      if (lableName == 'table') {
        const rowData = ele?.content[0]?.content
        if (rowData) {
          childrenList.push(
            h(lableName, getProps(ele), [
              h('colgroup', {}, getColList(rowData)),
              ...getChildren(ele),
            ]),
          )
        }
      } else {
        childrenList.push(
          h(lableName, getProps(ele, '', data.type), getChildren(ele)),
        )
      }
    }
  })

  return childrenList
}

/**
 * 获取表格列
 * @param {*} rowData
 * @returns
 */
const getColList = (rowData) => {
  const colList = []
  rowData.map((item) => {
    const { colwidth } = item.attrs
    if (colwidth) {
      colwidth.map((col) => {
        colList.push(
          h('col', {
            style: { width: `${parseInt(col, 10)}px` },
          }),
        )
      })
    } else {
      colList.push(h('col', { style: { 'min-width': '25px' } }))
    }
  })
  return colList
}

export const getParagraph = (paragraphData, checkTab) => {
  const data = toRaw(paragraphData)
  checkTabs = checkTab
  const lableName = getLableName(data)
  if (lableName == 'table') {
    const rowData = data?.content[0]?.content
    if (rowData) {
      return h(lableName, getProps(data), [
        h('colgroup', {}, getColList(rowData)),
        ...getChildren(data),
      ])
    }
  }
  return h(lableName, getProps(data), getChildren(data))
}
