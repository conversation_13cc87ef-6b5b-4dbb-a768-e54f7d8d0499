<template>
  <div class="analysisJson">
    <analysisJson-engine-page
      v-for="(ele, i) in pageConfigList"
      :key="i"
      :page-data="ele"
      :check-tabs="checkTabs"
    />
  </div>
</template>

<script setup>
const props = defineProps({
  pageConfigList: {
    type: Array,
    default: () => [],
  },
  checkTabs: {
    type: String,
    default: '',
  },
})
</script>

<style lang="less" scoped>
.analysisJson {
  background-color: #fff;
  color: #333;
  word-break: break-all;
}
</style>
