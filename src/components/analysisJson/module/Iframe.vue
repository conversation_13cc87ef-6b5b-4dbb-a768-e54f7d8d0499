<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-26 09:17:25
 * @LastEditTime: 2024-11-26 09:17:33
 * @FilePath: \dutp-editor\src\components\analysisJson\module\Iframe.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<style lang="less" scoped>
.Iframe {
  width: 100%;
  height: 100%;
}
</style>

<template>
  <div class="Iframe">
    <iframe :src="src" width="100%" :height="height"></iframe>
  </div>
</template>

<script setup>
const props = defineProps({
  height: String,
  src: String,
  width: String,
  margin: Object,
})
</script>
