<!-- 模块说明 -->
<template>
  <div class="audio">
    <div id="__audio__" class="audio-main">
      <audio
        class="audio-play"
        :src="src"
        preload="auto"
        controls
        crossorigin="anonymous"
      ></audio>
      <div class="audio-name __ellipsis" :style="{ width: width + 'px' }">
        {{ audioTitle }}
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  height: String,
  src: String,
  checkTabs: String,
  nodeAlign: String,
  audioTitle: String,
})
</script>

<style lang="less" scoped>
.audio {
  margin: 25px 0;
  width: 100%;
  height: auto;

  &:hover {
    box-shadow: var(--umo-shadow);
  }
  .audio-play {
    width: 100%;
    height: 50px;
  }
}
.audio-name {
  padding: 5px 0;
  text-align: center;
  font-size: 14px;
  color: #333;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 超出容器部分的文本隐藏 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
}
</style>
