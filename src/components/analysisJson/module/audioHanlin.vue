<template>
  <div class="audioHanlin-bg" @click="handleClick">
    <SoundIcon />
    <div id="__audio__" style="display: none">
      <audio
        ref="audioHanlinRef"
        class="audio-play"
        :src="src"
        preload="auto"
        controls
        crossorigin="anonymous"
      ></audio>
    </div>
  </div>
</template>
<script setup>
import { SoundIcon } from 'tdesign-icons-vue-next'
import { ref } from 'vue'
const props = defineProps({
  src: String,
  checkTabs: String,
})

const audioHanlinRef = ref(null)

const handleClick = () => {
  audioHanlinRef.value.play()
}
</script>

<style lang="less" scoped>
.audioHanlin-bg {
  display: inline-block;
  padding: 0 5px;
  cursor: pointer;
  position: relative;
  top: -4px;
}
</style>
