<template>
  <span class="bubble-main">
    <t-popup theme="default" show-arrow>
      <template #content>
        <div class="slotCss">
          <div v-html="bubbleContent"></div>
          <div v-if="bubbleUrl" class="bubble-url">
            <a :href="bubbleUrl" target="_blank">
              <ConstraintIcon />
            </a>
          </div>
        </div>
      </template>

      <span
        class="bubbleTitleCss"
        :style="{
          borderBottom:
            bubbleType == '2' ? 'none' : '2px solid var(--umo-primary-color)',
        }"
      >
        <span v-if="bubbleType != '2'">{{ bubbleTitle }}</span>
        <i v-if="bubbleType == '2'" class="bubble-icon"></i
      ></span>
    </t-popup>
  </span>
</template>

<script setup>
import { ConstraintIcon } from 'tdesign-icons-vue-next'
const { id, bubbleTitle, bubbleContent, bubbleUrl } = defineProps({
  id: {
    type: String,
    default: '',
  },
  bubbleTitle: {
    type: String,
    default: '',
  },
  bubbleType: {
    type: String,
    default: '1',
  },
  bubbleContent: {
    type: String,
    default: '',
  },
  bubbleUrl: {
    type: String,
    default: '',
  },
})
</script>

<style lang="less" scoped>
.slotCss {
  // padding: 5px 0px;
}

:global(.umo-popup__arrow) {
  bottom: -3px;
}

:global(.umo-popup) {
  max-width: 400px;
}

.bubble-url {
  text-align: right;
}

.bubble-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url('@/assets/images/bubble-icon.svg') no-repeat;
  background-size: 100% 100%;
}

.bubbleTitleCss {
  font-weight: bold;
  font-size: 1rem;
  color: var(--umo-primary-color);
  // border-bottom: 2px solid var(--umo-primary-color);
}
</style>
