<template>
  <div class="headerbg"
    :style="`height:${template?.chapterHeaderHeight / (checkTabs == 'phone' ? 3.4 : 2.5)}px;background:url(${template?.chapterHeaderUrl}) no-repeat;background-size:100% 100%;}`">
    <div class="header-title">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  title: String,
  checkTabs: String,
})

const { pageTemplateId } = useTemplate()
let template = $ref(null)

onMounted(() => {
  template = pageTemplateId.value
})

watch(
  () => pageTemplateId.value,
  (val) => {
    template = val
  },
)
</script>
<style lang="less" scoped>
.headerbg {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;

  .header-title {
    font-weight: bold;
    padding: 20px;
    outline: none;
    width: 100%;
  }
}
</style>
