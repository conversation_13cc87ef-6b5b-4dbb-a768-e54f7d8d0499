<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-28 09:20:51
 * @LastEditTime: 2024-11-28 10:19:24
 * @FilePath: \dutp-editor\src\components\analysisJson\module\codeBlock.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<template>
  <prism :language="language" class="line-numbers" :theme="theme">
    {{ code }}
  </prism>
</template>

<script setup>
import 'prismjs'
import 'prismjs/components/prism-sql'
import 'prismjs/components/prism-bash'
import 'prismjs/components/prism-css'
import 'prismjs/components/prism-ini'
import 'prismjs/components/prism-kotlin'
import 'prismjs/components/prism-markup'
import 'prismjs/components/prism-r'
import 'prismjs/components/prism-basic'
import 'prismjs/components/prism-vbnet'
import 'prismjs/components/prism-c'
import 'prismjs/components/prism-opencl'
import 'prismjs/components/prism-diff'
import 'prismjs/components/prism-java'
import 'prismjs/components/prism-less'
import 'prismjs/components/prism-objectivec'
import 'prismjs/components/prism-wasm'
import 'prismjs/components/prism-cpp'
import 'prismjs/components/prism-go'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-js-templates'
import 'prismjs/components/prism-jsx'
import 'prismjs/components/prism-lua'
import 'prismjs/components/prism-perl'
import 'prismjs/components/prism-python'
import 'prismjs/components/prism-rust'
import 'prismjs/components/prism-swift'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-csharp'
import 'prismjs/components/prism-graphql'
import 'prismjs/components/prism-json'
import 'prismjs/components/prism-makefile'
import 'prismjs/components/prism-scss'
import 'prismjs/components/prism-typescript'
import 'prismjs/components/prism-tsx'
import 'prismjs/components/prism-yaml'
import 'prismjs/components/prism-regex'

import Prism from 'vue-prism-component'

defineProps({
  margin: Object,
  theme: String,
  code: String,
  language: String,
  prismLanguage: String,
})
</script>

<style lang="less" scoped>
.line-numbers {
  overflow-x: auto;
}
.bellCss:hover {
  background: #2d2d2d;
}
</style>
