<template>
  <div class="tdesign-demo-image-viewer__base" :style="{ height: 'auto' }">
    <!--图片瀑布流组件-->

    <masonryLayout
      :images="imgList"
      :columns="columns"
      :is-show-no="isShowNo"
      :is-show-img-title="isShowImgTitle"
      :preview="true"
    />
    <div class="gallery-title">
      <text v-if="isShowGalleryTitle == '1'">{{ galleryTitle }}</text>
    </div>
  </div>
</template>
<script setup>
import masonryLayout from '@/components/menus/toolbar/insert/components/imageGallery/masonryLayout.vue'

const props = defineProps({
  height: Number,
  src: String,
  checkTabs: String,
  nodeAlign: String,
  galleryTitle: String,
  randerJson: String,
  isShowNo: String,
  isShowGalleryTitle: String,
  isShowImgTitle: String,
  imgList: Array,
  columns: Number,
})
// const imgList = ref(JSON.parse(props.randerJson));
const equipmentWidth = {
  phone: 'calc(22vw - 70px)',
  tablet: 'calc(50vw - 70px)',
  desktop: 'calc(55vw - 70px)',
}

const position = {
  'flex-start': 'left',
  'flex-end': 'right',
}
</script>
<style>
.umo-image-viewer-preview-image .umo-image-viewer__modal-mask {
  background-color: var(--td-mask-active) !important;
}
.umo-image-viewer-preview-image .umo-image-viewer__modal-pic {
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}
</style>
<style scoped lang="less">
.gallery-title {
  width: 100%;
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 14px;
  margin-top: 20px;
}
.tdesign-demo-image-viewer__ui-image {
  //display: inline-flex;
  //position: relative;
  //justify-content: center;
  //align-items: center;
  //border-radius: var(--td-radius-small);
  //overflow: hidden;
}

.tdesign-demo-image-viewer__ui-image--hover {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--td-text-color-anti);
  line-height: 22px;
  transition: 0.2s;
}

.tdesign-demo-image-viewer__ui-image:hover
  .tdesign-demo-image-viewer__ui-image--hover {
  opacity: 1;
  cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--footer {
  padding: 0 16px;
  height: 56px;
  width: 100%;
  line-height: 56px;
  font-size: 16px;
  position: absolute;
  bottom: 0;
  color: var(--td-text-color-anti);
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  display: flex;
  box-sizing: border-box;
}

.tdesign-demo-image-viewer__ui-image--title {
  flex: 1;
}

.tdesign-demo-popup__reference {
  margin-left: 16px;
}

.tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
  cursor: pointer;
}

.tdesign-demo-image-viewer__base {
  margin: 10px 0;
  //border: 4px solid var(--td-bg-color-secondarycontainer);
  border-radius: var(--td-radius-medium);

  position: relative;
}
</style>
