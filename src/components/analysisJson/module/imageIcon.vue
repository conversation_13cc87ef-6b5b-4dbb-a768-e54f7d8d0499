<template>
  <div
    class="tdesign-demo-image-viewer__base"
    :style="{
      height: height + 'px',
      width: width + 'px',
      float: nodeFloat,
    
    }"
  >
    <div class="tdesign-demo-image-viewer__ui-image" @click="viewerImg">
      <img
        :src="src"
        class="tdesign-demo-image-viewer__ui-image--img"
        :style="{ [position[nodeAlign]]: 0 }"
      />
    </div>
  </div>
  <t-dialog
    v-model:visible="viewerVisible"
    header="预览"
    attach="body"
    width="30%"
    :confirm-btn="null"
    :close-on-overlay-click="false"
  >
    <imgView
      :img-url="src"
      :img-title="imageTitle"
      :is-show-image-title="'1'"
      :link-address="linkAddress"
    />
  </t-dialog>
</template>
<script setup>
import imgView from '@/components/menus/toolbar/insert/components/image/imgView.vue'
const props = defineProps({
  height: [Number, String],
  src: String,
  checkTabs: String,
  nodeAlign: String,
  name: String,
  imageTitle: String,
  linkAddress: String,
  id: String,
  extend: Boolean,
  isShowNo: String,
  vnode: Boolean,
  type: String,
  size: Number,
  file: Object,
  content: String,
  width: [Number, String],
  left: Number,
  top: Number,
  angle: Number,
  draggable: Boolean,
  rotatable: Boolean,
  equalProportion: Boolean,
  flipX: Boolean,
  flipY: Boolean,
  uploaded: Boolean,
  error: Boolean,
  previewType: String,
  imgDescribe: String,
  nodeFloat: String,
})

const equipmentWidth = {
  phone: 'calc(22vw - 70px)',
  tablet: 'calc(50vw - 70px)',
  desktop: 'calc(55vw - 70px)',
}

// 预览
const viewerVisible = ref(false)
const viewerImg = () => {
  viewerVisible.value = true
}

const position = {
  'flex-start': 'left',
  'flex-end': 'right',
}
</script>
<style>
.umo-image-viewer-preview-image .umo-image-viewer__modal-mask {
  background-color: var(--td-mask-active) !important;
}
.umo-image-viewer-preview-image .umo-image-viewer__modal-pic {
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}
</style>
<style scoped lang="less">
.tdesign-demo-image-viewer__ui-image {
  width: 100%;
  height: 100%;
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: var(--td-radius-small);
  overflow: hidden;
}

.tdesign-demo-image-viewer__ui-image--hover {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--td-text-color-anti);
  line-height: 22px;
  transition: 0.2s;
}

.tdesign-demo-image-viewer__ui-image:hover
  .tdesign-demo-image-viewer__ui-image--hover {
  opacity: 1;
  cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  cursor: pointer;
  position: absolute;
}

.tdesign-demo-image-viewer__ui-image--footer {
  padding: 0 16px;
  height: 56px;
  width: 100%;
  line-height: 56px;
  font-size: 16px;
  position: absolute;
  bottom: 0;
  color: var(--td-text-color-anti);
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  display: flex;
  box-sizing: border-box;
}

.tdesign-demo-image-viewer__ui-image--title {
  flex: 1;
}

.tdesign-demo-popup__reference {
  margin-left: 16px;
}

.tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
  cursor: pointer;
}

.tdesign-demo-image-viewer__base {
  display: inline-flex;
  // margin: 10px;
  // border: 4px solid var(--td-bg-color-secondarycontainer);
  // border-radius: var(--td-radius-medium);
  position: relative;
  // top: 6px;
}
</style>
