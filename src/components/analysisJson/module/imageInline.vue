<template>
  <span class="tdesign-demo-image-viewer__base">
    <div class="tdesign-demo-image-viewer__ui-image">
      <span class="imgInline-icon"></span>
      <div
        class="tdesign-demo-image-viewer__ui-image--hover"
        @click="viewerImg"
      >
        <BrowseIcon size="1.4em" />
      </div>
    </div>
  </span>
  <t-dialog
    v-model:visible="viewerVisible"
    header="预览"
    attach="body"
    width="30%"
    :confirm-btn="null"
    :close-on-overlay-click="false"
  >
    <imgView
      :img-url="src"
      :img-title="imageTitle"
      :isShowImageTitle="'1'"
      :link-address="linkAddress"
      :img-describe="imgDescribe"
    />
  </t-dialog>
</template>

<script setup>
import { BrowseIcon } from 'tdesign-icons-vue-next'

import imgView from '@/components/menus/toolbar/insert/components/image/imgView.vue'
const props = defineProps({
  height: [Number, String],
  src: String,
  checkTabs: String,
  nodeAlign: String,
  name: String,
  isShowImageTitle: String,
  imageTitle: String,
  linkAddress: String,
  id: String,
  extend: Boolean,
  isShowNo: String,
  vnode: Boolean,
  type: String,
  size: Number,
  file: Object,
  content: String,
  width: [Number, String],
  left: Number,
  top: Number,
  angle: Number,
  draggable: Boolean,
  rotatable: Boolean,
  equalProportion: Boolean,
  flipX: Boolean,
  flipY: Boolean,
  uploaded: Boolean,
  error: Boolean,
  previewType: String,
  imgDescribe: String,
})

const equipmentWidth = {
  phone: 'calc(22vw - 70px)',
  tablet: 'calc(50vw - 70px)',
  desktop: 'calc(55vw - 70px)',
}
// 预览
const viewerVisible = ref(false)
const viewerImg = () => {
  viewerVisible.value = true
}

const position = {
  'flex-start': 'left',
  'flex-end': 'right',
}
</script>
<style scoped>
.umo-image-viewer-preview-image .umo-image-viewer__modal-mask {
  background-color: var(--td-mask-active) !important;
}
.umo-image-viewer-preview-image .umo-image-viewer__modal-pic {
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}
</style>
<style scoped lang="less">
.tdesign-demo-image-viewer__ui-image {
  width: 100%;
  height: 100%;
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: var(--td-radius-small);
  overflow: hidden;
}

.tdesign-demo-image-viewer__ui-image--hover {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--td-text-color-anti);
  line-height: 22px;
  transition: 0.2s;
  span {
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
    align-items: center;
  }
}

.tdesign-demo-image-viewer__ui-image:hover
  .tdesign-demo-image-viewer__ui-image--hover {
  opacity: 1;
  cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  cursor: pointer;
  position: absolute;
}

.tdesign-demo-image-viewer__ui-image--footer {
  padding: 0 16px;
  height: 56px;
  width: 100%;
  line-height: 56px;
  font-size: 16px;
  position: absolute;
  bottom: 0;
  color: var(--td-text-color-anti);
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  display: flex;
  box-sizing: border-box;
}

.tdesign-demo-image-viewer__ui-image--title {
  flex: 1;
}

.tdesign-demo-popup__reference {
  margin-left: 16px;
}

.tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
  cursor: pointer;
}

.tdesign-demo-image-viewer__base {
  display: inline-flex;
  align-items: center;
  position: relative;
  top: 12px;
}
.imgInline-icon {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: url('@/assets/images/imgInline.svg') no-repeat;
  background-size: 100% 100%;
  margin-left: 4px;
}
</style>
