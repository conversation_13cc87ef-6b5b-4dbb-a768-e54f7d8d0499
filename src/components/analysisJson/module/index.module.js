import {
  INTERACTION,
  PSYCHOLOGY_HEALTH,
  QUESTIONS,
  RESOURCE_COVER,
  TABLE_PLUS,
  TEST_PAPER,
} from '@/extensions/page/node-names'

import Audio from './audio.vue'
import audioHanlin from './audioHanlin.vue'
import bubbleView from './bubbleView.vue'
import Buttons from './buttons.vue'
import chapterHeader from './chapterHeader.vue'
import CodeBlock from './codeBlock.vue'
import ColumnItem from './columnItem.vue'
import DividingLine from './dividingLine.vue'
import extendedReading from './extendedReading.vue'
import Iframe from './Iframe.vue'
import Image from './image.vue'
import ImageGallery from './imageGallery.vue'
import ImageIcon from './imageIcon.vue'
import ImageInLine from './imageInline.vue'
import ImageLayout from './imageLayout.vue'
import interaction from './interaction.vue'
import jointHeader from './jointHeader.vue'
import LayoutColumn from './layoutColumn.vue'
import Links from './links.vue'
import PaperWrapping from './paperWrapping.vue'
import PegeToc from './pegeToc.vue'
import PreviewFile from './previewFile.vue'
import PsychologyHealth from './psychologyHealth.vue'
import Questions from './questions.vue'
import RemarkView from './remarkView.vue'
import ResourceCover from './resourceCover.vue'
import tablePlus from './tablePlus.vue'
import TestPaper from './testPaper.vue'
import Video from './video.vue'
/**
 * key : components
 * key 需要与extensions文件夹下index.ts 配置自定义组件文件内name type保持一致
 * */
export default {
  buttons: Buttons,
  image: Image,
  imageInLine: ImageInLine,
  imageIcon: ImageIcon,
  interaction,
  video: Video,
  audio: Audio,
  iframe: Iframe,
  file: PreviewFile,
  horizontalRule: DividingLine,
  toc: PegeToc,
  [RESOURCE_COVER]: ResourceCover,
  [INTERACTION]: interaction,
  bubbleInline: bubbleView,
  codeBlock: CodeBlock,
  links: Links,
  [QUESTIONS]: Questions,
  imageLayout: ImageLayout,
  imageGallery: ImageGallery,
  remarkInline: RemarkView,
  [TABLE_PLUS]: tablePlus,
  chapterHeader,
  jointHeader,
  [TEST_PAPER]: TestPaper,
  [PSYCHOLOGY_HEALTH]: PsychologyHealth,
  layoutColumn: LayoutColumn,
  columnItem: ColumnItem,
  paperWrapping: PaperWrapping,
  extendedReading,
  audioHanlin,
}
