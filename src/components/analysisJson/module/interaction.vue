<template>
  <div id="__interaction__" class="interaction-main">
    <div v-if="InteractionType == 0">
      <div class="interaction-main-title">
        <div>
          <span
            class="interaction-main-title-content"
            v-html="themeContent"
          ></span>
        </div>
        <div class="goOnSubmit" @click="goOnSubmit">
          {{ isCheck ? '查看结果' : '继续提交' }}
        </div>
      </div>
      <div class="interaction-progressBar-list">
        <div
          v-for="(item, index) in voteList"
          :key="index"
          class="interaction-progressBar-item"
        >
          <div
            class="interaction-progressBar-item-title"
            :style="{ color: item.value != 0 ? '#fff' : '#333' }"
          >
            <span v-if="isCheck" @click="checkItem(index, item)">
              <span
                class="checkBox"
                :class="{ active: selectIndex == index }"
              ></span
              >{{ item.name }}</span
            >
            <span v-else>{{ item.name }}</span>
          </div>
          <t-progress
            :percentage="item.value"
            size="large"
            :style="{ height: item.value > 9 ? '' : '20px' }"
            :theme="item.value > 9 ? 'plump' : 'line'"
          />
        </div>
      </div>
    </div>

    <div v-if="InteractionType == 1">
      <div class="interaction-main-headers">
        <div class="interaction-main-title-type">
          {{ title(InteractionType.WORD_CLOUD) }}
        </div>
        <div class="interaction-main-btns">
          <t-button
            v-if="!isShow"
            style="margin-right: 10px"
            variant="outline"
            @click="isShow = true"
          >
            <template #icon>
              <Edit2Icon />
            </template>
            继续提交
          </t-button>
        </div>
      </div>
      <div class="interaction-main-title">
        <span
          class="interaction-main-title-content"
          v-html="themeContent"
        ></span>
      </div>
      <div v-show="isShow" class="interaction-input-keywords">
        <t-input
          v-model="keyWords"
          placeholder="请输入关键词"
          style="width: 300px"
        />
        <t-button style="margin-left: 10px" @click="addKeyWords"
          >提交关键词</t-button
        >
      </div>
      <chartWordCloud v-show="!isShow" :id="id" :data-list="dataList" />
    </div>
    <div v-if="interactionType == 2">
      <discussion
        :title="title(InteractionType.DISCUSSION)"
        :theme-content="themeContent"
      />
    </div>
    <div v-if="interactionType == 3">
      <imageWaterFall
        :title="title(InteractionType.IMAGE_WATER_FALL)"
        :theme-content="themeContent"
        :images="images"
      />
    </div>
  </div>
</template>

<script setup>
import { nodeViewProps } from '@tiptap/vue-3'
import { Edit2Icon } from 'tdesign-icons-vue-next'

import discussion from '@/components/discussion/discussion.vue'
import imageWaterFall from '@/components/imageWaterfall/imageWaterFall.vue'
import chartWordCloud from '@/components/wordcloud/chartsWordCloud.vue'
import { InteractionType } from '@/enum/extensions/interaction'

const { id, type, content, images, voteList, node, updateAttributes } =
  defineProps({
    id: {
      type: String,
      default: '',
    },
    interactionType: {
      type: String,
      default: '',
    },
    themeContent: {
      type: String,
      default: '',
    },
    voteList: {
      type: Array,
      default: () => [],
    },
    nodeViewProps,
  })
const title = (value) => {
  switch (value) {
    case InteractionType.WORD_CLOUD:
      return '词云'
    case InteractionType.VOTE:
      return '投票'
    case InteractionType.DISCUSSION:
      return '讨论'
    case InteractionType.IMAGE_WATER_FALL:
      return '图片瀑布流'
    default:
      return ''
  }
}

const keyWords = ref('')
const isShow = ref(false)
const dataList = ref([])
const isCheck = ref(false)

const goOnSubmit = () => {
  isCheck.value = !isCheck.value
}
const selectIndex = ref(null)
const selectItem = ref(null)
const checkItem = (index, item) => {
  selectItem.value = item

  voteList.find((item, idx) => {
    if (index !== idx) {
      item.checked = false
    }
  })

  voteList[index].checked = true
  selectIndex.value = index

  if (selectItem.value) {
    voteList.forEach((option) => {
      if (option.name === selectItem.value.name) {
        option.value = 100
      } else {
        option.value = 0
      }
    })
    isCheck.value = false
  }

  updateAttributes({ voteList })
}

onMounted(() => {
  if (dataList.value.length > 0) {
    isShow.value = false
  } else {
    isShow.value = true
  }
})

const addKeyWords = () => {
  if (keyWords.value) {
    let is_add = true
    dataList.value.forEach((item) => {
      if (item.name === keyWords.value) {
        item.value++
        is_add = false
      }
    })
    if (is_add) {
      dataList.value.push({
        name: keyWords.value,
        value: 1,
      })
    }
    keyWords.value = null
    isShow.value = false
  } else {
    useMessage('error', {
      content: '请填写关键词',
    })
  }
}
</script>

<style lang="less">
.bestsellers-container {
  height: 38.56rem;
}

#charts-content {
  /* 需要设置宽高后才会显示 */
  width: 100%;
  height: 100%;
}

.interaction-main {
  position: relative;

  .interaction-main-headers {
    display: flex;
    justify-content: space-between;

    .interaction-main-title-type {
      font-size: 18px;
      font-weight: bold;
    }
  }

  .interaction-progressBar-list {
    padding: 10px 20px;

    .interaction-progressBar-item {
      margin: 10px 0;
      position: relative;

      .umo-progress__bar {
        height: 20px;
      }

      .interaction-progressBar-item-title {
        position: absolute;
        left: 10px;
        color: #fff;
        z-index: 1;
        outline: none;

        .checkBox {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: #fff;
          border: 1px solid #eee;
          justify-content: center;
          align-items: center;
          display: inline-flex;
          position: relative;
          top: 2px;
          margin-right: 10px;
        }

        .active {
          &::before {
            content: '';
            position: absolute;
            top: 1px;
            left: 50%;
            width: 8px;
            height: 3px;
            border-left: 2px solid green;
            border-bottom: 2px solid green;
            transform: translateX(-50%) rotate(-45deg);
          }
        }
      }
    }

    .interaction-progressBar-add {
      display: flex;
      justify-content: flex-end;
      cursor: pointer;
    }
  }

  .interaction-input-keywords {
    display: flex;
    padding: 20px 0;
    justify-content: center;
  }

  .interaction-main-btns {
    display: flex;
    align-items: center;
    justify-content: center;

    .interaction-main-btns-edit {
      width: 20px;
      height: 20px;
      background-image: url(./edit.png);
      background-size: cover;
      cursor: pointer;
    }

    .interaction-main-btns-delete {
      width: 20px;
      height: 20px;
      background-image: url(./delete.png);
      background-size: cover;
      cursor: pointer;
    }
  }

  .interaction-main-title {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
    font-size: 20px;
    color: #666;

    .goOnSubmit {
      font-size: 14px;
      color: #666;
      cursor: pointer;
      margin-left: 20px;
    }

    .interaction-main-title-content {
      width: 450px;
      margin: 0 auto;
      word-wrap: break-word;
      word-break: break-all;
      text-align: center;
    }
  }

  .interaction-content {
    width: 85%;
    word-wrap: break-word;
    word-break: break-all;
    // display: flex;
    // justify-content: flex-start;
    display: flex;
    // flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;

    span {
      min-width: 40px;
    }
  }

  .interaction-btns {
    width: 15%;
    // height: 50px;
    display: flex;
    padding-left: 5px;
    padding-right: 5px;
    justify-content: space-between;
    border-left: 1px solid #d9d9d9;
    align-items: center;

    .interaction-btns-edit {
      background: #fff;
      padding: 5px;
      border: 1px solid #d9d9d9;
    }

    .interaction-btns-del {
      background: #fff;
      padding: 5px;
      border: 1px solid #d9d9d9;
    }
  }
}
</style>
