<template>
  <div ref="containerRef" class="umo-node-view">
    <div
      class="jointHeaderbg"
      :style="`background:url(${template?.jointHeaderUrl}) no-repeat;background-size:100% 100%;height:${template?.jointHeight / (checkTabs == 'phone' ? 3.4 : 2.5)}px;`"
    >
      <div :style="`color:${template?.jointFontColor};`">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const { options, editor, templateObject } = useStore()
const { pageTemplateId } = useTemplate()
let template = $ref(null)
const editForm = $ref({})
const editModelVisibel = $ref(false)
const props = defineProps({
  title: String,
  checkTabs: String,
})
onMounted(() => {
  template = pageTemplateId.value
  console.log(props.checkTabs)
})

watch(
  () => pageTemplateId.value,
  (val) => {
    template = val
  },
)
</script>
<style lang="less" scoped>
.jointHeaderbg {
  width: 100%;
  padding-left: 10px;
  display: flex;
  align-items: center;
}
.footer-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  button {
    margin-left: 10px;
  }
}
</style>
