<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-02 15:30:13
 * @LastEditTime: 2024-12-05 11:55:41
 * @FilePath: \dutp-editor\src\components\analysisJson\module\links.vue
 * @Description:
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<template>
  <span class="links">
    <u class="title" @click="openLink">
      <span :style="style">
        {{ title }}
        <icon name="link" style="font-size: 1.125rem" />
      </span>

    </u>
  </span>

  <!-- 交叉引用 -->
  <modal v-model:visible="crossReferencingShow" :header="title" width="1050px">
    <div>
      <div v-if="quoteType === 'imageLayout,image,imageIcon,imageInLine'">
        <img :src="href" />
      </div>
      <div
        v-if="
          quoteType === 'resourceCover_0' ||
          quoteType === 'resourceCover_1' ||
          quoteType === 'resourceCover_3' ||
          quoteType === 'resourceCover_4' ||
          quoteType === 'resourceCover_5'
        "
      >
        <iframe
          :src="href"
          frameborder="0"
          width="1000px"
          height="800px"
        ></iframe>
      </div>

      <div
        v-if="quoteType === 'audio'"
        style="
          display: flex;
          width: 100%;
          justify-content: center;
          align-items: center;
        "
      >
        <audio controls>
          <source :src="href" type="audio/mpeg" />
        </audio>
      </div>
      <div
        v-if="quoteType === 'video'"
        style="
          display: flex;
          width: 100%;
          justify-content: center;
          align-items: center;
        "
      >
        <video controls :src="href"></video>
      </div>
    </div>
  </modal>
</template>

<script setup>
const props = defineProps({
  type: String,
  title: String,
  href: String,
  quoteType: String,
  src: String,
  style: Object,
})

const crossReferencingShow = ref(false)
const openLink = () => {
  switch (props.type) {
    case 'websiteLink':
      window.open(props.href, '_blank')
      break

    case 'crossReferencing':
      crossReferencingShow.value = true
      break

    case 'resourceLibrary':
      break

    default:
      break
  }
}
</script>

<style lang="less" scoped>
.links {
  display: inline-block;
  .title {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--umo-primary-color);
  }
}
</style>
