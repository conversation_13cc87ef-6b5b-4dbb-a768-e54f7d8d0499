<template>
  <div :id="props.id" class="umo-node-view">
    <!-- :style="{
      '--umo-page-background': page.background,
      '--umo-page-margin-top': (page.margin?.top ?? '0') + 'cm',
      '--umo-page-margin-bottom': (page.margin?.bottom ?? '0') + 'cm',
      '--umo-page-margin-left': (page.margin?.left ?? '0') + 'cm',
      '--umo-page-margin-right': (page.margin?.right ?? '0') + 'cm',
      '--umo-page-width': pageSize.width + 'cm',
      '--umo-page-height': pageSize.height + 'cm',
      postion: 'relative',
    }" -->
    <img
      :src="props.src"
      alt=""
      style="
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      "
    />
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  src: {
    type: String,
    default: '',
  },
})
const { page } = useStore()

const pageSize = $computed(() => {
  const { width, height } = page.value.size as any
  return {
    width: page.value.orientation === 'portrait' ? width : height,
    height: page.value.orientation === 'portrait' ? height : width,
  }
})

onMounted(() => {
  nextTick(() => {
    templateHeader()
  })
})

onBeforeUnmount(() => {
  const domFooter = document.getElementsByClassName('analysisJson-page-footer')
  const domHeader = document.getElementsByClassName('analysisJson-page-header')
  domFooter[0].style.display = 'block'
  domHeader[0].style.display = 'block'
})

const templateHeader = () => {

  const domFooter = document.getElementsByClassName('analysisJson-page-footer')
  const domHeader = document.getElementsByClassName('analysisJson-page-header')

  domFooter[0].style.display = 'none'
  domHeader[0].style.display = 'none'
}

// analysisJson-page-footer
</script>
<style lang="less" scoped>
.umo-node-view {
  height: 100%;
}
</style>
