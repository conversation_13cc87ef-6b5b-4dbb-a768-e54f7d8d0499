<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-26 09:51:05
 * @LastEditTime: 2024-11-26 09:54:10
 * @FilePath: \dutp-editor\src\components\analysisJson\module\pegeToc.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<style lang="less" scoped>
.pegeToc {
  width: 100%;
  height: 100px;
  border: 1px solid #666;
  border-radius: 5px;
  margin: 10px 0;
  padding: 10px;
  box-sizing: border-box;
}
</style>

<template>
  <div class="pegeToc">暂未适配,嘿嘿嘿</div>
</template>

<script setup></script>
