<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-25 13:25:53
 * @LastEditTime: 2024-11-25 16:12:06
 * @FilePath: \dutp-editor\src\components\analysisJson\module\previewFile.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<template>
  <div class="previewFile">
    <div class="previewFile-main" :style="{ [position[nodeAlign]]: 0 }">
      <div class="previewFile-main-logo">
        <!-- <img :src="previewTypes[previewType]" alt="" /> -->
        <img :src="previewTypesIcon(url)" alt="" />
      </div>
      <div class="previewFile-main-content">
        <div class="previewFile-main-name">{{ name }}</div>
        <div class="previewFile-main-size">
          {{ (size / 1024).toFixed(2) }}KB
        </div>
      </div>
      <div>
        <div class="umo-action-item" @click.stop="preview(url)">
          <icon name="view" />
        </div>
      </div>
      <!-- <div>
        <div class="umo-action-item" @click="download">
          <icon name="download" />
        </div>
      </div> -->
    </div>
    <t-image-viewer
      v-model="previewIsShow.image"
      :images="[url]"
      :show-overlay="true"
    ></t-image-viewer>

    <t-dialog
      v-model:visible="fileShow"
      attach="body"
      :footer="false"
      width="70%"
      :header="name"
    >
      <iframe
        :src="`${previewUrl + encodeURIComponent(Base64.encode(url))}`"
        frameborder="0"
        width="100%"
        height="600"
      ></iframe>
    </t-dialog>
    <!-- <t-dialog
      v-model:visible="previewIsShow.video"
      attach="body"
      :footer="false"
      width="60%"
      :top="50"
      :destroy-on-close="true"
    >
      <div class="dialog-main">
        <video
          class="video-play"
          :src="url"
          preload="metadata"
          controls
        ></video>
      </div>
    </t-dialog>
    <t-dialog
      v-model:visible="previewIsShow.audio"
      attach="body"
      :footer="false"
      width="40%"
      :destroy-on-close="true"
    >
      <audio
        class="dialog-audio"
        :src="url"
        preload="auto"
        controls
        autoplay
        crossorigin="anonymous"
      ></audio>
    </t-dialog> -->
  </div>
</template>
<script setup>
import { Base64 } from 'js-base64'
const props = defineProps({
  size: String,
  name: String,
  previewType: {
    type: String,
    default: 'audio',
  },
  nodeAlign: String,
  url: String,
})

const previewUrl = import.meta.env.VITE_ONLINE_PREVIEW

const previewIsShow = $ref({
  image: false,
  video: false,
  audio: false,
})
const download = () => {
  const link = document.createElement('a')
  link.href = props.url

  link.download = props.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  useMessage('success', {
    content: '操作成功，请稍后在下载内容处查看',
  })
}
const previewTypes = {
  image: 'https://cdn.umodoc.com/icons/file/img.svg',
  video: 'https://cdn.umodoc.com/icons/file/video.svg',
  word: 'https://cdn.umodoc.com/icons/file/word.svg',
  audio: 'https://cdn.umodoc.com/icons/file/music.svg',
  pdf: 'https://cdn.umodoc.com/icons/file/pdf.svg',
  ppt: 'https://cdn.umodoc.com/icons/file/ppt.svg',
  xls: 'https://cdn.umodoc.com/icons/file/excel.svg',
  txt: 'https://cdn.umodoc.com/icons/file/txt.svg',
  order: 'https://cdn.umodoc.com/icons/file/common.svg',
}

const previewTypesIcon = computed(() => {
  return function (url) {

    if (
      url.includes('.png') ||
      url.includes('.jpg') ||
      url.includes('.jpeg') ||
      url.includes('.gif')
    ) {
      return previewTypes.image
    } else if (
      url.includes('.mp4') ||
      url.includes('.avi') ||
      url.includes('.mov')
    ) {
      return previewTypes.video
    } else if (
      url.includes('.mp3') ||
      url.includes('.wav') ||
      url.includes('.ogg')
    ) {
      return previewTypes.audio
    } else if (url.includes('.pdf')) {
      return previewTypes.pdf
    } else if (url.includes('.ppt') || url.includes('.pptx')) {
      return previewTypes.ppt
    } else if (url.includes('.xls') || url.includes('.xlsx')) {
      return previewTypes.xls
    } else if (url.includes('.doc') || url.includes('.docx')) {
      return previewTypes.word
    } else if (url.includes('.txt')) {
      return previewTypes.txt
    } else {
      return previewTypes.order
    }
  }
})
const position = {
  'flex-start': 'left',
  'flex-end': 'right',
}
const fileShow = ref(false)
const preview = (url) => {

  if (
    url.includes('.png') ||
    url.includes('.jpg') ||
    url.includes('.jpeg') ||
    url.includes('.gif')
  ) {
    previewIsShow.image = true
  } else {

    fileShow.value = true
  }

  // if (!props.previewType) previewIsShow.audio = true
  // switch (props.previewType) {
  //   case 'image':
  //     previewIsShow.image
  //     break
  //   case 'video':
  //     previewIsShow.video = true
  //     break
  //   // case 'audio':
  //   // previewIsShow.video = true
  //   // break
  //   default:
  //     break
  // }
}
</script>

<style lang="less" scoped>
.previewFile {
  width: 100%;
  height: 60px;
  position: relative;
  display: flex;
  justify-content: space-around;
  .previewFile-main {
    height: 100%;
    width: 300px;
    outline: solid 1px var(--umo-content-node-border);
    border-radius: 3px;
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    position: absolute;
    .previewFile-main-logo {
      width: 32px;
      height: 32px;
      & > img {
        width: 100%;
        height: 100%;
      }
    }
    .previewFile-main-content {
      flex: 1;
      margin-left: 10px;
      .previewFile-main-name {
        font-size: 12px;
        font-weight: 500;
        line-height: 1.2;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
      }
      .previewFile-main-size {
        font-size: 12px;
        color: var(--umo-text-color-light);
        line-height: 1;
        margin-top: 6px;
      }
    }

    .umo-action-item {
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      width: 32px;
      background-color: var(--umo-color-white);
      box-sizing: border-box;
      cursor: pointer;
      border-radius: 50%;
      color: var(--umo-text-color-light);

      &:hover {
        border: solid 1px var(--umo-primary-color);
        color: var(--umo-primary-color);
      }

      .loading {
        animation: turn 1s linear infinite;
      }
    }
  }
}
.dialog-main {
  height: 640px;
  .video-play {
    width: 100%;
    height: 100%;
    background-color: #000;
  }
}
.dialog-audio {
  width: 100%;
}
</style>
