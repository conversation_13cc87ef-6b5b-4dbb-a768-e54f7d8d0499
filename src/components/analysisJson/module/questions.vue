<template>
  <div :id="id" ref="containerRef" class="umo-node-view-rc">
    <div class="questions-wrapper">
      <div class="questions-content">
        <div
          class="question-item"
          v-for="(item, index) in questionsList"
          :key="index"
        >
          <!-- 填空题 -->
          <template-questions-onview-fillinBlanksQuestion
            v-if="
              item.userQuestion &&
              isFillinBlanksQuestion(item.userQuestion.questionType)
            "
            :data="formatToQuestionData(item)"
            :config="{ hasAnswer: false }"
          />
          <!-- 判断题内容 -->
          <template-questions-onview-trueOrFalseQuestion
            v-if="
              item.userQuestion &&
              isTrueOrFalseQuestion(item.userQuestion.questionType)
            "
            :data="formatToQuestionData(item)"
            :config="{ hasAnswer: false }"
          />
          <!-- 单选题、多选题内容 -->
          <template-questions-onview-optionSelectQuestion
            v-if="
              item.userQuestion &&
              isOptionSelectQuestionType(item.userQuestion.questionType)
            "
            :data="formatToQuestionData(item)"
            :config="{ hasAnswer: false }"
          />
          <!-- 排序题内容 -->
          <template-questions-onview-sortingQuestion
            v-if="
              item.userQuestion &&
              isSortingQuestion(item.userQuestion.questionType)
            "
            :data="formatToQuestionData(item)"
            :config="{ hasAnswer: false }"
          />
          <!-- 简答题内容、编程简答题内容 -->
          <template-questions-onview-answerInShortQuestion
            v-if="
              item.userQuestion &&
              isDescriptiveAnswerQuestion(item.userQuestion.questionType)
            "
            :data="formatToQuestionData(item)"
            :config="{ hasAnswer: false }"
          />
          <!-- 连线题 -->
          <template-questions-onview-matchingQuestion
            v-if="
              item.userQuestion &&
              isMatchingQuestions(item.userQuestion.questionType)
            "
            :data="formatToQuestionData(item)"
            :leftOption="formatToLeftOption(item)"
            :rightOption="formatToRightOption(item)"
            :matching-result="formatToMatchResult(item)"
            :config="{ hasAnswer: false }"
          />
          <!-- 编程题 -->
          <template-questions-onview-programmingQuestion
            v-if="isProgrammingQuestion(item.userQuestion?.questionType)"
            :data="formatToQuestionData(item)"
            :config="{ hasAnswer: false }"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  isSortingQuestion,
  isOptionSelectQuestionType,
  isTrueOrFalseQuestion,
  isFillinBlanksQuestion,
  isProgrammingQuestion,
  isDescriptiveAnswerQuestion,
  isMatchingQuestions,
} from '@/utils/questionTypeUtil'

const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  questionsList: {
    type: Object,
    default: null,
  },
})

function formatToQuestionData(userQuestionItem) {
  let options = userQuestionItem.userQuestion?.options ?? []
  return {
    showAnswer: false,
    questionType: userQuestionItem.userQuestion?.questionType,
    questionContent: userQuestionItem.userQuestion?.questionContent,
    analysis: userQuestionItem.userQuestion?.analysis,
    codeContent: userQuestionItem.userQuestion?.codeContent,
    code: userQuestionItem.userQuestion?.code,
    langualge: userQuestionItem.userQuestion?.language,
    options,
  }
}
function formatToLeftOption(item) {
  const result =
    item?.userQuestion?.options?.filter(
      (qo) => `${qo.optionPosition}` === '1',
    ) ?? []
  return result
}
function formatToRightOption(item) {
  const result =
    item?.userQuestion.options?.filter(
      (qo) => `${qo.optionPosition}` === '2',
    ) ?? []
  return result
}
function formatToMatchResult(item) {
  let matchingResultJson = item.userQuestion.rightAnswer
  if (typeof matchingResultJson === 'string') {
    matchingResultJson = JSON.parse(matchingResultJson)
  }
  return matchingResultJson.map(({ source, target }) => {
    return { source, target }
  })
}
</script>

<style lang="less" scoped>
.umo-node-view-rc {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .questions-wrapper {
    width: 100%;
    padding: 5px;
    .top-node-mu {
      margin-bottom: -10px;
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .toggle-header {
      margin-left: 20px;
      margin-right: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      .short-img-con {
        display: flex;
        justify-content: center;
        align-items: center;
        .short-img-icon {
          width: 50px;
          height: 50px;
          margin-right: 20px;
        }
      }
    }
    .questions-content {
      width: 100%;
      margin-top: 12px;
      .question-title-con {
        font-size: 30px;
        width: 100%;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 16px;
      }
      .question-item {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        margin-bottom: 16px;
        .stem-con {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          align-items: center;
        }
      }
    }
  }
}

ul {
  list-style: none;
  padding: 0;
}
li {
  margin: 4px 0;
}
label {
  cursor: pointer;
}
</style>
