<template>
    <span>
      <t-popconfirm theme="default"
                    min-width="900px"
                    :confirm-btn="null"
                    :cancel-btn="null"
      >
        <template #icon>
          <icon name="cps-icon-tips" :url="newSvgUrl"/>
        </template>
        <template #content>
          <div style="display: flex;justify-content: space-between"><text>张三</text><text>2024-11-18 15:38</text></div>
          <hr>
          <div style="margin-top:30px;min-width:200px">{{ remarkContent }}</div>
        </template>
        <t-button variant="text" ghost class="ghost-button" style="borderBottom:none;">
          <i class="remark-icon"></i>
        </t-button>
      </t-popconfirm>
    </span>
</template>
<script setup>
import { Icon } from 'tdesign-icons-vue-next'
const props = defineProps({
  remarkContent: {
    type: String,
    default: ''
  }
})
</script>

<style lang="less">
.ghost-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  color: var(--umo-primary-color);
  border-bottom: 1px solid var(--umo-primary-color);
  border-left: none;
  border-right: none;
  border-top: none;

  &:hover {
    border-left: none;
    border-right: none;
    border-top: none;
  }

  .remark-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url('@/assets/icons/tips.svg') no-repeat;
    background-size: 100% 100%;
    margin-left: 4px;
  }
}
</style>