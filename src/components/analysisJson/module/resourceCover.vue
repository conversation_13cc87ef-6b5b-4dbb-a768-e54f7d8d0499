<template>
  <div :id="'__resourceCover__'" ref="containerRef" :src="bgSrc" :title="title" :fileUrl="url" :showTitle="showTitle"
    :type="rcType" class="umo-node-view-rc">
    <!-- 3D -->

    <div v-if="rcType == '0'" class="rc-3d" :style="`height:${imgHeight}`" @click="previewResource">
      <div class="rc-text">{{ title }}</div>
    </div>

    <div v-else-if="rcType == '1' || rcType == '2'" class="rc-vr" :style="`height:${imgHeight};`"
      @click="previewResource">
      <div class="rc-text">{{ title }}</div>
    </div>

    <div v-else-if="rcType == '4'" class="rc-game" :style="`height:${imgHeight}`" @click="previewResource">
      <div class="rc-text">{{ title }}</div>
    </div>

    <div v-else class="rc-bg"
      :style="`width:100%;height:55px;background:url(${template?.orderTemplateBgUrl ?? bgSrc}) no-repeat center center; background-size:100% 100%; display:flex;align-items:center;justify-content: space-between;`">
      <div class="rc-main">
        <div class="rc-left">
          <div class="rc-iconTXT">
            <img :src="template?.theme === 'light'
                ? rcTypeObject(rcType).lightIcon
                : rcTypeObject(rcType).darkIcon
              " style="width: 18px; height: 18px" />
          </div>
          <div class="rc-title" :style="`color:${template?.theme === 'light' ? '#fff' : '#333'}`">
            {{ rcTypeObject(rcType).text }}
          </div>
          <div class="rc-name"
            :style="`margin-left:${checkTabs != 'phone' ? Number(template?.orderTemplateMarginLeft) + 60 : template?.orderTemplateMarginLeft}px;color:${template?.orderTemplateColor}`">
            {{ title }}
          </div>
        </div>
        <div class="rc-right">
          <div v-if="rcType === RCType.GAME" :style="`color:${template?.theme === 'light' ? '#fff' : '#333'}`"
            @click="runGame">
            运行
          </div>
          <div v-if="rcType != RCType.GAME" :style="`color:${template?.theme === 'light' ? '#333' : '#fff'}`"
            @click="previewResource">
            预览
          </div>
        </div>
      </div>
    </div>
    <!-- 3d,avvr,game modal -->
    <modal :visible="resourceShow" :header="title + ''" width="1100px" :confirm-btn="null" :cancel-btn="null"
      @close="resourceShow = false">
      <div class="resource-bg">
        <iframe :src="url" style="width: 100%; min-height: 700px; border: none"></iframe>
      </div>
    </modal>

    <modal :visible="iframgeModelVisibel" :header="title + ''" width="1000px" :confirm-btn="null" :cancel-btn="null"
      @close="iframgeModelVisibel = false">
      <div style="display: flex; justify-content: center; align-items: center">
        <div v-if="!readingContent" class="">
          <img :src="noWeb" alt="" style="width: 194px; height: 109px" />
          <div style="text-align: center">稍等,文件加载中</div>
        </div>
        <div v-else class="readingContentCss" v-html="readingContent"></div>
      </div>
    </modal>
  </div>
</template>

<script setup lang="ts">
import noWeb from '@/assets/images/noWeb.png'
import dask3d from '@/assets/resources/dark/3d.svg'
import daskAr from '@/assets/resources/dark/arvr.svg'
import daskGame from '@/assets/resources/dark/game.svg'
import daskTraining from '@/assets/resources/dark/practical.svg'
import daskRead from '@/assets/resources/dark/read.svg'
import daskTeach from '@/assets/resources/dark/teach.svg'
import daskSimulation from '@/assets/resources/dark/virtual.svg'
//3d
import light3d from '@/assets/resources/light/3d.svg'
import lightAr from '@/assets/resources/light/arvr.svg'
import lightGame from '@/assets/resources/light/game.svg'
import lightTraining from '@/assets/resources/light/practical.svg'
import lightRead from '@/assets/resources/light/read.svg'
import lightTeach from '@/assets/resources/light/teach.svg'
import lightSimulation from '@/assets/resources/light/virtual.svg'
import { RCType } from '@/enum/extensions/RC'

const { rcType, bgSrc, showTitle, width, height, title, url, checkTabs } =
  defineProps({
    id: {
      default: null,
      type: String,
    },
    rcType: {
      default: RCType.TEACH,
      type: Number as () => RCType,
    },
    bgSrc: {
      default:
        'https://dlchhc-b2b.oss-cn-beijing.aliyuncs.com/images/1692943187718.png',
      type: String,
    },
    url: {
      default: '',
      type: String,
    },
    title: {
      default: '我是标题',
      type: String,
    },
    showTitle: {
      default: true,
      type: Boolean,
    },
    // 背景宽高 px
    width: {
      default: 100,
      type: Number,
    },
    height: {
      default: 54,
      type: Number,
    },
    readingContent: {
      default: '',
      type: String,
    },
    checkTabs: String,
  })
const iframgeModelVisibel = ref(false)
const resourceShow = ref(false)
const { pageTemplateId } = useTemplate()
let template = $ref(null)
const type = $ref('')
onMounted(() => {
  template = pageTemplateId.value
})

watch(
  () => pageTemplateId.value,
  (val) => {
    template = val
  },
)

const rcTypeObject = $computed(() => {
  return function (rcType: RCType) {
    switch (rcType) {
      case 0:
        return {
          text: '3D模型',
          lightIcon: light3d,
          darkIcon: dask3d,
        }
      case 1:
        return {
          text: 'AR/VR',
          lightIcon: lightAr,
          darkIcon: daskAr,
        }
      case 2:
        return {
          text: 'AR/VR',
          lightIcon: lightAr,
          darkIcon: daskAr,
        }
      case 3:
        return {
          text: '虚拟仿真',
          lightIcon: lightSimulation,
          darkIcon: daskSimulation,
        }
      case 4:
        return {
          text: '游戏',
          lightIcon: lightGame,
          darkIcon: daskGame,
        }
      case 5:
        return {
          text: '教学资源',
          lightIcon: lightTeach,
          darkIcon: daskTeach,
        }
      case 6:
        return {
          text: '拓展阅读',
          lightIcon: lightRead,
          darkIcon: daskRead,
        }
      case 7:
        return {
          text: '实训',
          lightIcon: lightTraining,
          darkIcon: daskTraining,
        }
      case 8:
        return { lightIcon: testPaperAd, darkIcon: testPaperAd, text: '试卷' }
      case 9:
        return {
          lightIcon: schoolAssignmentAd,
          darkIcon: schoolAssignmentAd,
          text: '作业',
        }
      default:
        return ''
    }
  }
})
const ImgStyle = $computed(() => {
  return {
    width: `${width}%`,
    height: `${height}px`,
  }
})

const imgHeight = $computed(() => {
  return checkTabs == 'phone' ? '306px' : '445px'
})

function runGame() {
  previewResource()
}
function previewResource() {
  if (rcType == 6) {
    iframgeModelVisibel.value = true
  } else if (rcType == 0 || rcType == 1 || rcType == 2 || rcType == 4) {
    resourceShow.value = true
  } else {
    if (url) {
      window.open(url, '_blank')
    } else {
      MessagePlugin({
        message: '暂无资源',
        type: 'warning',
      })
    }
  }

  //
}
</script>

<style lang="less" scoped>
.resource-bg {
  width: 100%;
}

.umo-node-view-rc {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 5px;

  .rc-3d {
    margin: 20px 0;
    width: 100%;
    min-height: 306px;
    background-image: url('@/assets/resources/bg/3d-bg.png');
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;

    .rc-text {
      font-size: 36px;
      line-height: 52px;
      color: #333;
      width: 70%;
      margin: -20px auto 0;
      text-align: center;
    }
  }

  .rc-vr {
    margin: 20px 0;
    width: 100%;
    min-height: 306px;
    background-image: url('@/assets/resources/bg/vr-bg.png');
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;

    .rc-text {
      font-size: 36px;
      line-height: 52px;
      color: #333;
      width: 70%;
      margin: -20px auto 0;
      text-align: center;
    }
  }

  .rc-game {
    margin: 20px 0;
    width: 100%;
    min-height: 306px;
    background-image: url('@/assets/resources/bg/game-bg.png');
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center;

    .rc-text {
      font-size: 36px;
      line-height: 52px;
      color: #333;
      width: 70%;
      margin: -20px auto 0;
      text-align: center;
    }
  }

  .rc-bg {
    margin: 10px 0;

    .rc-main {
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .rc-left {
        display: flex;
        align-items: center;

        .rc-iconTXT {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .rc-title {
          width: 80px;
        }

        .rc-name {
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 120px;
        }

        div {
          padding-right: 10px;
        }
      }

      .rc-right {
        margin-right: 10px;

        .rc-btn {
          border-radius: 57px;
          border-radius: 57px;
          padding: 5px 10px;
          cursor: pointer;
        }

        .blue-btn {
          background: linear-gradient(180deg, #fcfeff 0%, #9bd4ff 100%);
          box-shadow: 0px 4px 4px 0px rgba(5, 86, 154, 0.28);
          border: 1px solid rgba(9, 102, 180, 0.5);
          color: #0966b4;
        }

        .or-btn {
          background: linear-gradient(180deg, #ffcec9 0%, #ffa37c 100%);
          box-shadow: 0px 4px 4px 0px rgba(5, 86, 154, 0.28);
          border: 1px solid #ff955a;
          color: #fff;
        }
      }
    }
  }

  .rc-icon {
    // display: none;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    width: 30px;
    height: 30px;
  }
}

.rc-bottom {
  text-align: center;
  padding: 10px 0;

  .rc-title {
    color: black;
    outline: none;
  }
}

.readingContentCss {
  ::v-deep(a) {
    color: #409eff;
    text-decoration: underline;
    display: flex;
    align-items: center;
  }

  ::v-deep(a::after) {
    content: '';
    display: inline-flex;
    width: 16px;
    height: 16px;
    background: url(@/assets/icons/linkIcon.svg) no-repeat;
    background-size: 100% 100%;
    margin-left: 2px;
  }

  ::v-deep(pre) {
    margin: 20px 0;
    padding: 10px;
    background-color: #333;

    code {
      background: transparent;
    }
  }

  ::v-deep(code) {
    padding: 5px;
    margin: 10px 0;
    background-color: #f1f1f1;
  }

  ::v-deep(blockquote) {
    border-left: 3px solid #0966b4;
    padding: 10px;
    background-color: #f0f0f0;
    margin: 20px 0;

    code {
      background: transparent;
    }
  }

  ::v-deep(table) {
    margin: 20px 0;
    padding: 5px;
    border-collapse: collapse;

    th {
      padding: 5px;
      background-color: #f1f1f1;
      border: 1px solid #f1f1f1;
    }

    td {
      padding: 5px;
      border: 1px solid #f1f1f1;
    }
  }
}

// .umo-node-view-rc:hover {
//   cursor: pointer;
//   .rc-icon {
//     display: block;
//   }
// }</style>
