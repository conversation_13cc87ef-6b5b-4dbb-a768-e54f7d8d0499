<template>
  <div :id="id" ref="containerRef" class="umo-node-view">
    <div
      class="umo-node-container umo-hover-shadow umo-select-outline umo-node-tableplus"
    >
      <div
        v-if="isShowTitle && !isTitlePosition"
        class="umo-node-title"
        contenteditable="false"
        :style="{
          textAlign: hanlin,
        }"
      >
        <text style="margin-right: 10px">{{ number }}</text>
        <text>{{ name || '表格标题' }}</text>
      </div>
      <slot> </slot>

      <div
        v-if="isShowTitle && isTitlePosition"
        class="umo-node-title"
        contenteditable="false"
        :style="{
          textAlign: hanlin,
        }"
      >
        <text style="margin-right: 10px">{{ number }}</text>
        <text>{{ name || '表格标题' }}</text>
      </div>
    </div>
  </div>
</template>
<script setup>
const { options, editor } = useStore()
const { id, name, isShowTitle, isTitlePosition } = defineProps({
  id: {
    default: null,
    type: String,
  },
  name: {
    default: '我是标题',
    type: String,
  },
  isShowTitle: {
    default: true,
    type: Boolean,
  },
  isTitlePosition: {
    default: false,
    type: Boolean,
  },
  hanlin: {
    default: 'center',
    type: String,
  },
  number: {
    default: '',
    type: String,
  },
})
</script>
<style lang="less">
.umo-node-view {
  .umo-node-tableplus {
    max-width: 100%;
    width: 100%;
    position: relative;
    border-radius: var(--umo-radius);
    outline: solid 1px var(--umo-border-color);
    padding: 10px;
    margin: 10px 0;
    .umo-node-audio-title {
      text-align: center;
    }

    .icon {
      position: absolute;
      right: 10px;
      top: 10px;
      z-index: 99;
    }

    .uploading {
      position: absolute;
      z-index: 10;
      right: 0;
      top: 0;
      background: rgba(0, 0, 0, 0.2);
      height: 2px;
      left: 0;
      border-top-left-radius: var(--umo-radius);
      border-top-right-radius: var(--umo-radius);

      &:after {
        content: '';
        display: block;
        height: 100%;
        background-color: var(--umo-primary-color);
        animation: progress 1s linear infinite;
      }
    }

    .umo-node-title {
      text-align: center;
      padding: 10px 0 0;
    }
  }
}

@keyframes progress {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}
</style>
