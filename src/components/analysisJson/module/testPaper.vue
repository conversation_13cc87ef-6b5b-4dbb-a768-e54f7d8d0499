<template>
  <node-view-wrapper ref="containerRef" class="umo-node-view-rc">
    <div class="quiz-questions-list" @click="previewQuiz(node)">
      <div class="list-item-img">
        <img :src="paperIcon" alt="" />
      </div>
      <div class="list-item-info">
        {{ localQuizData.paperTitle }}
      </div>
      <div class="list-item-info-time">
        <Calendar1Icon />{{ localQuizData.createTime }}
      </div>
    </div>
  </node-view-wrapper>

  <template-paper-preview
    v-model="previewQuizVisibility"
    :paper-info="localQuizData"
    :paper-id="currentPaperId"
    :paper-type="paperType"
  ></template-paper-preview>
</template>

<script setup>
import { NodeViewWrapper } from '@tiptap/vue-3'
import { Calendar1Icon } from 'tdesign-icons-vue-next'

import paperIcon from '@/assets/images/paper.svg'

const props = defineProps({
  paperInfo: {
    type: String,
    default: '',
  },
})

const { editor } = useStore()
const showEditQuizPopup = $ref(false)
let localQuizData = $ref({})
let previewQuizVisibility = $ref(false)
const { paperInfo } = props
const { paperType, paperId } = paperInfo
let currentPaperId = $ref('')
// 方法部分
function previewQuiz(node) {
  previewQuizVisibility = true
}
// hook
onMounted(() => {
  currentPaperId = paperId
  localQuizData = JSON.parse(JSON.stringify(paperInfo))
})
</script>

<style lang="less" scoped>
@import '@/assets/styles/_common.less';
.umo-node-view-rc {
  .quiz-questions-list {
    cursor: pointer;
    display: flex;
    padding: 20px 10px;
    background-color: #fff;
    margin: 20px 0;
    border: 1px solid #eaeef3;
    cursor: pointer;
    transition: all 0.3s;
    border-radius: 5px;
    color: #666;
    &:hover {
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .list-item-img {
      background-color: #eaeef3;
      border-radius: 5px;
      img {
        width: 50px;
        height: 50px;
      }
    }

    .list-item-info {
      flex: 1;
      margin-left: 20px;
      justify-content: space-between;
      font-size: 16px;
      font-weight: bold;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      width: 270px;
    }
    .list-item-info-time {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999;
    }
  }
}

ul {
  list-style: none;
  padding: 0;
}

li {
  margin: 4px 0;
}

label {
  cursor: pointer;
}
</style>
<style lang="less">
.bellCss .umo-node-view-rc * {
  text-indent: 0;
}
.bellCss .umo-node-view-rc .question-item {
  .data-item {
    padding: 20px 10px;
    color: #333;
    label {
      min-width: fit-content;
    }
  }
}
</style>
