<template>
  <div class="video">
    <div id="__video__" class="video-main">
      <video
        ref="videoRef"
        class="video-play"
        :src="src"
        preload="metadata"
        controls
        crossorigin="anonymous"
      ></video>
      <div class="video-name" :style="{ width: width + 'px' }">
        {{ videoTitle }}
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  height: String,
  src: String,
  checkTabs: String,
  nodeAlign: String,
  videoTitle: String,
})
const videoRef = $ref(null)
</script>

<style lang="less" scoped>
.video {
  margin: 20px 0 40px;
  width: 100%;

  .video-main {
    height: 314px;
  }
  .video-play {
    width: 100%;
    height: 100%;
    background-color: #000;
  }
}
.video-name {
  padding: 5px 0;
  text-align: center;
  font-size: 14px;
  color: #333;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 超出容器部分的文本隐藏 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
}
</style>
