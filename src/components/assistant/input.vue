<template>
  <div class="umo-assistant-container">
    <!-- <div class="umo-assistant-input">
      <div class="ai-icon">
        <icon name="assistant" />
      </div>
      <t-textarea
        ref="inputRef"
        v-model="command"
        class="input"
        :maxlength="options.assistant?.maxlength"
        :readonly="generating"
        autocomplete="false"
        :placeholder="t('assistant.placeholder')"
        autosize
      />
      <div v-if="command !== ''" class="submit">
        <t-button
          theme="primary"
          :disabled="generating"
          :loading="generating"
          @click="send"
        >
          <span v-if="!generating" v-text="t('assistant.send')"></span>
        </t-button>
      </div>
    </div> -->
    <div class="umo-assistant-result">
      <div class="close">
        <tooltip :content="t('assistant.exit')">
          <t-button
            v-if="!generating"
            size="small"
            variant="text"
            shape="square"
            theme="default"
            @click="exitAssistant"
          >
            <icon name="exit" size="14" />
          </t-button>
        </tooltip>
      </div>
      <div class="commands-container">
        <div class="title" v-text="t('assistant.commands')"></div>
        <div class="commands">
          <t-button
            v-for="(item, index) in commands"
            :key="index"
            size="small"
            variant="outline"
            theme="default"
            @click="runCommand(item)"
            v-text="l(item.label)"
          ></t-button>
          <div
            style="display: flex; flex-direction: row; flex: auto"
            v-if="requestBody.ability == 23"
          >
            <t-select v-model="requestBody.fromLanguage" @change="sentRequest">
              <t-option
                v-for="item in languages"
                :key="item.parameter"
                :label="item.language"
                :value="item.parameter"
              />
            </t-select>
            <span style="display: flex; align-items: center; margin: 0 8px"
              >===></span
            >
            <t-select v-model="requestBody.toLanguage" @change="sentRequest">
              <t-option
                v-for="item in languages"
                :key="item.parameter"
                :label="item.language"
                :value="item.parameter"
              />
            </t-select>
          </div>
        </div>
      </div>
      <div class="result-container" v-if="requestBody.ability">
        <div class="title" v-text="t('assistant.result')"></div>
        <div
          class="result umo-editor-container"
          :class="{ error: result.error }"
          v-html="result.content"
        ></div>
        <t-loading :loading="generating" />
        <div class="actions" v-if="!generating">
          <div v-if="!result.error" class="main">
            <t-button theme="primary" @click="replaceContent">
              <icon name="check" />
              {{ t('assistant.replace') }}
            </t-button>
            <t-button
              variant="outline"
              theme="default"
              @click="insertContentAtAfter"
            >
              <icon name="table-add-column-before" />
              {{ t('assistant.insertAfter') }}
            </t-button>
            <t-button
              variant="outline"
              theme="default"
              @click="insertContentAtBelow"
            >
              <icon name="table-add-row-after" />
              {{ t('assistant.insertBelow') }}
            </t-button>
          </div>
          <div class="secondary">
            <tooltip v-if="!result.error" :content="t('assistant.copy')">
              <t-button
                variant="text"
                shape="square"
                theme="default"
                @click="copyResult"
              >
                <icon name="copy" /> </t-button
            ></tooltip>
            <tooltip :content="t('assistant.rewrite')">
              <t-button
                variant="text"
                shape="square"
                theme="default"
                @click="rewrite"
              >
                <icon name="reload" />
              </t-button>
            </tooltip>
            <tooltip :content="t('assistant.delete')">
              <t-button
                variant="text"
                shape="square"
                theme="default"
                @click="deleteResult"
              >
                <icon name="node-delete" />
              </t-button>
            </tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { isString } from '@tool-belt/type-predicates'
import { chatAiByPrompt } from '@/api/ai'
import { queryTranslationLanguageList } from '@/api/translation'
import { getSelectionText } from '@/extensions/selection'
import type {
  AssistantContent,
  AssistantPayload,
  AssistantResult,
  CommandItem,
} from '@/types'
import { AiPromtType } from '@/enum/ai'

const { options, editor, assistantBox } = useStore()

const inputRef = ref<HTMLElement | null>(null)
let command = $ref<string>('')
const result = $ref<AssistantResult>({
  prompt: '',
  content: '',
  error: false,
})
const requestBody = ref({
  ability: null,
  question: '',
  fromLanguage: 'cn',
  toLanguage: 'en',
  checkBlackWhite: true,
})
const generating = ref<boolean>(false)
const commands = [
  {
    label: { en_US: 'Continuation', zh_CN: '续写' },
    value: AiPromtType.Continuation,
  },
  {
    label: { en_US: 'Rewrite', zh_CN: '重写' },
    value: AiPromtType.Rewrite,
  },
  {
    label: { en_US: 'Abbreviation', zh_CN: '缩写' },
    value: AiPromtType.Abbreviation,
  },
  {
    label: { en_US: 'Expansion', zh_CN: '扩写' },
    value: AiPromtType.Expansion,
  },
  {
    label: { en_US: 'Polish', zh_CN: '润色' },
    value: AiPromtType.Polishing,
  },
  // {
  //   label: { en_US: 'Proofread', zh_CN: '校阅' },
  //   value: AiPromtType.Translation,
  // },
  {
    label: { en_US: 'Translate', zh_CN: '翻译' },
    value: AiPromtType.Translation,
    autoSend: false,
  },
]
const languages = ref([])
onMounted(() => {
  getLanguages()
})
const getLanguages = () => {
  queryTranslationLanguageList().then((res) => {
    languages.value = res.data
  })
}
const runCommand = ({ value, autoSend }: any) => {
  requestBody.value.question = getSelectionText(editor.value) || ''
  requestBody.value.ability = value
  sentRequest()
}
const sentRequest = () => {
  if (
    requestBody.value.ability == AiPromtType.Translation &&
    requestBody.value.fromLanguage == requestBody.value.toLanguage
  ) {
    result.content = requestBody.value.question
    result.error = false
    return
  }
  generating.value = true
  result.content = ''
  chatAiByPrompt(requestBody.value)
    .then((res) => {
      let content = ''
      if (requestBody.value.ability == AiPromtType.Translation) {
        if (res.data) {
          requestBody.value.userType = 1
          content = res.data.result.trans_result.dst
        } else {
          MessagePlugin.error(res);
        }

      } else if (requestBody.value.ability == AiPromtType.Rewrite) {
        // console.log('ai返回结果：', res)

        content = res[0][0]
        // console.log('content: ', content)
      } else {
        // console.log('ai返回结果：', res, JSON.parse(res.body.content))

        content = JSON.parse(res.body.content).result
        // console.log('content: ', content)
      }
      result.content = content
      result.error = false
      generating.value = false
    })
    .catch((err) => {
      result.error = true
      generating.value = false
      result.content = '生成错误，请重试'
      console.log('ai返回错误：', err)
    })
}

const exitAssistant = () => {
  assistantBox.value = false
  editor.value?.commands.focus()
}

const replaceContent = () => {
  editor.value?.chain().insertContent(result.content).run()
  exitAssistant()
}

const insertContentAtAfter = () => {
  const { to } = editor.value?.state.selection ?? {}
  if (to) {
    editor.value?.chain().insertContentAt(to, result.content).focus().run()
  }
  exitAssistant()
}

const insertContentAtBelow = () => {
  editor.value?.commands.selectParentNode()
  const { to } = editor.value?.state.selection ?? {}
  if (to) {
    editor.value?.chain().insertContentAt(to, result.content).focus().run()
  }
  exitAssistant()
}

const copyResult = () => {
  const { copy } = useClipboard({
    source: ref(result.content),
  })
  void copy()
  useMessage('success', t('assistant.copySuccess'))
}

const rewrite = () => {
  sentRequest()
}

const deleteResult = () => {
  result.content = ''
}
</script>

<style lang="less" scoped>
.umo-assistant-input {
  width: 480px;
  position: relative;
  .ai-icon {
    position: absolute;
    border-radius: var(--umo-radius);
    z-index: 10;
    font-size: 20px;
    margin: 12px;
    pointer-events: none;
    width: 20px;
    height: 20px;
    overflow: hidden;
    &::after {
      content: '';
      display: block;
      width: 4px;
      background-color: var(--umo-color-white);
      opacity: 0.25;
      height: 30px;
      transform: rotate(35deg);
      position: absolute;
      top: -5px;
      left: -5px;
      z-index: 10;
      animation: start 1s ease-in;
    }
  }
  .input {
    outline: none !important;
    box-shadow: var(--umo-shadow);
    background-color: var(--umo-color-white);
    :deep(textarea) {
      border-color: var(--umo-primary-color);
      padding: 10px 60px 10px 40px;
      min-height: 44px;
      height: 44px;
      resize: none;
      outline: none !important;
    }
    :deep(.umo-textarea__info_wrapper) {
      display: none;
    }
  }
  .submit {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 10;
    margin: 9px 10px;
    :deep(.umo-button) {
      height: 26px;
      width: 42px;
      font-size: 12px;
    }
  }
}

.umo-assistant-result {
  margin-top: 10px;
  padding: 15px;
  box-shadow: var(--umo-shadow);
  border: solid 1px var(--umo-primary-color);
  border-radius: var(--umo-radius);
  position: relative;
  background-color: var(--umo-color-white);
  width: 480px;
  box-sizing: border-box;
  .close {
    position: absolute;
    top: 12px;
    right: 12px;
    opacity: 0.6;
  }
  .commands {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }
  .title {
    font-size: 12px;
    color: var(--umo-text-color-light);
    margin-bottom: 10px;
  }
  .result {
    font-size: 12px;
    margin-bottom: 18px;
    width: 450px;
    text-align: justify;
    min-height: auto;
    * + * {
      margin-top: 6px;
    }
    &.error {
      color: var(--umo-error-color);
    }
  }
  .actions {
    display: flex;
    justify-content: space-between;
    .main {
      display: flex;
      gap: 8px;
    }
    .secondary {
      display: flex;
      gap: 8px;
      :deep(.umo-button__text) {
        font-size: 16px;
        opacity: 0.6;
        .umo-icon {
          margin-right: 0 !important;
        }
      }
    }
    :deep(.umo-button) {
      height: 28px;
      font-size: 12px;
      padding-left: 10px;
      padding-right: 10px;
      &.umo-button--shape-square {
        width: 28px;
      }
      .umo-button__text {
        display: flex;
        align-items: center;
        .umo-icon {
          font-size: 14px;
          margin-right: 3px;
        }
      }
    }
  }
  .result-container {
    margin-top: 18px;
  }
}
@keyframes start {
  0% {
    left: -5px;
  }
  100% {
    left: 25px;
  }
}
</style>
