<template>
  <div class="umo-comment-container">
    <t-comment>
      <template #content>
        <div class="umo-comment-form">
          <t-textarea
            v-model="comment"
            size="small"
            :placeholder="t('insert.remark.placeholder')"
            autosize
            autofocus
            :maxlength="200"
          />
          <div class="umo-comment-buttons">
            <t-button
              class="umo-comment-button"
              :disabled="comment === ''"
              @click="submitComment"
            >
              {{ t('insert.remark.push') }}
            </t-button>
            <t-button
              theme="default"
              class="umo-comment-button"
              variant="text"
              @click="closeComment"
            >
              {{ t('insert.remark.cancel') }}
            </t-button>
          </div>
        </div>
      </template>
    </t-comment>
  </div>
</template>

<script setup lang="ts" name="CommentInput">
import { MessagePlugin } from 'tdesign-vue-next'
import { useRoute } from 'vue-router'

import { addUserCommon } from '@/api/book/userCommon'
const { options, editor, commentBox, chapterId, getCommentList, setChapterId } =
  useStore()
const route = useRoute()
const props = defineProps({
  user: {
    type: Object,
    default: () => ({}),
  },
})

const comment = $ref('')
const submitComment = () => {
  const dataId = new Date().getTime()
  editor.value?.commands.setInputCommon({ id: dataId })

  const obj = {
    chapterId: chapterId.value,
    dataId,
    remarkContent: comment,
  }

  addUserCommon(obj).then((res) => {
    if (res.code === 200) {
      MessagePlugin.success('评论成功')
      saveContentMethod()
      getCommentList().then((res) => {})
    }
  })

  closeComment()
}

const saveContentMethod = inject('saveContent') as () => void

const closeComment = () => {
  commentBox.value = false
  editor.value?.commands.focus()
}

onMounted(() => {
  const { chapterId } = route.query
  if (chapterId) {
    setChapterId(chapterId)
  }
})
</script>

<style lang="less" scoped>
.umo-comment {
  &-container {
    width: 300px;
    padding: 5px 3px 6px;
    :deep(.umo-comment__inner) {
      .umo-comment {
        &__avatar {
          margin-right: 10px;
          cursor: default;
          img {
            width: 48px;
            height: 48px;
          }
        }
        &__name {
          cursor: default;
          font-size: 14px;
        }
      }
    }
  }
  &-form {
    .umo-comment-buttons {
      margin-top: 10px;
      :deep(.umo-button) {
        margin-right: 10px;
        padding: 0 15px;
        height: 28px;
        font-size: 12px;
      }
    }
  }
}
</style>
