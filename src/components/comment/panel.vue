<template>
  <div
    class="umo-comment-container"
    :data-id="remarkId"
    :class="{ active, done }"
    @click="markClick(dataId)"
  >
    <div class="umo-comment-body">
      <t-comment
        class="umo-comment-item"
        :author="author"
        :datetime="datetime"
        :content="content"
      >
        <template #actions>
          <tooltip content="编辑此备注">
            <span @click.stop="editComment"><icon name="edit" /></span>
          </tooltip>
          <tooltip content="删除此备注">
            <span @click.stop="deleteComment(remarkId, dataId)"
              ><icon name="node-delete"
            /></span>
          </tooltip>

          <tooltip content="审核此备注">
            <span @click.stop="examineComment(remarkId, dataId)"
              ><CheckCircleIcon
            /></span>
          </tooltip>
        </template>
      </t-comment>
    </div>

    <modal
      v-model:visible="visible"
      :footer="false"
      :header="t('insert.remark.edit')"
    >
      <div>
        <t-textarea
          v-model="commentEdit"
          :maxlength="200"
          placeholder="请输入备注内容"
          show-limit-number
        />
      </div>
      <div class="btn-group">
        <t-button theme="primary" @click="editDone(remarkId)">{{
          t('insert.games.insert')
        }}</t-button>
        <t-button theme="default" @click="visible = false">{{
          t('insert.simulation.cancel')
        }}</t-button>
      </div>
    </modal>
  </div>
</template>

<script setup lang="ts">
import { CheckCircleIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'

import {
  delUserCommon,
  examineUserCommon,
  updateUserCommon,
} from '@/api/book/userCommon'
const { editor } = useStore()
let visible = $ref(false)
let commentEdit = $ref('')
const props = defineProps({
  active: {
    type: Boolean,
    default: true,
  },
  done: {
    type: Boolean,
    default: true,
  },
  author: {
    type: String,
    default: '',
  },
  datetime: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
  remarkId: {
    type: String,
    default: '',
  },
  dataId: {
    type: String,
    default: '',
  },
})

const emits = defineEmits(['done'])

const comment = $ref('')

const markAsDone = () => {
  emits('done')
}

const editComment = () => {
  commentEdit = props.content
  visible = true
}

const editDone = (id) => {
  if (!commentEdit) return MessagePlugin.error('备注内容不能为空')
  const obj = {
    remarkId: id,
    remarkContent: commentEdit,
  }
  updateUserCommon(obj).then((res) => {
    if (res.code === 200) {
      MessagePlugin.success('修改成功')
      emits('getList')
      visible = false
    } else {
      MessagePlugin.error(res.msg)
    }
  })
}
const replyComment = () => {
  console.log('reply')
}
const deleteComment = (id, dataId) => {
  delUserCommon(id).then((res) => {
    if (res.code === 200) {
      MessagePlugin.success('删除成功')

      editor.value?.commands.unsetInputCommon(id)
      emits('done', dataId)
      saveContentMethod()
    } else {
      MessagePlugin.error(res.msg)
    }
  })
  console.log('delete')
}

// 审核备注
const examineComment = (id, dataId) => {
  examineUserCommon({ remarkId: id }).then((res) => {
    MessagePlugin.success('审核备注成功')
    emits('done', dataId)
    saveContentMethod()
  })
}

const saveContentMethod = inject('saveContent') as () => void
const createComment = () => {
  console.log('create')
}

const markClick = (dataId) => {
  console.log(dataId)
  emits('markClick', dataId)
}
</script>

<style lang="less" scoped>
.umo-comment {
  &-container {
    background-color: var(--umo-color-white);
    border-radius: var(--umo-radius);
    border: solid 1px var(--umo-border-color);
    padding: 12px 15px 15px;
    opacity: 0.6;
    &.done {
      border-color: #ccc;
      background-color: #fff;
    }
    &.active {
      border-color: #baddfa;
      opacity: 1;
      box-shadow: var(--umo-shadow);
    }

    &:hover {
      opacity: 1;
      cursor: pointer;
      border: 1px solid #baddfa;
    }
  }
  &-header {
    display: flex;
    align-items: center;
    padding-bottom: 13px;
    &-quote {
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      white-space: nowrap;
      font-size: 12px;
      line-height: 14px;
      color: var(--umo-text-color-light);
      padding: 0 10px;
      position: relative;
      opacity: 0.8;
      &::before {
        content: '';
        display: block;
        height: 14px;
        width: 4px;
        background-color: var(--umo-text-color-light);
        border-radius: 2px;
        position: absolute;
        left: 0;
      }
    }
    &-actions {
      --td-comp-size-xs: 20px;
      :deep(.umo-button) {
        color: var(--umo-text-color-light);
        &.done {
          color: #15b371;
        }
        .umo-icon {
          font-size: 14px;
        }
      }
    }
  }
  &-body {
    position: relative;
  }
}
.umo-comment-item {
  position: relative;
  &:not(:last-child) {
    margin-bottom: 12px;
    border-bottom: solid 1px var(--umo-border-color-light);
    padding-bottom: 12px;
  }
  :deep(.umo-comment__inner) {
    .umo-comment {
      &__avatar {
        margin-right: 12px;
        cursor: default;
        img {
          width: 32px;
          height: 32px;
        }
      }
      &__author {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 5px;
      }
      &__name {
        font-size: 14px;
        cursor: default;
      }
      &__time {
        padding: 0;
      }
      &__detail {
        font-size: 12px;
        word-break: break-all;
        word-wrap: break-word;
        line-height: 1.6;
      }
      &__quote {
        margin-top: 6px;
        border: none;
        padding: 3px 8px;
        border-radius: var(--umo-radius);
        background-color: var(--umo-content-node-selected-background);
        cursor: pointer;
        &:hover {
          background-color: var(--umo-button-hover-background);
        }
      }
      &__actions {
        position: absolute;
        top: 0;
        right: 0;
        gap: 2px;
        margin-top: 2px;
        display: none;
        .umo-button {
          margin: 0 !important;
          height: 20px;
          width: 20px;
          padding: 0;
          color: #999;
        }
      }
    }
  }
  :deep(.umo-comment__reply) {
    margin: 10px 0 0;
    background: none;
    padding: 0;
  }
  &:hover {
    :deep(.umo-comment__inner) {
      .umo-comment__actions {
        display: flex !important;
      }
      .umo-comment__time {
        display: none;
      }
    }
  }
}
.umo-comment-divider {
  margin: 12px 0;
  font-size: 12px;
  text-align: center;
  color: var(--umo-primary-color);
  border-bottom: solid 1px var(--umo-border-color-light);
  padding-bottom: 10px;
  cursor: pointer;
}
.umo-comment-reply {
  :deep(.umo-textarea__inner) {
    font-size: 12px;
    line-height: 1.6;
  }
  &-info {
    width: 154px;
    font-size: 12px;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    white-space: nowrap;
    box-sizing: border-box;
    color: var(--umo-text-color-light);
  }
  &-detail {
    font-size: 12px;
    padding: 8px 10px;
    max-width: 154px;
    line-height: 1.4;
    word-break: break-all;
    word-wrap: break-word;
  }
}

.btn-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  .umo-button {
    margin-left: 10px;
  }
}
</style>
