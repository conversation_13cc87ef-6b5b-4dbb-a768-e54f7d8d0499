<template>
  <div class="ai-container">
    <div class="ai-content-menu">
      <div class="ai-content-menu-item">
        <MenusAiKnowledgeGraph />
      </div>
      <div class="ai-content-menu-item">
        <MenusAiIntelligentProofreading />
      </div>
      <div class="ai-content-menu-item">
        <MenusAiMatchCase />
      </div>
      <div class="ai-content-menu-item">
        <MenusAiGenerateBrainMaps />
      </div>

      <div class="ai-content-menu-item">
        <MenusAiGenerateLearningObjectives />
      </div>
      <div class="ai-content-menu-item">
        <MenusAiGenerateSummary />
      </div>

      <div class="ai-content-menu-item">
        <MenusAiGenerateOutline />
      </div>
      <div class="ai-content-menu-item">
        <MenusAiAIPolishing />
      </div>

      <div class="ai-content-menu-item">
        <MenusAiAIVoicePackage />
      </div>
      <div class="ai-content-menu-item">
        <MenusAiAIVideo />
      </div>

      <div class="ai-content-menu-item">
        <MenusAiAIimage />
      </div>
      <div class="ai-content-menu-item">
        <MenusAiAITestQuestions />
      </div>
    </div>
  </div>
</template>

<script></script>

<style scoped lang="less">
.ai-container {
  margin: 20px 10px;

  .ai-content-menu {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;

    .ai-content-menu-item {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-bottom: 10px;
    }
  }
}
</style>
