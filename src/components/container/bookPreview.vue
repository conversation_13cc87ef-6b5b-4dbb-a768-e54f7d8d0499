<!-- 非编辑内预览 -->
<template>
  <t-dialog
    v-model:visible="bookPreviewVisible"
    mode="full-screen"
    :footer="false"
    :destroy-on-close="true"
    @close="emit('update')"
  >
    <template #header>
      <div class="my-header">
        <div
          v-for="tab in tabs"
          :key="tab.icon"
          :class="{ selected: checkTabs === tab.icon }"
          class="tab-item"
          @click="changeTab(tab.icon)"
        >
          <i :class="tab.icon"></i>{{ tab.value }}
        </div>
      </div>
    </template>
    <div class="preview-menus">
      <div
        class="preview-menus-content"
        :style="{
          width: menuIsShow ? '300px' : '0',
          height: '100vh',
          overflowY: 'auto',
        }"
      >
        {{ menuIsShow }}
        <t-tabs
          v-if="menuIsShow"
          :default-value="1"
          style="
            width: 300px;
            height: 100vh !important;
            overflow-y: auto !important;
          "
        >
          <t-tab-panel :value="1" label="目录" :destroy-on-hide="false">
            <div v-if="bookId">
              <div v-for="item in chapterList" :key="item.chapterId">
                <div @click="changeChapter(item)">
                  <t-tooltip :content="item.chapterName">
                    {{ item.chapterName }}
                  </t-tooltip>
                </div>
                <t-menu
                  v-if="curChapterId == item.chapterId"
                  width="300px"
                  :expanded="expandedNames"
                >
                  <container-toc-item
                    :list="newMenuList"
                    @on-click="(e, item) => headingClick(item)"
                  />
                </t-menu>
              </div>
            </div>

            <t-menu v-else width="300px" :expanded="expandedNames">
              <container-toc-item
                :list="newMenuList"
                @on-click="(e, item) => headingClick(item)"
              />
            </t-menu>
          </t-tab-panel>
          <t-tab-panel :value="2" label="知识图谱" :destroy-on-hide="false">
            <p style="margin: 20px">选项卡2内容区</p>
          </t-tab-panel>
          <t-tab-panel :value="3" label="搜索" :destroy-on-hide="false">
            <!-- <container-search-preview :allDomList="allDomList" /> -->
          </t-tab-panel>
        </t-tabs>
      </div>
      <div
        v-if="!menuIsShow"
        class="preview-menu-open"
        @click="menuIsShow = true"
      >
        <MenuFoldIcon />
      </div>

      <div v-else class="preview-menu-cloce" @click="menuIsShow = false">
        1
        <MenuUnfoldIcon />
      </div>
    </div>

    <div class="content-container">
      <div v-if="checkTabs === 'phone'" class="phonebg-phone">
        <div class="phoneMain">
          <div class="phoneHeader">
            <div>{{ formattedTime }}</div>
            <div>
              <img
                v-if="bookPreviewVisible"
                src="@/assets/editorImg/phoneHeaderTools.png"
                alt=""
                height="12px"
                @load="onLoad"
              />
            </div>
          </div>

          <div
            ref="appContent"
            class="phoneContent umo-editor-container"
            contenteditable="false"
          >
            <analysisJson
              :check-tabs="checkTabs"
              :page-config-list="pageConfigList"
            />
          </div>
        </div>
      </div>

      <div v-else-if="checkTabs === 'tablet'" class="content-tablet">
        <div class="padMain">
          <div class="phoneHeader">
            <div>{{ formattedTime }}</div>
            <div>
              <img
                v-if="bookPreviewVisible"
                src="@/assets/editorImg/phoneHeaderTools.png"
                alt=""
                height="12px"
                @load="onLoad"
              />
            </div>
          </div>

          <div
            ref="appContent"
            class="padContent preview-box"
            contenteditable="false"
          >
            <analysisJson
              :check-tabs="checkTabs"
              :page-config-list="pageConfigList"
            />
          </div>
        </div>
      </div>

      <div v-else="checkTabs === 'desktop'" class="content-pc">
        <div class="pcbg">
          <div class="pcMain">
            <div ref="appContent" class="pcContent" contenteditable="false">
              <analysisJson
                :check-tabs="checkTabs"
                :page-config-list="pageConfigList"
              />
            </div>
          </div>
          <div class="pcFooter">
            <img
              v-if="bookPreviewVisible"
              src="@/assets/editorImg/mac-bottom.png"
              alt=""
              @load="onLoad"
            />
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>
<script setup lang="ts">
import { MenuFoldIcon, MenuUnfoldIcon } from 'tdesign-icons-vue-next'

import { queryChapterList } from '@/api/book/chapter'
import { getChapterContentInfo } from '@/api/chapterContent'
import { getChapterCatalogue } from '@/api/chapterContent'
const props = defineProps({
  chapterId: {
    type: String,
  },
  visible: {
    type: Boolean,
    required: true,
  },
  bookId: {
    type: String,
  },
})
const emit = defineEmits(['update'])
let checkTabs = $ref('phone')
const menuIsShow: boolean = $ref(true)
let pageConfigList: any = $ref([])
const appContent: any = $ref(null)
let expandedNames: any = $ref([])
let newMenuList: any = $ref([])
let allDomList: any = $ref([])
const bookPreviewVisible = ref(false)
const chapterList = ref([])
const curChapterId = ref(null)
const tabs = ref([
  {
    name: 'headerTools.preview.phonePreview',
    icon: 'phone',
    value: '手机',
  },
  {
    name: 'headerTools.preview.tabletPreview',
    icon: 'tablet',
    value: '平板',
  },
  {
    name: 'headerTools.preview.desktopPreview',
    icon: 'desktop',
    value: '电脑',
  },
])

watch(
  () => props.chapterId,
  (newVal) => {
    if (newVal) {
      getChapterDetail(newVal)
    }
  },
)
watch(
  () => props.visible,
  (newVal) => {
    checkTabs = 'phone'
    newMenuList = []
    bookPreviewVisible.value = newVal
    if (newVal) {
    }
  },
)

watch(
  () => props.bookId,
  (newVal) => {
    if (newVal) {
      getChapterList(newVal)
    }
  },
)

// 获取章节详情
function getChapterDetail(chapterId) {
  getChapterContentInfo(chapterId).then((response) => {
    const { data } = response
    if (data) {
      pageConfigList = JSON.parse(data.content).content || []
    } else {
      pageConfigList = []
    }
  })
}

// 更换章节
function changeChapter(item) {
  curChapterId.value = item.chapterId
  getChapterDetail(item.chapterId)
}

// 获取章节目录
async function chapterCatalogue() {
  const res = await getChapterCatalogue(props.chapterId)
  // if (res.code === 200) {
  //   newMenuList = res.data
  // }
}

// 获取章节列表
function getChapterList(bookId) {
  queryChapterList(bookId).then((res) => {
    chapterList.value = res.data || []
  })
}

/**遍历出菜单的树结构*/
function nestGrades(arr: any[]): any[] {
  const result: any[] = []
  let currentLevel = result
  const stack = []
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    const newLevel = { ...item }

    if (stack.length == 0 || stack[stack.length - 1].nodeName < item.nodeName) {
      if (
        currentLevel.length > 0 &&
        currentLevel[currentLevel.length - 1].children
      ) {
        currentLevel[currentLevel.length - 1].children.push(newLevel)
      } else {
        currentLevel.push(newLevel)
        if (currentLevel[currentLevel.length - 1].children === undefined) {
          currentLevel[currentLevel.length - 1].children = []
        }
      }
      stack.push(newLevel)
      currentLevel = newLevel.children
    } else {
      while (
        stack.length &&
        stack[stack.length - 1].nodeName >= item.nodeName
      ) {
        stack.pop()
      }
      if (stack[stack.length - 1])
        stack[stack.length - 1].children.push(newLevel)
      stack.push(newLevel)
      currentLevel = newLevel.children = []
    }
  }
  return result
}

const levelStandards = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
const onLoad = () => {
  nextTick(() => {
    const menuLs: any[] = []
    const idList: string[] = []
    const _allDomList: any[] = []

    if (!appContent?.firstChild?.childNodes) return false

    Array.from(appContent.firstChild.childNodes).forEach(
      (ele: any, i: number) => {
        if (ele.childNodes) {
          Array.from(ele.childNodes).forEach((item: any, index: number) => {
            _allDomList.push(item)
            if (levelStandards.includes(item.localName)) {
              const id = `${i}_${index}`
              idList.push(id)

              menuLs.push({
                dom: item,
                id,
                textContent: item.innerText,
              })
            }
          })
        }
      },
    )
    expandedNames = idList
    allDomList = _allDomList

    /**生成标签*/
    const tableOfContentsList: any[] = []
    let countArr: any[] = []
    menuLs.forEach((ele: any) => {
      if (ele.dom.nodeName === 'H1' && countArr.length) {
        tableOfContentsList.push(countArr)
        countArr = []
      }
      countArr.push(toRaw(ele))
    })

    if (countArr.length) {
      tableOfContentsList.push(countArr)
    }
    const newTableOfContents: any[] = []

    tableOfContentsList.forEach((ele) => {
      newTableOfContents.push(nestGrades(ele)[0])
    })
    newMenuList = newTableOfContents
  })
}

/**点击菜单*/
const headingClick = (heading: any) => {
  if (expandedNames.includes(heading?.id)) {
    expandedNames = expandedNames.filter((ele: string) => ele !== heading?.id)
  } else {
    expandedNames.push(heading?.id)
  }

  //把元素滚动到可视区
  heading.dom.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
  })
  const HTML: any = document.querySelector('html')
  HTML.scrollTop = 0
}

const changeTab = (index: string) => {
  checkTabs = index
}
const formattedTime = computed(() => {
  const currentTime = new Date()
  const hours = currentTime.getHours().toString().padStart(2, '0')
  const minutes = currentTime.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
})
</script>
<style lang="less" scoped>
.my-header {
  width: 380px;
  margin: 0 auto;
  display: flex;
  padding: 0 20px;
  align-items: center;
  background-color: var(--umo-container-background);
  height: 60px;

  .tab-item {
    display: flex;
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;

    .phone {
      display: inline-flex;
      margin-top: 3px;
      margin-right: 5px;
      width: 16px;
      height: 16px;
      background: url(@/assets/editorImg/phone.svg) no-repeat;
      background-size: contain;
    }

    .tablet {
      display: inline-flex;
      width: 16px;
      height: 16px;
      margin-top: 5px;
      margin-right: 5px;
      background: url(@/assets/editorImg/header-pad.svg) no-repeat;
      background-size: contain;
    }

    .desktop {
      display: inline-flex;
      width: 24px;
      height: 24px;

      margin-right: 5px;
      background: url(@/assets/editorImg/header-pc.svg) no-repeat;
      background-size: contain;
    }
  }

  .selected {
    background: var(--umo-primary-color);
    color: var(--umo-color-white);
    border-radius: var(--umo-radius-medium);
  }
}
.content-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  img {
    width: 100%;
  }
}
.phonebg-phone {
  width: 100%;
  overflow: hidden;
  .phoneMain {
    width: 22vw;
    height: calc(22vw * 1.78);
    border-radius: 52px;
    border-width: 11px;
    border-style: solid;
    border-color: rgb(0, 0, 0);
    border-image-source: url('@/assets/editorImg/phoneBg.png');
    border-image-slice: 120 fill;
    border-image-width: 80px;
    border-image-outset: initial;
    border-image-repeat: initial;
    margin: 0 auto;
    overflow: hidden;
    transform-origin: 50% 0;
    background-color: #ece8e8;
    margin-top: 80px;
    .phoneHeader {
      height: 28px;
      background-color: var(--umo-primary-color);
      line-height: 28px;
      padding: 0 30px;
      display: flex;
      justify-content: space-between;
      font-size: 1em;
      color: #fff;
    }

    .phoneContent {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      // background-color: #ff0;
      padding-bottom: 50px;
      box-sizing: border-box;
      overflow-y: scroll;
      scrollbar-width: none;
      cursor: pointer;

      ::-webkit-scrollbar {
        display: none;
      }
      .header {
        min-height: 71px;
      }

      p {
        margin: 0 10px;
      }

      table {
        width: 96%;
        margin: 0 auto;
        border-spacing: 0;
        border-collapse: 0;
        border-color: #333;
        margin: 0 auto;

        tr td {
          border: 1px solid;
          border-color: #333;
        }
      }
    }
  }
}
.content-tablet {
  width: 100%;
  .padMain {
    width: 50vw;
    height: calc(50vw / 1.6);
    border-radius: 28px;
    border-width: 10px;
    border-style: solid;
    border-color: rgb(0, 0, 0);
    border-image-source: url('@/assets/editorImg/ipadBg.png');
    border-image-slice: 121 fill;
    border-image-width: 60px;
    border-image-outset: initial;
    border-image-repeat: initial;
    background-color: #ece8e8;
    margin: 0 auto;
    overflow: auto;
    margin-top: 100px;
    scrollbar-width: none;
    .phoneHeader {
      height: 20px;
      background-color: var(--umo-primary-color);
      line-height: 20px;
      padding: 0 40px;
      display: flex;
      justify-content: space-between;
      font-size: 1em;
      color: #fff;
    }

    .preview-box {
      position: relative;
      overflow: hidden;
    }

    .padContent {
      overflow-y: scroll;
      scrollbar-width: none;
      cursor: pointer;
      flex: 1;

      ::-webkit-scrollbar {
        display: none;
      }

      p {
        margin: 0 10px;
      }

      table {
        width: 96%;
        margin: 0 auto;
        border-spacing: 0;
        border-collapse: 0;
        border-color: #333;
        margin: 0 auto;

        tr td {
          border: 1px solid;
          border-color: #333;
        }
      }
    }
  }
}

.content-pc {
  .pcbg {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 600px;
    margin-top: 70px;
    .pcMain {
      width: 55vw;
      height: calc(55vw / 1.6);
      border-radius: 0px;
      border-width: 28px 26px 32px;
      border-style: solid;
      border-color: rgb(0, 0, 0);
      border-image-source: url('@/assets/editorImg/mac.png');
      border-image-slice: 40 fill;
      border-image-width: 35px;
      border-image-outset: initial;
      border-image-repeat: initial;
      position: relative;
      overflow: hidden;
      margin: 0 auto;
      flex: auto;
      border-radius: 40px;
      background-color: #ece8e8;
      .pcContent {
        padding: 10px;
        height: 100%;
        overflow-y: scroll;
        scrollbar-width: none;
        cursor: pointer;

        ::-webkit-scrollbar {
          display: none;
        }

        p {
          margin: 0 10px;
        }

        table {
          width: 96%;
          margin: 0 auto;
          border-spacing: 0;
          border-collapse: 0;
          border-color: #333;
          margin: 0 auto;

          tr td {
            border: 1px solid;
            border-color: #333;
          }
        }
      }
    }

    .pcFooter {
      flex-grow: 1 !important;
      margin: 0 auto;
      position: relative;
      top: -20px;
      & > img {
        width: 69vw;
      }
    }
  }
}
.preview-menus {
  position: fixed;
  top: 61px;
  left: 0;
  width: auto;
  height: calc(100vh - 60px);
  background-color: red;
  box-shadow: 10px 0 15px -3px rgba(88, 87, 87, 0.1);
  border-right: 1px solid var(--umo-border-color);
  /* 设置滚动条的宽度 */
  ::-webkit-scrollbar {
    width: 0px;
  }
  /* 自定义滚动条轨道 */
  ::-webkit-scrollbar-track {
    background-color: #f1f1f1;
  }

  /* 自定义滚动条的滑块 */
  ::-webkit-scrollbar-thumb {
    background-color: #888;
  }
  .preview-menus-content {
    overflow-y: auto;
    // background-color: #ff0;
    height: 100%;
    transition: 0.3s;
    .preview-menus-roll {
      height: calc(100vh - 120px);
      overflow-y: auto;
    }
  }
  .preview-menu-open {
    position: absolute;
    left: 20px;
    top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--umo-container-background);
    border: 1px solid var(--umo-border-color-dark);
    cursor: pointer;
  }
  .preview-menu-cloce {
    position: absolute;
    right: 14px;
    top: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    cursor: pointer;
  }
}
</style>

<style lang="less">
.phoneMain-page-item {
  background-color: #fff;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px;
  box-sizing: border-box;
}
</style>
