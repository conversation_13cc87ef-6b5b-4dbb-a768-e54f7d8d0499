<!-- 模块说明 -->
<template>
  <div class="catalogue">
    <div class="catalogue-main" :style="{ width: page.showToc ? '300px' : 0 }">
      <container-toc />
    </div>

    <div v-if="show">
      <div v-if="page.showToc" class="umo-dialog__close" @click="page.showToc = false">
        <MenuUnfoldIcon />
      </div>

      <div v-if="!page.showToc" class="umo-dialog__open" @click="page.showToc = true">
        <MenuFoldIcon />
      </div>
    </div>
  </div>
</template>

<script setup>
import { MenuFoldIcon, MenuUnfoldIcon } from 'tdesign-icons-vue-next'
const props = defineProps({
  show: {
    type: Boolean,
    default: true,
  },
})
const { page } = useStore()
</script>

<style lang="less" scoped>
.catalogue {
  height: 100%;
  display: flex;
  position: absolute;

  .umo-dialog__close {
    position: absolute;
    left: 260px;
    top: 170px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9;
  }

  .umo-dialog__open {
    position: absolute;
    left: 20px;
    top: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    // background-color: var(--umo-container-background);
    border: 1px solid var(--umo-border-color-dark);
    cursor: pointer;
  }

  .catalogue-main {
    width: 0;
    height: 100%;
    transition: 0.3s;
    overflow: hidden;
    // background-color: var(--umo-container-background);
    // box-shadow: 10px 0 15px -3px rgba(88, 87, 87, 0.1);
    position: relative;
    z-index: 1;
  }
}
</style>
