<template>
  <!-- 
   TODO: 
    1. 每个有评论的节点后面显示一个数字，点击后打开评论列表
    2. 点击文档中的标注正文也可以打开评论
  -->
  <div v-if="visible" class="umo-comments-group-container">
    <!-- 仅显示当前节点下的所有评论 -->
    <div>
      <div class="umo-comments-group-body" :style="`height:${pageZoomHeight}`">
        <comment-panel
          v-for="(item, index) in commentList"
          :key="index"
          :remark-id="item.remarkId"
          :author="item.nickname"
          :datetime="item.createTime"
          :data-id="item.dataId"
          :content="item.remarkContent"
          :active="selectIndex === index"
          @click="selectItem(index)"
          @done="deleteComment"
          @get-list="getList"
          @mark-click="getMarkClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Editor } from '@tiptap/vue-3'
import { useRoute } from 'vue-router'
// import { listUserCommon } from '@/api/book/userCommon'
const { page, editor, commentList, getCommentList, container, setChapterId } =
  useStore()
const route = useRoute()
let visible = $ref(true)
let scrollTop = $ref(0)
let selectIndex = $ref(0)
const dataList = $ref([])
// 更新位置
const updatePostion = () => {
  const { offsetTop } = useNodePostion()
  if (offsetTop === null) {
    return
  }
  // visible = true
  scrollTop = 20
  // scrollTop = offsetTop
}

const getList = () => {
  getCommentList().then((res) => {})
}

const selectItem = (index: number) => {
  selectIndex = index
}

const deleteComment = (data: string) => {
  const element = document.querySelector(`[dataid="${data}"`)
  element.style.border = ''

  getList()
}

const getMarkClick = (data) => {
  const pageContainer = document.querySelector(
    `${container} .umo-page-container`,
  )
  const element = document.querySelector(`[dataid="${data}"`)

  if (!element) return MessagePlugin.error('没有找到该备注')
  element.style.borderBottom = '1px solid red'

  element.scrollIntoView({ behavior: 'smooth', block: 'center' })

  pageContainer?.scrollTo({
    top: (element as HTMLElement)?.offsetTop ?? 0 + 10,
  })
}
let pageZoomHeight = $ref('')

onMounted(() => {
  const { chapterId } = route.query
  if (chapterId) {
    setChapterId(chapterId)
  }
  getList()
  const el = document.querySelector(`.umo-main-bg`)

  pageZoomHeight = `${(el.clientHeight * (page.value.zoomLevel ?? 1)) / 100 - 100}px`
})
watch(
  editor,
  (value: Editor | undefined) => {
    if (value) {
      editor.value?.on('selectionUpdate', updatePostion)
      editor.value?.on('focus', updatePostion)
    } else {
      visible = false
    }
  },
  { immediate: true },
)
watch(() => page.value.pagination, updatePostion)
</script>

<style lang="less" scoped>
.umo-comments-group {
  position: relative;

  &-container {
    min-width: 238px;
    width: 238px;
    right: 580px;
    top: 180px;
    z-index: 1;
    position: absolute;
    overflow-y: auto;
    min-height: 100%;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 20px;
    padding: 10px;
    margin-bottom: 10px;
    background-color: var(--umo-color-white);
    border-radius: var(--umo-radius);
    border: solid 1px var(--umo-border-color);
    &-title {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      :deep(.umo-icon) {
        margin-right: 6px;
        font-size: 18px;
      }
    }
    &-actions {
      :deep(.umo-icon) {
        font-size: 18px;
        color: var(--umo-text-color-light);
      }
    }
  }
  &-body {
    margin-top: 20px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
    :deep(.umo-comment-container) {
      &:not(last-child) {
        margin-bottom: 10px;
      }
    }
  }
}

@media screen and (max-width: 1920px) {
  .umo-comments-group {
    &-container {
      right: 310px;
    }
    &-body {
      height: 615px;
    }
  }
}
</style>
