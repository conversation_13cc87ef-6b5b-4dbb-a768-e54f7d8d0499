<template>
  <!-- 
   TODO: 
    1. 每个有评论的节点后面显示一个数字，点击后打开评论列表
    2. 点击文档中的标注正文也可以打开评论
  -->

  <DragBox
    v-if="commentList.length"
    :start-option="{ x: 100, y: 120 }"
    local-name="__initLocalName__"
  >
    <div v-if="openStatus" class="open">
      <span class="header">备注</span>
      <div class="btn" @click="open">展开</div>
    </div>
    <div
      v-else
      class="umo-comments-group-container"
      @mouseenter="mouseenter"
      @mouseleave="mouseleave"
    >
      <header :style="{ color: remarksStatus ? '#000' : 'rgba(0,0,0,0)' }">
        <span class="header">备注</span>
        <div class="btn" @click="open">折叠</div>
      </header>
      <div class="umo-comments-group-body">
        <comment-panel
          v-for="(item, index) in commentList"
          :key="index"
          :remark-id="item.remarkId"
          :author="item.nickname"
          :datetime="item.createTime"
          :data-id="item.dataId"
          :content="item.remarkContent"
          :active="selectIndex === index"
          @click="selectItem(index)"
          @done="deleteComment"
          @get-list="getList"
          @mark-click="getMarkClick"
        />
      </div>
    </div>
  </DragBox>
</template>

<script setup lang="ts">
import type { Editor } from '@tiptap/vue-3'
import { useRoute } from 'vue-router'
// import { listUserCommon } from '@/api/book/userCommon'
const { page, editor, commentList, getCommentList, container, setChapterId } =
  useStore()
const route = useRoute()
let visible = $ref(true)
let scrollTop = $ref(0)
let selectIndex = $ref(0)
const dataList = $ref([])
// 更新位置
const updatePostion = () => {
  const { offsetTop } = useNodePostion()
  if (offsetTop === null) {
    return
  }
  visible = true
  scrollTop = 20
  // scrollTop = offsetTop
}

const getList = () => {
  getCommentList().then((res) => {})
}

const selectItem = (index: number) => {
  selectIndex = index
}

const deleteComment = (data: string) => {

  const element = document.querySelector(`[dataid="${data}"`)
  if (element) {
    element.style.border = ''
  }
  getList()
}

const getMarkClick = (data) => {
  const element: HTMLElement | null = document.querySelector(
    `[dataid="${data}"`,
  )

  if (!element) return MessagePlugin.error('没有找到该备注')
  element.style.borderBottom = '1px solid red'

  const pageContainer = document.querySelector(
    `${container} .umo-zoomable-content`,
  )
  pageContainer?.scrollTo({
    top: (element?.offsetTop ?? 0) - 60,
  })
}
const openStatus = ref(false)
const open = () => {
  openStatus.value = !openStatus.value
  localStorage.setItem('openStatus', openStatus.value.toString())
}
const remarksStatus = ref(false)
const mouseenter = () => {
  remarksStatus.value = true
}
const mouseleave = () => {
  remarksStatus.value = false
}

onMounted(() => {
  const { chapterId } = route.query
  if (chapterId) {
    setChapterId(chapterId)
  }
  getList()
  openStatus.value = localStorage.getItem('openStatus') === 'true'
})
watch(
  editor,
  (value: Editor | undefined) => {
    if (value) {
      editor.value?.on('selectionUpdate', updatePostion)
      editor.value?.on('focus', updatePostion)
    } else {
      visible = false
    }
  },
  { immediate: true },
)
watch(() => page.value.pagination, updatePostion)
</script>

<style lang="less" scoped>
.open {
  width: 238px;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
  display: flex;
  justify-content: space-between;
  border-radius: 8px;
  .header {
    font-weight: bold;
    font-size: 16px;
  }
  .btn {
    cursor: pointer;
    &:hover {
      color: var(--umo-primary-color);
    }
  }
}
.umo-comments-group {
  // position: relative;

  &-container {
    width: 238px;
    // right: 580px;
    // top: 180px;
    z-index: 1;
    // position: absolute;
    overflow-y: auto;
    transition: 0.3s;
    padding: 20px;
    & > header {
      .header {
        font-weight: bold;
        font-size: 16px;
      }

      display: flex;
      justify-content: space-between;
      .btn {
        cursor: pointer;
        &:hover {
          color: var(--umo-primary-color);
        }
      }
    }
    &:hover {
      background-color: #fff;
      border-radius: 8px;
      right: 290px;
      cursor: move;
      box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
    }
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 20px;
    padding: 10px;
    margin-bottom: 10px;
    background-color: var(--umo-color-white);
    border-radius: var(--umo-radius);
    border: solid 1px var(--umo-border-color);
    &-title {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      :deep(.umo-icon) {
        margin-right: 6px;
        font-size: 18px;
      }
    }
    &-actions {
      :deep(.umo-icon) {
        font-size: 18px;
        color: var(--umo-text-color-light);
      }
    }
  }
  &-body {
    margin-top: 20px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
    :deep(.umo-comment-container) {
      &:not(last-child) {
        margin-bottom: 10px;
      }
    }
  }
}

@media screen and (max-width: 1920px) {
  .umo-comments-group {
    &-container {
      right: 310px;
    }
    &-body {
      height: calc(100vh - 210px);
    }
  }
}
</style>
