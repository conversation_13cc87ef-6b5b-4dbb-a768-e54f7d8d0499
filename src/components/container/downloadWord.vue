<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-04 14:15:43
 * @LastEditTime: 2025-02-14 09:49:47
 * @FilePath: \dutp-editor\src\components\container\downloadWord.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<template>
  <t-dialog
    v-model:visible="visible"
    theme="info"
    :header="t('export.word.exportWord')"
    :on-close="onClose"
    :footer="null"
    width="1200px"
    top="30"
    :destroy-on-close="true"
  >
    <div class="main">
      <div class="main-left">
        <analysisJson :page-config-list="pageConfigList" />
      </div>
      <div class="main-right">
        <p>{{ t('export.word.content') }}</p>
        <br />
        <section class="main-right-btn">
          <t-button theme="default" variant="base" @click="onClose"
            >取消</t-button
          >
          &nbsp;
          <t-button theme="primary" @click="onOk">导出</t-button>
        </section>
      </div>
    </div>
  </t-dialog>
</template>

<script setup>
// import htmlDocx from 'html-docx-js/dist/html-docx'
import { saveAs } from 'file-saver'

import { getInnerHtml } from '@/utils/createRichText.js'
const { downloadWordDialog, editor, loading } = useStore()
let visible = $ref(false)
let pageConfigList = $ref([])
watch(
  () => [downloadWordDialog],
  (val) => {
    if (val[0].value) {
      visible = true
      pageConfigList = editor.value.getJSON().content
    }
  },
  { deep: true },
)

const onClose = () => {
  downloadWordDialog.value = false
  visible = false
}
const onOk = async () => {
  loading.value = true
  await nextTick()
  try {
    const Views = document.querySelectorAll('.analysisJson')
    const innerHtml = await getInnerHtml(Views)
    const docx = htmlDocx.asBlob(innerHtml)
    // 通过 file-saver 保存到本地
    saveAs(docx, 'document.docx')
    useMessage('success', '操作成功,请耐心等待!')
    onClose()
    loading.value = false
  } catch (error) {
    onClose()
    useMessage('error', '网络错误,请稍后重试!')
    loading.value = false
  }
}
</script>

<style lang="less" scoped>
.main {
  width: 100%;
  height: 800px;
  display: flex;
  .main-left {
    width: 800px;
    height: 100%;
    background-color: #fff;
    margin-right: 20px;
    overflow-y: auto;
    scrollbar-width: none; /* 针对 Firefox */
    -ms-overflow-style: none; /* 针对 Internet Explorer 和 Edge */
    border: 1px solid var(--umo-border-color);
  }
  .main-right {
    width: 380px;
    position: relative;
    .main-right-btn {
      right: 0;
      position: absolute;
      bottom: 0;
    }
  }
}
</style>
