<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-13 09:44:18
 * @LastEditTime: 2024-12-11 14:01:32
 * @FilePath: \dutp-editor\src\components\container\headerTools.vue
 * @Description: 最上边导航栏
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <header class="header-title">
    <div class="header-title-back" @click="back">
      <ChevronLeftIcon />{{ t('headerTools.back') }}
    </div>
    <div class="header-title-name">
      <t-tooltip :content="bookName + '—' + chapterName" theme="light" placement="top"
        class="header-title-name-tooltip">
        <div class="header-title-name-title">{{ bookName }}</div>
        <div style="padding: 0 5px">—</div>
        <div class="header-title-name-chapterName">{{ chapterName }}</div>
      </t-tooltip>
    </div>
    <div class="header-title-btns">
      <!-- 设置按钮 -->
      <!-- <t-button theme="default" variant="outline">{{
        t('headerTools.setUp.title')
      }}</t-button> -->
      <t-tooltip v-if="funType != 2" :content="t('headerTools.setUp.title')" theme="light" placement="top"
        :show-arrow="false" destroy-on-close>
        <div :class="options.theme != 'dark' ? 'header-setting' : 'header-settingDark'
          " @click="handleSettings"></div>
      </t-tooltip>

      <t-tooltip v-if="funType != 2" :content="t('headerTools.print')" theme="light" placement="top" :show-arrow="false"
        destroy-on-close>
        <div :class="options.theme != 'dark' ? 'header-print' : 'header-printDask'" @click="printing = true"></div>
      </t-tooltip>

      <t-tooltip :content="t('headerTools.previewReader')" theme="light" placement="top" :show-arrow="false"
        destroy-on-close>
        <div :class="options.theme != 'dark' ? 'preview-reader' : 'preview-readerDask'
          " @click="jumpReader"></div>
      </t-tooltip>
      <!-- :class="editor?.isEmpty ? 'isEmptyCss' : ''" -->
      <!-- <container-preview /> -->
      <!-- <t-button
        theme="default"
        variant="outline"
        :disabled="editor?.isEmpty"
        @click="printing = true"
        >{{ t('headerTools.print') }}</t-button
      > -->
      <!-- 提交按钮 -->

      <!-- 导入按钮 -->
      <t-button v-if="funType != 2" theme="default" variant="outline" @click="importChapter">{{
        t('headerTools.importWord') }}</t-button>
      <!--      <t-button theme="default" variant="outline" @click="importChapterPdf">{{-->
      <!--          t('headerTools.importPdf')-->
      <!--        }}</t-button>-->
      <!-- <t-button theme="default" variant="outline">{{
        t('headerTools.recycleBin')
      }}</t-button> -->
      <!-- 保存按钮 -->
      <t-button :loading="loadingSet" theme="primary" variant="outline" @click="saveContent">{{ t('headerTools.save')
      }}</t-button>
      <!-- 打印按钮 -->

      <t-button v-if="funType != 2" theme="primary" @click="sumbit">{{ t('headerTools.setUp.submit') }}
      </t-button>

      <!-- 预览按钮 -->


      <t-switch v-model="checked" size="large" @change="change">
        <template #label="slotProps">
          <FogSunnyIcon v-if="checked" />
          <MoonFallIcon v-else />
        </template></t-switch>
    </div>
    <t-dialog v-model:visible="upload.open" header="章节导入" width="400px" destroy-on-close :confirm-btn="null"
      @cancel="upload.open = false">
      <t-upload ref="uploadRef" :limit="1" accept=".docx,.pdf" :headers="upload.headers"
        :action="upload.url + '?chapterId=' + upload.chapterId" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" draggable />
      仅允许导入docx格式文件。
      <div class="worn-text">
        docx文件第一行请设置为一级标题！（文字不能含有空格，且不可覆盖在图片上方）
      </div>
    </t-dialog>
    <t-dialog v-model:visible="upload.openPdf" header="章节导入" width="400px" destroy-on-close :confirm-btn="null"
      @cancel="upload.openPdf = false">
      <t-upload ref="uploadRef" :limit="1" accept=".pdf" :headers="upload.headers"
        :action="upload.pdfUrl + '?chapterId=' + upload.chapterId" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccessPdf" :auto-upload="false"
        tips="仅允许导入pdf格式文件。" draggable />
    </t-dialog>
  </header>
  <setting v-model:visible="settingsShow" />
</template>

<script setup>
import { ChevronLeftIcon } from 'tdesign-icons-vue-next'
import { FogSunnyIcon, MoonFallIcon } from 'tdesign-icons-vue-next'
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next'
import { useRoute, useRouter } from 'vue-router'

import { updateChapter } from '@/api/book/chapter'
import { getChapterInfo } from '@/api/book/chapter.js'
import { propsOptions } from '@/options'
import { getToken } from '@/request/token.js'

import mixins from '../menus/toolbar/base/import-word-mixins.js'
import setting from './setting.vue'
const { proxy } = getCurrentInstance()
const { options } = useStore()
const { pageTemplateId } = useTemplate()
const { importWord, editor, printing, page } = mixins()
const route = useRoute()
const router = useRouter()
const props = defineProps(propsOptions)
const $toolbar = useState('toolbar', props.editorKey)
const $document = useState('document', props.editorKey)
const { savedAt, settingsShow } = useStore()
let checked = $ref(false)
const chapterName = ref('')
const bookName = ref('')
const bookId = ref(null)
const curToken = ref(getToken())
// 1编辑器后台 2管理后台
const formType = ref(1)
// 1 教材列表 2 待办中心
const funType = ref(1)
const loadingSet = ref(false)
const upload = reactive({
  open: false,
  openPdf: false,
  isUploading: false,
  chapterId: null,
  headers: {
    Authorization: `Bearer ${curToken.value}`,
    Language: sessionStorage.getItem('Language'),
  },
  url: `${import.meta.env.VITE_APP_BASE_API}/book/chapter/importChapter`,
  pdfUrl: `${import.meta.env.VITE_APP_BASE_API}/book/chapter/importChapterPdf`,
})
onMounted(() => {
  formType.value = route.query.formType || 1
  funType.value = route.query.funType || 1
  bookId.value = route.query.bookId
  curToken.value = route.query.token || getToken()
  upload.headers.Authorization = `Bearer ${curToken.value}`
})
// 文件上传中处理
function handleFileUploadProgress(event, file, fileList) {
  upload.isUploading = true
}

// 文件上传成功处理
function handleFileSuccess(response, file, fileList) {
  upload.open = false
  upload.isUploading = false
  MessagePlugin.success('上传成功')
  const confirmDia = DialogPlugin.confirm({
    header: '导入Word',
    body: '导入操作已成功完成，请您移步至任务中心查看详细的导入进度信息。在此期间，为了确保数据处理的完整性和准确性，请勿进行保存操作。',
    theme: 'warning',
    cancelBtn: '留在编辑器',
    confirmBtn: '前往任务中心',
    onClose: () => {
      confirmDia.destroy()
    },
    onConfirm() {
      if (formType.value == 1) {
        router.push('/pages/taskCenter')
      } else {
        window.location.href = `${import.meta.env.VITE_APP_MANAGE_URL}book/task`
      }

      confirmDia.destroy()
    },
  })
}

// 文件上传成功处理
function handleFileSuccessPdf(response, file, fileList) {
  upload.openPdf = false
  upload.isUploading = false
  MessagePlugin.success('上传成功')
  // const confirmDia = DialogPlugin.confirm({
  //   header: '导入Word',
  //   body: '导入操作已成功完成，请您移步至任务中心查看详细的导入进度信息。在此期间，为了确保数据处理的完整性和准确性，请勿进行保存操作。',
  //   theme: 'warning',
  //   onClose: () => {
  //     console.log('close')
  //     confirmDia.destroy()
  //   },
  //   onConfirm() {
  //     router.push('/pages/taskCenter')
  //     confirmDia.destroy()
  //   },
  // })
}
// 跳转阅读器
function jumpReader() {
  const { chapterId } = route.query
  
  window.open(
    `${`${import.meta.env.VITE_READER_PREVIEW_URL}?k=${bookId.value}&cid=${chapterId}&fromType=2`}`,
  )
}

// 导入章节
function importChapter() {
  upload.open = true
  upload.chapterId = route.query.chapterId
}
function importChapterPdf() {
  upload.openPdf = true
  upload.chapterId = route.query.chapterId
}

// 保存
const saveContent = () => {
  loadingSet.value = true
  if ($toolbar.value.mode === 'source' || options.value.document?.readOnly) {
    return
  }
  try {
    setTimeout(async () => {
      const message = await useMessage('loading', {
        content: t('save.saving'),
        placement: 'bottom',
        closeBtn: true,
        offset: [0, -20],
      })
      const success = await options.value?.onSave?.(
        {
          html: editor.value?.getHTML(),
          json: editor.value?.getJSON(),
          text: editor.value?.getHTML(),
        },
        page.value,
        $document.value,
      )
      if (!success) {
        message.close()
        useMessage('error', {
          content: t('save.failed'),
          placement: 'bottom',
          offset: [0, -20],
        })
        return
      }
      if (pageTemplateId.value?.id) {
        pageTemplateId.value.templateId = pageTemplateId.value.id
      }

      message.close()
      useMessage('success', {
        content: t('save.success'),
        placement: 'bottom',
        offset: [0, -20],
      })
      const time = useTimestamp({ offset: 0 })
      savedAt.value = time.value
      loadingSet.value = false
    }, 3000)
  } catch (e) {
    useMessage('error', {
      content: t('save.error'),
      placement: 'bottom',
      offset: [0, -20],
    })
    console.error(e.message)
  }
}

// 提交
const sumbit = async () => {
  const confirmDia = DialogPlugin.confirm({
    header: '提交章节',
    body: '确定要提交该章节内容？',
    theme: 'warning',
    onCancel: () => {
      confirmDia.destroy()
    },
    onClose: () => {
      confirmDia.destroy()
    },
    onConfirm() {
      updateChapter({
        chapterId: chapterId.value,
        chapterStatus: 1,
      }).then((res) => {
        back()
        confirmDia.destroy()
        MessagePlugin.success('提交成功')
      })
    },
  })
}

const handleSettings = () => {
  settingsShow.value = true
}

/**切换主题*/
const change = (v) => {
  const val = !v ? 'light' : 'dark'
  options.value.theme = val
  localStorage.setItem('__theme__', val)
}

const getInfo = async () => {
  const { data } = await getChapterInfo(chapterId.value)
  bookName.value = data.bookName
  chapterName.value = data.chapterName
}

const back = () => {
  if (formType.value == 1) {
    if (funType.value == 2) {
      router.push({
        path: '/pages/bookApprove',
        query: {
          bookId: bookId.value,
          masterFlag: route.query.masterFlag,
          bookOrganize: route.query.bookOrganize,
          stepId: route.query.stepId,
          processId: route.query.processId,
          auditUserId: route.query.auditUserId,
          stepValue: 4,
        },
      })
    } else {
      router.push({
        path: '/pages/detail',
        query: { bookId: bookId.value },
      })
    }
  } else if (formType.value == 2) {
    if (funType.value == 2) {
      window.location.href =
        `${import.meta.env.VITE_APP_MANAGE_URL}book/approve` +
        `?bookId=${bookId.value}`
        + `&masterFlag=${route.query.masterFlag
        }&bookOrganize=${route.query.bookOrganize
        }&stepId=${route.query.stepId
        }&processId=${route.query.processId
        }&auditUserId=${route.query.auditUserId
        }&activeTab=4`

    } else {
      window.location.href =
        `${import.meta.env.VITE_APP_MANAGE_URL}book/bookDetail` +
        `?bookId=${bookId.value}`
    }
  }
}

const chapterId = ref(route.query.chapterId)
onMounted(() => {
  checked = options.value.theme !== 'light'
  if (!localStorage.getItem('__theme__')) {
    localStorage.setItem('__theme__', options.value.theme)
  }
  getInfo()
})
</script>

<style lang="less" scoped>
:deep(.umo-upload__single-name) {
  width: 50%;
}

.catalogue {
  margin-top: -20px;
  height: calc(100vh - 70px);
  overflow: hidden;
}

.worn-text {
  margin-top: 10px;
  margin-right: 7px;
  padding: 10px;
  border-radius: 5px;
  color: #ff6262;
  background-color: #ffeded;
  border: 2px solid #ffc8c8;
}

.header-title {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  height: 50px;
  width: 100vw;
  // background-color: #ff0;
  box-sizing: border-box;
  border-bottom: 1px solid var(--umo-border-color);

  .header-title-back {
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100px;
  }



  .header-title-name {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
    width: 937px;

    :global(.umo-tooltip .umo-popup__arrow) {
      background: inherit;
      height: 8px;
    }


    ::v-deep(.header-title-name-tooltip) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;


      //书名
      .header-title-name-title {
        width: 100%;
        overflow: hidden;
        text-align: right;
        /* 显示省略号 */

      }


      //章节名
      .header-title-name-chapterName {
        width: 100%;
        white-space: nowrap;
        /* 强制文本不换行 */
        overflow: hidden;
        /* 隐藏溢出内容 */
        text-overflow: ellipsis;
        /* 显示省略号 */

      }
    }

  }

  .header-title-btns {
    padding: 0 30px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .header-setting {
      width: 24px;
      height: 24px;
      cursor: pointer;
      background: url('@/assets/icons/settingIcon.svg') no-repeat;
      background-size: 100% 100%;
      margin-right: 20px;
    }

    .header-settingDark {
      width: 24px;
      height: 24px;
      cursor: pointer;
      background: url('@/assets/icons/settingIconDark.svg') no-repeat;
      background-size: 100% 100%;
      margin-right: 20px;
    }

    .header-print {
      width: 24px;
      height: 24px;
      cursor: pointer;
      background: url('@/assets/icons/settingPrint.svg') no-repeat;
      background-size: 100% 100%;
      margin-right: 20px;
    }

    .header-printDask {
      width: 24px;
      height: 24px;
      cursor: pointer;
      background: url('@/assets/icons/settingPrintDask.svg') no-repeat;
      background-size: 100% 100%;
      margin-right: 20px;
    }

    .preview-reader {
      width: 24px;
      height: 24px;
      cursor: pointer;
      background: url('@/assets/icons/preview-reader.svg') no-repeat;
      background-size: 100% 100%;
      margin-right: 20px;
    }

    .preview-readerDask {
      width: 24px;
      height: 24px;
      cursor: pointer;
      background: url('@/assets/icons/preview-readerDask.svg') no-repeat;
      background-size: 100% 100%;
      margin-right: 20px;
    }

    .isEmptyCss {
      pointer-events: none;
      cursor: not-allowed;
    }

    button {
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
