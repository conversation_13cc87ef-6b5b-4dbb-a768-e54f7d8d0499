<!-- 模块说明 -->
<template>
  <!-- <t-button theme="default" variant="outline" @click="openIsShow">{{
    t('headerTools.preview.text')
  }}</t-button> -->
  <t-tooltip
    :content="t('headerTools.preview.text')"
    theme="light"
    placement="top"
    :show-arrow="false"
    destroy-on-close
  >
    <div
      :class="options.theme != 'dark' ? 'preview-btn' : 'preview-btnDark'"
      @click="openIsShow"
    ></div>
  </t-tooltip>

  <t-dialog
    v-model:visible="isShow"
    mode="full-screen"
    :footer="false"
    :destroy-on-close="true"
    @close="colse"
  >
    <template #header>
      <div class="my-header">
        <div
          v-for="tab in tabs"
          :key="tab.icon"
          :class="{ selected: checkTabs === tab.icon }"
          class="tab-item"
          @click="changeTab(tab.icon)"
        >
          <i :class="tab.icon"></i>{{ t(tab.name) }}
        </div>
      </div>
    </template>
    <div class="preview-menus">
      <div
        class="preview-menus-content"
        :style="{ width: menuIsShow ? '300px' : '0' }"
      >
        <t-tabs
          :default-value="1"
          style="width: 300px; height: 100vh; overflow-y: auto"
        >
          <t-tab-panel
            :value="1"
            :label="t('newToc.catalogue')"
            :destroy-on-hide="false"
            class="preview-menus-catalog"
          >
            <t-menu width="300px" :expanded="expanded">
              <container-toc-item
                :list="chapterLogList"
                @on-click="(e, item) => headingClick(e, item)"
              />
            </t-menu>
          </t-tab-panel>
          <!-- <t-tab-panel
            :value="2"
            :label="t('newToc.knowledgeGraph')"
            :destroy-on-hide="false"
          >
            <p style="margin: 20px">选项卡2内容区</p>
          </t-tab-panel> -->
          <t-tab-panel
            :value="3"
            :label="t('newToc.search')"
            :destroy-on-hide="false"
          >
            <container-search-preview :all-dom-list="allDomList" />
          </t-tab-panel>
        </t-tabs>
      </div>
      <div
        v-if="!menuIsShow"
        class="preview-menu-open"
        @click="menuIsShow = true"
      >
        <MenuFoldIcon />
      </div>

      <div v-else class="preview-menu-cloce" @click="menuIsShow = false">
        <MenuUnfoldIcon />
      </div>
    </div>

    <div class="content-container">
      <div v-if="checkTabs === 'phone'" class="phonebg-phone">
        <div class="phoneMain">
          <div class="phoneHeader">
            <div>{{ formattedTime }}</div>
            <div>
              <img
                src="@/assets/editorImg/phoneHeaderTools.png"
                alt=""
                height="12px"
              />
            </div>
          </div>

          <div
            ref="appContent"
            class="phoneContent umo-editor-container"
            contenteditable="false"
          >
            <analysisJson
              :check-tabs="checkTabs"
              :page-config-list="pageConfigList"
            />
          </div>
        </div>
      </div>

      <div v-else-if="checkTabs === 'tablet'" class="content-tablet">
        <div class="padMain">
          <div class="phoneHeader">
            <div>{{ formattedTime }}</div>
            <div>
              <img
                src="@/assets/editorImg/phoneHeaderTools.png"
                alt=""
                height="12px"
              />
            </div>
          </div>

          <div
            ref="appContent"
            class="padContent preview-box"
            contenteditable="false"
          >
            <analysisJson
              :check-tabs="checkTabs"
              :page-config-list="pageConfigList"
            />
          </div>
        </div>
      </div>

      <div v-else="checkTabs === 'desktop'" class="content-pc">
        <div class="pcbg">
          <div class="pcMain">
            <div ref="appContent" class="pcContent" contenteditable="false">
              <analysisJson
                :check-tabs="checkTabs"
                :page-config-list="pageConfigList"
              />
            </div>
          </div>
          <div class="pcFooter">
            <img src="@/assets/editorImg/mac-bottom.png" alt="" />
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>
<script setup lang="ts">
import { MenuFoldIcon, MenuUnfoldIcon } from 'tdesign-icons-vue-next'

import { chapterCatalogList } from '@/api/book/chapter.js'
let checkTabs = $ref('phone')
let isShow = $ref(false)
const menuIsShow: boolean = $ref(true)
let pageConfigList: any = $ref([])
const appContent: any = $ref(null)
let expanded: any = $ref([])
let allDomList: any = $ref([])
const chapterLogList: any = ref([])
const { editor, chapterId, options } = useStore()

const tabs = ref([
  {
    name: 'headerTools.preview.phonePreview',
    icon: 'phone',
  },
  {
    name: 'headerTools.preview.tabletPreview',
    icon: 'tablet',
  },
  {
    name: 'headerTools.preview.desktopPreview',
    icon: 'desktop',
  },
])

// 获取章节目录
async function chapterCatalogue() {
  if (chapterId.value) {
    const res = await chapterCatalogList(chapterId.value)
    chapterLogList.value = res.data
  }
}

function generateDomList() {
  const _allDomList: any = []

  Array.from(appContent.firstChild.childNodes).forEach(
    (ele: any, i: number) => {
      if (ele.className == 'is_page') {
        Array.from(ele.childNodes).forEach((item: any, index: number) => {
          if (item.className == 'analysisJson-page') {
            Array.from(item.childNodes).forEach((p: any, idx: number) => {
              _allDomList.push(p)
            })
          }
        })
      }
    },
  )
  allDomList = _allDomList
}

// 点击菜单跳转或者展开
const headingClick = (e: any, heading: any) => {
  if (e.target?.className !== 'menuItems-maskLayer') {
    if (expanded.includes(heading?.id)) {
      expanded = expanded.filter((ele: string) => ele !== heading?.id)
    } else {
      expanded.push(heading?.id)
    }
  }

  //把元素滚动到可视区
  const targetDiv = document.querySelector(`[data-heading-id="${heading.id}"]`)
  if (targetDiv) {
    // 平滑滚动到目标位置
    targetDiv.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    })
  }
}

const colse = () => {
  isShow = false
}

// 打开预览弹窗
const openIsShow = () => {
  isShow = true
  chapterCatalogue()
  pageConfigList = (editor.value as any).getJSON()?.content
  pageConfigList?.forEach((page: any) => {
    page?.content.forEach((item: any) => {
      if (item.type === 'heading') {
        expanded.push(item?.attrs?.id)
      }
    })
  })
  setTimeout(() => {
    generateDomList()
  }, 500)
}
const changeTab = (index: string) => {
  checkTabs = index
}

const formattedTime = computed(() => {
  const currentTime = new Date()
  const hours = currentTime.getHours().toString().padStart(2, '0')
  const minutes = currentTime.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
})
</script>
<style lang="less" scoped>
.my-header {
  width: 380px;
  margin: 0 auto;
  display: flex;
  padding: 0 20px;
  align-items: center;
  background-color: var(--umo-bg-color);
  height: 60px;

  .tab-item {
    display: flex;
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;

    .phone {
      display: inline-flex;
      margin-top: 3px;
      margin-right: 5px;
      width: 16px;
      height: 16px;
      background: url(@/assets/editorImg/phone.svg) no-repeat;
      background-size: contain;
    }

    .tablet {
      display: inline-flex;
      width: 16px;
      height: 16px;
      margin-top: 5px;
      margin-right: 5px;
      background: url(@/assets/editorImg/header-pad.svg) no-repeat;
      background-size: contain;
    }

    .desktop {
      display: inline-flex;
      width: 24px;
      height: 24px;

      margin-right: 5px;
      background: url(@/assets/editorImg/header-pc.svg) no-repeat;
      background-size: contain;
    }
  }

  .selected {
    background: var(--umo-primary-color);
    color: var(--umo-color-white);
    border-radius: var(--umo-radius-medium);
  }
}
.content-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  img {
    width: 100%;
  }
}
.phonebg-phone {
  width: 100%;
  overflow: hidden;
  .phoneMain {
    width: 22vw;
    height: calc(22vw * 1.78);
    border-radius: 52px;
    border-width: 11px;
    border-style: solid;
    border-color: rgb(0, 0, 0);
    border-image-source: url('@/assets/editorImg/phoneBg.png');
    border-image-slice: 120 fill;
    border-image-width: 80px;
    border-image-outset: initial;
    border-image-repeat: initial;
    margin: 0 auto;
    overflow: hidden;
    transform-origin: 50% 0;
    background-color: #ece8e8;
    margin-top: 80px;
    .phoneHeader {
      height: 28px;
      background-color: var(--umo-primary-color);
      line-height: 28px;
      padding: 0 30px;
      display: flex;
      justify-content: space-between;
      font-size: 1em;
      color: #fff;
    }

    .phoneContent {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      // background-color: #ff0;

      padding-bottom: 50px;
      box-sizing: border-box;
      overflow-y: scroll;
      scrollbar-width: none;
      cursor: pointer;

      ::-webkit-scrollbar {
        display: none;
      }

      p {
        margin: 0 10px;
      }

      table {
        width: 96%;
        margin: 0 auto;
        border-spacing: 0;
        border-collapse: 0;
        border-color: #333;
        margin: 0 auto;

        tr td {
          border: 1px solid;
          border-color: #333;
        }
      }
    }
  }
}
.content-tablet {
  width: 100%;
  .padMain {
    width: 50vw;
    height: calc(50vw / 1.6);
    border-radius: 28px;
    border-width: 10px;
    border-style: solid;
    border-color: rgb(0, 0, 0);
    border-image-source: url('@/assets/editorImg/ipadBg.png');
    border-image-slice: 121 fill;
    border-image-width: 60px;
    border-image-outset: initial;
    border-image-repeat: initial;
    background-color: #fff;
    margin: 0 auto;
    overflow: auto;
    margin-top: 100px;
    scrollbar-width: none;
    .phoneHeader {
      height: 20px;
      background-color: var(--umo-primary-color);
      line-height: 20px;
      padding: 0 40px;
      display: flex;
      justify-content: space-between;
      font-size: 1em;
      color: #fff;
    }

    .preview-box {
      position: relative;
      overflow: hidden;
    }

    .padContent {
      overflow-y: scroll;
      scrollbar-width: none;
      cursor: pointer;
      flex: 1;

      ::-webkit-scrollbar {
        display: none;
      }

      p {
        margin: 0 10px;
      }

      table {
        width: 96%;
        margin: 0 auto;
        border-spacing: 0;
        border-collapse: 0;
        border-color: #333;
        margin: 0 auto;

        tr td {
          border: 1px solid;
          border-color: #333;
        }
      }
    }
  }
}

.content-pc {
  .pcbg {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 600px;
    margin-top: 70px;
    .pcMain {
      width: 55vw;
      height: calc(55vw / 1.6);
      border-radius: 0px;
      border-width: 28px 26px 32px;
      border-style: solid;
      border-color: rgb(0, 0, 0);
      border-image-source: url('@/assets/editorImg/mac.png');
      border-image-slice: 40 fill;
      border-image-width: 35px;
      border-image-outset: initial;
      border-image-repeat: initial;
      position: relative;
      overflow: hidden;
      margin: 0 auto;
      flex: auto;
      border-radius: 40px;
      background-color: #fff;
      .pcContent {
        height: 100%;
        overflow-y: scroll;
        scrollbar-width: none;
        cursor: pointer;

        ::-webkit-scrollbar {
          display: none;
        }

        p {
          margin: 0 10px;
        }

        table {
          width: 96%;
          margin: 0 auto;
          border-spacing: 0;
          border-collapse: 0;
          border-color: #333;
          margin: 0 auto;

          tr td {
            border: 1px solid;
            border-color: #333;
          }
        }
      }
    }

    .pcFooter {
      flex-grow: 1 !important;
      margin: 0 auto;
      position: relative;
      top: -20px;
      & > img {
        width: 69vw;
      }
    }
  }
}
.preview-menus {
  position: fixed;
  top: 61px;
  left: 0;
  width: auto;
  height: calc(100vh - 60px);
  box-shadow: 10px 0 15px -3px rgba(88, 87, 87, 0.1);
  border-right: 1px solid var(--umo-border-color);
  .preview-menus-content {
    overflow: hidden;
    background-color: #ff0;
    height: 100%;
    transition: 0.3s;
    .preview-menus-roll {
      height: calc(100vh - 120px);
      overflow-y: auto;
    }
  }
  .preview-menu-open {
    position: absolute;
    left: 20px;
    top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--umo-container-background);
    border: 1px solid var(--umo-border-color-dark);
    cursor: pointer;
  }
  .preview-menu-cloce {
    position: absolute;
    right: 14px;
    top: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    cursor: pointer;
  }
}
</style>

<style lang="less">
.phoneMain-page-item {
  background-color: #fff;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px;
  box-sizing: border-box;
}
</style>
<style lang="less">
.preview-btn {
  background: url('@/assets/icons/settingPreview.svg') no-repeat;
  background-size: 100% 100%;
  width: 24px;
  height: 24px;
  margin-right: 20px;
  cursor: pointer;
}
.preview-btnDark {
  background: url('@/assets/icons/settingPreviewDask.svg') no-repeat;
  background-size: 100% 100%;
  width: 24px;
  height: 24px;
  margin-right: 20px;
  cursor: pointer;
}
.preview-menus .umo-tabs__content {
  height: 100%;
  .preview-menus-catalog {
    /* 设置滚动条的宽度 */
    ::-webkit-scrollbar {
      width: 0px;
    }
    /* 自定义滚动条轨道 */
    ::-webkit-scrollbar-track {
      background-color: #f1f1f1;
    }

    /* 自定义滚动条的滑块 */
    ::-webkit-scrollbar-thumb {
      background-color: #888;
    }
    ul {
      list-style: none;
    }
  }
}
</style>
