<template>
  <div class="search-bg">
    <div class="search-box">
      <t-input
        :placeholder="t('newToc.placeholder')"
        class="searchInput"
        v-model="searchText"
      >
        <template #suffixIcon>
          <search-icon :style="{ cursor: 'pointer' }" />
        </template>
      </t-input>
    </div>
    <div class="search-result">
      <strong>{{ nodeList?.length }}</strong
      >{{ t('newToc.aResult') }}
    </div>

    <div class="search-list">
      <div
        class="search-item"
        v-for="(ele, i) in nodeList"
        :key="i"
        v-html="ele.innerHTML"
        @click="locationNode(ele)"
      ></div>
    </div>
  </div>
</template>
<script name="searchTxt" setup>
import { SearchIcon } from 'tdesign-icons-vue-next'
let searchText = $ref('')
let nodeList = $ref('')
const props = defineProps({
  allDomList: {
    type: Array,
    default: () => [],
  },
})

/**模糊搜索*/
const fuzzySearch = (list, search) => {
  let data = []
  if (list.length != 0 && search) {
    let str = `\S*${search}\S*`
    let reg = new RegExp(str, 'i') //不区分大小写
    list.map((item) => {
      //item是一个对象,name循环这个对象的每一项进行模糊查询
      if (reg.test(item.innerText)) {
        data.push(item)
      }
    })
  }
  return data
}

let lastCheckNode = $ref({ dom: null, value: null })
const locationNode = (node) => {
  node.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
  })
  if (lastCheckNode.dom) {
    lastCheckNode.dom.style.backgroundColor = lastCheckNode.value
  }
  lastCheckNode = { dom: node, value: node.style.backgroundColor }
  node.style.backgroundColor = 'var(--umo-content-search-result-background)'
  const HTML = document.querySelector('html')
  HTML.scrollTop = 0
}

watch(
  () => [searchText.trim(), props.allDomList],
  (val) => {
    nodeList = fuzzySearch(val[1], val[0])
    if (lastCheckNode.dom) {
      lastCheckNode.dom.style.backgroundColor = lastCheckNode.value
    }
  },
  { deep: true },
)
</script>
<style lang="less">
.search-box {
  .umo-input {
    background-color: var(--umo-container-background);
  }
}
</style>
<style lang="less" scoped>
.search-bg {
  padding: 20px;

  :deep(.umo-input) {
    height: 40px;
    border-radius: 20px;
  }

  .search-box {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .searchInput {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border: none;
    font-size: 16px;
    color: var(--umo-text-color);
    border-radius: 20px;
    margin-bottom: 10px;
  }

  .search-result {
    text-align: lefts;
    font-size: 14px;
    color: var(--umo-text-color);

    strong {
      padding: 0 5px;
      color: var(--umo-text-color);
    }
  }

  .search-list {
    margin: 10px 0;
    height: calc(100vh - 225px);
    overflow-y: auto;
    .search-item {
      &:hover {
        background-color: var(--umo-button-hover-background);
        cursor: pointer;
      }

      margin-bottom: 10px;
      border-radius: 5px;
      border: 1px solid var(--umo-border-color-dark);
      padding: 10px;
    }
  }
}
</style>
