<template>
  <div class="search-bg">
    <div class="search-box">
      <t-input
        :placeholder="t('newToc.placeholder')"
        class="searchInput"
        v-model="searchText"
        @enter="enter"
      >
        <template #suffixIcon>
          <search-icon :style="{ cursor: 'pointer' }" @click="enter" />
        </template>
      </t-input>
    </div>
    <div class="search-result">
      <strong>{{ nodeList?.length }}</strong
      >{{ t('newToc.aResult') }}
    </div>

    <div
      class="search-list"
      ref="searchMenuRefs"
      :style="`height:calc(100vh - ${searchMenuHeight}px)`"
    >
      <div
        class="search-item"
        v-for="(ele, i) in nodeList"
        :key="i"
        v-html="ele.innerHTML"
        @click="locationNode(ele)"
      ></div>
    </div>
  </div>
</template>
<script name="searchTxt" setup>
import { SearchIcon } from 'tdesign-icons-vue-next'
const { editor } = useStore()
const emit = defineEmits(['onWatchToolbar'])
let searchText = $ref('')
let nodeList = $ref('')

const search = (clearIndex = false) => {
  if (!editor.value) {
    return
  }
  if (clearIndex) {
    editor.value.commands.resetIndex()
  }
  editor.value.commands.setSearchTerm(searchText)
  goToSelection()
}

const goToSelection = () => {
  if (!editor.value) {
    return
  }
  const { results, resultIndex } = editor.value.storage.searchAndReplace
  const _nodeList = []
  if (results.length) {
    results.forEach((ele) => {
      editor.value.commands.setTextSelection(ele)
      const { node } = editor.value.view.domAtPos(
        editor.value.state.selection.anchor,
      )
      if (!_nodeList.includes(node)) {
        _nodeList.push(node)
      }
    })
    editor.value.commands.setTextSelection(results[resultIndex])
  }

  nodeList = _nodeList
}

const locationNode = (node) => {
  node.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
  })
  const HTML = document.querySelector('html')
  HTML.scrollTop = 0
}

let searchMenuHeight = $ref(0)
let searchMenuRefs = $ref(null)
const leftSearchHeight = () => {
  if (!searchMenuRefs) return
  searchMenuHeight = searchMenuRefs.getBoundingClientRect().top + 30
}

watch(
  () => searchText.trim(),
  (val) => {
    if (!val) {
      editor.value?.commands.resetIndex()
      search(true)
      leftSearchHeight()
    }
  },
)
const enter = () => {
  const val = searchText.trim()
  if (!val) {
    editor.value?.commands.resetIndex()
  }
  search(true)
  leftSearchHeight()
}
onMounted(() => {
  leftSearchHeight()
  emit('onWatchToolbar', leftSearchHeight)
})
</script>
<style lang="less">
.search-box {
  .umo-input {
    background-color: var(--umo-container-background);
  }
}
</style>
<style lang="less" scoped>
.search-bg {
  padding: 20px;

  :deep(.umo-input) {
    height: 40px;
    border-radius: 20px;
  }

  .search-box {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .searchInput {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border: none;
    font-size: 16px;
    color: var(--umo-text-color);
    border-radius: 20px;
    margin-bottom: 10px;
  }

  .search-result {
    text-align: lefts;
    font-size: 14px;
    color: var(--umo-text-color);

    strong {
      padding: 0 5px;
      color: var(--umo-text-color);
    }
  }

  .search-list {
    margin: 10px 0;
    overflow-y: auto;
    .search-item {
      &:hover {
        background-color: var(--umo-button-hover-background);
        cursor: pointer;
      }

      margin-bottom: 10px;
      border-radius: 5px;
      border: 1px solid var(--umo-border-color-dark);
      padding: 10px;
    }
  }
}
</style>
