<template>
  <modal
    :visible="settingsShow"
    :header="t('headerTools.setUp.headerTitle')"
    width="400px"
    height="400px"
    :footer="false"
    @close="closeModel"
  >
    <div>
      <t-form
        ref="settingFormRef"
        :data="settingForm"
        :rules="rules"
        label-width="100px"
        @submit="onSubmit"
      >
        <t-form-item :label="t('headerTools.setUp.formLabel')" name="time">
          <t-input-number
            v-model="settingForm.time"
            :min="5"
            :max="30"
            :step="1"
            :suffix="t('headerTools.setUp.minute')"
            style="width: 200px"
          />
        </t-form-item>
        <div class="footerBtn">
          <t-button
            theme="default"
            style="margin-right: 10px"
            @click="closeModel"
            >{{ t('headerTools.setUp.cancel') }}</t-button
          >
          <t-button theme="primary" type="submit">{{
            t('headerTools.setUp.submit')
          }}</t-button>
        </div>
      </t-form>
    </div>
  </modal>
</template>
<script setup>
import { MessagePlugin } from 'tdesign-vue-next'
const { settingsShow, setOptions, options } = useStore()
const settingFormRef = ref(null)
const settingForm = reactive({
  time: 5,
})

onMounted(() => {
  settingForm.time = localStorage.getItem('autoSaveTime') ?? 5
})

const timeValidator = (value) => {
  if (value < 5) {
    return false
  }
  if (value > 30) {
    return false
  }
  const reg = /^[1-9]+\d*$/

  if (reg.test(Number(value))) {
    return true
  } else {
    // return MessagePlugin.error('自动保存时间必须为正整数')
    return false
  }
}
const rules = {
  time: [
    { required: true, message: '请输入自动保存时间', trigger: 'blur' },
    {
      validator: timeValidator,
      trigger: 'blur',
      message: '时间必须为正整数, 且在5分钟和30分钟之间',
    },
  ],
}

const onSubmit = ({ validateResult }) => {
  if (validateResult === true) {
    MessagePlugin.success('保存成功')
    localStorage.setItem('autoSaveTime', settingForm.time)
    setOptions({
      document: {
        autoSave: {
          interval: settingForm.time * 60 * 1000,
        },
      },
    })
    settingsShow.value = false
  }
}

const closeModel = () => {
  settingForm.time = localStorage.getItem('autoSaveTime') ?? 5
  settingsShow.value = false
}
</script>
<style scoped lang="less">
.footerBtn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
