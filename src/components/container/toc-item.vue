<template>
  <div class="tree-list">
    <div v-for="item in list" :key="item.id" class="tree-item">
      <div class="tree-header" :class="{
        active: item.isActive,
      }">
        <div class="tree-title" :style="{ paddingLeft: `${level * 15}px` }" @click="clickMenu($event, item)">
          <t-tooltip :content="returnText(item.textContent || item.name || item.text)" :show-arrow="false">
            <div class="tree-name">{{ returnText(item.textContent || item.name || item.text) }}</div>
          </t-tooltip>
        </div>
        <div v-if="item.children?.length" style="" @click="handleShowToggle(item.id)">
          <ChevronDownIcon v-if="getIsExpent(item.id)" style="margin-right: 5px; margin-left: 5px;" />
          <ChevronUpIcon v-else style="margin-right: 5px; margin-left: 5px;" />
        </div>
      </div>
      <toc-item v-show="getIsExpent(item.id) && item.children && item.children.length > 0"
        :list="getIsExpent(item.id) ? item.children : item.children" :level="level + 1" @on-click="clickMenu" />
    </div>
  </div>
</template>

<script setup lang="ts" name="ContainerTocItem">
import { ChevronDownIcon, ChevronUpIcon } from 'tdesign-icons-vue-next'

import tocItem from './toc-item.vue'

const emits = defineEmits(['onClick'])
defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  level: {
    type: Number,
    default: 1,
  }
})

const expendDataList = ref([])

const clickMenu = (e: any, item: any) => {
  debugger
  e.stopPropagation?.()
  emits('onClick', e, toRaw(item))
}

const getIsExpent = (id) => {
  const e = expendDataList.value.filter(it => it.id == id)
  if (e.length) {
    return e[0].isOpen
  }
  return true

}

// 切换展示展示子菜单
function handleShowToggle(id) {
  debugger
  const e = expendDataList.value.filter(it => it.id == id)
  if (e.length) {
    e[0].isOpen = !e[0].isOpen
  } else {
    expendDataList.value.push({ id, isOpen: false })
  }

}

// 选择目录时 前面有一个图片  图片不能展示在本前面 输出了null
const returnText = (value) => {
  // 使用正则表达式删除前面的 "null"（包括多个连续的 "null"）
  value = value?.replace(/^null+/, '')
  // 使用正则表达式删除后面的 "null"（包括多个连续的 "null"）
  value = value?.replace(/null+$/, '')
  // 去除前面的空格
  value = value.trim()
  return value
}

</script>
<style lang="less" scoped>
.active {
  background-color: rgba(9, 102, 180, .1);
  color: #0966B4;
}

.tree-list {
  .tree-item {
    .tree-header {
      height: 54px;
      display: flex;
      align-items: center;
      width: 100%;

      .tree-title {
        width: 100%;
        font-size: 14px;
        font-weight: bold;
        display: flex;
        align-items: center;
        overflow: hidden;

        .tree-name {
          width: 220px;
         
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      &:hover {
        background: #f6f6f6;
        cursor: pointer;
      }
    }


  }
}
</style>
