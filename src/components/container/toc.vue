<template>
  <t-tabs :default-value="1" style="width: 300px; height: 100%; margin-top: 160px">
    <t-tab-panel :value="1" :destroy-on-hide="false">
      <template #label>
        <div class="catalogue-label" style="margin-left: 0 !important">
          <span class="catalog-icon"></span><span style="width: 45px; display: inline-block">{{
            t('newToc.catalogue')
          }}</span>
        </div>
      </template>
      <div class="umo-toc-container">
        <div class="umo-toc-content umo-scrollbar" :style="`height:calc(100vh - ${leftMenuHeight}px)`">
          <container-toc-item :list="menuList" @on-click="(e, item) => headingClick(e, item)" />
        </div>
      </div>
    </t-tab-panel>
    <t-tab-panel :value="2" :destroy-on-hide="false">
      <template #label>
        <div class="knowledge_bg">
          {{ t('newToc.knowledgeGraph') }} <i class="knowledge_icon"></i>
        </div>
      </template>
      <p style="margin: 20px">正在努力的开发中~！</p>
    </t-tab-panel>
    <t-tab-panel :value="3" :label="t('newToc.search')" :destroy-on-hide="false">
      <container-search-txt @on-watch-toolbar="(fun) => (leftSearchHeight = fun)" />
    </t-tab-panel>
  </t-tabs>
</template>

<script setup lang="ts">
import { TextSelection } from '@tiptap/pm/state'

import type { TableOfContentItem } from '@/composables/store'
const { container, editor, tableOfContents, editorUpdate } = useStore()
const $toolbar = useState('toolbar')

const leftSearchHeight: any = $ref(null)

let leftMenuHeight = $ref(0)
let allMenusLs: string = $ref('')
let menuList: any[] = $ref([])
let expandeIdList: any = $ref([])

interface TocItem {
  level: number
  id: string
  isActive: boolean
  text: string
  children: TocItem[]
}

const updataMenu = async () => {
  const newHeadingListStr = JSON.stringify(
    tableOfContents.value.map(
      (ele) =>
        ele.textContent +
        ele.node?.attrs?.isNotAsCatalog +
        ele.node?.attrs?.level +
        ele?.isActive
    ),
  )
  if (newHeadingListStr !== allMenusLs) {
    allMenusLs = newHeadingListStr
    const idList: string[] = []
    const stack: TocItem[] = []
    const root: TocItem[] = []
    if (!tableOfContents.value || tableOfContents.value.length === 0) {
      expandeIdList = idList
      menuList = root
      return
    }
    for (const item of tableOfContents.value) {
      const { id, level } = item?.node?.attrs
      const text = item.textContent
      idList.push(id)

      while (stack.length > 0 && stack[stack.length - 1].level >= level) {
        stack.pop()
      }
      const headNode = { level, id, text, children: [], isActive: item?.isActive }
      if (stack.length === 0) {
        root.push(headNode)
      } else {
        stack[stack.length - 1].children.push(headNode)
      }
      stack.push(headNode)
    }
    expandeIdList = idList
    menuList = root
  }
}

onMounted(() => {
  editorUpdate('menus', updataMenu, true)
})

watch(
  $toolbar,
  () => {
    let leftMenu = 0
    if ($toolbar.value.show) {
      if ($toolbar.value.mode === 'ribbon') {
        leftMenu = 110
      } else if ($toolbar.value.mode === 'classic') {
        leftMenu = 45
      }
    } else {
      leftMenu = 0
    }
    leftSearchHeight?.()
    leftMenuHeight = leftMenu + 60 + 30 + 90
  },
  { deep: true, immediate: true },
)

const emit = defineEmits(['close'])

const headingClick = (e: any, heading: TableOfContentItem) => {
  if (e.target?.className !== 'menuItems-maskLayer') {
    if (expandeIdList.includes(heading?.id)) {
      expandeIdList = expandeIdList.filter((ele: string) => ele !== heading?.id)
    } else {
      expandeIdList.push(heading?.id)
    }
  }

  if (!editor.value) {
    return
  }
  const activeHeading = tableOfContents.value.find(
    (item: any) => 'isActive' in item && item.isActive,
  )
  if (activeHeading && 'isActive' in activeHeading) {
    activeHeading.isActive = false
  }
  if ('isActive' in heading) {
    heading.isActive = true
  }
  //把元素滚动到可视区
  const targetDiv = document.getElementById(heading?.id)
  if (targetDiv) {
    // 平滑滚动到目标位置
    targetDiv.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    })
    const pos = editor.value.view.posAtDOM(targetDiv as Node, 0)
    const { tr } = editor.value.view.state
    tr.setSelection(new TextSelection(tr.doc.resolve(pos)))
    editor.value.view.dispatch(tr)
    editor.value.view.focus()
  }
}
</script>

<style lang="less">
.umo-toc-content {
  ul {
    list-style-type: none;
  }
}

.umo-default-menu__inner .umo-menu {
  padding: 0;
}

.umo-editor-container ul li,
.umo-editor-container ol li {
  padding: 0;
  margin: 0;

  li {
    height: 54x;
  }
}

.umo-default-menu__inner .umo-menu .umo-menu-group>*:not(:first-child),
.umo-default-menu__inner .umo-menu .umo-menu__sub>*:not(:first-child),
.umo-default-menu__inner .umo-menu .umo-submenu>*:not(:first-child) {
  margin-top: 0;
}

.umo-menu__content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.umo-default-menu .umo-menu__item.umo-is-active:not(.umo-is-opened) {
  color: var(--td-brand-color);
}

.umo-default-menu .umo-menu__item.umo-is-active:not(.umo-is-opened) {
  color: var(--td-brand-color);
  height: 54px;
}

.umo-default-menu .umo-menu__item.umo-is-opened {
  color: var(--td-text-color-primary);
  height: 54px;
  font-weight: bold;
}

.umo-default-menu .umo-menu__item:hover:not(.umo-is-active):not(.umo-is-disabled) {
  background: var(--umo-menu-item-hover-background);
  height: 54px;
}

.umo-default-menu .umo-menu__item {
  height: 54px;
}

.umo-default-menu .umo-menu__item.umo-is-active:not(.umo-is-opened) {
  color: var(--td-brand-color);
  background-color: var(--umo-menu-item-selected-background);
}
</style>
<style lang="less" scoped>
.catalogue-label {
  display: flex;
  justify-content: center;
  align-items: center;

  .catalog-icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    background: url('@/assets/icons/catalogIcon.svg');
    background-size: 100% 100%;
    margin-right: 2px;
  }
}

.umo-is-active {
  .catalog-icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    background: url('@/assets/icons/catalogActiveIcon.svg');
    background-size: 100% 100%;
    margin-right: 5px;
  }

  .knowledge_bg {
    width: 66px;

    flex: 1;

    .knowledge_icon {
      display: inline-flex;
      background: url('@/assets/images/knowledge_bg.gif') no-repeat;
      width: 24px;
      height: 24px;
      align-items: center;
      justify-content: center;
      position: relative;
      top: 8px;
      background-size: 100% 100%;
    }
  }
}

.umo-toc-container {
  background-color: var(umo-toc-title);
  border-right: solid 1px var(--umo-border-color);
  width: 300px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;

  .umo-toc-title {
    border-bottom: solid 1px var(--umo-border-color-light);
    display: flex;
    align-items: center;
    position: relative;
    padding: 20px;
    cursor: pointer;
    font-size: 14px;
    color: var(--umo-primary-color);

    .icon-toc {
      margin-right: 5px;
      font-size: 20px;
    }
  }

  .umo-toc-content {
    list-style: none;
    overflow-y: auto;
    overflow-x: hidden;

    .umo-toc-empty {
      font-size: 14px;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--umo-text-color-light);
    }

    .umo-toc-item {
      border-radius: 3px;
      padding: 5px 7px 5px 37px;
      box-sizing: border-box;
      align-items: center;
      position: relative;
      margin: 2px 0;

      &::before {
        position: absolute;
        content: attr(data-heading);
        color: var(--umo-text-color-light);
        margin-right: 8px;
        border-radius: 2px;
        border: solid 1px var(--umo-border-color);
        font-size: 8px;
        padding: 5px 4px;
        left: 7px;
        top: 50%;
        transform: translateY(-50%);
      }

      &:hover {
        cursor: pointer;
        background: var(--umo-content-node-selected-background);
        color: var(--umo-primary-color);

        &::before {
          color: var(--umo-primary-color);
          border-color: var(--umo-primary-color);
        }
      }

      &.active {
        background: var(--umo-button-hover-background);
        color: var(--umo-primary-color);

        &::before {
          color: var(--umo-primary-color);
          border-color: var(--umo-primary-color);
        }
      }

      &.level-1 {
        margin-left: 0;
        width: 100%;
      }

      &.level-2 {
        margin-left: 15px;
        width: calc(100% - 15px);
      }

      &.level-3 {
        margin-left: 30px;
        width: calc(100% - 30px);
      }

      &.level-4 {
        margin-left: 45px;
        width: calc(100% - 45px);
      }

      &.level-5 {
        margin-left: 60px;
        width: calc(100% - 60px);
      }

      &.level-6 {
        margin-left: 75px;
        width: calc(100% - 75px);
      }

      .umo-toc-text {
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }
}

::v-deep(.umo-tabs__bar) {
  position: absolute;
  background-color: var(--td-brand-color);
  z-index: 1;
  transition: all 0.24s cubic-bezier(0.38, 0, 0.24, 1);
  border-radius: var(--td-radius-small);

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 30px;
    height: 3px;
    background: #00b286;
  }
}
</style>
