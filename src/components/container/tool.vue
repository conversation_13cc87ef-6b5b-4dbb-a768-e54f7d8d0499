<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-13 08:38:31
 * @LastEditTime: 2024-11-27 10:37:13
 * @FilePath: \dutp-editor\src\components\container\tool.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <div class="tool">
    <div class="tool-main" :style="{ width: isShow ? '300px' : 0, display: isShow ? 'block' : 'none' }">
      <t-tabs :default-value="1" style="width: 300px; height: 100%">
        <t-tab-panel :value="1" :label="t('containerTool.commonComponents')">
          <container-commonUse />
        </t-tab-panel>
        <t-tab-panel :value="2" :label="t('containerTool.textbookTemplate')">
          <container-iconTemplate />
        </t-tab-panel>
        <t-tab-panel :value="3">
          <!-- :label="t('containerTool.aiComponents')" -->
          <template #label>
            {{ t('containerTool.aiComponents') }}<span class="ai"></span>
          </template>

          <ContainerAiComponent />
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
  <div class="umo-dialog-bg" :style="{ right: isShow ? '315px' : '15px' }">
    <div class="umo-dialog-right" @click="isShow = !isShow">
      <span v-if="isShow" class="umo-icon">
        <ChevronRightDoubleSIcon size="30px" />
      </span>

      <span v-else class="umo-icon">
        <ChevronLeftDoubleSIcon size="30px" />
      </span>
    </div>
  </div>
</template>
<script setup>
import {
  ChevronLeftDoubleSIcon,
  ChevronRightDoubleSIcon,
} from 'tdesign-icons-vue-next'

const isShow = $ref(true)
</script>
<style lang="less" scoped>
.tool {
  height: 100%;
  display: flex;
  position: absolute;
  right: 15px;
  overflow-y: auto;

  .tool-main {
    width: 0;
    height: 100%;
    top: 160px;
    background-color: var(--umo-container-background);
    transition: 0.3s;
    z-index: 1;
    position: relative;
    overflow-y: auto;
    box-shadow: -10px 0 15px -3px rgba(88, 87, 87, 0.1);

    .ai {
      background: url('@/assets/icons/AItabs.svg') no-repeat;
      width: 15px;
      height: 15px;
      background-size: 100% 100%;
      display: inline-block;
      margin-left: 5px;
    }

    /* 自定义滚动条轨道 */
    ::-webkit-scrollbar-track {
      background-color: #f1f1f1;
    }

    /* 自定义滚动条的滑块 */
    ::-webkit-scrollbar-thumb {
      background-color: #888;
    }

    /* 设置滚动条的宽度 */
    ::-webkit-scrollbar {
      width: 0px;
    }

    ::v-deep(.umo-tabs__nav-scroll) {
      position: fixed;
      width: 300px;
      z-index: 999;
      background-color: var(--umo-container-background);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    ::v-deep(.umo-tabs__content) {
      margin-top: 50px;
    }

    .umo-tabs {
      overflow-y: auto;
    }
  }
}

.umo-dialog-bg {
  width: 25px;
  height: 50px;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 300px;
}

.umo-dialog-right {
  border-radius: 50%;
  display: flex;
  align-items: center;
  width: 50px;
  height: 50px;
  overflow: hidden;
  background-color: var(--umo-container-background);
  border: 1px solid var(--umo-border-color-dark);
  cursor: pointer;
  color: var(--umo-text-color);
  transition: 0.3s;
}
</style>

<style lang="less">
.tool-main {
  .umo-tab-panel {
    height: 100%;
  }

  .umo-tabs__content {
    height: 100% !important;
    overflow-y: auto !important;
    scrollbar-width: none;
    /* 针对 Firefox */
    -ms-overflow-style: none;
    /* 针对 Internet Explorer 和 Edge */
  }

  .umo-collapse-panel__header {
    color: var(--umo-text-color);
  }
}
</style>
