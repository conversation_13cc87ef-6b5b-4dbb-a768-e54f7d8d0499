<template>
  <t-dialog
    :visible="innerVisible"
    attach="body"
    :header="t('insert.image.cropImg')"
    mode="full-screen"
    :close-on-overlay-click="false"
    @confirm="onCropper"
    @close="onClose"
    @cancel="onClose"
  >
    <div class="cropper-container">
      <vueCropper
        ref="cropper"
        :img="option.img"
        :output-size="option.outputSize"
        :output-type="option.outputType"
        :info="option.info"
        :can-scale="option.canScale"
        :auto-crop="option.autoCrop"
        :fixed-box="option.fixedBox"
        :fixed="option.fixed"
        :fixed-number="option.fixedNumber"
        :can-move="option.canMove"
        :can-move-box="option.canMoveBox"
        :original="option.original"
        :center-box="option.centerBox"
        :info-true="option.infoTrue"
        :full="option.full"
        :enlarge="option.enlarge"
        :mode="option.mode"
      >
      </vueCropper>
    </div>
  </t-dialog>
</template>

<script setup name="CropperDialog">
import 'vue-cropper/dist/index.css'
import { base64ToFile } from 'file64'
import { VueCropper } from 'vue-cropper'
const props = defineProps({
  cropperVisible: {
    type: Boolean,
    default: 'false',
  },
  url: {
    type: String,
    default: '',
  },
  fileName: {
    type: String,
    default: '1.png',
  },
})

const emit = defineEmits(['update:cropperVisible', 'saveCropper'])
const { options } = useStore()
const innerVisible = ref(false)
const cropper = ref(null)
const option = ref({
  outputSize: 1, // 裁剪生成图片的质量
  outputType: 'png', // 裁剪生成图片的格式 jpeg, png, webp
  info: true, // 裁剪框的大小信息
  canScale: true, // 图片是否允许滚轮缩放
  autoCrop: true, // 是否默认生成截图框
  // autoCropWidth: 750, // 默认生成截图框宽度
  // autoCropHeight: 300, // 默认生成截图框高度
  fixedBox: false, // 固定截图框大小 不允许改变
  fixed: false, // 是否开启截图框宽高固定比例，这个如果设置为true，截图框会是固定比例缩放的，如果设置为false，则截图框的狂宽高比例就不固定了
  fixedNumber: [1, 1], // 截图框的宽高比例 [ 宽度 , 高度 ]
  canMove: true, // 上传图片是否可以移动
  canMoveBox: true, // 截图框能否拖动
  original: false, // 上传图片按照原始比例渲染
  centerBox: true, // 截图框是否被限制在图片里面
  infoTrue: false, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
  full: false, // 是否输出原图比例的截图
  enlarge: '1', // 图片根据截图框输出比例倍数
  mode: 'contain', // 图片默认渲染方式 contain , cover, 100px, 100% auto
})

watch(
  () => props.cropperVisible,
  (newVal) => {
    option.value.img = newVal ? props.url : ''
    innerVisible.value = newVal
  },
)

// 截取保存事件
const onCropper = () => {
  const cropperRef = cropper.value
  if (cropperRef) {
    cropperRef.getCropData(async (base64) => {
      const fileName = generateFileName(props.fileName)
      const file = await base64ToFile(base64, fileName)
      if (!file) {
        useMessage('error', '保存截图失败')
        return
      }
      try {
        const { url } = (await options.value?.onFileUpload?.(file)) ?? {}
        if (!url) {
          useMessage('error', '保存截图失败')
          return
        }

        emit('saveCropper', url)
        emit('update:cropperVisible', false)
      } catch (error) {
        useMessage('error', error?.message)
      }
    })
  }
}

// 生成文件名的函数
function generateFileName(baseName) {
  // 提取文件名的基本部分（去掉扩展名）
  const nameParts = baseName.split('.')
  // 去掉最后一个部分（扩展名）
  const baseFileName = nameParts.slice(0, -1).join('.')

  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substr(2, 5)

  const extension = 'png'

  return `${baseFileName}-${timestamp}-${randomString}.${extension}`
}

// 关闭弹窗
const onClose = () => {
  emit('update:cropperVisible', false)
}
</script>

<style lang="less" scoped>
.cropper-container {
  width: 100%;
  height: 100%;
  margin-top: 50px;
}
</style>
