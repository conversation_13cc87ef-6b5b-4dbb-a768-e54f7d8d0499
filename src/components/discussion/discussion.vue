<template>
  <div>
    <div class="discussion-container">
      <!-- 头部部分 -->
      <div class="conponent-name">
        {{ title }}
      </div>
      <div class="interaction-main-title">
        <div>
          <span class="interaction-main-title-content" v-html="themeContent"></span>
        </div>
      </div>
      <!-- 内容部分 -->
      <div class="discussion-content show-discussion">

        <!-- tips 提交按钮等 -->
        <div class="discussion-content-header">
          <div class="content-title">
            {{ t('insert.interaction.discussion.tips') }}
            <div v-if="showFlag" class="viewResults" @click="viewResults">
              {{ t('insert.interaction.discussion.viewResults') }}
            </div>
          </div>
          <div class="discussion-btn-group">
            <t-button v-if="showFlag" variant="outline" theme="primary" @click="onSubmit">
              <!--          {{ t('bubbleMenu.image.upload') }}-->
              {{ t('insert.interaction.imageWaterfall.imageSubmit') }}
            </t-button>
            <div v-if="!showFlag" class="goOnSubmit" @click="onContinueSubmit">
              {{ t('insert.interaction.continueSubmitting') }}
            </div>
          </div>
        </div>

        <!-- 文本框 回显框等 -->
        <div v-if="showFlag" class="discussion-text">
          <t-textarea v-model="discussionText" class="discussion-text-textarea"
                      :placeholder="t('insert.interaction.discussion.discussionPlaceholder')" />
        </div>

        <div v-if="!showFlag" v-for="(item,index) in discussionList" class="discussion-item">
          <div class="disscussion-item-profilePictuer"
               :style="{ backgroundImage: `url(${item.profilePicture})` }"></div>
          <div class="discussion-item-right">
            <div class="discussion-item-right-name">
              {{ item.name }}
            </div>
            <div class="discussion-item-right-content">
              {{ item.discussionText }}
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import profilePicture from '@/assets/images/profilePicture.png'
import { Edit2Icon } from 'tdesign-icons-vue-next'

const { options, editor, imageViewer } = useStore()
const props = defineProps({
  title: String,
  themeContent: String
})

// const 显隐标识
const showFlag = ref(true)

// 讨论内容
const discussionText = ref('')

// todo 用户信息 后边改成真实的
const userInfo = {
  profilePicture: profilePicture,
  name: '张三'
}

// 讨论数据集合
const discussionList = ref([])

// 查看结果
const viewResults = () => {
  showFlag.value = false
}
// 讨论内容提交
const onSubmit = () => {
  if (discussionText.value.trim() == ''){
    useMessage('error', t('insert.interaction.discussion.discussionPlaceholder'))
    return
  }
  const discussDetail = {
    profilePicture: userInfo.profilePicture,
    name: userInfo.name,
    discussionText: discussionText.value
  }
  discussionList.value.push(discussDetail)
  showFlag.value = false
}
// 继续提交
const onContinueSubmit = () => {
  discussionText.value = ''
  showFlag.value = true
}
</script>

<style lang="less" scoped>
.discussion-container {
  width: 98%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;

  .conponent-name {
    width: 100%;
    font-size: 18px;
    font-weight: bold;
    margin-top: 30px;
  }

  .interaction-main-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 20px 0;
    font-size: 20px;
    color: #666;

    .interaction-main-title-content {
      width: 450px;
      margin: 0 auto;
      word-wrap: break-word;
      word-break: break-all;
      text-align: center;
    }
  }

  .discussion-content {
    width: 98%;
    background-color: #f2f2f2;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 5px;
    max-height: 200px;
    overflow-y: auto;

    .discussion-content-header {
      width: 100%;
      height: 40px;
      display: flex;
      justify-content: space-between;

      .content-title {
        font-size: 16px;
      }

      .viewResults {
        display: inline-block;
        cursor: pointer;
        font-size: 12px;
      }
    }

    .discussion-text {
      width: 98%;
      margin-top: 10px;
    }

    .discussion-item {
      width: 90%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
      margin-top: 10px;
      .disscussion-item-profilePictuer {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-repeat: no-repeat;
        background-size: contain;
        margin-top: 5px;
      }

      .discussion-item-right {
        width: calc(100% - 50px);
        height: 100%;
        display: flex;
        flex-direction: column;

        .discussion-item-right-content {
          background-color: #dc4c7e;
          border-radius: 5px;
          color: #ffffff;
          padding: 0 5px 0 5px;
        }
      }

      .goOnSubmit {
        font-size: 14px;
        color: #666;
        cursor: pointer;
        margin-left: 20px;
      }
    }
  }
}
</style>