<template>
    <div :id="echartId" :style="chartStyle" ></div>
</template>

<script setup>
import * as echarts from 'echarts'
import { ref, onBeforeMount, onMounted, defineProps } from 'vue'

const props = defineProps({
  chartStyle: {
    type: Object,
    default: () => ({
      width: '600px',
      height: '400px'
    })
  },
  chartOption: {
    type: Object,
    default: () => ({}),
    required: true
  }
})

const { proxy } = getCurrentInstance()
const drawChart = () => {
  const echart = echarts.init(document.getElementById(echartId.value))
  echart.setOption(props.chartOption)
  window.addEventListener('resize', () => {
    echart.resize({
      animation: { duration: 300 }
    })
  })
}
const echartId = ref(null)

onBeforeMount(() => {
  echartId.value = `echarts-id-${parseInt(Math.random() * 1000000)}`
})

onMounted(() => {
  drawChart()
})

watch(
  props.chartOption,
 
  () => {
    drawChart()
  }
)

</script>