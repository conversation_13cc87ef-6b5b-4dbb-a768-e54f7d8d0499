<template>
  <editor-content
    class="umo-editor-container"
    :class="{
      'show-line-number': page.showLineNumber,
      'format-painter': painter.enabled,
      'disable-page-break': !page.pagination,
    }"
    :editor="editor"
    :style="{
      lineHeight: defaultLineHeight,
    }"
    :spellcheck="
      options.document?.enableSpellcheck && $document.enableSpellcheck
    "
  />
  <menus-bubble v-show="editor" />
  <columnBubble v-show="editor"/>
  <menus-context-block v-show="editor" />
</template>

<script setup lang="ts">
import Typography from '@tiptap/extension-typography'
import { Editor, EditorContent, type Extension } from '@tiptap/vue-3'
import { useRoute } from 'vue-router'
import columnBubble from '@/components/menus/bubble/columnBubble.vue'
import { getChapterContentInfo } from '@/api/chapterContent'
import { extensions } from '@/extensions'
import Image from '@/extensions/image'
import Page from '@/extensions/page'
import { pagePlugin } from '@/extensions/page/page-plugin'
const {
  options,
  editor,
  page,
  painter,
  setEditor,
  editorD<PERSON>royed,
  setChapterId,
  setBookId,
  chapterId,
  setOptions,
  editorUpdate,
  getCaptionStyle
} = useStore()
const route = useRoute()
const $document = useState('document')
let enableRules: boolean | Extension[] = true
if (
  !options.value.document?.enableMarkdown ||
  !$document.value?.enableMarkdown
) {
  enableRules = [Typography, Image as Extension]
}

const defaultLineHeight = $computed(
  () =>
    options.value.dicts?.lineHeights?.find((item: any) => item.default)?.value,
)

let isReady = $ref<boolean>(false)

const editorInstance: Editor = new Editor({
  editable: !options.value.document?.readOnly,
  autofocus: options.value.document?.autofocus,
  content: options.value.document?.content,
  enableInputRules: enableRules,
  enablePasteRules: enableRules, 
  editorProps: {
    attributes: {
      class: 'umo-editor',
    },
    ...options.value.document?.editorProps,
  },
  parseOptions: options.value.document?.parseOptions,
  extensions: [
    Page.configure({
      types: options.value.page.nodesComputedOption?.types ?? [],
      slots: useSlots(),
    }),
    ...(extensions as any),
    ...(options.value.extensions as Extension[]),
  ],
  onCreate({ editor }) {
    const { chapterId } = route.query
    const { bookId } = route.query
    if (bookId) {
      setBookId(bookId);
    }
    if (chapterId) {
      setChapterId(chapterId)
      getChapterContent()
    }
    
  },
  onTransaction({ editor }) {
    // console.log('tr', editor.state.tr)
  },
  onUpdate({ editor }) {
    editorUpdate()
    isReady = true
    $document.value.content = editor.getHTML()
  },
})
setEditor(editorInstance)

// 注册分页组件
const registerPagePlugin = async () => {
  await nextTick()
  const { nodesComputed } = options.value.page.nodesComputedOption ?? {}

  editorInstance.registerPlugin(pagePlugin(editorInstance, nodesComputed ?? {}))
  setTimeout(() => {
    // 编辑器中暂时没有合并页的需求
    // const tr = editorInstance.state.tr.setMeta('initSplit', true)
    // editorInstance.view.dispatch(tr)
  }, 500)
}
watch(
  () => isReady,
  () => {
    if (isReady) {
      void registerPagePlugin()
    }
  },
  { once: true },
)

// 获取当前章节内容
async function getChapterContent() {
  if (chapterId.value) {
    await getCaptionStyle()
    getChapterContentInfo(chapterId.value).then((response) => {
      const { data } = response
      if (data) {
        if (editorInstance) {
          let dataJson = JSON.parse(data.content || '{}') || []
          dataJson = fixJson(dataJson)
          // console.log('content~~~~~~~~~~~~~~~~~~~~~~~~~~~~~', dataJson)
          try {
            editorInstance.commands.setContent(dataJson, true)
          } catch (e) {
            console.error(e)
            // 出现报错尝试修复json
            dataJson = fixJson(dataJson)
            // console.log('fixJson~~~~~~~~~~~~~~~~~~~~~~~~~~~~~', dataJson)
            editorInstance.commands.setContent(dataJson, true)
          }
        } else {
          setOptions({
            ...options.value,
            document: {
              ...options.value.document,
              content: JSON.parse(data.content),
            },
          })
        }
      }
    })
  }
}
const fixJson = (dataJson) => {
  for (const pageItem of dataJson?.content || []) {
    let stack = []
    stack.push(pageItem)
    while (stack.length > 0) {
      const item = stack.pop()
      const type = item.type

      if ('paragraph' === type) {
        continue
      }

      if ('imageLayout' === type) {
        const content = item?.content
        const attrs = item?.attrs
        if (!content || content.length === 0) {
          item.content = [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: attrs.imageTitle || '图片标题',
                },
              ],
            },
          ]
        }
        continue
      }
      for (const node of item?.content || []) {
        stack.push(node)
      }
    }
  }
  return dataJson
}
// 销毁编辑器实例
onBeforeUnmount(() => {
  editorInstance.destroy()
})
</script>

<style lang="less">
@import '@/assets/styles/editor.less';
@import '@/assets/styles/drager.less';
</style>
