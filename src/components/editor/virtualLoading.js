/**用作编辑器大数据卡顿的优化*/
class VirtualLoading {
  constructor() {
    this.editor = {}//editor实例
    this.rawData = []//原始数据
    this.currentData = []
    this.Main = null
    this.count = 1//当前第几部分的计数
    this.nextStatus = true
    this.lastStatus = true
  }

  init(editor) {
    this.editor = editor
    this.rawData = editor.getJSON()?.content
    // console.log(this.rawData[0]);
    this.render(this.rawData.filter((ele, i) => i < 10))
    // editor.commands.setContent('')
    this.Main = document.querySelector('.umo-editor-container .umo-main-bg')
    this.Main.addEventListener('scroll', (e) => this.scroll(e))
  }

  render(content) {
    this.editor.commands.setContent({ type: 'doc', content })
  }

  scroll(e) {
    const scrollHeight = e.target.scrollHeight;
    const clientHeight = e.target.clientHeight;
    const distanceToBottom = scrollHeight - clientHeight - e.target.scrollTop;
    if (distanceToBottom < 800) {
      if (this.nextStatus) {
        this.nextStatus = false
        this.next()
        setTimeout(() => this.nextStatus = true, 1000)
      }
    }
    
    if (e.target.scrollTop < 800) {
      if (this.lastStatus) {
        this.lastStatus = false
        this.next()
        setTimeout(() => this.lastStatus = true, 1000)
      }
    }
  }

  next() {
    console.log('下一部分');

  }

  last() {
    console.log('上一部分');


  }


}


const Load = new VirtualLoading()
export default Load