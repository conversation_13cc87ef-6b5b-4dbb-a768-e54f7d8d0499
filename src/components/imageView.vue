<template>
  <div class="tdesign-demo-image-viewer__base">
    <t-image-viewer :images="[img]">
      <template #trigger="{ open }">
        <div class="tdesign-demo-image-viewer__ui-image">
          <img
            :alt="alt"
            :src="img"
            class="tdesign-demo-image-viewer__ui-image--img"
          />
          <div class="tdesign-demo-image-viewer__ui-image--hover" @click="open">
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <div><BrowseIcon /></div>
              <div>预览</div>
            </div>
          </div>
        </div>
      </template>
    </t-image-viewer>
  </div>
</template>

<script setup>
import { BrowseIcon } from 'tdesign-icons-vue-next'

const props = defineProps({
  img: {
    type: String,
    default: '',
  },
  alt: {
    type: String,
    default: '',
  },
})
</script>

<style scoped>
.tdesign-demo-image-viewer__ui-image {
  min-width: 78px;
  min-height: 78px;
  max-width: 300px;
  max-height: 300px;
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: var(--td-radius-small);
  overflow: hidden;
}

.tdesign-demo-image-viewer__ui-image--hover {
  min-width: 78px;
  min-height: 78px;
  max-width: 300px;
  max-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--td-text-color-anti);
  line-height: 22px;
  transition: 0.2s;
}

.tdesign-demo-image-viewer__ui-image:hover
  .tdesign-demo-image-viewer__ui-image--hover {
  opacity: 1;
  cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--img {
  width: auto;
  height: auto;
  max-width: 300px;
  max-height: 300px;
  cursor: pointer;
  object-fit: cover;
}

.tdesign-demo-image-viewer__ui-image--footer {
  padding: 0 16px;
  height: 56px;
  width: 100%;
  line-height: 56px;
  font-size: 16px;
  position: absolute;
  bottom: 0;
  color: var(--td-text-color-anti);
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  display: flex;
  box-sizing: border-box;
}

.tdesign-demo-image-viewer__ui-image--title {
  flex: 1;
}

.tdesign-demo-popup__reference {
  margin-left: 16px;
}

.tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
  cursor: pointer;
}

.tdesign-demo-image-viewer__base {
  min-width: 78px;
  min-height: 78px;
  border-radius: var(--td-radius-medium);
}
</style>
