<template>
  <div class="waterFall-contain">
    <div class="conponent-name">
      {{ title }}
    </div>
    <div class="interaction-main-title">
      <div>
        <span
          class="interaction-main-title-content"
          v-html="themeContent"
        ></span>
      </div>
    </div>

    <div class="waterFall-header">
      <div>
        <t-button
          v-if="imagesList.length > 0 && uploadFile.length == 0 && !uploadShow"
          variant="outline"
          @click="onUpload"
        >
          {{ t('insert.interaction.imageWaterfall.continueUpload') }}
        </t-button>
        <t-button
          v-if="uploadFile.length > 0 && !uploadShow"
          variant="outline"
          theme="primary"
          @click="onSubmit"
        >
          <!--          {{ t('bubbleMenu.image.upload') }}-->
          {{ t('insert.interaction.imageWaterfall.imageSubmit') }}
        </t-button>
      </div>
    </div>
    <div class="images-content">
      <!-- 上传组件 在页面中隐藏 -->
      <t-upload
        v-show="false"
        ref="uploadRef"
        v-model="uploadFile"
        :before-upload="handleBeforeUpload"
        :show-image-file-name="false"
        theme="image"
      >
      </t-upload>
      <!-- 初始时div 点击调起上传组件 -->
      <div
        v-if="uploadFile.length == 0 && uploadShow"
        class="no-image"
        @click="clickUpload"
      >
        <span>{{
          t('insert.interaction.imageWaterfall.uploadImageTips')
        }}</span>
      </div>
      <!-- 图片上传后的组件 -->
      <div
        v-if="uploadFile.length > 0 && !uploadShow"
        class="show-image"
        @mouseenter="showButtons = true"
        @mouseleave="showButtons = false"
      >
        <img :src="uploadFile[0].url" />
        <div class="img-btn-group" v-if="showButtons">
          <t-button variant="text" @click="viewerImage">
            <template #icon>
              <EllipsisIcon style="color: #0966b4" />
            </template>
            <span style="color: #0966b4" class="tdesign-demo-dropdown__text">
              {{ t('insert.image.preview') }}
            </span>
          </t-button>
          <t-button theme="danger" variant="text" @click="removeImg">
            <template #icon>
              <CloseIcon style="color: var(--umo-error-color)" />
            </template>
            {{ t('insert.image.cancel') }}
          </t-button>
        </div>
      </div>
      <div v-if="uploadFile.length > 0" class="image-text">
        <t-textarea
          v-model="imageText"
          class="image-text-textarea"
          :placeholder="t('insert.interaction.imageWaterfall.imageText')"
        />
      </div>
      <!-- 图片瀑布流组件 -->
      <masonryLayout
        v-if="imagesList.length > 0 && uploadFile.length == 0 && !uploadShow"
        :images="imagesList"
        :columns="columns"
      />
    </div>
    <!-- 预览组件 -->
    <t-image-viewer
      v-if="uploadFile.length > 0"
      v-model:visible="viewerVisible"
      :images="[uploadFile[0].url]"
    >
    </t-image-viewer>
  </div>
</template>
<script setup lang="ts">
import masonryLayout from '@/components/menus/toolbar/insert/components/imageGallery/masonryLayout.vue'
import { reactive, watch } from 'vue'
import { getSelectionNode } from '@/extensions/selection'
import { DeleteIcon, FileImageIcon, CloseIcon } from 'tdesign-icons-vue-next'

const { options, editor, imageViewer } = useStore()
const props = defineProps({
  title: String,
  themeContent: String,
  images: {
    type: Array,
    default: () => [],
  },
})
// 内容
const imageText = ref('')

// 上传组件
const uploadRef = ref()

const uploadShow = ref(true)
// 点击调起upload
const clickUpload = () => {
  uploadRef.value.triggerUpload()
}

// 继续上传
const onUpload = () => {
  uploadShow.value = true
  imageText.value = ''
}

// 上传组件文件
const uploadFile = ref([])

// 上传前钩子 上传到 oss
const handleBeforeUpload = async (file) => {
  uploadFile.value = []
  try {
    const res = (await options.value?.onFileUpload?.(file.raw)) ?? {}
    console.log(res)
    uploadFile.value.push(res)
    uploadShow.value = false
  } catch (error) {
    useMessage('error', (error as Error).message)
  }
  return false
}

// 图片瀑布流显示几列
const columns = ref(3)

const imagesList = ref(props.images)

// 图片操作按钮显示标识
const showButtons = ref(false)

// 提交
const onSubmit = () => {
  // imageslist.value.push({
  //   src:"https://gimg2.baidu.com/image_search/src=http%3a%2f%2fci.xiaohongshu.com%2f9bb6a2a0-d9c3-7100-f9f2-0a3667a23798%3fimageview2%2f2%2fw%2f1080%2fformat%2fjpg&refer=http%3a%2f%2fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1737020623&t=25a045115225065c701ef36b19f99cac",
  //   name:"图1"
  // })
  if (uploadFile.value.length == 0) {
    useMessage('error', '请上传图片')
    return
  }
  const imgFile = uploadFile.value[0]
  imgFile.src = imgFile.url
  imgFile.name = imageText.value
  imagesList.value.push(imgFile)
  uploadShow.value = false
  uploadFile.value = []
  // // 处理后清空 uploadFile
  // uploadFile.value = [];
}

// 预览图片
const viewerVisible = ref(false)
const viewerImage = () => {
  viewerVisible.value = true
}

// 取消
const removeImg = () => {
  if (imagesList.value.length > 0) {
    uploadFile.value = []
    uploadShow.value = false
  } else {
    uploadFile.value = []
    uploadShow.value = true
  }
}
</script>
<style lang="less" scoped>
.waterFall-contain {
  width: 98%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;

  .conponent-name {
    width: 100%;
    font-size: 18px;
    font-weight: bold;
    margin-top: 30px;
  }

  .waterFall-header {
    width: 100%;
    text-align: right;
  }

  .images-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
    margin-top: 10px;
    border-radius: 5px;
    max-height: 370px;
    overflow-y: auto;
  }

  .interaction-main-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 20px 0;
    font-size: 20px;
    color: #666;

    .interaction-main-title-content {
      width: 450px;
      margin: 0 auto;
      word-wrap: break-word;
      word-break: break-all;
      text-align: center;
    }
  }
  .no-image {
    width: 98%;
    height: 100px;
    border-radius: 5px;
    border: 1px dashed #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ccc;
  }
  .show-image {
    width: 98%;
    border-radius: 5px;
    border: 1px dashed #ccc;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    color: #ccc;
    position: relative;
    img {
      max-width: 100%;
      max-height: 250px;
      object-fit: contain;
      border-radius: 5px;
    }
    .img-btn-group {
      position: absolute;
      bottom: 10px;
      width: 100%;
      height: 30px;
      display: flex;
      justify-content: flex-end;
    }
  }
  .image-text {
    margin-top: 10px;
    width: 98%;
  }
}
</style>
