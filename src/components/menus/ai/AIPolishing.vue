<!-- AI润色 -->
<template>
  <t-tooltip :content="t('ai.AIPolishing')" theme="light" placement="top" :show-arrow="false" destroy-on-close>
    <div class="menusButtonBg" @click="openModal">
      <!-- <menus-button
        ico="bold"
        hide-text
        style="width: 60px; height: 60px; background-color: #0066ff"
      /> -->
      <img src="@/assets/icons/AIPolishing.svg" alt="" />
      <div class="fount-bold-text">{{ t('ai.AIPolishing') }}</div>
    </div>
  </t-tooltip>

  <modal :visible="visible" :show-header="true" :is-table="true" width="760px" :footer="false"
    dialog-class-name="t-class-dialog-first" @close="visible = false">
    <template #templateHeader>
      <div style="text-align: center">
        <i :class="isDark === 'light' ? 'headerIcon' : 'headerIcon_drak'"></i>{{ t('ai.AIPolishing') }}
      </div>
    </template>
    <div class="topInputBg">
      <t-textarea v-model="AIPolishingValue" :placeholder="t('ai.matchCaseInfo.inputSearch')"
        :autosize="{ minRows: 3, maxRows: 5 }" :maxlength="10000" style="border: none; outline: none"></t-textarea>
      <div class="matchCaseSeacherBtn" @click="generate">
        <div class="btn">
          <i class="icon"></i>{{ t('ai.matchCaseInfo.generate') }}
        </div>
      </div>
    </div>

    <div class="topInputTitle">{{ t('ai.polishing.polishingResults') }}</div>
    <div class="topInputBg">
      <div v-if="generateing">
        <t-loading content="正在生成"></t-loading>
      </div>
      <div v-else-if="!resultVisible" class="topInputNoData">
        <div class="topInputNoDataImg"></div>
        <div class="topInputNoDataText">{{ t('ai.polishing.noData') }}</div>
      </div>

      <div v-else>
        {{ AIPolishingValueAfter }}
      </div>
    </div>
    <div v-if="resultVisible" class="txtRight">
      {{
        t('ai.polishing.polishingTxtNumber', {
          number: AIPolishingValueAfter.length,
        })
      }}
    </div>

    <div class="buttonGroup">
      <t-button theme="default" style="margin-right: 18px" @click="visible = false">{{ t('ai.polishing.cancel')
      }}</t-button>
      <t-button theme="primary" @click="copyResult">{{
        t('ai.polishing.copy')
      }}</t-button>
    </div>
  </modal>
</template>
<script setup>
import { ref } from 'vue'

import { chatAiByPrompt } from '@/api/ai'
import modal from '@/components/modal.vue'
import { AiPromtType } from '@/enum/ai'
import { getSelectionText } from '@/extensions/selection'
const { editor, options } = useStore()
const visible = ref(false)
const openModal = () => {
  const test = getSelectionText(editor?.value) || ''
  AIPolishingValue.value = test
  visible.value = true
}

const AIPolishingValue = ref('')
const AIPolishingValueAfter = ref('我是润色后的内容')
const resultVisible = ref(false)
const generateing = ref(false)
const generate = () => {
  generateing.value = true
  chatAiByPrompt({
    question: AIPolishingValue.value,
    ability: AiPromtType.Polishing,
  }).then((res) => {
    AIPolishingValueAfter.value = JSON.parse(res.body.content).result
    resultVisible.value = true
    generateing.value = false
  })
}

// 判断 黑夜模式与白天模式
const isDark = $computed(() => {
  return options.value.theme
})
const copyResult = () => {
  // console.log(AIPolishingValueAfter.value)

  const { copy } = useClipboard({
    source: ref(AIPolishingValueAfter.value),
  })
  void copy()
  useMessage('success', t('assistant.copySuccess'))
  // navigator.clipboard
  //   .writeText(AIPolishingValueAfter.value)
  //   .then(() => {

  //   })
  //   .catch((err) => {
  //     console.log('Async: Could not copy text: ', err)
  //     useMessage('error', t('assistant.copyFail'))
  //   })
}
</script>
<style lang="less" scoped>
.menusButtonBg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  .fount-bold-text {
    margin: 16px 0 8px;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 60px;
  }
}

.t-class-dialog-first {
  padding: 20px 30px;
  background: linear-gradient(180deg, #e5f3ff 0%, #ffffff 100%);
  border-radius: 12px;

  .headerIcon {
    display: inline-block;
    width: 22px;
    height: 22px;
    background: url('../../../assets/images/polishing.svg') no-repeat;
    background-size: cover;
    margin-right: 5px;
  }

  .headerIcon_drak {
    display: inline-block;
    width: 22px;
    height: 22px;
    background: url('../../../assets/images/polishing_drak.svg') no-repeat;
    background-size: cover;
    margin-right: 5px;
  }

  .topInputTitle {
    margin: 20px 0 12px;
    font-weight: bold;
    font-size: 16px;
  }

  .txtRight {
    text-align: right;
    margin: 10px 0 20px;
    color: #999;
  }

  .buttonGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }

  .topInputBg {
    background: var(--td-bg-color-container);
    border-radius: 8px;
    border: 1px solid var(--td-brand-color);
    padding: 20px;
    position: relative;

    &:focus {
      border-color: var(--td-brand-color);
    }

    &:hover {
      border: 1px solid var(--td-brand-color);
      //  box-shadow: 0 0 0 2px var(--td-brand-color-focus);
    }

    textarea {
      border: none;
    }

    .topInputNoData {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .topInputNoDataImg {
        background: url('../../../assets/images/noData.svg') no-repeat;
        width: 41px;
        height: 28px;
        background-size: contain;
      }

      .topInputNoDataText {
        margin-top: 10px;
        font-size: 14px;
        color: var(--td-text-color-primary);
        text-align: center;
      }
    }

    .umo-textarea__info_wrapper_align {
      justify-content: end;
      margin-right: 120px;
    }

    .matchCaseSeacherBtn {
      position: absolute;
      right: 20px;
      bottom: 15px;

      .btn {
        min-width: 80px;
        padding: 0 10px;
        height: 32px;
        background: linear-gradient(67deg, #4ba3de 10%, #98e3ca 100%);
        border-radius: 36px 36px 36px 36px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .icon {
          display: inline-block;
          width: 15px;
          height: 15px;
          background-image: url(../../../assets/images/phont.svg);
          background-size: cover;
          background-repeat: no-repeat;
          margin-right: 5px;
        }
      }
    }
  }
}
</style>
