<!-- AI 试题 -->
<!-- 生成学习目标 -->
<template>
  <t-tooltip :content="t('ai.AITestQuestions')" theme="light" placement="top" :show-arrow="false" destroy-on-close>
    <div class="menusButtonBg">
      <!-- <menus-button ico="bold" hide-text style="width: 60px;height: 60px; background-color: #0066FF;"
                @menu-click="showModal" /> -->
      <img src="@/assets/icons/AITestQuestions.svg" alt="" />
      <div class="fount-bold-text">{{ t('ai.AITestQuestions') }}</div>
    </div>
  </t-tooltip>
  <Modal :visible="visible" :header="t('ai.AITestQuestions')" @close="visible = false" @confirm="visible = false">
    <div>功能尚未开发尽情期待~</div>
  </Modal>
  <!-- <Modal
    :visible="visible"
    mode="full-screen"
    :header="t('ai.AITestQuestions')"
    :footer="false"
    dialog-class-name="t-class-dialog-questions"
    @close="visible = false"
  >
    <template #templateHeader>
      <div style="text-align: center">{{ t('ai.AITestQuestions') }}</div>
    </template>

<div v-if="historyList.length == 0" class="matchCaseMain" style="
        flex-direction: column;
        padding-top: 188px;
        text-align: center;
        overflow: hidden;
      ">
  <div class="matchCaseTitle">
    {{ t('ai.AITestQuestionsInfo.title') }}
  </div>
  <div class="matchCaseAdTitle">{{ t('ai.AITestQuestionsInfo.ad') }}</div>

  <div class="matchCaseMainCenter" :style="{ width: pageZoomWidth }">
    <div class="matchCaseSeacherInput">
      <t-textarea v-model="matchCaseSeacherValue" :placeholder="t('ai.matchCaseInfo.inputSearch')"
        :autosize="{ minRows: 3, maxRows: 5 }" :maxlength="10000"></t-textarea>
      <div class="matchCaseSeacherBtn">
        <div class="btn" @click="generateClick">
          <i class="icon"></i>{{ t('ai.matchCaseInfo.generate') }}
        </div>
      </div>
    </div>
    <div class="bg-bottom"></div>
  </div>
</div>
<div v-else class="matchCaseMain">
  <div class="matchCaseMainLeft">
    <div class="matchCaseMainLeftTop">
      {{ t('ai.matchCaseInfo.historicalRecords') }}
    </div>
    <div class="matchCaseMainleftInput">
      <div class="searchInput">
        <t-input :placeholder="t('ai.matchCaseInfo.inputPlaceholder')" clearable>
          <template #suffixIcon>
                <search-icon :style="{ cursor: 'pointer' }" />
              </template>
        </t-input>
      </div>

      <div class="matchCaseMainHistoryList">
        <div v-for="item in historyList" :key="item.id" class="matchCaseMainHistoryListItem"
          :class="{ active: item.active }">
          <div class="historyTitle">{{ item.title }}</div>
          <div class="historyDate">
            <div>{{ item.date }}</div>
            <div class="del" @click="delClick(item.id)">
              <DeleteIcon />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="matchCaseMainCenter" :style="{ width: pageZoomWidth }">
    <div class="matchCaseSeacherInput">
      <t-textarea v-model="matchCaseSeacherValue" :placeholder="t('ai.matchCaseInfo.inputSearch')"
        :autosize="{ minRows: 5, maxRows: 100 }" :maxlength="10000"></t-textarea>
      <div class="questionSettingRules">
        <div class="questionSettingRules-title">
          <div class="title">
            {{ t('ai.AITestQuestionsInfo.questionSettingRules') }}
          </div>
          <div class="addBtn" @click="addQuestions">
            <AddCircleIcon style="margin-right: 5px" />{{
            t('ai.AITestQuestionsInfo.addQuestionType')
            }}
          </div>
        </div>
        <div class="questionSettingRules-content">
          <div v-for="item in questionList" :key="item.id" class="questionSettingRules-content-item">
            <div style="display: flex">
              题型
              <t-select v-model="item.type" style="width: 120px; margin-left: 5px">
                <t-option v-for="items in questionType" :key="items.value" :label="items.label" :value="items.value" />
              </t-select>
            </div>
            <div style="margin-left: 5px">
              数量
              <t-input-number v-model="item.number" />
            </div>
            <div style="margin-left: 5px; cursor: pointer">
              <CloseCircleIcon @click="deleteQuestion(item.id)" />
            </div>
          </div>
        </div>
      </div>
      <div class="matchCaseSeacherBtn">
        <div class="btn">
          <i class="icon"></i>{{ t('ai.matchCaseInfo.generate') }}
        </div>
      </div>
    </div>

    <div class="matchCaseSeacherResult" :style="{ width: pageZoomWidth }">
      <div class="matchCaseSeacherResultTitle">
        <i class="lingIcon"></i> {{ t('ai.matchCaseInfo.generateResults') }}
      </div>
      <div class="matchCaseSeacherResultBtnGroup">
        <t-button theme="default" variant="outline" style="margin-right: 10px; width: 80px">{{
          t('ai.matchCaseInfo.export') }}</t-button>
        <t-button theme="primary" style="width: 80px">{{
          t('ai.matchCaseInfo.copy')
          }}</t-button>
      </div>
    </div>
    <div class="matchCaseSeacherResultContent" :style="{ width: pageZoomWidth }">
      <div class="contentMain">
        <p>
          大学英语教科书的练习题结构通常是根据教学目标、学生需求以及课程内容来设计的。这些练习题旨在帮助学生巩固课堂上学到的语言知识，提升语言技能，包括听、说、读、写和译等方面。以下是对大学英语教科书练习题结构的一个概括性分析：
        </p>
        <p>
          一、练习题的主要类型 词汇练习：
          这类练习通常包括单词拼写、词义辨析、词汇搭配、构词法等，旨在帮助学生扩大词汇量，掌握词汇的正确用法。
        </p>
        <p>
          例如，练习中可能会要求学生根据上下文猜测词义，或者选择正确的词汇填空。
        </p>
        <p>
          语法练习：
          这类练习涉及时态、语态、从句、非谓语动词等语法点的运用，旨在帮助学生掌握正确的语法结构。
          练习形式可能包括句子改写、语法填空、错误识别与纠正等。
          阅读理解练习：
        </p>
        <p>
          这类练习通常包括阅读短文后回答问题、判断正误、填空、选择等，旨在提升学生的阅读理解能力。
          阅读材料可能涉及日常生活、科技、文化、历史等多个领域。
          听力理解练习：
        </p>
        <p>
          这类练习包括听对话或短文后回答问题、选择答案、填空等，旨在训练学生的听力理解能力。
          听力材料可能包括日常对话、讲座、新闻报道等。 口语练习：
        </p>
        <p>
          这类练习通常包括角色扮演、讨论、演讲等，旨在提升学生的口语表达能力和交际能力。
          口语练习可能涉及日常对话、话题讨论、辩论等多种形式。 写作练习：
        </p>
        <p>
          这类练习可能包括写日记、作文、电子邮件、报告等，旨在训练学生的写作能力。
          写作主题可能涉及校园生活、社会现象、个人经历等。 翻译练习：
        </p>
        <p>
          这类练习可能包括英译汉、汉译英等，旨在提升学生的翻译能力和跨文化交际能力。
        </p>
        <p>翻译材料可能包括句子、段落或短文。</p>
      </div>
    </div>
  </div>
</div>
</Modal> -->
</template>
<script setup>
import { ref } from 'vue'

import Modal from '@/components/modal.vue'
const { container, page, imageViewer } = useStore()
const visible = ref(false)
const historyList = ref([])
const questionList = ref([])
const addQuestions = () => {
  questionList.value.push({
    id: questionList.value.length + 1,
    number: 0,
    type: 1,
  })
}

const matchCaseSeacherValue = ref('')
const showModal = () => {
  visible.value = true
}

const deleteQuestion = (id) => {
  questionList.value = questionList.value.filter((item) => item.id !== id)
}

const generateClick = () => {
  if (!matchCaseSeacherValue.value) return useMessage('error', '请输入标题')
  historyList.value.push({
    id: historyList.value.length + 1,
    title: matchCaseSeacherValue.value,
    date: '2023-11-11',
    active: true,
  })
  matchCaseSeacherValue.value = ''
}

const pageSize = $computed(() => {
  const { width, height } = page.value.size ?? { width: 0, height: 0 }
  return {
    width: page.value.orientation === 'portrait' ? width : height,
    height: page.value.orientation === 'portrait' ? height : width,
  }
})
// 页面缩放后的大小
const pageZoomWidth = $computed(() => {
  return `calc(${pageSize.width}cm * ${page.value.zoomLevel ? page.value.zoomLevel / 100 : 1})`
})
</script>
<style lang="less" scoped>
.menusButtonBg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .fount-bold-text {
    margin: 16px 0 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 60px;
    text-align: center;
  }
}

.t-class-dialog-questions {

  .umo-dialog__fullscreen,
  .t-dialog__fullscreen {
    border-radius: 0;
    display: flex;
    flex-direction: column;
    box-shadow: none;
    height: 100vh;
    background: #eaeaea;
    overflow: hidden;
  }

  .umo-dialog__fullscreen {
    width: 100%;
    height: 100vh;
    background: #f6f7f9;
    border-radius: 0;
    overflow: hidden;
  }

  .matchCaseMain {
    background-color: #f6f7f9;
    width: 100%;
    position: fixed;
    overflow: hidden;
    height: 100%;
    text-align: center;

    .matchCaseTitle {
      font-size: 46px;
      font-weight: bold;
      text-transform: none;
      background: -webkit-linear-gradient(270deg, #4ba3de 10%, #90dccd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      height: 80px;
      line-height: 80px;
      margin-top: 80px;
      text-align: center;
    }

    .matchCaseAdTitle {
      font-size: 36px;
      font-weight: bold;
      color: #333;
      line-height: 50px;
      text-align: center;
    }

    .matchCaseMainLeft {
      position: absolute;
      left: 0;
      top: 0px;
      width: 320px;
      height: 100vh;

      background-color: #fff;

      .matchCaseMainLeftTop {
        height: 54px;
        line-height: 54px;
        padding-left: 18px;
        border-bottom: 1px solid #eaeaea;
      }

      .matchCaseMainleftInput {
        padding: 20px;

        .searchInput {
          :deep(.umo-input) {
            border-radius: 114px;
            height: 38px;
            padding-left: 14px;
          }
        }
      }

      .matchCaseMainHistoryList {
        overflow-y: auto;
        height: calc(100vh - 200px);

        &::-webkit-scrollbar {
          width: 2px;
          /* 设置滚动条的宽度 */
        }

        .matchCaseMainHistoryListItem {
          border: 1px solid #eaeaea;
          border-radius: 8px;
          padding: 10px;
          margin: 10px 0;
          cursor: pointer;

          &:hover {
            background-color: #eaeaea;
          }

          .historyTitle {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .historyDate {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #999;
            margin-top: 10px;

            .del {
              cursor: pointer;
            }
          }
        }

        .active {
          border: 1px solid #1890ff;
        }
      }
    }

    .matchCaseMainCenter {
      min-height: 100%;
      height: auto;
      max-width: 1000px;
      width: 1000px;
      min-height: 1000px;
      margin: 0 auto;
      line-height: 28px;
      border-radius: 14px;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      margin: 60px auto;
      padding: 20px 40px;

      .matchCaseSeacherInput {
        background-color: #fff;
        border-radius: 12px;
        padding: 20px;
        position: relative;
        min-height: 180px;
        border: 1px solid #eaeaea;

        .questionSettingRules {
          .questionSettingRules-title {
            display: flex;
            align-items: center;

            .title {
              font-weight: bold;
              font-size: 14px;
              color: #333;
            }

            .addBtn {
              margin-left: 16px;
              color: #0966b4;
              cursor: pointer;
            }
          }

          .questionSettingRules-content {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 20px;
            padding-bottom: 30px;

            .questionSettingRules-content-item {
              display: flex;
              align-items: center;
              background-color: #f4f4f4;
              border-radius: 6px;
              padding: 5px;
            }
          }
        }

        &:hover {
          border: 1px solid var(--td-brand-color);
          box-shadow: 0 0 0 2px var(--td-brand-color-focus);
        }

        .umo-textarea .umo-textarea__inner:focus {
          border-color: none !important;
          box-shadow: none !important;
        }

        .umo-textarea__inner:focus {
          border: none !important;
        }

        .umo-textarea .umo-textarea__inner:hover {
          border-color: none !important;
        }

        textarea {
          border: none;
        }

        .umo-textarea__info_wrapper_align {
          justify-content: end;
        }

        .matchCaseSeacherBtn {
          position: absolute;
          right: 10px;
          bottom: 10px;

          .btn {
            min-width: 80px;
            padding: 0 10px;
            height: 32px;
            background: linear-gradient(67deg, #4ba3de 10%, #98e3ca 100%);
            border-radius: 36px 36px 36px 36px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .icon {
              display: inline-block;
              width: 15px;
              height: 15px;
              background-image: url(../../../assets/images/phont.svg);
              background-size: cover;
              background-repeat: no-repeat;
              margin-right: 5px;
            }
          }
        }

        .umo-textarea__inner {
          display: flex;
          width: 100%;
          height: var(--td-comp-size-xxxl);
          min-height: var(--td-comp-size-xxxl);
          /* border: 1px solid var(--td-border-level-2-color); */
          /* border-radius: var(--td-radius-default); */
          padding: calc(calc(var(--td-comp-size-m) - var(--td-line-height-body-medium)) / 2) var(--td-comp-paddingLR-s);
          /* background-color: var(--td-bg-color-specialcomponent); */
          font: var(--td-font-body-medium);
          color: var(--td-text-color-primary);
          /* resize: vertical; */
          outline: none;

          /* transition: all cubic-bezier(0.38, 0, 0.24, 1) 0.2s, height 0s; */
          /* box-sizing: border-box; */
        }
      }

      .bg-bottom {
        width: 300px;
        height: 158px;
        margin: 68px auto;
        background: url('@/assets/images/bg-bottom.png') no-repeat bottom;
        background-size: 100% 100%;
      }

      .matchCaseSeacherResult {
        margin: 30px 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .matchCaseSeacherResultTitle {
          font-size: 20px;
          font-weight: bold;
          color: #000;
          display: flex;
          justify-content: center;
          align-items: center;

          .lingIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            background: url('../../../assets/images/result.svg') no-repeat;
            background-size: cover;
            margin-right: 5px;
          }
        }
      }

      .matchCaseSeacherResultContent {
        min-height: 100%;
        height: auto;
        max-width: 1000px;
        width: 1000px;
        min-height: 500px;
        background-color: #fff;
        margin: 0 auto;
        line-height: 28px;
        border-radius: 14px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        border: 1px solid var(--td-brand-color);

        .contentMain {
          padding: 20px;
          height: 450px;
          overflow-y: auto;
          line-height: 24px;

          &::-webkit-scrollbar {
            width: 5px;
            /* 设置滚动条的宽度 */
          }
        }
      }
    }
  }
}
</style>
