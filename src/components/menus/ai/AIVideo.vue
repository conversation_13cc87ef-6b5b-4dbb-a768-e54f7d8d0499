<!-- AI 视频 -->
<template>
  <t-tooltip :content="t('ai.AIVideo')" theme="light" placement="top" :show-arrow="false" destroy-on-close>
    <div class="menusButtonBg">
      <!-- <menus-button ico="bold" hide-text style="width: 60px;height: 60px; background-color: #0066FF;"
                @menu-click="showModal" /> -->
      <img src="@/assets/icons/AIVideo.svg" alt="" />
      <div class="fount-bold-text">{{ t('ai.AIVideo') }}</div>
    </div>
  </t-tooltip>
  <Modal :visible="visible" mode="full-screen" :header="t('ai.AIVideo')" :footer="false"
    dialog-class-name="t-class-dialog-video" @close="visible = false">
    <template #templateHeader>
      <div style="text-align: center">{{ t('ai.AIVideo') }}</div>
    </template>

    <div v-if="historyList.length == 0" class="matchCaseMain"
      style="padding-top: 108px; text-align: center; overflow: hidden">
      <div class="matchCaseTitle">
        {{ t('ai.videoInfo.videoTitle') }}
      </div>
      <div class="matchCaseAdTitle">
        {{ t('ai.videoInfo.videoTitleAd') }}
      </div>

      <div class="matchCaseMainCenter" :style="{ width: pageZoomWidth }">
        <div class="matchCaseSeacherInput">
          <t-textarea v-model="matchCaseSeacherValue" :placeholder="t('ai.matchCaseInfo.inputSearch')"
            :autosize="{ minRows: 3, maxRows: 5 }" :maxlength="10000"></t-textarea>
          <div class="videoTxt">视频比例</div>
          <div class="videoRatio">
            <div class="videoRatioItem" :class="{ active: videoRatio == 1 }" @click="videoRatio = 1">
              <span class="icon169" :class="{ active: videoRatio == 1 }"></span>16:9
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 2 }" @click="videoRatio = 2">
              <span class="icon916" :class="{ active: videoRatio == 2 }"></span>9:16
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 3 }" @click="videoRatio = 3">
              <span class="icon11" :class="{ active: videoRatio == 3 }"></span>1:1
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 4 }" @click="videoRatio = 4">
              <span class="icon53" :class="{ active: videoRatio == 4 }"></span>5:3
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 5 }" @click="videoRatio = 5">
              <span class="icon43" :class="{ active: videoRatio == 5 }"></span>4:3
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 6 }" @click="videoRatio = 6">
              <span class="icon34" :class="{ active: videoRatio == 6 }"></span>3:4
            </div>
          </div>
          <div class="matchCaseSeacherBtn">
            <div class="btn" @click="generateClick">
              <i class="icon"></i>{{ t('ai.matchCaseInfo.generate') }}
            </div>
          </div>
        </div>
        <div class="bg-bottom"></div>
      </div>
    </div>
    <div v-else class="matchCaseMain">
      <div class="matchCaseMainLeft">
        <div class="matchCaseMainLeftTop">
          {{ t('ai.matchCaseInfo.historicalRecords') }}
        </div>
        <div class="matchCaseMainleftInput">
          <div class="searchInput">
            <t-input :placeholder="t('ai.matchCaseInfo.inputPlaceholder')" clearable>
              <template #suffixIcon>
                <search-icon :style="{ cursor: 'pointer' }" />
              </template>
            </t-input>
          </div>

          <div class="matchCaseMainHistoryList">
            <div v-for="item in historyList" :key="item.id" class="matchCaseMainHistoryListItem"
              :class="{ active: item.active }">
              <div class="historyTitle">{{ item.title }}</div>
              <div class="historyDate">
                <div>{{ item.date }}</div>
                <div class="del" @click="delClick(item.id)">
                  <DeleteIcon />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="matchCaseMainCenter" :style="{ width: pageZoomWidth }">
        <div class="matchCaseSeacherInput">
          <t-textarea v-model="matchCaseSeacherValue" :placeholder="t('ai.matchCaseInfo.inputSearch')"
            :autosize="{ minRows: 4, maxRows: 5 }" :maxlength="10000"></t-textarea>
          <div class="videoTxt">视频比例</div>
          <div class="videoRatio">
            <div class="videoRatioItem" :class="{ active: videoRatio == 1 }" @click="videoRatio = 1">
              <span class="icon169" :class="{ active: videoRatio == 1 }"></span>16:9
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 2 }" @click="videoRatio = 2">
              <span class="icon916" :class="{ active: videoRatio == 2 }"></span>9:16
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 3 }" @click="videoRatio = 3">
              <span class="icon11" :class="{ active: videoRatio == 3 }"></span>1:1
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 4 }" @click="videoRatio = 4">
              <span class="icon53" :class="{ active: videoRatio == 4 }"></span>5:3
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 5 }" @click="videoRatio = 5">
              <span class="icon43" :class="{ active: videoRatio == 5 }"></span>4:3
            </div>
            <div class="videoRatioItem" :class="{ active: videoRatio == 6 }" @click="videoRatio = 6">
              <span class="icon34" :class="{ active: videoRatio == 6 }"></span>3:4
            </div>
          </div>
          <div class="matchCaseSeacherBtn">
            <div class="btn">
              <i class="icon"></i>{{ t('ai.matchCaseInfo.generate') }}
            </div>
          </div>
        </div>

        <div class="matchCaseSeacherResult" :style="{ width: pageZoomWidth }">
          <div class="matchCaseSeacherResultTitle">
            <i class="lingIcon"></i> {{ t('ai.matchCaseInfo.generateResults') }}
          </div>
          <div class="matchCaseSeacherResultBtnGroup">
            <t-button theme="default" variant="outline" style="margin-right: 10px; width: 80px">{{
              t('ai.matchCaseInfo.export') }}</t-button>
            <t-button theme="primary" style="width: 80px">{{
              t('ai.videoInfo.used')
            }}</t-button>
          </div>
        </div>
        <video src="https://tdesign.gtimg.com/tdesign-website/videos/tdesign-video.mp4" controls></video>
      </div>
    </div>
  </Modal>
</template>
<script setup>
import { DeleteIcon, SearchIcon } from 'tdesign-icons-vue-next'
import { ref } from 'vue'

import Modal from '@/components/modal.vue'
const { container, page, imageViewer } = useStore()
const visible = ref(false)
const historyList = ref([])
const videoRatio = ref(1)
const generateClick = () => {
  if (!matchCaseSeacherValue.value) return useMessage('error', '请输入搜索内容')
  historyList.value.push({
    id: historyList.value.length + 1,
    title: matchCaseSeacherValue.value,
    date: '2023-11-11',
    proportion: videoRatio.value,
  })
}
// const historyList = ref([
//     {
//         id: 1,
//         title: '标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1标题1',
//         date: '2023-11-11',
//         active: true
//     },
//     {
//         id: 2,
//         title: '标题2',
//         date: '2023-11-11'
//     },
//     {
//         id: 3,
//         title: '标题3',
//         date: '2023-11-11'
//     }
// ])

const matchCaseSeacherValue = ref('')
const showModal = () => {
  visible.value = true
}

const pageSize = $computed(() => {
  const { width, height } = page.value.size ?? { width: 0, height: 0 }
  return {
    width: page.value.orientation === 'portrait' ? width : height,
    height: page.value.orientation === 'portrait' ? height : width,
  }
})
// 页面缩放后的大小
const pageZoomWidth = $computed(() => {
  return `calc(${pageSize.width}cm * ${page.value.zoomLevel ? page.value.zoomLevel / 100 : 1})`
})
</script>
<style lang="less" scoped>
.menusButtonBg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  cursor: not-allowed;

  .fount-bold-text {
    margin: 16px 0 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    width: 60px;
  }
}

.t-class-dialog-video {
  .umo-dialog__body--fullscreen--without-footer {
    height: 100vh;
  }

  .matchCaseMain {
    background-color: #f6f7f9;
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;

    .matchCaseTitle {
      font-size: 46px;
      font-weight: bold;
      text-transform: none;
      background: -webkit-linear-gradient(270deg, #4ba3de 10%, #90dccd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      height: 80px;
      line-height: 80px;
    }

    .matchCaseAdTitle {
      font-size: 36px;
      font-weight: bold;
      color: #333;
      line-height: 50px;
    }

    .matchCaseMainLeft {
      position: absolute;
      left: 0;
      top: 0px;
      width: 320px;
      min-height: calc(100vh);
      background-color: #fff;

      .matchCaseMainLeftTop {
        height: 54px;
        line-height: 54px;
        padding-left: 18px;
        border-bottom: 1px solid #eaeaea;
      }

      .matchCaseMainleftInput {
        padding: 20px;

        .searchInput {
          :deep(.umo-input) {
            border-radius: 114px;
            height: 38px;
            padding-left: 14px;
          }
        }
      }

      .matchCaseMainHistoryList {
        .matchCaseMainHistoryListItem {
          border: 1px solid #eaeaea;
          border-radius: 8px;
          padding: 10px;
          margin: 10px 0;
          cursor: pointer;

          &:hover {
            background-color: #eaeaea;
          }

          .historyTitle {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .historyDate {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #999;
            margin-top: 10px;

            .del {
              cursor: pointer;
            }
          }
        }

        .active {
          border: 1px solid #1890ff;
        }
      }
    }

    .matchCaseMainCenter {
      min-height: 100%;
      height: auto;
      max-width: 1000px;
      width: 1000px;
      min-height: 1000px;
      margin: 0 auto;
      line-height: 28px;
      border-radius: 14px;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      margin: 60px auto;
      padding: 20px 40px;

      .matchCaseSeacherInput {
        background-color: #fff;
        border-radius: 12px;
        padding: 20px;
        position: relative;
        height: 250px;

        .umo-textarea__inner {
          display: flex;
          width: 100%;
          height: var(--td-comp-size-xxxl);
          min-height: var(--td-comp-size-xxxl);
          /* border: 1px solid var(--td-border-level-2-color); */
          /* border-radius: var(--td-radius-default); */
          padding: calc(calc(var(--td-comp-size-m) - var(--td-line-height-body-medium)) / 2) var(--td-comp-paddingLR-s);
          /* background-color: var(--td-bg-color-specialcomponent); */
          font: var(--td-font-body-medium);
          color: var(--td-text-color-primary);
          /* resize: vertical; */
          outline: none;

          /* transition: all cubic-bezier(0.38, 0, 0.24, 1) 0.2s, height 0s; */
          /* box-sizing: border-box; */
        }

        .umo-textarea .umo-textarea__inner:focus {
          border-color: none !important;
          box-shadow: none !important;
        }

        .umo-textarea .umo-textarea__inner:hover {
          border-color: none !important;
        }

        textarea {
          border: none;
        }

        .videoTxt {
          text-align: left;
          color: #333;
          margin-bottom: 15px;
        }

        .videoRatio {
          display: grid;
          grid-template-columns: repeat(6, 1fr);
          gap: 10px;

          .videoRatioItem {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            background-color: #f3f3f3;
            height: 58px;
            cursor: pointer;

            .icon169 {
              border: 3px solid;
              width: 16px;
              height: 9px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon916 {
              border: 3px solid;
              width: 9px;
              height: 16px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon11 {
              border: 3px solid;
              width: 10px;
              height: 10px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon53 {
              border: 3px solid;
              width: 12px;
              height: 9px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon43 {
              border: 3px solid;
              width: 10px;
              height: 6px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon34 {
              border: 3px solid;
              width: 6px;
              height: 10px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .active {
              border-color: #0966b4;
            }
          }

          .active {
            background-color: #e3f0ff;
            border: 1px solid #0966b4;
            color: #0966b4;
          }
        }

        &:focus {
          border-color: var(--td-brand-color);
          box-shadow: 0 0 0 2px var(--td-brand-color-focus);
        }

        &:hover {
          border: 1px solid var(--td-brand-color);
          box-shadow: 0 0 0 2px var(--td-brand-color-focus);
        }

        .umo-textarea__info_wrapper_align {
          justify-content: end;
        }

        .matchCaseSeacherBtn {
          position: absolute;
          right: 20px;
          bottom: 10px;

          .btn {
            min-width: 80px;
            padding: 0 10px;
            height: 32px;
            background: linear-gradient(67deg, #4ba3de 10%, #98e3ca 100%);
            border-radius: 36px 36px 36px 36px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .icon {
              display: inline-block;
              width: 15px;
              height: 15px;
              background-image: url(../../../assets/images/phont.svg);
              background-size: cover;
              background-repeat: no-repeat;
              margin-right: 5px;
            }
          }
        }
      }

      .bg-bottom {
        width: 300px;
        height: 158px;
        margin: 68px auto;
        background: url('@/assets/images/bg-bottom.png') no-repeat bottom;
        background-size: 100% 100%;
      }

      .matchCaseSeacherResult {
        margin: 30px 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .matchCaseSeacherResultTitle {
          font-size: 20px;
          font-weight: bold;
          color: #000;
          display: flex;
          justify-content: center;
          align-items: center;

          .lingIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            background: url('../../../assets/images/result.svg') no-repeat;
            background-size: cover;
            margin-right: 5px;
          }
        }
      }

      .matchCaseSeacherResultContent {
        min-height: 100%;
        height: auto;
        max-width: 1000px;
        width: 1000px;
        min-height: 500px;
        background-color: #fff;
        margin: 0 auto;
        line-height: 28px;
        border-radius: 14px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        border: 1px solid var(--td-brand-color);
      }

      .umo-dialog__body--fullscreen--without-footer {
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      }
    }
  }
}
</style>
