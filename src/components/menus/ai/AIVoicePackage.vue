<!-- AI语音包 -->
<template>
  <t-tooltip :content="t('ai.AIVoicePackage.title')" theme="light" placement="top" :show-arrow="false" destroy-on-close>
    <div class="menusButtonBg" @click="openModal">
      <!-- <menus-button ico="bold" hide-text style="width: 60px;height: 60px; background-color: #0066FF;" /> -->
      <img src="@/assets/icons/AIVoicePackage.svg" alt="" />
      <div class="fount-bold-text">{{ t('ai.AIVoicePackage.title') }}</div>
    </div>
  </t-tooltip>
  <Modal :visible="visible" mode="full-screen" :header="t('ai.AIVoicePackage.title')" :footer="false"
    dialog-class-name="t-class-dialog-video" @close="visible = false">
    <template #templateHeader>
      <div style="text-align: center">{{ t('ai.AIVoicePackage.title') }}</div>
    </template>

    <div v-if="historyList.length == 0" class="matchCaseMain"
      style="padding-top: 108px; text-align: center; overflow: hidden">
      <div class="matchCaseTitle">
        {{ t('ai.AIVoicePackage.title') }}
      </div>
      <div class="matchCaseAdTitle">
        {{ t('ai.AIVoicePackage.ad') }}
      </div>

      <div class="matchCaseMainCenter">
        <div class="matchCaseSeacherInput">
          <div class="audioBg">
            <div class="audioItem" @click="playAudio">
              <div class="playIcon"></div>
              <div class="">{{ t('ai.AIVoicePackage.play') }}</div>
            </div>

            <div class="audioItem">
              <div class="musicIcon"></div>
              <div class="musicText">
                <t-select v-model="VoiceName" :placeholder="t('ai.AIVoicePackage.select')" borderless :popup-props="{
                  overlayClassName: 'tdesign-demo-select__overlay-option',
                }" style="width: 150px" size="large">
                  <t-option v-for="item in options" :key="item.value" :value="item.vcn" :label="item.name">
                    <template #content>
                      <div class="tdesign-demo__user-option">
                        <img :src="item.profilePicture" />
                        <div class="tdesign-demo__user-option-info">
                          <div class="name">{{ item.name }}</div>
                          <div class="tdesign-demo__user-option-desc">
                            {{ item.description }}
                          </div>
                        </div>
                      </div>
                    </template>
                  </t-option>
                </t-select>
              </div>
            </div>
            <t-popconfirm theme="default" :cancel-btn="null" :confirm-btn="null">
              <template #content>
                <p class="title">{{ t('ai.AIVoicePackage.percentageTip') }}</p>
                <p class="describe" style="margin-top: 20px">
                  <t-slider v-model="percentageNum" :default-value="50" />
                </p>
              </template>
              <div class="audioItem">
                <div class="speedIcon"></div>
                <div class="">{{ t('ai.AIVoicePackage.speed') }}</div>
              </div>
            </t-popconfirm>
          </div>

          <t-textarea v-model="matchCaseSeacherValue" :placeholder="t('ai.AIVoicePackage.tip')"
            :autosize="{ minRows: 5, maxRows: 10 }" :maxlength="10000"
            style="border: none; margin-top: 10px"></t-textarea>

          <div class="footerBtn" style="display: flex; justify-content: flex-end; margin-top: 20px">
            <div class="footerItemBtn">
              <div class="btn" @click="generateClick">
                <i class="icon"></i>{{ t('ai.AIVoicePackage.generate') }}
              </div>
            </div>

            <div class="footerItemBtn">
              <div class="btn" @click="insertHtml">
                <i class="icon"></i>{{ t('ai.AIVoicePackage.insert') }}
              </div>
            </div>
          </div>
        </div>
        <div v-if="audioSrc" class="audioCss">
          <audio ref="audioRef" :src="audioSrc" controls="controls" class="audio"></audio>
        </div>
        <div class="bg-bottom"></div>
      </div>
    </div>
  </Modal>
</template>
<script setup>
import { MessagePlugin } from 'tdesign-vue-next'

import { chatAiByPrompt, getTtsList } from '@/api/ai'
import Modal from '@/components/modal.vue'
import { AiPromtType } from '@/enum/ai'
const { editor } = useStore()
const visible = ref(false)
const VoiceName = ref('xiaoyan')
const audioSrc = ref('')
const matchCaseSeacherValue = ref('')
const historyList = ref([])
const percentageNum = ref(50)
const audioRef = ref(null)
const openModal = () => {
  visible.value = true
}

const options = ref([])

const playAudio = () => {
  if (!audioSrc.value) return MessagePlugin.error(t('ai.AIVoicePackage.err'))
  if (audioRef.value.paused) {
    audioRef.value.play()
  } else {
    audioRef.value.pause()
  }
  // autoPlay.value = true
}

onMounted(() => {
  getReadRole()
})
const generateClick = () => {
  if (!matchCaseSeacherValue.value)
    return MessagePlugin.error(t('ai.AIVoicePackage.err'))
  chatAiByPrompt({
    vcn: VoiceName.value,
    speed: percentageNum.value,
    ability: AiPromtType.TextToSpeech,
    question: matchCaseSeacherValue.value,
  }).then(async (res) => {
    audioSrc.value = res
  })
}

const getReadRole = () => {
  getTtsList({ type: 1 }).then((res) => {
    options.value = res.data
  })
}

const insertHtml = () => {
  if (!audioSrc.value) return MessagePlugin.error(t('ai.AIVoicePackage.err'))

  editor.value
    ?.chain()
    .focus()
    .setAudio({
      src: audioSrc.value,
      audioTitle: audioSrc.value,
      name: 'AI语音生成文件',
    })
    .run()
  visible.value = false
}
</script>
<style lang="less" scoped>
.audioCss {
  padding: 20px 0;
}

.footerBtn {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  bottom: 10px;
  right: 20px;

  .footerItemBtn {
    margin-left: 20px;

    .btn {
      min-width: 80px;
      padding: 0 10px;
      height: 32px;
      background: linear-gradient(67deg, #4ba3de 10%, #98e3ca 100%);
      border-radius: 36px 36px 36px 36px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
}

.matchCaseSeacherInput {
  .audioBg {
    width: 100%;
    background-color: var(--umo-color-white);
    border-radius: 8px;
    display: flex;
    align-items: center;
    min-height: 58px;
    border: 1px solid #eaeaea;

    .audioItem {
      display: flex;
      padding-right: 20px;
      font-size: 14px;
      cursor: pointer;
      color: var();

      &:first-child {
        padding-left: 40px;
      }

      .playIcon {
        width: 26px;
        height: 26px;
        background: url('@/assets/images/trialListening.png') no-repeat;
        background-size: 100% 100%;
        margin-right: 10px;
      }

      .musicIcon {
        width: 26px;
        height: 26px;
        background: url('@/assets/images/music.svg') no-repeat;
        background-size: 100% 100%;
        margin-right: 10px;
        margin-left: 30px;
        margin-top: 5px;
      }

      .speedIcon {
        width: 26px;
        height: 26px;
        background: url('@/assets/images/speedIcon.png') no-repeat;
        background-size: 100% 100%;
        margin-right: 10px;
      }
    }
  }
}

.tdesign-demo__user-option {
  display: flex;
  align-items: center;

  img {
    width: 26px;
    height: 26px;
    border-radius: 5px;
    margin-right: 5px;
  }

  .tdesign-demo__user-option-info {
    .name {
      font-size: 12px;
      position: relative;
      top: 5px;
      color: var(--td-text-color-primary);
    }

    .tdesign-demo__user-option-desc {
      font-size: 12px;
      margin-bottom: 8px;
      color: #1890ff;
    }
  }
}

:v-deep(.umo-select-option) {
  height: 100px;
}

.menusButtonBg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  .fount-bold-text {
    margin: 16px 0 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 60px;
  }
}

.t-class-dialog-video {
  .umo-dialog__body--fullscreen--without-footer {
    height: 100vh;
  }

  .matchCaseMain {
    background-color: #f6f7f9;
    width: 100%;
    position: relative;
    overflow: hidden;
    background-color: var(--td-bg-color-container);
    height: 100%;

    .matchCaseTitle {
      font-size: 46px;
      font-weight: bold;
      text-transform: none;
      background: -webkit-linear-gradient(270deg, #4ba3de 10%, #90dccd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      height: 80px;
      line-height: 80px;
    }

    .matchCaseAdTitle {
      font-size: 36px;
      font-weight: bold;
      color: var(--td-text-color-primary);
      line-height: 50px;
    }

    .matchCaseMainLeft {
      position: absolute;
      left: 0;
      top: 0px;
      width: 320px;
      min-height: calc(100vh);
      background-color: #fff;

      .matchCaseMainLeftTop {
        height: 54px;
        line-height: 54px;
        padding-left: 18px;
        border-bottom: 1px solid #eaeaea;
      }

      .matchCaseMainleftInput {
        padding: 20px;

        .searchInput {
          :deep(.umo-input) {
            border-radius: 114px;
            height: 38px;
            padding-left: 14px;
          }
        }
      }

      .matchCaseMainHistoryList {
        .matchCaseMainHistoryListItem {
          border: 1px solid #eaeaea;
          border-radius: 8px;
          padding: 10px;
          margin: 10px 0;
          cursor: pointer;

          &:hover {
            background-color: #eaeaea;
          }

          .historyTitle {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .historyDate {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #999;
            margin-top: 10px;

            .del {
              cursor: pointer;
            }
          }
        }

        .active {
          border: 1px solid #1890ff;
        }
      }
    }

    .matchCaseMainCenter {
      min-height: 100%;
      height: auto;
      max-width: 1000px;
      width: 1000px;
      min-height: 1000px;
      margin: 0 auto;
      line-height: 28px;
      border-radius: 14px;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      margin: 60px auto;
      padding: 20px 40px;

      .matchCaseSeacherInput {
        background-color: var(--umo-color-white);
        border-radius: 12px;
        padding: 40px 20px;
        position: relative;
        min-height: 300px;

        .umo-textarea__inner {
          display: flex;
          width: 100%;
          height: var(--td-comp-size-xxxl);
          min-height: var(--td-comp-size-xxxl);
          /* border: 1px solid var(--td-border-level-2-color); */
          /* border-radius: var(--td-radius-default); */
          padding: calc(calc(var(--td-comp-size-m) - var(--td-line-height-body-medium)) / 2) var(--td-comp-paddingLR-s);
          /* background-color: var(--td-bg-color-specialcomponent); */
          font: var(--td-font-body-medium);
          color: var(--td-text-color-primary);
          /* resize: vertical; */
          outline: none;

          /* transition: all cubic-bezier(0.38, 0, 0.24, 1) 0.2s, height 0s; */
          /* box-sizing: border-box; */
        }

        .umo-textarea .umo-textarea__inner:focus {
          border-color: none !important;
          box-shadow: none !important;
        }

        .umo-textarea .umo-textarea__inner:hover {
          border-color: none !important;
        }

        textarea {
          border: none;
        }

        .videoTxt {
          text-align: left;
          color: #333;
          margin-bottom: 15px;
        }

        .videoRatio {
          display: grid;
          grid-template-columns: repeat(6, 1fr);
          gap: 10px;

          .videoRatioItem {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            background-color: #f3f3f3;
            height: 58px;
            cursor: pointer;

            .icon169 {
              border: 3px solid;
              width: 16px;
              height: 9px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon916 {
              border: 3px solid;
              width: 9px;
              height: 16px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon11 {
              border: 3px solid;
              width: 10px;
              height: 10px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon53 {
              border: 3px solid;
              width: 12px;
              height: 9px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon43 {
              border: 3px solid;
              width: 10px;
              height: 6px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .icon34 {
              border: 3px solid;
              width: 6px;
              height: 10px;
              display: inline-block;
              border-radius: 4px;
              margin-right: 4px;
              border-color: #333;
            }

            .active {
              border-color: #0966b4;
            }
          }

          .active {
            background-color: #e3f0ff;
            border: 1px solid #0966b4;
            color: #0966b4;
          }
        }

        &:focus {
          border-color: var(--td-brand-color);
          box-shadow: 0 0 0 2px var(--td-brand-color-focus);
        }

        &:hover {
          border: 1px solid var(--td-brand-color);
          box-shadow: 0 0 0 2px var(--td-brand-color-focus);
        }

        .umo-textarea__info_wrapper_align {
          justify-content: end;
        }

        .matchCaseSeacherBtn {
          position: absolute;
          right: 20px;
          bottom: 10px;

          .btn {
            min-width: 80px;
            padding: 0 10px;
            height: 32px;
            background: linear-gradient(67deg, #4ba3de 10%, #98e3ca 100%);
            border-radius: 36px 36px 36px 36px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .icon {
              display: inline-block;
              width: 15px;
              height: 15px;
              background-image: url(../../../assets/images/phont.svg);
              background-size: cover;
              background-repeat: no-repeat;
              margin-right: 5px;
            }
          }
        }
      }

      .bg-bottom {
        width: 300px;
        height: 158px;
        margin: 68px auto;
        background: url('@/assets/images/bg-bottom.png') no-repeat bottom;
        background-size: 100% 100%;
      }

      .matchCaseSeacherResult {
        margin: 30px 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .matchCaseSeacherResultTitle {
          font-size: 20px;
          font-weight: bold;
          color: #000;
          display: flex;
          justify-content: center;
          align-items: center;

          .lingIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            background: url('../../../assets/images/result.svg') no-repeat;
            background-size: cover;
            margin-right: 5px;
          }
        }
      }

      .matchCaseSeacherResultContent {
        min-height: 100%;
        height: auto;
        max-width: 1000px;
        width: 1000px;
        min-height: 500px;
        background-color: #fff;
        margin: 0 auto;
        line-height: 28px;
        border-radius: 14px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        border: 1px solid var(--td-brand-color);
      }

      .umo-dialog__body--fullscreen--without-footer {
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      }
    }
  }
}
</style>
