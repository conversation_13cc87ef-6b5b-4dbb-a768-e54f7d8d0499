<!-- AI 图片 MenusAiAIimage-->
<template>
  <t-tooltip :content="t('ai.AIimage')" theme="light" placement="top" :show-arrow="false" destroy-on-close>
    <div class="menusButtonBg" @click="openModal">
      <!-- <menus-button
        ico="bold"
        hide-text
        style="width: 60px; height: 60px; background-color: #0066ff"
      /> -->
      <img src="@/assets/icons/AIimage.svg" alt="" />
      <div class="fount-bold-text">{{ t('ai.AIimage') }}</div>
    </div>
  </t-tooltip>

  <modal :visible="visible" :show-header="true" :is-table="true" width="760px" :footer="false"
    dialog-class-name="t-class-dialog-image" @close="visible = false">
    <template #templateHeader>
      <div style="text-align: center">
        <i :class="isDark === 'light' ? 'headerIcon' : 'headerIconDark'"></i>{{ t('ai.AIimage') }}
      </div>
    </template>
    <div class="topInputBg">
      <t-textarea v-model="theme" :placeholder="t('ai.imageInfo.placeholder')" :autosize="{ minRows: 3, maxRows: 5 }"
        :maxlength="10000" style="border: none; outline: none"></t-textarea>
      <div class="matchCaseSeacherBtn" @click="generate">
        <div class="btn">
          <i class="icon"></i>{{ t('ai.matchCaseInfo.generate') }}
        </div>
      </div>
    </div>

    <div class="topInputTitle">{{ t('ai.imageInfo.illustrationResults') }}</div>
    <div class="topInputBg">
      <div v-if="generateing">
        <t-loading content="正在生成"></t-loading>
      </div>
      <div v-else-if="!resultVisible" class="topInputNoData">
        <div class="topInputNoDataImg"></div>
        <div class="topInputNoDataText">{{ t('ai.polishing.noData') }}</div>
      </div>
      <div v-else class="topInputResult">
        <ImageView v-if="urlImg" :img="urlImg" :alt="urlImg" />
      </div>
    </div>

    <div class="buttonGroup">
      <t-button theme="default" style="margin-right: 18px" @click="visible = false">{{ t('ai.imageInfo.cancel')
      }}</t-button>
      <t-button theme="primary" @click="insertResult">{{
        t('ai.imageInfo.insert')
      }}</t-button>
    </div>
  </modal>
</template>
<script setup>
import { ref } from 'vue'

import { chatAiByPrompt } from '@/api/ai'
import { AiPromtType } from '@/enum/ai'
import { OssService } from '@/utils/aliOss'
const { editor, options } = useStore()

const openModal = () => {
  visible.value = true
}
const visible = ref(false)
const theme = ref('')
const generateing = ref(false)
const resultVisible = ref(false)
const urlImg = ref(null)
const generate = () => {
  if (!theme.value) return MessagePlugin.error('请输入关键字')
  if (generateing.value || !theme.value) return
  generateing.value = true
  chatAiByPrompt({
    question: theme.value,
    ability: AiPromtType.TextToImage,
  }).then(async (res) => {
    const imgRes = await OssService(
      base64ToFile(res.payload.choices.text[0].content),
    )
    urlImg.value = imgRes.url
    resultVisible.value = true
    generateing.value = false
  })
}
const insertResult = () => {
  if (!urlImg.value) return
  editor.value
    ?.chain()
    .focus()
    .setImageLayout({
      src: urlImg.value,
      width: 300,
      height: 300,
    })
    .run()
  visible.value = false
}
// 定义一个函数，用于将Base64转换为File对象
const base64ToFile = (
  base64Data,
  fileName = `${new Date().getTime()}.png`,
  mimeType = 'image/png',
) => {
  // 提取Base64数据中逗号之后的实际数据部分
  const base64Str = base64Data
  const byteCharacters = atob(base64Str)
  const byteArrays = []
  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512)
    const byteNumbers = new Array(slice.length)
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i)
    }
    const byteArray = new Uint8Array(byteNumbers)
    byteArrays.push(byteArray)
  }
  return new File(byteArrays, fileName, { type: mimeType })
}


// 判断 黑夜模式与白天模式
const isDark = $computed(() => {
  return options.value.theme
})
</script>
<style lang="less" scoped>
.menusButtonBg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  .fount-bold-text {
    margin: 16px 0 8px;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 60px;
  }
}

.t-class-dialog-image {
  padding: 20px 30px;
  background: linear-gradient(180deg, #e5f3ff 0%, #ffffff 100%);
  border-radius: 12px;

  .headerIcon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url('../../../assets/images/imageAI.svg') no-repeat;
    background-size: contain;
    margin-right: 5px;
    position: relative;
    top: 3px;
  }

  .headerIconDark {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url('../../../assets/images/imageAIDark.svg') no-repeat;
    background-size: contain;
    margin-right: 5px;
    position: relative;
    top: 3px;
  }

  .topInputTitle {
    margin: 20px 0 12px;
    font-weight: bold;
    font-size: 16px;
    color: var(--td-text-color-primary);
  }

  .txtRight {
    text-align: right;
    margin: 10px 0 20px;
    color: #999;
  }

  .buttonGroup {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }

  .topInputBg {
    background-color: var(--umo-color-white);
    border-radius: 8px;
    border: 1px solid #e5e6e7;
    padding: 20px;
    position: relative;

    &:focus {
      border-color: var(--td-brand-color);
    }

    &:hover {
      border: 1px solid var(--td-brand-color);
      //  box-shadow: 0 0 0 2px var(--td-brand-color-focus);
    }

    .umo-textarea__inner:focus {
      border-color: var(--td-brand-color);
      box-shadow: none;
    }

    textarea {
      border: none;
    }

    .topInputResult {
      display: flex;
      justify-content: center;
      align-items: center;

      .tdesign-demo-image-viewer__base {
        width: 300px;
        height: 300px;
      }

      .tdesign-demo-image-viewer__ui-image {
        width: 300px;
        height: 300px;

        display: inline-flex;
        position: relative;
        justify-content: center;
        align-items: center;
        border-radius: var(--td-radius-small);
        overflow: hidden;
      }

      .tdesign-demo-image-viewer__ui-image--img {
        width: 300px;
        height: 300px;
        cursor: pointer;
        position: relative;
      }

      .tdesign-demo-image-viewer__ui-image--hover {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        background-color: rgba(0, 0, 0, 0.6);
        color: var(--td-text-color-anti);
        line-height: 22px;
        transition: 0.2s;
      }

      ::deep(.tdesign-demo-image-viewer__ui-image) {
        width: 100%;
        height: 100%;
        display: inline-flex;
        position: relative;
        justify-content: center;
        align-items: center;
        border-radius: var(--td-radius-small);
        overflow: hidden;
      }
    }

    .topInputNoData {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;

      .topInputNoDataImg {
        background: url('../../../assets/images/noData.svg') no-repeat;
        width: 41px;
        height: 28px;
        background-size: contain;
      }

      .topInputNoDataText {
        margin-top: 10px;
        font-size: 14px;
        color: var(--td-text-color-primary);
        text-align: center;
      }
    }

    .umo-textarea__info_wrapper_align {
      justify-content: end;
      margin-right: 120px;
    }

    .matchCaseSeacherBtn {
      position: absolute;
      right: 20px;
      bottom: 15px;

      .btn {
        min-width: 80px;
        padding: 0 10px;
        height: 32px;
        background: linear-gradient(67deg, #4ba3de 10%, #98e3ca 100%);
        border-radius: 36px 36px 36px 36px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .icon {
          display: inline-block;
          width: 15px;
          height: 15px;
          background-image: url(../../../assets/images/phont.svg);
          background-size: cover;
          background-repeat: no-repeat;
          margin-right: 5px;
        }
      }
    }
  }
}
</style>
