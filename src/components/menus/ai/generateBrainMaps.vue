<!-- 生产脑图 -->
<template>
  <t-tooltip
    :content="t('ai.generateBrainMaps')"
    theme="light"
    placement="top"
    :show-arrow="false"
    destroy-on-close
  >
    <div class="menusButtonBg" @click="openModal">
      <img src="@/assets/icons/generateBrainMaps.svg" alt="" />
      <div class="fount-bold-text">{{ t('ai.generateBrainMaps') }}</div>
    </div>
  </t-tooltip>
  <Modal
    :visible="visible"
    :header="t('ai.generateBrainMaps')"
    @close="visible = false"
    @confirm="visible = false"
  >
    <div>功能尚未开发尽情期待~</div>
  </Modal>
</template>
<script setup>
const visible = ref(false)
const openModal = () => {
  visible.value = true
}
</script>
<style lang="less" scoped>
.menusButtonBg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .fount-bold-text {
    margin: 16px 0 8px;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 60px;
  }
}
</style>
