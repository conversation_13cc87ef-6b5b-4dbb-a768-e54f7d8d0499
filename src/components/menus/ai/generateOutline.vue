<!-- 生成大纲 -->

<template>
  <t-tooltip :content="t('ai.generateOutline')" theme="light" placement="top" :show-arrow="false" destroy-on-close>
    <div class="menusButtonBg">
      <!-- <menus-button
        ico="bold"
        hide-text
        style="width: 60px; height: 60px; background-color: #0066ff"
        @menu-click="showModal"
      /> -->
      <img src="@/assets/icons/generateOutline.svg" alt="" />
      <div class="fount-bold-text">{{ t('ai.generateOutline') }}</div>
    </div>
  </t-tooltip>
  <Modal :visible="visible" mode="full-screen" :header="t('ai.generateOutline')" :footer="false"
    dialog-class-name="t-class-dialog-generate" @close="visible = false">
    <template #templateHeader>
      <div style="text-align: center">{{ t('ai.generateOutline') }}</div>
    </template>

    <div v-if="historyList.length == 0" class="matchCaseMain" style="
        flex-direction: column;
        padding-top: 188px;
        text-align: center;
        overflow: hidden;
      ">
      <div class="matchCaseTitle">
        {{ t('ai.generateOptions.title') }}
      </div>
      <div class="matchCaseAdTitle">{{ t('ai.generateOptions.ad') }}</div>

      <div class="matchCaseMainCenter" :style="{ width: pageZoomWidth }">
        <div class="matchCaseSeacherInput">
          <t-textarea v-model="curQuestion.question" :placeholder="t('ai.matchCaseInfo.inputSearch')"
            :autosize="{ minRows: 3, maxRows: 5 }" :maxlength="10000"></t-textarea>
          <div class="matchCaseSeacherBtn">
            <div class="btn" @click="generateAnswer">
              <i class="icon"></i>{{ t('ai.matchCaseInfo.generate') }}
            </div>
          </div>
        </div>
        <div class="bg-bottom"></div>
      </div>
    </div>
    <div v-else class="matchCaseMain">
      <div class="matchCaseMainLeft">
        <div class="matchCaseMainLeftTop">
          {{ t('ai.matchCaseInfo.historicalRecords') }}
        </div>
        <div class="matchCaseMainleftInput">
          <div class="searchInput">
            <t-input v-model="historyQuestion" :placeholder="t('ai.matchCaseInfo.inputPlaceholder')" clearable>
              <template #suffixIcon>
                <search-icon :style="{ cursor: 'pointer' }" @click="queryHistoryList" />
              </template>
            </t-input>
          </div>

          <div class="matchCaseMainHistoryList">
            <div v-for="item in historyList" :key="item.promptHistoryId" class="matchCaseMainHistoryListItem" :class="{
              active:
                item.promptHistoryId == activePromptHistoryId &&
                curQuestion.question,
            }" @click="queryHistory(item.promptHistoryId)">
              <div class="historyTitle">{{ item.question }}</div>
              <div class="historyDate">
                <div>{{ item.createTime }}</div>
                <div class="del" @click.stop="deleteHistory(item.promptHistoryId)">
                  <DeleteIcon />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="matchCaseMainCenter" :style="{ width: pageZoomWidth }">
        <div class="matchCaseSeacherInput">
          <t-textarea v-model="curQuestion.question" :placeholder="t('ai.matchCaseInfo.inputSearch')"
            :autosize="{ minRows: 5, maxRows: 100 }" :maxlength="10000"></t-textarea>
          <div class="matchCaseSeacherBtn" @click="generateAnswer">
            <div class="btn">
              <i class="icon"></i>{{ t('ai.matchCaseInfo.generate') }}
            </div>
          </div>
        </div>

        <div class="matchCaseSeacherResult" :style="{ width: pageZoomWidth }">
          <div class="matchCaseSeacherResultTitle">
            <i class="lingIcon"></i> {{ t('ai.matchCaseInfo.generateResults') }}
          </div>
          <div class="matchCaseSeacherResultBtnGroup">
            <t-button theme="default" variant="outline" style="margin-right: 10px; width: 80px">{{
              t('ai.matchCaseInfo.export') }}</t-button>
            <t-button theme="primary" style="width: 80px" @click="copyResult">{{
              t('ai.matchCaseInfo.copy')
            }}</t-button>
          </div>
        </div>
        <div class="matchCaseSeacherResultContent" :style="{ width: pageZoomWidth }">
          <div style="padding: 20px" v-html="curQuestion.answer"></div>
        </div>
      </div>
    </div>
  </Modal>
</template>
<script setup>
import { DeleteIcon, SearchIcon } from 'tdesign-icons-vue-next'
import { ref } from 'vue'

import { chatAiByPrompt } from '@/api/ai'
import {
  deleteHistoryByPromptHistoryId,
  queryHistoryByChapterId,
  queryHistoryByPromptHistoryId,
} from '@/api/aiHistory'
import Modal from '@/components/modal.vue'
import { AiPromtType } from '@/enum/ai'
const { page, chapterId, editor } = useStore()
const visible = ref(false)
const historyList = ref([])
const historyQuestion = ref('')
const activePromptHistoryId = ref(null)
const curQuestion = ref({
  question: '',
  answer: '',
})

onMounted(() => {
  queryHistoryList()
})

const deleteHistory = async (id) => {
  if (id == activePromptHistoryId.value) {
    activePromptHistoryId.value = null
    curQuestion.value = {
      question: '',
      answer: '',
    }
  }
  await deleteHistoryByPromptHistoryId({ promptHistoryId: id })
  queryHistoryList()
}
const queryHistory = async (id) => {
  activePromptHistoryId.value = id
  const res = await queryHistoryByPromptHistoryId({ promptHistoryId: id })
  //   console.log(
  //     'res',
  //     res,
  //     JSON.parse(
  //       JSON.parse(JSON.parse(res.data.answer).body)
  //         .choices[0].message.content.replace('```json', '')
  //         .replace('```', ''),
  //     ).result,
  //   )
  curQuestion.value = {
    question: res.data.question,
    answer: JSON.parse(
      JSON.parse(JSON.parse(res.data.answer).body)
        .choices[0].message.content.replace('```json', '')
        .replace('```', ''),
    ).result,
  }
}
const queryHistoryList = async () => {
  const res = await queryHistoryByChapterId({
    chapterId: chapterId.value,
    question: historyQuestion.value,
    ability: AiPromtType.GenerateOutline,
  })
  // if (res.code === 200)
  historyList.value = res.data || []
  activePromptHistoryId.value = historyList.value[0]?.promptHistoryId
}

const copyResult = () => {
  const { copy } = useClipboard({
    source: ref(curQuestion.value.answer),
  })
  void copy()
  useMessage('success', t('assistant.copySuccess'))
}

const showModal = () => {
  activePromptHistoryId.value = null
  curQuestion.value = {
    question: '',
    answer: '',
  }
  visible.value = true
}

const generateAnswer = () => {
  if (!curQuestion.value.question) return
  chatAiByPrompt({
    question: curQuestion.value.question,
    ability: AiPromtType.GenerateOutline,
    chapterId: chapterId.value,
  })
    .then((res) => {
      //   console.log('res', JSON.parse(res.body.content))
      curQuestion.value.answer = JSON.parse(res.body.content).result
    })
    .finally(() => {
      queryHistoryList()
    })
}

const pageSize = $computed(() => {
  const { width, height } = page.value.size ?? { width: 0, height: 0 }
  return {
    width: page.value.orientation === 'portrait' ? width : height,
    height: page.value.orientation === 'portrait' ? height : width,
  }
})
// 页面缩放后的大小
const pageZoomWidth = $computed(() => {
  return `calc(${pageSize.width}cm * ${page.value.zoomLevel ? page.value.zoomLevel / 100 : 1})`
})
</script>
<style lang="less" scoped>
.menusButtonBg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: not-allowed;

  .fount-bold-text {
    margin: 16px 0 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 60px;
  }
}

.t-class-dialog-generate {
  .matchCaseMain {
    background-color: #eaeaea;
    width: 100%;
    position: relative;

    .matchCaseTitle {
      font-size: 46px;
      font-weight: bold;
      text-transform: none;
      background: -webkit-linear-gradient(270deg, #4ba3de 10%, #90dccd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      height: 80px;
      line-height: 80px;
    }

    .matchCaseAdTitle {
      font-size: 36px;
      font-weight: bold;
      color: #333;
      line-height: 50px;
    }

    .matchCaseMainLeft {
      position: absolute;
      left: 0;
      top: 0px;
      width: 320px;
      min-height: calc(100vh - 50px);
      background-color: #fff;

      .matchCaseMainLeftTop {
        height: 54px;
        line-height: 54px;
        padding-left: 18px;
        border-bottom: 1px solid #eaeaea;
      }

      .matchCaseMainleftInput {
        padding: 20px;

        .searchInput {
          :deep(.umo-input) {
            border-radius: 114px;
            height: 38px;
            padding-left: 14px;
          }
        }
      }

      .matchCaseMainHistoryList {
        .matchCaseMainHistoryListItem {
          border: 1px solid #eaeaea;
          border-radius: 8px;
          padding: 10px;
          margin: 10px 0;
          cursor: pointer;

          &:hover {
            background-color: #eaeaea;
          }

          .historyTitle {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .historyDate {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #999;
            margin-top: 10px;

            .del {
              cursor: pointer;
            }
          }
        }

        .active {
          border: 1px solid #1890ff;
        }
      }
    }

    .matchCaseMainCenter {
      min-height: 100%;
      height: auto;
      max-width: 1000px;
      width: 1000px;
      min-height: 1000px;
      margin: 0 auto;
      line-height: 28px;
      border-radius: 14px;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      margin: 60px auto;
      padding: 20px 40px;

      .matchCaseSeacherInput {
        background-color: #fff;
        border-radius: 12px;
        padding: 20px;
        position: relative;
        height: 180px;

        &:hover {
          border: 1px solid var(--td-brand-color);
          box-shadow: 0 0 0 2px var(--td-brand-color-focus);
        }

        .umo-textarea .umo-textarea__inner:focus {
          border-color: none !important;
          box-shadow: none !important;
        }

        .umo-textarea__inner:focus {
          border: none !important;
        }

        .umo-textarea .umo-textarea__inner:hover {
          border-color: none !important;
        }

        textarea {
          border: none;
        }

        .umo-textarea__info_wrapper_align {
          justify-content: end;
        }

        .matchCaseSeacherBtn {
          position: absolute;
          right: 10px;
          bottom: 10px;

          .btn {
            min-width: 80px;
            padding: 0 10px;
            height: 32px;
            background: linear-gradient(67deg, #4ba3de 10%, #98e3ca 100%);
            border-radius: 36px 36px 36px 36px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .icon {
              display: inline-block;
              width: 15px;
              height: 15px;
              background-image: url(../../../assets/images/phont.svg);
              background-size: cover;
              background-repeat: no-repeat;
              margin-right: 5px;
            }
          }
        }

        .umo-textarea__inner {
          display: flex;
          width: 100%;
          height: var(--td-comp-size-xxxl);
          min-height: var(--td-comp-size-xxxl);
          /* border: 1px solid var(--td-border-level-2-color); */
          /* border-radius: var(--td-radius-default); */
          padding: calc(calc(var(--td-comp-size-m) - var(--td-line-height-body-medium)) / 2) var(--td-comp-paddingLR-s);
          /* background-color: var(--td-bg-color-specialcomponent); */
          font: var(--td-font-body-medium);
          color: var(--td-text-color-primary);
          /* resize: vertical; */
          outline: none;

          /* transition: all cubic-bezier(0.38, 0, 0.24, 1) 0.2s, height 0s; */
          /* box-sizing: border-box; */
        }
      }

      .matchCaseSeacherResult {
        margin: 30px 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .matchCaseSeacherResultTitle {
          font-size: 20px;
          font-weight: bold;
          color: #000;
          display: flex;
          justify-content: center;
          align-items: center;

          .lingIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            background: url('../../../assets/images/result.svg') no-repeat;
            background-size: cover;
            margin-right: 5px;
          }
        }
      }

      .matchCaseSeacherResultContent {
        min-height: 100%;
        height: auto;
        max-width: 1000px;
        width: 1000px;
        min-height: 500px;
        background-color: #fff;
        margin: 0 auto;
        line-height: 28px;
        border-radius: 14px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        border: 1px solid var(--td-brand-color);
      }
    }
  }
}
</style>
