<!-- 智能校对 -->
<template>
  <t-tooltip
    :content="t('ai.intelligentProofreading')"
    theme="light"
    placement="top"
    :show-arrow="false"
    destroy-on-close
  >
    <div class="menusButtonBg" @click="openModal">
      <img src="@/assets/icons/intelligentProofreading.svg" alt="" />
      <div class="fount-bold-text">{{ t('ai.intelligentProofreading') }}</div>
    </div>
  </t-tooltip>
  <t-drawer
    v-model:visible="visible"
    size="medium"
    :footer="false"
    :header="false"
    :destroy-on-close="true"
    :close-btn="true"
    :on-close="closeModal"
    :append-to="app"
    :z-index="9999"
  >
    <div class="intelligentProofreadingMainRight">
      <div class="intelligentProofreadingMainRight-title">
        <div>{{ t('ai.intelligentProofreading') }}</div>
        <div>
          <t-button @click="adoptedErrorCorrection">{{
            t('ai.intelligentInfo.adopt')
          }}</t-button>
        </div>
      </div>
      <div class="errorlist">
        <div class="errorlist-item">
          {{
            t('ai.intelligentInfo.adoptInfo', {
              number: aiErrorCorrectionList.length,
            })
          }}
        </div>
        <div class="errorlist-icon" @click="generateErrorCorrectionInfo">
          <RefreshIcon style="margin-right: 4px" />{{
            t('ai.intelligentInfo.again')
          }}
        </div>
      </div>
      <div v-if="!loading" class="intelligentProofreadingMainRight-list">
        <div
          v-for="item in aiErrorCorrectionList"
          :key="item.id"
          class="intelligentProofreadingMainRight-item"
        >
          <div class="td-checkbox">
            <t-checkbox v-model="item.adopted"></t-checkbox>
          </div>
          <div @click="goto(item)">
            <strong>{{ t('ai.intelligentInfo.errorType') }} :</strong
            >{{ ErrorCorrectionType[item.type] }}
          </div>
          <div @click="goto(item)">
            <strong>{{ t('ai.intelligentInfo.original') }}：</strong>
          </div>
          <div @click="goto(item)">{{ item.origin }}</div>
          <div @click="goto(item)">
            <strong>{{ t('ai.intelligentInfo.suggested') }}：</strong>
          </div>
          <div @click="goto(item)">{{ item.new }}</div>
        </div>
        <div
          v-for="item in aiErrorCorrectionImgList"
          :key="item.id"
          class="intelligentProofreadingMainRight-item"
        >
          <t-image-viewer :images="[item.url]">
            <template #trigger="{ open }">
              <div style="display: flex">
                <img
                  alt="test"
                  :src="item.url"
                  style="width: 100px; height: 100px; object-fit: contain"
                  @click="open"
                />
                <div
                  style="
                    margin-left: 34px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    color: red;
                  "
                >
                  <div v-for="(c, index) in item.categoryList" :key="index">
                    {{ c.category_description }}
                  </div>
                </div>
              </div>
            </template>
          </t-image-viewer>
          <!-- <t-image img :src=""  /> -->
        </div>
      </div>
      <div v-if="loading" class="intelligentProofreadingMainRight-list">
        <t-loading
          style="margin-top: 20px; height: 100px"
          content="正在智能校对中"
        />
      </div>
    </div>
  </t-drawer>
</template>
<script setup>
import { RefreshIcon } from 'tdesign-icons-vue-next'
import { ref } from 'vue'

import { chatAiByPrompt } from '@/api/ai'
import { AiPromtType, ErrorCorrectionType } from '@/enum/ai'
import {
  BLOCK_QUOTE,
  BULLETLIST,
  ERROR_CORRECTION,
  HEADING,
  IMAGE,
  IMAGE_ICON,
  IMAGE_INLINE,
  IMAGEGALLERY,
  IMAGELAYOUT,
  LISTITEM,
  ORDEREDLIST,
  PAGE,
  PARAGRAPH,
  RESOURCE_COVER,
  TABLE,
  TABLE_CELL,
  TABLE_PLUS,
  TABLE_ROW,
  TABLEHEADER,
  TASKITEM,
  TASKLIST,
  TEXT_BOX,
} from '@/extensions/page/node-names'
const { editor } = useStore()
const visible = ref(false)

const aiErrorCorrectionList = ref([])
const aiErrorCorrectionImgList = ref([])
const whilteList = [
  PARAGRAPH,
  PAGE,
  HEADING,
  BULLETLIST,
  LISTITEM,
  TASKLIST,
  TASKITEM,
  ORDEREDLIST,
  TABLE,
  TABLEHEADER,
  TABLE_ROW,
  TABLE_CELL,
  TEXT_BOX,
  IMAGE,
  IMAGEGALLERY,
  IMAGELAYOUT,
  RESOURCE_COVER,
  BLOCK_QUOTE,
  TABLE_PLUS,
  IMAGE_INLINE,
  IMAGE_ICON,
]
const loading = ref(false)
const errorCorrectionTextList = ref([])
const errorCorrectionImgList = ref([])
let errorCorrectionTextIndex = 0
const openModal = () => {
  //   console.log('openModal', editor.value.view.state.doc)
  errorCorrectionTextList.value = []
  errorCorrectionImgList.value = []
  errorCorrectionTextIndex = 0

  editor.value.view.state.doc.descendants(buildErrorCorrectionContent)
  visible.value = true
  generateErrorCorrectionInfo()
  // setTimeout(() => {

  // }, 2000)
}
const closeModal = () => {
  editor.value?.chain().setErrorCorrectionList([]).run()
}
const buildErrorCorrectionContent = (node, pos, parent, index) => {
  const nodeTypeName = node.type.name
  if (whilteList.includes(nodeTypeName)) {
    // console.log('node', node)
    if (nodeTypeName == PARAGRAPH || nodeTypeName == HEADING) {
      if (node.textContent.length > 0) {
        errorCorrectionTextList.value.push({
          type: 'p',
          id: node.attrs.id,
          content: node.textContent,
          startIndex: errorCorrectionTextIndex,
          endIndex: errorCorrectionTextIndex + node.textContent.length,
        })
        errorCorrectionTextIndex += node.textContent.length
      }
    } else if (
      nodeTypeName == IMAGELAYOUT ||
      nodeTypeName == IMAGE_INLINE ||
      nodeTypeName == IMAGE_ICON
    ) {
      console.log('ImgaNode', node)
      errorCorrectionImgList.value.push({
        type: 'img',
        id: node.attrs.id || new Date().getTime(),
        url: node.attrs.src,
      })
    } else if (nodeTypeName == IMAGEGALLERY) {
      node.attrs.imgList.forEach((img) => {
        errorCorrectionImgList.value.push({
          type: 'img',
          id: img.id || new Date().getTime(),
          url: img.src,
        })
      })
    }
    return true
  }
  return false
}

const generateErrorCorrectionInfo = async () => {
  loading.value = true
  aiErrorCorrectionImgList.value = []
  errorCorrectionImgList.value.forEach((item) => {
    chatAiByPrompt({
      ability: AiPromtType.ImageCompliance,
      question: item.url,
    })
      .then((res) => {
        // let res = {
        //   code: '000000',
        //   desc: '成功',
        //   data: {
        //     result: {
        //       suggest: 'block',
        //       detail: {
        //         category_list: [
        //           {
        //             confidence: 93,
        //             category: 'political',
        //             suggest: 'block',
        //             category_description: '涉政_政治象征_国旗',
        //             object_detail: [
        //               {
        //                 name: 'chinaflag',
        //                 confidence: 93,
        //                 location: {
        //                   x: 124.0,
        //                   y: 271.0,
        //                   w: 415.0,
        //                   h: 307.0,
        //                 },
        //               },
        //             ],
        //           },
        //           {
        //             confidence: 99,
        //             category: 'political',
        //             suggest: 'block',
        //             category_description: '涉政_政治象征_国旗国徽',
        //           },
        //         ],
        //       },
        //     },
        //     request_id: 'T20230302164002016a33b6197100000',
        //   },
        //   sid: 'a154ee7126314a029a5ab5a7491ffa1c',
        // }
        if (res.data?.result?.suggest == 'block') {
          // console.log('res', res)
          aiErrorCorrectionImgList.value.push({
            id: item.id,
            url: item.url,
            result: res.data.result.suggest,
            categoryList: res.data.result.detail.category_list,
          })
        }
      })
      .catch((err) => {
        console.log('err', err)
      })
  })

  const res = await chatAiByPrompt({
    ability: AiPromtType.TextErrorCorrection,
    question: errorCorrectionTextList.value
      .map((item) => item.content)
      .join('\n'),
    checkBlackWhite: true,
  })
  // console.log('res', res)
  aiErrorCorrectionList.value = []
  for (const key in res) {
    aiErrorCorrectionList.value = [...aiErrorCorrectionList.value, ...res[key]]
  }
  // console.log('aiErrorCorrectionList', aiErrorCorrectionList.value)
  // aiErrorCorrectionList.value = [
  //   [5, '长', '常', 'char'],
  //   [17, '效', '郊', 'char'],
  //   [20, '清', '青', 'char'],
  //   [64, '滩', '毯', 'char'],
  //   [99, '篮', '蓝', 'char'],
  //   [109, '扶', '拂', 'char'],
  //   [36, '俩', '', 'redund'],
  //   [4, '非长', '非常长', 'miss'],
  // ]
  aiErrorCorrectionList.value = aiErrorCorrectionList.value
    .map((o) => {
      return {
        index: o[0],
        originIndex: o[0],
        origin: o[1],
        new: o[2],
        type: o[3],
        adopted: false,
      }
    })
    .sort((a, b) => a.index - b.index)
  errorCorrectionTextList.value.forEach((item) => {
    aiErrorCorrectionList.value.forEach((o) => {
      if (o.index >= item.startIndex && o.index < item.endIndex) {
        o.id = item.id
      }
    })
  })
  editor.value
    ?.chain()
    .setErrorCorrectionList(aiErrorCorrectionList.value)
    .run()
  loading.value = false
  // console.log('aiErrorCorrectionList', aiErrorCorrectionList.value)
}
const goto = (item) => {
  const { result } = editor.value.storage[ERROR_CORRECTION]
  const position = result.find((o) => item.originIndex === o.originIndex)
  if (!position) {
    return
  }
  editor.value
    .chain()
    .setTextSelection({
      from: position.from,
      to: position.to,
    })
    .scrollIntoView()
    .run()
}
const adoptedErrorCorrection = () => {
  editor.value
    ?.chain()
    .setErrorCorrectionAdopted(aiErrorCorrectionList.value)
    .run()
  // console.log('adoptedErrorCorrection', aiErrorCorrectionList.value)
}
</script>
<style lang="less" scoped>
.menusButtonBg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  .fount-bold-text {
    margin: 16px 0 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 60px;
  }
}

.intelligentProofreadingBg {
  padding: 20px 0px;
}

.intelligentProofreadingMainRight {
  margin-top: 20px;
  position: absolute;
  top: 150px;
  width: 450px;
  height: calc(100% - 64px);
  z-index: 99999;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  font-size: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 20px 10px;

  .intelligentProofreadingMainRight-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 40px;
    background-color: var(--td-bg-color-container);
    padding-bottom: 10px;
    border-bottom: 1px solid #f1f1f1;
  }

  .errorlist {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    padding: 10px 0 0;

    .errorlist-item {
      strong {
        padding: 0 5px;
        color: red;
      }
    }

    .errorlist-icon {
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .intelligentProofreadingMainRight-list {
    overflow-y: auto;

    /* 自定义整个滚动条 */
    &::-webkit-scrollbar {
      width: 0px;
      /* 设置滚动条的宽度 */
    }

    .intelligentProofreadingMainRight-item {
      margin: 20px 5px;
      -webkit-box-shadow: 0 0 10px #e5e5e5;
      box-shadow: 0 0 10px #e5e5e5;
      padding: 12px 10px 12px 40px;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      line-height: 28px;
      border: 1px solid #f1f1f1;
      position: relative;

      .td-checkbox {
        width: 20px;
        height: 20px;
        position: absolute;
        left: 10px;
        top: 16px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;

        .td-checkbox-icon {
          width: 12px;
          height: 12px;
          background-color: #409eff;
          border-radius: 50%;
        }
      }

      &:hover {
        border: 1px solid #409eff;
      }
    }
  }
}

.intelligentProofreadingMain {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  .intelligentProofreadingMainLeft {
    position: fixed;
    left: 0;
    top: 58px;
    min-height: calc(100vh - 58px);
    width: 280px;
    padding: 20px;

    .intelligentProofreadingTitle {
      font-weight: bold;
      font-size: 16px;
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 10px;
      display: flex;
      justify-content: space-between;
    }
  }

  .intelligentProofreadingMainPage {
    min-height: 100%;
    height: auto;
    background-color: #fff;
    -webkit-box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.06),
      0 2px 12px rgba(0, 0, 0, 0.1);
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.06),
      0 2px 12px rgba(0, 0, 0, 0.1);
    max-width: 1000px;
    width: 1000px;
    min-height: 1000px;
    margin: 0 auto;
    line-height: 28px;
    border-radius: 14px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin: 60px auto;
    padding: 20px 40px;
  }
}
</style>
