<!-- 知识图谱 -->
<template>
  <t-tooltip
    :content="t('ai.knowledgeGraph')"
    theme="light"
    placement="top"
    :show-arrow="false"
    destroy-on-close
  >
    <div class="menusButtonBg">
      <!-- <menus-button
        ico="knowledgeGraph"
        hide-text
        style="width: 60px; height: 60px; background-color: #0066ff"
      /> -->
      <img src="@/assets/icons/knowledgeGraph.svg" alt="" />
      <div class="fount-bold-text">{{ t('ai.knowledgeGraph') }}</div>
    </div>
  </t-tooltip>
  <modal
    v-model:visible="visible"
    header="知识图谱"
    @close="visible = false"
    @confirm="visible = false"
  >
    <div>功能尚未开发尽情期待~</div>
  </modal>
</template>
<script setup>
const visible = ref(false)
const openModal = () => {
  visible.value = true
}
</script>
<style lang="less" scoped>
.menusButtonBg {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: not-allowed;
  // cursor: pointer;

  .fount-bold-text {
    margin: 16px 0 8px;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 60px;
  }
}
</style>
