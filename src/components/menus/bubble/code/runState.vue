<!-- @Author: <PERSON><PERSON><PERSON><PERSON> -->
<template>
  <menus-button
    :text="t('bubbleMenu.code.runState')"
    ico="code-run-state"
    :menu-active="editor?.getAttributes('codeBlock')?.runState"
    @menu-click="toggleRunState"
  />
</template>

<script setup lang="ts">
import { getSelectionNode } from '@/extensions/selection'

const { editor } = useStore()

const toggleRunState = () => {
  const codeBlock = editor.value ? getSelectionNode(editor.value) : null
  if (codeBlock) {
    editor.value?.commands.updateAttributes(codeBlock.type, {
      runState: !codeBlock.attrs.runState,
    })
  }
}
</script>