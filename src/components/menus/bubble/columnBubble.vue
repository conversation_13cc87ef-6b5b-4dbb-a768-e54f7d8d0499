<template>
  <div>
    <bubble-menu :editor="editor!" :plugin-key="`columnsMenu-${uuid()}`" :tippy-options="tippyOpitons"
      :should-show="() => editor!.isActive('layoutColumn')">
      <div class="column-container">
        <div class="edit-layout" @click="openEditModal">
          <ComponentLayoutIcon /> 调整布局
        </div>
        <div class="del-layout" @click="deleteLayoutColumns">
          <DeleteIcon />删除
        </div>
      </div>
    </bubble-menu>


    <modal :visible="visible" :header="t('base.layoutColumn.text')" width="600px" @close="visible = false"
      @confirm="handleConfirm">
      <div>
        <t-form :data="formData">
          <!-- <t-radio-group v-model="formData.layout" variant="primary-filled" default-value="1" @change="changeType">
          <t-radio-button value="1">{{
            t('base.layoutColumn.twoColumn')
          }}</t-radio-button>
          <t-radio-button value="2">{{
            t('base.layoutColumn.threeColumn')
          }}</t-radio-button>
        </t-radio-group> -->
          <div class="form-header-title">
            {{ formData.layout === '1' ? t('base.layoutColumn.twoColumn') : t('base.layoutColumn.threeColumn') }}布局分栏
          </div>
          <div class="form-alert">
            <t-alert theme="info">
              <template #message>{{ t('base.layoutColumn.tips') }}</template>
            </t-alert>
          </div>

          <div class="form-item">
            <t-form-item :label="t('base.layoutColumn.left')" required-mark>
              <t-input-number v-model="formData.leftWidth" suffix="%" :min="1" :max="99" />
            </t-form-item>
            <t-form-item v-if="formData.layout === '2'" :label="t('base.layoutColumn.center')" required-mark>
              <t-input-number v-model="formData.centerWidth" suffix="%" :min="1" :max="99" />
            </t-form-item>
            <t-form-item :label="t('base.layoutColumn.right')" required-mark>
              <t-input-number v-model="rightWidthComputed" suffix="%" :min="1" :max="99" />
            </t-form-item>
          </div>

          <div class="form-item">
            <t-form-item :label="t('base.layoutColumn.borderOpen')">
              <t-switch v-model="formData.borderOpen" @change="borderOpenChange" />
            </t-form-item>

            <t-form-item v-if="formData.borderOpen" :label="t('base.layoutColumn.borderColor')">
              <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                  <div
                    :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.borderColor};margin-right:10px;`"
                    :class="`${formData.borderColor == 'transparent' || !formData.borderColor ? 'transparent' : ''}`">
                  </div>
                  <div class="umo-color-picker-more-menu" :style="`border-bottom: 3px solid ${formData.borderColor};}`">
                    <span v-text="t('base.layoutColumn.borderColor')"></span>
                  </div>
                </div>
                <template #content>
                  <div style="padding: 10px">
                    <color-picker :default-color="formData.borderColor" @change="backgroundColorChange" />
                  </div>
                </template>
              </t-popup>
            </t-form-item>
            <t-form-item v-if="formData.borderOpen" :label="t('base.layoutColumn.borderWidth')">
              <t-input-number v-model="formData.borderWidth" suffix="px" :min="1" :max="99" />
            </t-form-item>
            <t-form-item v-if="formData.borderOpen" :label="t('base.layoutColumn.borderStyle')">
              <t-radio-group v-model="formData.borderStyle">
                <t-radio value="dotted">{{
                  t('insert.block.dotted')
                  }}</t-radio>
                <t-radio value="solid">{{
                  t('insert.block.solid')
                  }}</t-radio>
                <t-radio value="double">{{
                  t('insert.block.double')
                  }}</t-radio>
                <t-radio value="dashed">{{
                  t('insert.block.dashed')
                  }}</t-radio>
              </t-radio-group>
            </t-form-item>



            <div class="padding-main">
              <div class="padding-title">{{ t('base.layoutColumn.layoutColumnMargin') }}</div>
              <div class="linkage" @click="handLinkage">
                <LinkIcon :color="linkage ? '#0052d9' : '#333'" size="large" />
              </div>
              <div class="padding-left">
                <t-form-item :label="t('base.layoutColumn.topPadding')">
                  <t-input-number v-model="formData.topPadding" suffix="px" @change="changePadding"></t-input-number>
                </t-form-item>
                <div class="padding-icon">
                  <ConstraintIcon />
                </div>

                <t-form-item :label="t('base.layoutColumn.bottomPadding')">
                  <t-input-number v-model="formData.bottomPadding" suffix="px"></t-input-number>
                </t-form-item>
              </div>

              <div class="padding-left">
                <t-form-item :label="t('base.layoutColumn.leftPadding')">
                  <t-input-number v-model="formData.leftPadding" suffix="px"></t-input-number>
                </t-form-item>
                <div class="padding-icon">
                  <ConstraintIcon />
                </div>

                <t-form-item :label="t('base.layoutColumn.rightPadding')">
                  <t-input-number v-model="formData.rightPadding" suffix="px"></t-input-number>
                </t-form-item>
              </div>
            </div>


          </div>

        </t-form>
      </div>
    </modal>
  </div>
</template>

<script setup lang="ts">
import { BubbleMenu } from '@tiptap/vue-3'
import { ComponentLayoutIcon, DeleteIcon, LinkIcon, ConstraintIcon } from 'tdesign-icons-vue-next'
import type { Instance } from 'tippy.js'
import { v4 as uuid } from 'uuid'


const { options, editor, container } = useStore()
const visible = ref(false)
const formData = ref({
  layout: '1',
  leftWidth: 1,
  rightWidth: 1,
  centerWidth: 1,
  borderOpen: false,
})
const linkage = ref(false)
const layoutColumnNum = ref(2);
// 气泡菜单
const tippyOpitons = $ref<Partial<Instance>>({
  appendTo: 'parent',
  maxWidth: 580,
  zIndex: 99,
  offset: [0, 8],
  getReferenceClientRect: () => {
    const renderContainer = getRenderContainer(editor.value, 'layout-columns')
    const rect = renderContainer?.getBoundingClientRect() || new DOMRect(-1000, -1000, 0, 0)
    return rect;
  },
  popperOptions: {
    modifiers: [{ name: 'flip', enabled: false }],
  },
})

const handLinkage = () => {
  linkage.value = !linkage.value
  if (linkage.value) {
    return MessagePlugin.success(t('base.layoutColumn.openlinkage'))
  } else {
    return MessagePlugin.error(t('base.layoutColumn.closelinkage'))
  }
}

const changePadding = () => {
  if (linkage.value) {
    formData.value.rightPadding = formData.value.topPadding
    formData.value.leftPadding = formData.value.topPadding
    formData.value.bottomPadding = formData.value.topPadding
  }

}

// 分栏删除
const deleteLayoutColumns = () => {
  editor?.value.chain().focus().deleteNode("layoutColumn").run()
}

// 打开编辑弹窗
const openEditModal = () => {
  visible.value = true

  const { tr } = editor?.value.view.state
  const $pos = tr.selection.$anchor
  const widthArr = []
  const styleArr = []
  for (let { depth } = $pos; depth > 0; depth -= 1) {
    const node = $pos.node(depth)
    if (node.type.name === 'layoutColumn') {
      layoutColumnNum.value = node.childCount
      node.forEach((columnItem, offset, i) => {
        if (columnItem.type.name === 'columnItem') {
          widthArr.push(columnItem.attrs.width)
        }

        if (columnItem.attrs.styleText.includes('undefined')) {
          columnItem.attrs.styleText = columnItem.attrs.styleText.replaceAll('undefined', '0');
        }

        if (layoutColumnNum.value === 2 && i === 0) {
          styleArr.push(columnItem.attrs.styleText)
        }
        if (layoutColumnNum.value === 3 && i === 1) {
          styleArr.push(columnItem.attrs.styleText)
        }
      })
    }
  }
  //updateLayoutColumnsWidth([10, 90])
  // console.log("🚀 ~ columnBubble.vue:44 ~ openEditModal ~ widthArr:", widthArr, styleArr, layoutColumnNum.value)
  if (layoutColumnNum.value === 2) {
    formData.value.layout = '1'
    formData.value.leftWidth = widthArr[0]
    rightWidthComputed.value = 100 - widthArr[0]
    const parsedStyles = parseCSSDeclarations(styleArr);
    // console.log("🚀 ~ columnBubble.vue:44 ~ openEditModal ~ parsedStyles:", parsedStyles)
    if (parsedStyles['border-width-right']) {
      formData.value.borderOpen = true
    }
    formData.value.borderStyle = parsedStyles ? parsedStyles['border-style-right'] : '';
    formData.value.borderColor = parsedStyles['border-color-right'];
    formData.value.borderWidth = parsedStyles['border-width-right']?.replace(/px$/, "") || 0;
    formData.value.topPadding = parsedStyles['padding-top']?.replace(/px$/, "") || 0;
    formData.value.bottomPadding = parsedStyles['padding-bottom']?.replace(/px$/, "") || 0;
    formData.value.leftPadding = parsedStyles['padding-left']?.replace(/px$/, "") || 0;
    formData.value.rightPadding = parsedStyles['padding-right']?.replace(/px$/, "") || 0;

  }
  if (layoutColumnNum.value === 3) {
    formData.value.layout = '2'
    formData.value.leftWidth = widthArr[0]
    formData.value.centerWidth = widthArr[1]
    rightWidthComputed.value = 100 - widthArr[0] - widthArr[1]
    const parsedStyles = parseCSSDeclarations(styleArr);
    if (parsedStyles['border-width-right']) {
      formData.value.borderOpen = true
    }
    formData.value.borderStyle = parsedStyles['border-style-right'];
    formData.value.borderColor = parsedStyles['border-color-right'];
    formData.value.borderWidth = parsedStyles['border-width-right']?.replace(/px$/, "") || 0;
    formData.value.topPadding = parsedStyles['padding-top']?.replace(/px$/, "") || 0;
    formData.value.bottomPadding = parsedStyles['padding-bottom']?.replace(/px$/, "") || 0;
    formData.value.leftPadding = parsedStyles['padding-left']?.replace(/px$/, "") || 0;
    formData.value.rightPadding = parsedStyles['padding-right']?.replace(/px$/, "") || 0;
  }




}


function parseCSSDeclarations(styles) {
  const result = {};

  // 处理每个样式字符串
  styles.forEach(styleString => {
    // 按分号分割声明
    const declarations = styleString.split(';');
    declarations.forEach(declaration => {
      declaration = declaration.trim();
      if (!declaration) return; // 跳过空声明

      // 分割属性名和值
      const [property, value] = declaration.split(':');
      const propName = property.trim();
      const propValue = value.trim();

      // 处理边框方向属性
      if (propName.startsWith('border-')) {
        const direction = propName.split('border-')[1];
        const values = propValue.split(/\s+/);
        if (values.length === 3) {
          result[`border-width-${direction}`] = values[0];
          result[`border-style-${direction}`] = values[1];
          result[`border-color-${direction}`] = values[2];
        }
      }
      // 处理内边距/外边距
      else if (['padding', 'margin'].includes(propName)) {
        const values = propValue.split(/\s+/);
        result[`${propName}-top`] = values[0] || 0;
        result[`${propName}-right`] = values[1] || 0;
        result[`${propName}-bottom`] = values[2] || 0;
        result[`${propName}-left`] = values[3] || 0;
      }
      // 其他属性直接保留
      else {
        result[propName] = propValue;
      }
    });
  });


  // console.log("🚀 ~ columnBubble.vue:303 ~ parseCSSDeclarations ~ result:", result)
  return result;
}


const borderOpenChange = (val) => {
  formData.value.borderOpen = val
}

// 示例用法


const handleConfirm = () => {
  if (formData.value.layout === '1') {
    if (formData.value.borderOpen) {
      if (formData.value.borderWidth < 1) return MessagePlugin.error('边框宽度不能小于0')
      updateLayoutColumnsWidth([formData.value.leftWidth, formData.value.rightWidth],
        `border-right: ${formData.value.borderWidth}px ${formData.value.borderStyle} ${formData.value.borderColor};padding:${formData.value.topPadding}px ${formData.value.rightPadding}px ${formData.value.bottomPadding}px ${formData.value.leftPadding}px;`,
      )
    }
    else {
      updateLayoutColumnsWidth([formData.value.leftWidth, formData.value.rightWidth], `padding:${formData.value.topPadding}px ${formData.value.rightPadding}px ${formData.value.bottomPadding}px ${formData.value.leftPadding}px;`)
    }
  } else {
    if (formData.value.borderOpen) {
      if (formData.value.borderWidth < 1) return MessagePlugin.error('边框宽度不能小于0')
      updateLayoutColumnsWidth([formData.value.leftWidth, formData.value.centerWidth, formData.value.rightWidth],
        `border-left: ${formData.value.borderWidth}px ${formData.value.borderStyle} ${formData.value.borderColor};border-right: ${formData.value.borderWidth}px ${formData.value.borderStyle} ${formData.value.borderColor};padding:${formData.value.topPadding}px ${formData.value.rightPadding}px ${formData.value.bottomPadding}px ${formData.value.leftPadding}px;`,
      )
    }
    else {
      updateLayoutColumnsWidth([formData.value.leftWidth, formData.value.centerWidth, formData.value.rightWidth], `padding:${formData.value.topPadding}px ${formData.value.rightPadding}px ${formData.value.bottomPadding}px ${formData.value.leftPadding}px;`)
    }

  }

  visible.value = false
}

const backgroundColorChange = (e) => {
  formData.value.borderColor = e
}

const rightWidthComputed = computed(() => {
  if (formData.value.layout === '1') {
    formData.value.rightWidth = 100 - formData.value.leftWidth
  } else {
    formData.value.rightWidth = 100 - formData.value.leftWidth - formData.value.centerWidth
  }

  return formData.value.rightWidth
})

// 调整分栏布局
const updateLayoutColumnsWidth = (widthArr, styleString) => {
  if (layoutColumnNum.value != widthArr.length) {
    console.error('分栏数量不一致')
    return
  }
  editor.value
    ?.chain()
    .focus()
    .command(({ tr, dispatch }) => {
      if (dispatch) {
        const $pos = tr.selection.$anchor
        for (let { depth } = $pos; depth > 0; depth -= 1) {
          const node = $pos.node(depth)
          // console.log('node', node, $pos.before(depth), $pos.after(depth))
          if (node.type.name === 'layoutColumn') {
            const from = $pos.before(depth)
            node.forEach((columnItem, offset, i) => {
              if (columnItem.type.name === 'columnItem') {
                if (formData.value.layout == 1 && i === 0) {
                  tr.setNodeMarkup(from + offset + 1, undefined, { ...columnItem.attrs, width: widthArr[i], styleText: styleString })
                  return true
                } else {
                  tr.setNodeMarkup(from + offset + 1, undefined, { ...columnItem.attrs, width: widthArr[i], styleText: `padding:${formData.value.topPadding}px ${formData.value.rightPadding}px ${formData.value.bottomPadding}px ${formData.value.leftPadding}px;` })
                }


                if (formData.value.layout == 2 && i === 1) {
                  tr.setNodeMarkup(from + offset + 1, undefined, { ...columnItem.attrs, width: widthArr[i], styleText: styleString })
                  return true
                } else {
                  tr.setNodeMarkup(from + offset + 1, undefined, { ...columnItem.attrs, width: widthArr[i], styleText: `padding:${formData.value.topPadding}px ${formData.value.rightPadding}px ${formData.value.bottomPadding}px ${formData.value.leftPadding}px;` })
                }

                // if (columnItem.content.size === 3 && i === 1) {
                //   tr.setNodeMarkup(from + offset + 1, undefined, { width: widthArr[i], styleText: styleString })
                // } else {
                //   tr.setNodeMarkup(from + offset + 1, undefined, { width: widthArr[i], styleText: `padding:${formData.value.topPadding}px ${formData.value.rightPadding}px ${formData.value.bottomPadding}px ${formData.value.leftPadding}px` })
                // }


              }
            })
            return true;
          }
        }
      }
      return true
    })
    .run()
}

// 确定渲染容器
const getRenderContainer = (editor: Editor, nodeType: string) => {
  const {
    view,
    state: {
      selection: { from },
    },
  } = editor

  const elements = document.querySelectorAll('.has-focus')
  const elementCount = elements.length
  const innermostNode = elements[elementCount - 1]
  const element = innermostNode

  if (
    (element && element.getAttribute('data-type') && element.getAttribute('data-type') === nodeType) ||
    (element && element.classList && element.classList.contains(nodeType))
  ) {
    return element
  }

  const node = view.domAtPos(from).node as HTMLElement
  let container: HTMLElement | null = node

  if (!container.tagName) {
    container = node.parentElement
  }

  while (
    container &&
    !(container.getAttribute('data-type') && container.getAttribute('data-type') === nodeType) &&
    !container.classList.contains(nodeType)
  ) {
    container = container.parentElement
  }

  return container
}
</script>

<style lang="less" scoped>
.column-container {
  position: relative;
  right: 0;
  height: 35px;
  background-color: #fff;
  border-radius: 5px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);

  .edit-layout {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    border-right: 1px solid #d4d4d4;
    padding: 0 10px;
  }

  .del-layout {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    padding: 0 10px;
  }
}

.form-header-title {
  font-size: 16px;
  font-weight: bolder;
  margin-bottom: 10px;
  color: #333;
}

.form-item {
  margin: 10px 0;
}

.form-alert {
  margin-top: 10px;
}

.padding-main {
  display: flex;
  align-content: center;
  justify-content: center;
  padding: 10px;
  position: relative;
  border: 1px solid #dcdcdc;
  border-radius: 5px;

  .padding-title {
    position: absolute;
    top: -15px;
    left: 10px;
    background-color: #fff;
    font-size: 16px;
    font-weight: bold;
    padding: 0 5px;
    color: #333;
  }

  .linkage {
    position: absolute;
    top: 35%;
    left: 10px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    transform: rotate(135deg);
    cursor: pointer;
  }
}

.padding-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;

  .umo-form__item {
    margin-bottom: 0;
  }

  .padding-icon {
    margin: 10px 0;
  }
}

.transparent {
  background:
    linear-gradient(to bottom right,
      transparent 49%,
      #ff0000 49%,
      #ff0000 51%,
      transparent 51%),
    #f0f0f0 !important;
}
</style>
