<template>
  <menus-button
    ico="image-draggable"
    :text="t('bubbleMenu.image.draggable')"
    :menu-active="editor?.getAttributes('image')?.draggable || editor?.getAttributes('imageLayout')?.draggable"
    @menu-click="toggleDraggable"
  />
</template>

<script setup lang="ts">
import { getSelectionNode } from '@/extensions/selection'

const { editor } = useStore()

const toggleDraggable = () => {
  const image = editor.value ? getSelectionNode(editor.value) : null
  if (image) {
    editor.value?.commands.updateAttributes(image.type, {
      draggable: !image.attrs.draggable,
    })
  }
  const imageLayout = editor.value ? getSelectionNode(editor.value) : null
  if (imageLayout) {
    editor.value?.commands.updateAttributes(imageLayout.type, {
      draggable: !imageLayout.attrs.draggable,
    })
  }
}
</script>
