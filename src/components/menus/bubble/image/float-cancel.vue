<template>
  <menus-button
    v-if="
        editor?.can().chain().focus().setNodeFloat('left').run() ||
        editor?.can().chain().focus().setNodeFloat('right').run()
      "
    ico="float-cancel"
    :text="t('base.float.cancel')"
    hide-text
    :menu-active="
        editor?.isActive({ nodeFloat: 'cancel' })
      "
    @menu-click="setFloatUnset"
  />
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
const setFloatUnset = () => {
  if (editor.value?.can().chain().focus().setNodeFloat('unset').run()) {
    editor.value.chain().focus().setNodeFloat('unset').run()
  }
}
</script>