<template>
  <menus-button
    ico="float-left"
    :text="t('base.float.left')"
    hide-text
    :menu-active="
        editor?.isActive({ nodeFloat: 'left' })
      "
    :disabled="
        !editor?.can().chain().focus().setNodeFloat('left').run()
      "
    @menu-click="setFloatLeft"
  />
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
const setFloatLeft = () => {
  if (editor.value?.can().chain().focus().setNodeFloat('left').run()) {
    editor.value.chain().focus().setNodeFloat('left').run()
  }
}
</script>