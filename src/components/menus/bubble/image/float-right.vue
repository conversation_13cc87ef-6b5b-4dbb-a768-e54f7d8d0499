<template>
  <menus-button
    ico="float-right"
    :text="t('base.float.right')"
    hide-text
    :menu-active="
        editor?.isActive({ nodeFloat: 'right' })
      "
    :disabled="
        !editor?.can().chain().focus().setNodeFloat('right').run()
      "
    @menu-click="setFloatRight"
  />
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
const setFloatRight = () => {
  if (editor.value?.can().chain().focus().setNodeFloat('right').run()) {
    editor.value.chain().focus().setNodeFloat('right').run()
  }
}
</script>