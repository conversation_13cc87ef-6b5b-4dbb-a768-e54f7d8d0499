<template>
  <template v-if="
    editor?.isActive('toc') ||
    //editor?.isActive('pagination') ||
    editor?.isActive('horizontalRule') ||
    editor?.isActive('imageGallery') ||
    editor?.isActive('remarkInline') ||
    editor?.isActive('imageInLine') ||
    editor?.isActive('audioHanlin') ||

    editor?.getAttributes('image').error
  ">
    <!-- <menus-bubble-node-delete /> -->
  </template>
  <template v-else-if="
    editor?.isActive('image') && !editor?.getAttributes('image').error
  ">
    <menus-toolbar-base-align-left />
    <menus-toolbar-base-align-center />
    <menus-toolbar-base-align-right />
    <div class="umo-bubble-menu-divider"></div>
    <menus-bubble-image-flip />
    <menus-bubble-image-proportion />
    <!--    <menus-bubble-image-draggable />-->
    <!--    <menus-bubble-image-reset />-->
    <div class="umo-bubble-menu-divider"></div>
    <menus-bubble-image-remove-background v-if="
      editor?.getAttributes('image')?.type === 'image' ||
      ['image/png', 'image/jpeg'].includes(
        editor?.getAttributes('image')?.type,
      )
    " />
    <menus-bubble-image-preview v-if="
      editor?.getAttributes('image')?.type === 'image' ||
      ['image/png', 'image/jpeg'].includes(
        editor?.getAttributes('image')?.type,
      )
    " />
    <!-- <menus-bubble-image-open /> -->
    <div class="umo-bubble-menu-divider"></div>

    <menus-bubble-node-duplicate v-if="
      editor?.isActive('image') && editor?.getAttributes('image').draggable
    " />
    <!-- <menus-bubble-node-tofile
      v-if="editor?.getAttributes('image').previewType !== null"
    /> -->
    <menus-bubble-node-delete />
  </template>
  <template v-else-if="
    editor?.isActive('imageLayout') &&
    !editor?.getAttributes('imageLayout').error
  ">
    <menus-toolbar-base-align-left />
    <menus-toolbar-base-align-center />
    <menus-toolbar-base-align-right />
    <div class="umo-bubble-menu-divider"></div>
    <menus-bubble-image-flip />
    <menus-bubble-image-proportion />
    <!--    <menus-bubble-image-draggable />-->
    <!--    <menus-bubble-image-reset />-->
    <div class="umo-bubble-menu-divider"></div>
    <menus-bubble-image-remove-background />
    <menus-bubble-image-preview />
    <!-- <menus-bubble-image-open /> -->
    <div class="umo-bubble-menu-divider"></div>
    <menus-bubble-image-edit />
    <menus-bubble-node-duplicate />
    <!-- <menus-bubble-node-tofile /> -->
    <menus-bubble-node-delete />
  </template>
  <template v-else-if="
    (editor?.isActive('imageIcon') && !editor?.getAttributes('imageIcon').error) || editor?.isActive('formulaInLine')
  ">
    <menus-bubble-image-flip />
    <menus-bubble-image-proportion />
    <!-- <menus-bubble-image-float-left />
    <menus-bubble-image-float-right /> -->
    <!-- <menus-bubble-image-float-cancel /> -->
    <div class="umo-bubble-menu-divider"></div>
    <menus-bubble-image-remove-background />
    <!-- <menus-bubble-image-open /> -->
    <div class="umo-bubble-menu-divider"></div>
    <menus-bubble-image-edit />
    <menus-bubble-node-duplicate />
    <menus-bubble-node-delete />
  </template>
  <template v-else-if="
    editor?.isActive('video')">
    <menus-toolbar-base-align-left />
    <menus-toolbar-base-align-center />
    <menus-toolbar-base-align-right />
    <div class="umo-bubble-menu-divider"></div>
    <!-- <menus-bubble-file-download
      v-if="
        editor?.isActive('file') ||
        editor?.isActive('video') ||
        editor?.isActive('audio')
      "
    /> -->
    <!-- <menus-bubble-node-tofile
      v-if="editor?.isActive('video') || editor?.isActive('audio')"
    /> -->
    <menus-bubble-node-delete />
  </template>
  <template v-else-if="editor?.isActive('table')">
    <menus-toolbar-table-cells-align />
    <menus-toolbar-table-cells-background />
    <!-- <menus-toolbar-table-border-color  /> -->
    <div class="umo-bubble-menu-divider"></div>
    <menus-toolbar-table-add-row-before />
    <menus-toolbar-table-add-row-after />
    <menus-toolbar-table-add-column-before />
    <menus-toolbar-table-add-column-after />
    <div class="umo-bubble-menu-divider"></div>
    <menus-toolbar-table-delete-row />
    <menus-toolbar-table-delete-column />
    <div class="umo-bubble-menu-divider"></div>
    <menus-toolbar-table-merge-cells />
    <menus-toolbar-table-split-cell />
  </template>
  <template v-else-if="editor?.isActive('codeBlock')">
    <menus-bubble-code-languages />
    <menus-bubble-code-themes />
    <div class="umo-bubble-menu-divider"></div>
    <menus-bubble-code-line-numbers />
    <menus-bubble-code-word-wrap />
    <menus-bubble-code-run-state />
    <div class="umo-bubble-menu-divider"></div>
    <menus-bubble-code-copy />
    <menus-bubble-node-delete />
  </template>
  <template v-else-if="
    editor?.isActive('resourceCover') ||
    editor?.isActive('interaction') ||
    editor?.isActive('tablePlus') ||
    editor?.isActive('paperWrapping') ||
    editor?.isActive('jointHeader') ||
    editor?.isActive('chapterHeader') ||
    editor?.isActive('papers') ||
    editor?.isActive('psychologyHealth') ||
    editor?.isActive('questions')
  ">
  </template>
  <!-- <template v-else-if="editor?.isActive('layoutColumn')">
    <menus-bubble-node-delete />
  </template> -->
  <!-- 
  <template v-else-if="editor?.isActive('paragraph')">
   
  </template> -->
  <template v-else>
    <template v-if="options.assistant?.enabled">
      <menus-bubble-assistant />
      <div class="umo-bubble-menu-divider"></div>
    </template>
    <menus-toolbar-base-font-size :select="false" />
    <div class="umo-bubble-menu-divider"></div>
    <menus-toolbar-base-bold />
    <menus-toolbar-base-italic />
    <menus-toolbar-base-underline />
    <menus-toolbar-base-strike />
    <menus-toolbar-base-format-painter />
    <div class="umo-bubble-menu-divider"></div>
    <menus-toolbar-base-align-dropdown />
    <div class="umo-bubble-menu-divider"></div>
    <menus-toolbar-base-color />
    <menus-toolbar-base-background-color />
    <!-- <menus-toolbar-base-highlight /> -->
    <div class="umo-bubble-menu-divider"></div>
    <template v-if="editor?.isActive('textBox')">
      <menus-bubble-text-box-border />
      <menus-bubble-text-box-background />
      <div class="umo-bubble-menu-divider"></div>
    </template>
    <template v-if="options.document?.enableComment">
      <menus-bubble-comment />
      <div class="umo-bubble-menu-divider"></div>
    </template>

    <slot name="bubble_menu" />
  </template>
</template>

<script setup lang="ts">


const { options, editor } = useStore()
</script>

<style lang="less" scoped>
.umo-bubble-menu-divider {
  width: 1px;
  border-right: solid 1px var(--umo-border-color-light);
  height: 16px;
  margin: 0 10px 0 5px;

  &:last-child:is(.umo-bubble-menu-divider) {
    display: none;
  }
}
</style>
