<template>
  <t-dropdown :attach="`${container} .umo-page-container`" placement="bottom-right"
    overlay-class-name="umo-block-menu-dropdown" trigger="click" :destroy-on-close="false" :popup-props="popupProps">
    <menus-button class="umo-block-menu-button" :menu-active="menuActive" ico="block-menu" hide-text />
    <t-dropdown-menu>
      <t-dropdown-item class="umo-block-menu-group-name" disabled>
        {{ t('blockMenu.backgroundTitle') }}
      </t-dropdown-item>
      <t-dropdown-item>
        <menus-toolbar-base-backgroundImages />
      </t-dropdown-item>
      <t-dropdown-item>
        <menus-toolbar-base-backgroundBorder />
      </t-dropdown-item>
      <t-dropdown-item>
        <menus-toolbar-base-movebackground />
      </t-dropdown-item>
      <t-dropdown-item>
        <menus-button ico="backgroundOptions" :text="t('base.backgroundOption')" :tooltip="false" />
        <t-dropdown-menu>
          <t-dropdown-item v-for="(item, index) in options" :key="index" @click="handleClick(item)">
            <div class="option-item">
              <CheckIcon v-if="item.value != 'del' &&
    editor?.isActive({ backgroundSize: item.value })
    " style="color: #06d279; margin-right: 5px" size="large" />{{ item.label }}
            </div>
          </t-dropdown-item>
        </t-dropdown-menu>
      </t-dropdown-item>

      <t-dropdown-item>
        <t-popup placement="right-bottom" :attach="`${container} .umo-page-container`" :destroy-on-close="false">
          <div class="umo-color-picker-more">
            <div class="umo-color-picker-more-menu">
              <icon :style="{ color }" name="palette-color" />
              <span v-text="t('base.containerColor')"></span>
            </div>
            <div class="umo-color-picker-more-arrow">
              <icon name="arrow-down" />
            </div>
          </div>
          <template #content>
            <div style="padding: 10px">
              <color-picker :default-color="defaultColor" @change="colorChange" />
            </div>
          </template>
        </t-popup>
        <!-- <menus-toolbar-base-containerColor /> -->
      </t-dropdown-item>

      <t-dropdown-item class="umo-block-menu-group-name" disabled>
        {{ t('blockMenu.common') }}
      </t-dropdown-item>
      <t-dropdown-item>
        <menus-button ico="node-clear-format" :text="t('blockMenu.clearFormat')" :tooltip="false"
          @menu-click="clearTextFormatting" />
      </t-dropdown-item>

      <t-dropdown-item>
        <menus-button ico="node-duplicate" :text="t('blockMenu.duplicate')" :tooltip="false" @menu-click="copyNode" />
      </t-dropdown-item>
      <t-dropdown-item divider>
        <menus-button ico="paste-node" :text="t('blockMenu.pasteNode')" :tooltip="false" @menu-click="pasteNode" />
      </t-dropdown-item>
      <t-dropdown-item>
        <menus-button ico="node-copy" :text="t('blockMenu.copy')" :tooltip="false" @menu-click="copyNodeToClipboard" />
      </t-dropdown-item>
      <t-dropdown-item>
        <menus-button ico="node-cut" :text="t('blockMenu.cut')" :tooltip="false" @menu-click="cutNodeToClipboard" />
      </t-dropdown-item>
      <t-dropdown-item class="umo-delete-node">
        <menus-button ico="node-delete-2" :text="t('blockMenu.delete')" :tooltip="false" @menu-click="deleteNode" />
      </t-dropdown-item>
    </t-dropdown-menu>
  </t-dropdown>
</template>

<script setup lang="ts">
import type { Node } from '@tiptap/pm/model'
import { CheckIcon } from 'tdesign-icons-vue-next'

import { getSelectionNode } from '@/extensions/selection'
const props = defineProps({
  defaultColor: {
    type: String,
    default: '#000',
  },
  modeless: {
    type: Boolean,
    default: false,
  },
})
const { container, editor, blockMenu } = useStore()
const color = $ref(props.defaultColor)
let currentColor = $ref<string | undefined>()

let menuActive = $ref(false)

const popupProps = {
  onVisibleChange(visible: boolean) {
    editor.value.commands.focus()
    blockMenu.value = visible
    menuActive = visible
  },
}
const emits = defineEmits(['change'])
const colorChange = (color: string) => {
  currentColor = color
  if (props.modeless) {
    emits('change', currentColor)
    return
  }
  if (color === '') {
    editor.value?.chain().focus().setContainerColor('transparent').run()
  } else {
    editor.value?.chain().focus().setContainerColor(color).run()
  }
}

const options = [
  { label: t('base.backgroundOptions.remove'), value: 'del', isChecked: false },
  { label: t('base.backgroundOptions.fill'), value: 'cover', isChecked: false },
  {
    label: t('base.backgroundOptions.adapt'),
    value: 'contain',
    isChecked: false,
  },
  {
    label: t('base.backgroundOptions.stretch'),
    value: '100% 100%',
    isChecked: false,
  },
  { label: t('base.backgroundOptions.tile'), value: 'lay', isChecked: false },
  {
    label: t('base.backgroundOptions.widthStretching'),
    value: '100% auto',
    isChecked: false,
  },
  {
    label: t('base.backgroundOptions.heightStretching'),
    value: 'auto 100%',
    isChecked: false,
  },
]

const handleClick = (itemValue: any) => {
  options.forEach((item) => {
    item.isChecked = false
  })
  const selectedOption = options.find((item) => item.value === itemValue.value)
  if (selectedOption) {
    selectedOption.isChecked = true
    editor?.value.chain().focus().setBackgroundSize(itemValue.isChecked).run()
  }

  editor?.value.chain().focus().setBackgroundSize(itemValue.value).run()
}

const clearTextFormatting = () => {
  editor.value?.chain().focus().setCurrentNodeSelection().unsetAllMarks().run()
}
const copyNodeToClipboard = () => {
  editor.value?.commands.setCurrentNodeSelection()
  document.execCommand('copy')
}
const cutNodeToClipboard = () => {
  editor.value?.commands.setCurrentNodeSelection()
  document.execCommand('cut')
}
const duplicateNode = () => {
  const selectionNode = editor.value ? getSelectionNode(editor.value) : null
  console.log(selectionNode)
  const getPosition = () => {
    let point = 0
    editor.value?.state.doc.descendants((node: Node, pos: number) => {
      if (node === selectionNode) {
        point = pos + node.nodeSize // 返回节点结束位置
      }
    })
    return point
  }
  const copeNode = selectionNode?.type.create(
    {
      ...selectionNode.attrs,
      id: null,
    },
    selectionNode.content,
    selectionNode.marks,
  )
  editor.value?.commands.insertContentAt(getPosition(), copeNode?.toJSON())
}

const copyNode = () => {
  editor.value
    ?.chain()
    .focus()
    .copyNode()
    .run()
}

const pasteNode = () => {
  editor.value?.chain()
    .focus()
    .pasteNode()
    .run()
}

const deleteNode = () => {
  editor.value
    ?.chain()
    .focus()
    .command(({ tr, dispatch }) => {
      if (dispatch) {
        const $pos = tr.selection.$anchor
        if ($pos.depth == 1) {
          const from = $pos.pos
          const to = from + 1
          tr.delete(from, to)
        } else {
          const from = $pos.before(2)
          const to = $pos.after(2)
          tr.delete(from, to)
        }
      }
      return true
    })
    .focus()
    .run()
}
</script>

<style lang="less" scoped>
.option-item {
  padding: 0 10px;
  height: 30px;
  line-height: 30px;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }
}

.umo-color-picker {
  &-container {
    width: 236px;
  }

  &-default-button {
    .umo-button {
      height: 28px;
    }
  }

  &-group {
    display: flex;
    flex-wrap: wrap;
    margin: 8px 0;
    gap: 4px;

    &-title {
      color: var(--umo-text-color-light);
      font-size: 12px;
      margin: 5px 0 2px;
    }
  }

  &-item {
    width: 20px;
    height: 20px;
    border: solid 1px rgba(0, 0, 0, 0.1);
    margin-bottom: 2px;
    flex-basis: 20px;
    box-sizing: border-box;
    transition: all 0.2s;
    cursor: pointer;
    border-radius: 3px;

    &:hover {
      transform: scale(1.1);
      border-color: rgba(0, 0, 0, 0.3);
    }
  }

  &-divider {
    height: 1px;
    background-color: var(--umo-border-color-light);
    margin: 10px 0;
  }

  &-more {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    cursor: pointer;

    &:hover,
    &.active {
      background-color: var(--td-bg-color-container-hover);
      border-radius: var(--umo-radius);
    }

    &-menu {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: var(--umo-text-color);
      cursor: pointer;

      .umo-icon {
        margin-right: 5px;
        font-size: 18px;
      }
    }

    &-arrow {
      .umo-icon {
        margin-right: -6px;
        transform: rotate(-90deg);
      }
    }
  }
}
</style>
