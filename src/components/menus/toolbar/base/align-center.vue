<template>
  <template v-if="source !== 'tool'">
    <!-- shortcut="Ctrl+Shift+E" -->
    <menus-button :text="t('base.align.center')" ico="align-center" hide-text :menu-active="editor?.isActive({ textAlign: 'center' }) ||
    editor?.isActive({ nodeAlign: 'CENTER' })
    " :disabled="!editor?.can().chain().focus().setTextAlign('center').run() &&
    !editor?.can().chain().focus().setNodeAlign('center').run()
    " @menu-click="setAlignCenter" />
  </template>

  <template v-else>
    <div :class="editor?.isActive({ textAlign: 'center' }) || editor?.isActive({ textAlign: 'CENTER' }) ||
    editor?.isActive({ nodeAlign: 'center' })
    ? 'chectbold fount-bold'
    : 'fount-bold'
    " @click="setAlignCenter">
      <div>
        <!-- shortcut="Ctrl+Shift+E" -->
        <menus-button :text="t('base.align.center')" ico="align-center" hide-text :disabled="!editor?.can().chain().focus().setTextAlign('center').run() &&
    !editor?.can().chain().focus().setNodeAlign('center').run()
    " />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.align.center') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
const setAlignCenter = () => {
  if (editor.value?.can().chain().focus().setTextAlign('center').run()) {
    editor.value.chain().focus().setTextAlign('center').run()
  }
  if (editor.value?.can().chain().focus().setNodeAlign('center').run()) {
    editor.value.chain().focus().setNodeAlign('center').run()
  }
}
</script>
