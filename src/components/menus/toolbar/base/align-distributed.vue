<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="align-distributed"
      :text="t('base.align.distributed')"
      shortcut="Ctrl+Shift+D"
      hide-text
      :menu-active="editor?.isActive({ textAlign: 'distributed' })"
      :disabled="
        !editor?.can().chain().focus().setTextAlign('distributed').run()
      "
      @menu-click="editor?.chain().focus().setTextAlign('distributed').run()"
    />
  </template>

  <template v-else>
    <div
      @click="editor?.chain().focus().setTextAlign('distributed').run()"
      :class="
        editor?.isActive({ textAlign: 'distributed' })
          ? 'chectbold fount-bold'
          : 'fount-bold'
      "
    >
      <div>
        <menus-button
          ico="align-distributed"
          :text="t('base.align.distributed')"
          shortcut="Ctrl+Shift+D"
          hide-text
          :disabled="
            !editor?.can().chain().focus().setTextAlign('distributed').run()
          "
        />
      </div>

      <div class="fount-bold-text __ellipsis">
        {{ t('base.align.distributed') }}
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
