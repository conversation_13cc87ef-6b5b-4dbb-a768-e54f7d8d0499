<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-27 11:44:35
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\align-justify.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="align-justify"
      :text="t('base.align.justify')"
      shortcut="Ctrl+Shift+J"
      hide-text
      :menu-active="editor?.isActive({ textAlign: 'justify' })"
      :disabled="!editor?.can().chain().focus().setTextAlign('justify').run()"
      @menu-click="editor?.chain().focus().setTextAlign('justify').run()"
    />
  </template>

  <template v-else>
    <div
      @click="editor?.chain().focus().setTextAlign('justify').run()"
      :class="
        editor?.isActive({ textAlign: 'justify' })
          ? 'chectbold fount-bold'
          : 'fount-bold'
      "
    >
      <div>
        <menus-button
          ico="align-justify"
          :text="t('base.align.justify')"
          shortcut="Ctrl+Shift+J"
          hide-text
          :disabled="
            !editor?.can().chain().focus().setTextAlign('justify').run()
          "
        />
      </div>

      <div class="fount-bold-text __ellipsis">
        {{ t('base.align.justify') }}
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
