<template>
  <template v-if="source !== 'tool'">
    <!-- shortcut="Ctrl+Shift+L" -->
    <menus-button ico="align-left" :text="t('base.align.left')" hide-text :menu-active="editor?.isActive({ textAlign: 'left' }) ||
    editor?.isActive({ textAlign: 'LEFT' }) ||
    editor?.isActive({ nodeAlign: 'flex-start' })
    " :disabled="!editor?.can().chain().focus().setTextAlign('left').run() &&
    !editor?.can().chain().focus().setNodeAlign('left').run()
    " @menu-click="setAlignLeft" /></template>

  <template v-else>
    <div :class="editor?.isActive({ textAlign: 'left' }) || editor?.isActive({ textAlign: 'LEFT' }) ||
    editor?.isActive({ nodeAlign: 'flex-start' })
    ? 'chectbold fount-bold'
    : 'fount-bold'
    " @click="setAlignLeft">
      <div>
        <!-- shortcut="Ctrl+Shift+L" -->
        <menus-button ico="align-left" :text="t('base.align.left')" hide-text :disabled="!editor?.can().chain().focus().setTextAlign('left').run() &&
    !editor?.can().chain().focus().setNodeAlign('left').run()
    " />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.align.left') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
const setAlignLeft = () => {
  if (editor.value?.can().chain().focus().setTextAlign('left').run()) {
    editor.value.chain().focus().setTextAlign('left').run()
  }
  if (editor.value?.can().chain().focus().setNodeAlign('flex-start').run()) {
    editor.value.chain().focus().setNodeAlign('flex-start').run()
  }
}
</script>
