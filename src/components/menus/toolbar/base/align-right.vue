<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-13 16:42:03
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\align-right.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <template v-if="source !== 'tool'">
    <!-- shortcut="Ctrl+Shift+R" -->
    <menus-button ico="align-right" :text="t('base.align.right')" hide-text :menu-active="editor?.isActive({ textAlign: 'right' }) ||
    editor?.isActive({ textAlign: 'RIGHT' }) ||
    editor?.isActive({ nodeAlign: 'flex-end' })
    " :disabled="!editor?.can().chain().focus().setTextAlign('right').run() &&
    !editor?.can().chain().focus().setNodeAlign('flex-end').run()
    " @menu-click="setAlignRight" />
  </template>

  <template v-else>
    <div :class="editor?.isActive({ textAlign: 'right' }) || editor?.isActive({ textAlign: 'RIGHT' }) ||
    editor?.isActive({ nodeAlign: 'flex-end' })
    ? 'chectbold fount-bold'
    : 'fount-bold'
    " @click="setAlignRight">
      <div>
        <!-- shortcut="Ctrl+Shift+R" -->
        <menus-button ico="align-right" :text="t('base.align.right')" hide-text :disabled="!editor?.can().chain().focus().setTextAlign('right').run() &&
    !editor?.can().chain().focus().setNodeAlign('flex-end').run()
    " />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.align.right') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
const setAlignRight = () => {
  if (editor.value?.can().chain().focus().setTextAlign('right').run()) {
    editor.value.chain().focus().setTextAlign('right').run()
  }
  if (editor.value?.can().chain().focus().setNodeAlign('flex-end').run()) {
    editor.value.chain().focus().setNodeAlign('flex-end').run()
  }
}
</script>
