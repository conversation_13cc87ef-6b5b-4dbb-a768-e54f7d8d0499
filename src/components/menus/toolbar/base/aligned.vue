<template>
  <div :class="'fount-bold'">
    <div>
      <menus-button
        :text="t('insert.aligned.text')"
        menu-type="dropdown"
        popup-handle="arrow"
        hide-text
        overlay-class-name="umo-columnLayout-dropdown"
      >
        <icon name="aligned" class="icon-aligned" />
        <template #dropmenu>
          <t-dropdown-menu>
            <t-dropdown-item
              v-for="item in options"
              :key="item.value"
              class="umo-text-columnLayout-menu"
              :value="item.value"
              @click="setAligned(item)"
            >
              {{ item.label }}
            </t-dropdown-item>
          </t-dropdown-menu>
        </template>
      </menus-button>
    </div>
    <div class="fount-bold-text __ellipsis">
      {{ t('insert.aligned.text') }}
    </div>
  </div>
</template>

<script setup lang="ts">
const { editor } = useStore()
const options = [
  {
    label: t('insert.aligned.top'),
    value: 'display:flex;flex-direction: column;justify-content: flex-start;',
  },
  {
    label: t('insert.aligned.center'),
    value: 'display:flex;flex-direction: column;justify-content: center;',
  },
  {
    label: t('insert.aligned.bottom'),
    value: 'display:flex;flex-direction: column;justify-content: flex-end;',
  },
]

const setAligned = (item: any) => {
  editor.value
    ?.chain()
    .focus()
    .setParagraphAlign({ styleText: item.value })
    .run()
}
</script>
