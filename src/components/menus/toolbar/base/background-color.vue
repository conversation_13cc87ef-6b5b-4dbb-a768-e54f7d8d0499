<template>
  <template v-if="source !== 'tool'">
    <div @click="setColorChange">
      <menus-button
        :text="text || t('base.bgColor')"
        menu-type="popup"
        popup-handle="arrow"
        hide-text
        :popup-visible="popupVisible"
        @toggle-popup="togglePopup"
      >
        <icon
          name="background-color"
          class="umo-icon-background-color"
          :style="{
            background: currentColor,
          }"
        />

        <!-- //background: editor?.getAttributes('highlight')?.color || currentColor, -->
        <template #content>
          <color-picker :default-color="defaultColor" @change="colorChange" />
        </template>
      </menus-button>
    </div>
  </template>
  <template v-else>
    <t-tooltip
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
    >
      <div
        :class="
          editor?.isActive('bgColor') ? 'chectbold fount-bold' : 'fount-bold'
        "
        @click="setColorChange"
      >
        <div>
          <menus-button
            :text="text || t('base.highlight.text')"
            menu-type="popup"
            popup-handle="arrow"
            hide-text
            :popup-visible="popupVisible"
            @toggle-popup="togglePopup"
          >
            <icon
              name="background-color"
              class="umo-icon-background-color"
              :style="{
                background: currentColor,
              }"
            />

            <template #content>
              <color-picker
                :default-color="defaultColor"
                @change="colorChange"
              />
            </template>
          </menus-button>
        </div>

        <div class="fount-bold-text __ellipsis">
          {{ t('base.highlight.text') }}
        </div>
      </div>
    </t-tooltip>
  </template>
</template>

<script setup lang="ts">
const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  modeless: {
    type: Boolean,
    default: false,
  },
  defaultColor: {
    type: String,
    default: '',
  },
  source: String,
})
const emits = defineEmits(['change'])

const { popupVisible, togglePopup } = usePopup()
const { editor } = useStore()

let currentColor = $ref<string | undefined>()
const setColorChange = () => {
  if (currentColor) {
    editor.value?.chain().focus().setHighlight({ color: currentColor }).run()
  }
}
const colorChange = (color: string) => {
  currentColor = color
  popupVisible.value = false

  if (props.modeless) {
    emits('change', currentColor)
    return
  }

  if (color === '') {
    editor.value?.chain().focus().unsetHighlight().run()
  } else {
    editor.value?.chain().focus().setHighlight({ color }).run()
  }
}
</script>

<style lang="less" scoped>
.umo-icon-background-color {
  border-radius: 2px;
}
</style>
