<template>
  <div style="display: flex" @click="openClick">
    <div>
      <menus-button
        ico="backgroundBorder"
        style="padding: 0 0 0 15px"
        :text="t('base.backgroundBorder.text')"
        hide-text
      ></menus-button>
    </div>
    <div style="flex: 1" class="fount-bold-text __ellipsis">
      {{ t('base.backgroundBorder.text') }}
    </div>
  </div>
  <t-dialog
    :visible="visible"
    :header="t('base.backgroundBorder.text')"
    :footer="false"
    attach="body"
    width="288px"
    @close="handleClose"
  >
    <div>
      <div class="label">{{ t('base.backgroundBorder.borderWidth') }}</div>
      <div>
        <t-input-number
          v-model="borderWidth"
          
          :min="0"
          allow-input-over-limit
          size="large"
          style="width: 100%"
        />
      </div>

      <div class="label">{{ t('base.backgroundBorder.borderRadius') }}</div>
      <div>
        <t-input-number
          v-model="borderRadius"
          
          :min="0"
          allow-input-over-limit
          size="large"
          style="width: 100%"
        />
      </div>
      <div class="label">{{ t('base.backgroundBorder.padding') }}</div>
      <div>
        <t-input-number
          v-model="borderPadding"
          
          :min="0"
          allow-input-over-limit
          size="large"
          style="width: 100%"
        />
      </div>
      <div class="label">{{ t('base.backgroundBorder.borderStyle') }}</div>
      <div>
        <t-select v-model="borderStyle" size="large">
            <t-option value="solid">
            <hr class="border-demo solid"></hr>
          </t-option>
          <t-option value="dotted">
            <hr class="border-demo dotted"></hr>
          </t-option>
          <t-option value="dashed">
            <hr class="border-demo dashed"></hr>
          </t-option>
        </t-select>
      </div>
      <div class="label">{{ t('base.backgroundBorder.borderColor') }}</div>
      <div>
        <color-picker :default-color="defaultColor" @change="colorChange" />
      </div>

      <div style="margin-top: 30px">
        <t-button block size="large" @click="handleSubmit">{{ t('base.backgroundBorder.submit') }}</t-button>
      </div>
    </div>
  </t-dialog>
</template>
<script setup>
const visible = ref(false)
const borderWidth=ref(0)
const borderStyle=ref('solid')
const borderRadius=ref(0)
const borderPadding=ref(0)
const defaultColor=ref('#000')
const { editor } = useStore()
const openClick = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
}

const emit = defineEmits(['change'])
const colorChange= (color)=>{
   defaultColor.value=color
}
const handleSubmit = () => {
  
    editor.value
    ?.chain()
    .focus()
    .setBackgroundBorder({
      borderColor: defaultColor.value,
      borderWidth: borderWidth.value,
      borderStyle: borderStyle.value,
      borderRadius: borderRadius.value,
      borderPadding: borderPadding.value,
    })
    .run()
    visible.value = false
    
   
}
</script>

<style lang="less" scoped>
.label {
  margin: 5px 0;
  color: #666;
}

.border-demo {
  width: 100%;
  height: 1px;
  margin: 5px 0;
  :global(.umo-select-option span) {
    width: 100%;
    position: relative;
    white-space: nowrap;
    word-wrap: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}
  &.solid {
    border-top: 1px solid #000;
  }
  &.dashed {
    border-top: 1px dashed #000;
  }
  &.dotted {
    border-top: 1px dotted #000;
  }
}

</style>
