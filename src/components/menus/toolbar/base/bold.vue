<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-27 11:13:28
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\bold.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <template v-if="source !== 'tool'">
    <t-tooltip
      :content="t('base.bold') + 'Ctrl + B'"
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
    >
    <menus-button
      ico="bold"
      hide-text
      :menu-active="editor?.isActive('bold')"
      :disabled="!editor?.can().chain().focus().toggleBold().run()"
      @menu-click="editor?.chain().focus().toggleBold().run()"
    />
  </t-tooltip>
  </template>

  <template v-else>
    <t-tooltip
      :content="t('base.bold') + 'Ctrl + B'"
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
    >
      <div
        :class="
          editor?.isActive('bold') ? 'chectbold fount-bold' : 'fount-bold'
        "
        @click="editor?.chain().focus().toggleBold().run()"
      >
        <div>
          <menus-button
            ico="bold"
       
            hide-text
            :menu-active="editor?.isActive('bold')"
            :disabled="!editor?.can().chain().focus().toggleBold().run()"
          />
        </div>

        <div class="fount-bold-text">{{ t('base.bold') }}</div>
      </div>
    </t-tooltip>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
