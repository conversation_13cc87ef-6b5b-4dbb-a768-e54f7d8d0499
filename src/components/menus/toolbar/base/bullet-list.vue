<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="bullet-list"
      :text="t('list.bullet.text')"
      :shortcut="t('list.bullet.info')"
      menu-type="popup"
      popup-handle="arrow"
      hide-text
      :menu-active="editor?.isActive('bulletList')"
      :popup-visible="popupVisible"
      @toggle-popup="togglePopup"
      @menu-click="toggleBulletList(options[0].value)"
    >
      <template #content>
        <div class="umo-bullet-list-group">
          <tooltip
            v-for="item in options"
            :key="item.value"
            :content="item.label"
          >
            <div
              class="umo-bullet-list-item"
              :class="{ active: listStyleType === item.value }"
              @click="toggleBulletList(item.value)"
            >
              <icon
                class="umo-icon-bullet-list"
                :name="`bullet-list-${item.value}`"
              />
            </div>
          </tooltip>
        </div>
      </template> </menus-button
  ></template>

  <template v-else>
    <div
      :class="
        editor?.isActive('bulletList') ? 'chectbold fount-bold' : 'fount-bold'
      "
    >
      <div>
        <menus-button
          ico="bullet-list"
          :text="t('list.bullet.text')"
          :shortcut="t('list.bullet.info')"
          menu-type="popup"
          popup-handle="arrow"
          hide-text
          :popup-visible="popupVisible"
          @toggle-popup="togglePopup"
          @menu-click="toggleBulletList(options[0].value)"
        >
          <template #content>
            <div class="umo-bullet-list-group">
              <tooltip
                v-for="item in options"
                :key="item.value"
                :content="item.label"
              >
                <div
                  class="umo-bullet-list-item"
                  :class="{ active: listStyleType === item.value }"
                  @click="toggleBulletList(item.value)"
                >
                  <icon
                    class="umo-icon-bullet-list"
                    :name="`bullet-list-${item.value}`"
                  />
                </div>
              </tooltip>
            </div>
          </template>
        </menus-button>
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('list.bullet.text') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { popupVisible, togglePopup } = usePopup()
const { editor } = useStore()
defineProps({
  source: String,
})
const options = [
  { label: t('list.bullet.disc'), value: 'disc' },
  { label: t('list.bullet.circle'), value: 'circle' },
  { label: t('list.bullet.square'), value: 'square' },
]

let listStyleType = $ref('')
watch(
  () => popupVisible.value,
  (val: boolean) => {
    if (val && editor.value) {
      const { listType } = editor.value.getAttributes('bulletList')
      listStyleType = listType
    }
  },
)
const toggleBulletList = (listType: string) => {
  const chain = editor.value?.chain().focus()
  if (editor.value?.isActive('bulletList')) {
    if (editor.value.getAttributes('bulletList').listType === listType) {
      chain
        ?.updateAttributes('paragraph', {
          containerColor: null,
          backgroundImage: null,
          backgroundSize: null,
        })
        .toggleBulletList()
        .run()
    } else {
      chain?.updateAttributes('bulletList', { listType }).run()
    }
  } else {
    chain
      ?.updateAttributes('paragraph', {
        containerColor: null,
        backgroundImage: null,
        backgroundSize: null,
      })
      .toggleBulletList()
      ?.updateAttributes('bulletList', { listType })
      ?.run()
  }
  listStyleType = listType
  popupVisible.value = false
}
</script>

<style lang="less" scoped>
.umo-bullet-list-group {
  display: flex;
  align-items: center;
  gap: 8px;
  .umo-bullet-list-item {
    cursor: pointer;
    padding: 5px;
    border: solid 1px var(--umo-border-color);
    box-sizing: border-box;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      background-color: var(--umo-button-hover-background);
    }
    &.active {
      border-color: var(--umo-primary-color);
    }
  }
  .umo-icon-bullet-list {
    font-size: 44px;
  }
}
</style>
