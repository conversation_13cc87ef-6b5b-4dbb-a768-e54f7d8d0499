<style lang="less" scoped>
.fount-bold {
  width: 45px;
  height: 45px;
  margin: 5px;
  border-radius: 5px;
  text-align: center;
  &:hover {
    background-color: var(--umo-button-hover-background);
  }
  .fount-bold-text {
    font-size: 12px;
    text-align: center;
    cursor: pointer;
  }
}
.chectbold {
  background-color: var(--umo-button-hover-background);
}
</style>
<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="code"
      :text="t('base.code')"
      shortcut="Ctrl+E"
      hide-text
      :menu-active="editor?.isActive('code')"
      :disabled="!editor?.can().chain().focus().toggleCode().run()"
      @menu-click="editor?.chain().focus().toggleCode().run()"
    />
  </template>

  <template v-else>
    <div
      :class="editor?.isActive('bold') ? 'chectbold fount-bold' : 'fount-bold'"
    >
      <div>
        <menus-button
          ico="code"
          :text="t('base.code')"
          shortcut="Ctrl+E"
          hide-text
          :menu-active="editor?.isActive('code')"
          :disabled="!editor?.can().chain().focus().toggleCode().run()"
          @menu-click="editor?.chain().focus().toggleCode().run()"
        />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.code') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
