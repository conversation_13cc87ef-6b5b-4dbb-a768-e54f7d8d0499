<template>
  <t-collapse default-expand-all v-if="data && data.length > 0" v-for="(item, index) in data" :borderless="true" class="collapse-item">
    <t-collapse-panel :header="item.label" v-if="item.children && item.children.length > 0">
      <collapse-item :data="item.children" @click="clickHandler"/>
    </t-collapse-panel>
    <p v-else-if="!item.children || item.children.length === 0" class="collapse-item-leaf-node" @click="clickHandler(item)">
      {{ item.label }}
    </p>
  </t-collapse>
</template>
<script setup>
defineOptions({
  name: 'collapse-item'
})
const emits = defineEmits(['click'])
const props = defineProps({
  tier: {
    type: Number,
    default: 1
  },
  data: {
    type: Object,
    default: null
  },
  parentItem: {
    type: Object,
    default: {}
  }
})

// 方法
function clickHandler (item) {
  emits('click', item)
}
</script>
<style lang="less">
.collapse-item {
  .umo-collapse-panel__wrapper {
    .umo-collapse-panel__content {
      padding-top: 0;
      padding-left: 0;
    }
  }
  .collapse-item-leaf-node {
    cursor: pointer;
    padding: var(--td-comp-paddingTB-m) var(--td-comp-paddingLR-l)
  }
}
</style>