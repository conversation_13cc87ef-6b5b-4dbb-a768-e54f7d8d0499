<template>
  <template v-if="source !== 'tool'">
    <menus-button :text="text || t('base.color')" menu-type="popup" popup-handle="arrow" hide-text
      :popup-visible="popupVisible" @toggle-popup="togglePopup" @menu-click="colorChange(currentColor)">
      <!-- <div class="umo-current-color" :style="{
        background:currentColor,
      }"></div> -->
   
      <icon name="color" :style="{
        borderColor: currentColor,
        borderWidth: '2px',
        borderBottomStyle: 'solid',
        // background:
        //   editor?.getAttributes('textStyle')?.color || currentColor,
      }" />
      <template #content>
        <color-picker :default-color="defaultColor" @change="colorChange" />
      </template>
    </menus-button></template>
  <template v-else>
    <div :class="editor?.isActive('color') ? 'chectbold fount-bold' : 'fount-bold'">
      <div>

        <menus-button :text="text || t('base.color')" menu-type="popup" popup-handle="arrow" hide-text
          :popup-visible="popupVisible" @toggle-popup="togglePopup" @menu-click="colorChange(currentColor)">
          <!-- :style="{
              borderColor:
                editor?.getAttributes('textStyle')?.color || currentColor,
              borderWidth: '2px',
              borderBottomStyle: 'solid',
            }" -->
           
          <icon name="color" :style="{
            borderColor: currentColor,
            borderWidth: '2px',
            borderBottomStyle: 'solid',
            // background:
            //   editor?.getAttributes('textStyle')?.color || currentColor,
          }" />
          <template #content>
            <color-picker :default-color="defaultColor" @change="colorChange" />
          </template>
        </menus-button>
      </div>
      <div class="fount-bold-text __ellipsis">{{ t('base.color') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  modeless: {
    type: Boolean,
    default: false,
  },
  defaultColor: {
    type: String,
    default: '#000',
  },
  source: String,
})
const emits = defineEmits(['change'])

const { popupVisible, togglePopup } = usePopup()
const { editor } = useStore()

let currentColor = $ref<any>()
const colorChange = (color: string) => {
  currentColor = color
  popupVisible.value = false

  if (props.modeless) {
    emits('change', currentColor)
    return
  }

  if (color === '') {
    editor.value?.chain().focus().unsetColor().run()
  } else {
    editor.value?.chain().focus().setColor(color).run()
  }
}
</script>
