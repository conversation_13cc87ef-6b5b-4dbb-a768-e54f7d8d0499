<template>
  <template v-if="source !== 'tool'">
    <menus-button
      :text="t('base.columnLayout.text')"
      menu-type="dropdown"
      popup-handle="arrow"
      hide-text
      overlay-class-name="umo-columnLayout-dropdown"
    >
      <icon name="columnLayout" class="icon-columnLayout" />
      <template #dropmenu>
        <t-dropdown-menu>
          <t-dropdown-item
            class="umo-text-columnLayout-menu"
            v-for="item in options"
            :key="item.value"
            :value="item.value"
            :divider="item.divider"
            @click="columnLayoutChange(item as columnLayoutOption)"
          >
            <span>{{ item.label }}</span>
          </t-dropdown-item>
        </t-dropdown-menu>
      </template>
    </menus-button>
  </template>

  <template v-else>
    <div
      :class="'fount-bold'"
    >
      <div>
        <menus-button
          :text="t('base.columnLayout.text')"
          menu-type="dropdown"
          popup-handle="arrow"
          hide-text
          overlay-class-name="umo-columnLayout-dropdown"
        >
          <icon name="columnLayout" class="icon-columnLayout" />
          <template #dropmenu>
            <t-dropdown-menu>
              <t-dropdown-item
                class="umo-text-columnLayout-menu"
                v-for="item in options"
                :key="item.value"
                :value="item.value"
                :divider="item.divider"
                @click="columnLayoutChange(item as columnLayoutOption)"
              >
                <span>{{ item.label }}</span>
              </t-dropdown-item>
            </t-dropdown-menu>
          </template>
        </menus-button>
      </div>
      <div class="fount-bold-text __ellipsis">
        {{ t('base.columnLayout.text') }}
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
interface columnLayoutOption {
  label: string
  columns?: number
}
const options = [
  { label: t('base.columnLayout.oneColumn'), columns: 1 },
  { label: t('base.columnLayout.twoColumn'), columns: 2 },
  { label: t('base.columnLayout.threeColumn'), columns: 3 },
]
let columnLayout = $ref<columnLayoutOption | undefined>()
const columnLayoutChange = (item: columnLayoutOption) => {
  if (item.columns) {
    if (item.columns === 1){
      editor.value?.chain().focus().unsetColumnCount().run()
      return false;
    }
    editor.value?.chain().focus().setColumnCount(item.columns).run()
  }
  columnLayout = item
}
const clearFormat = () => {
  editor.value?.chain().focus().unsetHighlight().run()
  editor.value?.chain().focus().unsetColor().run()
  columnLayout = {}
}
</script>

<style lang="less" scoped>
.icon-columnLayout {
  border-radius: 2px;
}
</style>
<style lang="less">
.umo-text-columnLayout-dropdown {
  .umo-popup__content {
    .umo-divider {
      margin-top: 8px;
      margin-bottom: 8px;
    }
  }
}
.umo-text-columnLayout-menu {
  width: 140px;
  margin-bottom: 6px;
  border: solid 1px transparent;
  &.umo-clear-format-menu {
    margin-bottom: 0;
  }
  &:hover {
    border-color: var(--umo-primary-color);
    background-color: inherit;
  }
  .umo-dropdown__item-text {
    display: flex;
    align-items: center;
    padding: 2px;
    .umo-icon {
      font-size: 16px;
      margin-right: 5px;
    }
  }
}
</style>
