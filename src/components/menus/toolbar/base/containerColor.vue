<template>
    <menus-button :text="text || t('base.containerColor')" menu-type="popup" :popup-visible="popupVisible"
        @toggle-popup="togglePopup" style="padding: 0 0 0 15px;display: flex;justify-content: space-between;">
        <icon name="background-color" class="umo-icon-background-color" :style="{
            background:
                editor?.getAttributes('highlight')?.color || currentColor,
        }" />
        <template #content>
            <color-picker :default-color="defaultColor" @change="colorChange" />
        </template>
    </menus-button>
</template>

<script setup lang="ts">
const { popupVisible, togglePopup } = usePopup()
const props = defineProps({
    text: {
        type: String,
        default: '',
    },
    modeless: {
        type: Boolean,
        default: false,
    },
    defaultColor: {
        type: String,
        default: '',
    },
    source: String,
})
const fileInput = ref(null)
const emits = defineEmits(['change'])
const { editor } = useStore()

let currentColor = $ref<string | undefined>()
const colorChange = (color: string) => {
    currentColor = color
    popupVisible.value = false
    if (props.modeless) {
        emits('change', currentColor)
        return
    }
    if (color === '') {
        editor.value?.chain().focus().setContainerColor('transparent').run()
    } else {
        editor.value?.chain().focus().setContainerColor(color).run()
    }
}

const setColor = () => {
    if (currentColor) {
        editor.value?.chain().focus().setContainerColor(currentColor).run()
    } else {
        editor.value?.chain().focus().setContainerColor('transparent').run()
    }
}
</script>

<style lang="less">
.umo-icon-background-color {
    border-radius: 2px;
}

::deep(.umo-menu-button .umo-button-icon-arrow) {
    display: none;
}
</style>
