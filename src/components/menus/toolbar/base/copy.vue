<template>
  <!-- <menus-button
      ico="align-right"
      :text="t('base.align.right')"
      shortcut="Ctrl+Shift+R"
      hide-text
      :menu-active="
        editor?.isActive({ textAlign: 'right' }) ||
        editor?.isActive({ nodeAlign: 'flex-end' })
      "
      :disabled="
        !editor?.can().chain().focus().setTextAlign('right').run() &&
        !editor?.can().chain().focus().setNodeAlign('flex-end').run()
      "
      @menu-click="setAlignRight"
    /> -->
  <div
    :class="editor?.isActive('bold') ? 'chectbold fount-bold' : 'fount-bold'"
  >
    <div>
      <menus-button
        ico="copy"
        :text="t('base.align.copy')"
        shortcut="Ctrl+C"
        hide-text
        @menu-click="copyClick"
      />
    </div>

    <div class="fount-bold-text __ellipsis">{{ t('base.align.copy') }}</div>
  </div>
</template>

<script setup>
import { getSelectionText } from '@/extensions/selection'
const { editor } = useStore()
const copyClick = () => {
  const codeBlock = editor.value ? getSelectionText(editor.value) : null
  if (codeBlock) {
    useMessage('success', t('bubbleMenu.code.copy.success'))
    document.execCommand('copy')
  }
}
</script>
<!-- 模块说明 -->
<style lang="less" scoped>
.fount-bold {
  width: 45px;
  height: 45px;
  margin: 5px;
  border-radius: 5px;
  text-align: center;

  &:hover {
    background-color: var(--umo-button-hover-background);
  }

  .fount-bold-text {
    font-size: 12px;
    text-align: center;
    cursor: pointer;
  }
}

.chectbold {
  background-color: var(--umo-button-hover-background);
}
</style>
