<template>

  <t-tooltip :content="t('base.align.cut') + 'Ctrl + X'" theme="light" placement="top" :show-arrow="false"
    destroy-on-close>
    <div :class="editor?.isActive('bold') ? 'chectbold fount-bold' : 'fount-bold'" @click="copyClick">
      <div>
        <menus-button ico="cut" :text="t('base.align.cut')" hide-text />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.align.cut') }}</div>
    </div>
  </t-tooltip>
</template>

<script setup>
import { getSelectionText } from '@/extensions/selection'
const { editor } = useStore()
const copyClick = () => {
  const codeBlock = editor.value ? getSelectionText(editor.value) : null
  if (codeBlock) {
    useMessage('success', t('bubbleMenu.code.cut.success'))
    document.execCommand('cut')
  }
}
</script>
<!-- 模块说明 -->
<style lang="less" scoped>
.fount-bold {
  width: 45px;
  height: 45px;
  margin: 5px;
  border-radius: 5px;
  text-align: center;

  &:hover {
    background-color: var(--umo-button-hover-background);
  }

  .fount-bold-text {
    font-size: 12px;
    text-align: center;
    cursor: pointer;
  }
}

.chectbold {
  background-color: var(--umo-button-hover-background);
}
</style>
