<template>




  <t-tooltip
    :content="t('base.fontBorder.text')"
    theme="light"
    placement="top"
    :show-arrow="false"
    destroy-on-close
  >


  <div class="fount-bold"  @click="handleOpen">
    <menus-button
    ico="fontBorder"
    :text="t('base.fontBorder.text')"
    hide-text
  
  />

  <div class="fount-bold-text __ellipsis" style="margin-top: 5px;">
      {{ t('base.fontBorder.text') }}
    </div>
  </div>
  </t-tooltip>
  <modal
    :visible="visible"
    :header="t('base.fontBorder.text')"
    width="290px"
    @close="visible = false"
    @confirm="handleClick">
    <div class="border-container">
        <div class="border-title">{{ t('base.fontBorder.BorderThickness') }}</div>
        <div>
          <t-input-number v-model="borderWidth"   />
        </div>
        <div class="border-title">{{ t('base.fontBorder.BorderDash') }}</div>
        <t-select v-model="borderStyle">
          <t-option value="solid" :label=" t('base.fontBorder.solid') ">
            <hr class="border-demo solid"></hr>
          </t-option>
          <t-option value="dotted" :label=" t('base.fontBorder.dotted') ">
            <hr class="border-demo dotted"></hr>
          </t-option>
          <t-option value="dashed" :label=" t('base.fontBorder.dashed') ">
            <hr class="border-demo dashed"></hr>
          </t-option>
        </t-select>
        <div class="border-title">{{ t('base.fontBorder.BorderColor') }}</div>
        <color-picker :default-color="defaultColor" @change="colorChange" />
<!-- 
        <div >
          <t-button theme="default" class="btn" @click="handleClick()">{{ t('base.fontBorder.BorderBtn') }}</t-button>
        </div> -->
        
      </div>


  </modal>
</template>

<script setup lang="ts">
const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  modeless: {
    type: Boolean,
    default: false,
  },
  defaultColor: {
    type: String,
    default: 'rgba(0,0,0,0.6)',
  },
  source: String,
})
const { popupVisible, togglePopup } = usePopup()
const { editor } = useStore()
const borderWidth = ref(0)
const borderStyle = ref('solid')

let visible=$ref(false)
const emits = defineEmits(['change'])

let currentColor = $ref<any>()
const colorChange = (color: string) => {
  currentColor = color
}

const handleOpen = () => {
  visible = true
}

const handleClick = () => {
 
  editor.value
    ?.chain()
    .focus()
    .setTextBorder({
      borderColor: currentColor,
      borderWidth: `${borderWidth.value}px`,
      borderStyle: borderStyle.value,
    })
    .run()
    visible = false
   
}
</script>

<style lang="less" scoped>
:global(.umo-menu-button.huge .umo-button-content .umo-button-text) {
    display: block;
    font-size: 12px;
    margin-top:5px;
   color:rgba(0, 0, 0,0.6);
}

.border-demo {
  width: 100%;
  height: 1px;
  margin: 5px 0;
  :global(.umo-select-option span) {
    width: 100%;
    position: relative;
    white-space: nowrap;
    word-wrap: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}
  &.solid {
    border-top: 1px solid #000;
  }
  &.dashed {
    border-top: 1px dashed #000;
  }
  &.dotted {
    border-top: 1px dotted #000;
  }
}

.border-title{
  padding: 10px 0;
  font-size: 12px;
  color: var(--umo-text-color);
  font-weight: bold;
}

.button-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.btn{
  width: 100%;
  margin:5px 0;
}

</style>
