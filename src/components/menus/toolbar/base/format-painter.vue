<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-27 11:03:36
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\format-painter.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->

<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="format-painter"
      :text="t('base.formatPainter.text')"
      :tooltip="t('base.formatPainter.tip')"
      :menu-active="painter.enabled"
      :disabled="editor?.state?.selection?.empty"
      hide-text
      @menu-click="setFormatPainter(true)"
      @dblclick="setFormatPainter(false)"
  /></template>

  <template v-else>
    <div
      :class="painter.enabled ? 'chectbold fount-bold' : 'fount-bold'"
      @click="setFormatPainter(true)"
    >
      <div>
        <menus-button
          ico="format-painter"
          :text="t('base.formatPainter.text')"
          :tooltip="t('base.formatPainter.tip')"
          :disabled="editor?.state?.selection?.empty"
          hide-text
          @dblclick="setFormatPainter(false)"
        />
      </div>

      <div class="fount-bold-text __ellipsis">
        {{ t('base.formatPainter.text') }}
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor, painter } = useStore()
defineProps({
  source: String,
})
const setFormatPainter = (once: boolean) => {
  painter.value.enabled = !painter.value.enabled
  if (painter.value.enabled) {
    editor.value?.chain().focus().setFormatPainter(once).run()
  }
}
</script>
