/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-14 13:57:10
 * @LastEditTime: 2024-12-12 15:25:41
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\import-word-mixins.js
 * @Description:
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import { DialogPlugin } from 'tdesign-vue-next'

function base64ToBlob(base64Data) {
  let arr = base64Data.split(',');
  let mime = arr[0].match(/:(.*?);/)[1];
  let bstr = atob(arr[1]);
  let n = bstr.length;
  let u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new Blob([u8arr], { type: mime });
}
export default function () {
  const { editor, options, printing, page, loading, editorUpdate } = useStore()

  //动态导入 mammoth.js
  onMounted(() => {
    const mammothScriptElement = document.querySelector('#mammoth-script')
    if (
      mammothScriptElement === null &&
      options.value.toolbar?.importWord.enabled
    ) {
      const style = document.createElement('script')
      style.src = `${options.value.cdnUrl}/libs/mammoth/mammoth.browser.min.js`
      style.id = 'mammoth-script'
      document.querySelector('head')?.append(style)
    }
  })

  const importWord = () => {

    // @ts-expect-error, global variable injected by script
    if (!mammoth) {
      const dialog = useAlert({
        theme: 'warning',
        header: t('base.importWord.loadScript.title'),
        body: t('base.importWord.loadScript.message'),
        onConfirm() {
          dialog.destroy()
        },
      })
      return
    }
    const { open, onChange } = useFileDialog({
      accept: '.docx',
      reset: true,
      multiple: false,
    })
    // 打开文件对话框
    open()
    // 插入文件
    onChange(async (files) => {
      const [file] = Array.from(files ?? [])
      if (!file) {
        return
      }
      loading.value = true
      if (file.size > 1024 * 1024 * 20) {
        useMessage('error', t('base.importWord.limitSize'))
        loading.value = false
        return
      }
      const message = await useMessage('loading', t('base.importWord.converting'))

      // 使用用户自定义导入方法
      if (options.value.toolbar?.importWord?.useCustomMethod) {
        const result =
          await options.value.toolbar?.importWord.onCustomImportWordMethod?.(file)
        message.close()
        try {
          if (result?.messages?.type === 'error') {
            useMessage(
              'error',
              `${t('base.importWord.convertError')} (${result.messages.message})`,
            )
            loading.value = false
            return
          }
          if (result?.value) {
            editor.value?.commands.setContent(result.value)
            setTimeout(() => {
              editor.value?.commands.autoPaging()
            }, 500)
          } else {
            loading.value = false
            useMessage('error', t('base.importWord.importError'))
          }
        } catch {
          useMessage('error', t('base.importWord.importError'))
          loading.value = false
        }
        return
      }

      // 默认使用 Mammoth 导入
      const arrayBuffer = file.arrayBuffer()
      // @ts-expect-error, global variable injected by script
      if (!mammoth) {
        return
      }
      // @ts-expect-error, global variable injected by script
      const { messages, value } = await mammoth.convertToHtml(
        { arrayBuffer },
        options.value.toolbar?.importWord.options,
      )
      message.close()
      if (messages.type === 'error') {
        useMessage(
          'error',
          `${t('base.importWord.convertError')} (${messages.message})`,
        )
        return
      }
      // console.log(value, 'valuevaluevalue');
      try {
        // 解析和加工 Mammoth 返回的 HTML 内容
        const domparser = new DOMParser()
        const doc = domparser.parseFromString(value, 'text/html')
        for (const img of doc.querySelectorAll('img')) {
          const blob = base64ToBlob(img.src);
          const fileName = "image_word_import_" + new Date().getTime() + ".png"
          const file = new File([blob], fileName, { type: "image/png" });
          const result = await options.value.onFileUpload(file)
          img.src = result.url
          img.name = fileName
          img.type = file.type
          img.fileSize = file.size
          const parent = img.parentElement
          if (parent?.tagName === 'P') {
            parent.insertAdjacentElement('beforebegin', img)
            if (!parent.hasChildNodes() && parent.textContent === '') {
              parent.remove()
            }
          }
        }



        const content = doc.body.innerHTML.toString()
        const DIV = document.createElement('div')
        DIV.innerHTML = editor.value.getHTML()

        /**覆盖*/
        function cover() {
          editor.value.commands.insertContent(' ')
          editor.value?.commands.setContent(content)
          /**-----------------------这里逻辑是看看word进来得有没有链接，如果有就替换成自定义节点得格式---------------------------**/
          const JSON = editor.value.getJSON()
          editor.value?.commands.setContent('')
          JSON.content = JSON.content.map(element => ({
            type: 'page',
            attrs: element.attrs,
            content: element.content.map(ele => {
              if (ele.content?.length !== 1) return ele
              if (!ele.content[0].marks) return ele
              const item = ele.content[0].marks.find(item => item.type === 'link')
              if (!item) return ele
              ele = {
                type: "paragraph",
                attrs: ele.attrs,
                content: [{
                  type: 'links',
                  attrs: {
                    href: item.attrs?.href, plateType: "titleIcon", quoteType: null, title: ele.content[0]?.text, type: "websiteLink",
                  }
                }]
              }
              return ele
            })
          }))
          /**-----------------------------------------------------------------------------------------------------------------**/
          editor.value?.commands.setContent(JSON)
          editor.value?.commands.autoPaging()
          setTimeout(() => {
            editorUpdate()
          }, 500)

        }

        if (!DIV.innerText.trim()) {
          cover()
          loading.value = false
        } else {
          loading.value = false
          const confirmDialog = DialogPlugin.confirm({
            header: t('headerTools.word.header'),
            body: t('headerTools.word.body'),
            destroyOnClose: true,
            cancelBtn: {
              content: t('headerTools.word.cancelBtn'),
              theme: 'primary',
            },
            confirmBtn: {
              content: t('headerTools.word.confirmBtn'),
              theme: 'primary',
            },
            theme: 'warning',
            onConfirm: () => {
              editor.value?.commands.insertContent(content)
              confirmDialog.hide();
              setTimeout(() => {
                editorUpdate()
              }, 500)
            },
            onCancel: () => {
              cover()
            }
          });
        }

      } catch (err) {
        useMessage('error', t('base.importWord.importError'))
        loading.value = false
      }

    })
  }
  return {
    importWord,
    editor,
    options,
    printing,
    page
  };
}

