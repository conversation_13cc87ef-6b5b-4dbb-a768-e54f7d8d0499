<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-27 11:40:09
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\indent.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <template v-if="source !== 'tool'">
    <menus-button
      :text="t('base.indent')"
      ico="indent"
      hide-text
      @menu-click="editor?.chain().focus().indent().run()"
  /></template>

  <template v-else>
    <div :class="'fount-bold'" @click="editor?.chain().focus().indent().run()">
      <div>
        <menus-button :text="t('base.indent')" ico="indent" hide-text />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.indent') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
