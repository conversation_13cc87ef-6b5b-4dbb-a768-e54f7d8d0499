<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-27 11:41:39
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\line-height.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <template v-if="source !== 'tool'">
    <menus-button
      :text="t('base.lineHeight.text')"
      ico="line-height"
      menu-type="dropdown"
      hide-text
      :select-options="lineHeights"
      @click="setLineHeight"
  /></template>

  <template v-else>
    <div :class="'fount-bold'">
      <div>
        <menus-button
          :text="t('base.lineHeight.text')"
          ico="line-height"
          menu-type="dropdown"
          hide-text
          :select-options="lineHeights"
          @click="setLineHeight"
        />
      </div>

      <div class="fount-bold-text __ellipsis">
        {{ t('base.lineHeight.text') }}
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { options, editor } = useStore()
defineProps({
  source: String,
})
const lineHeights = computed(() => {
  return options.value.dicts?.lineHeights.map((item: any) => {
    return {
      content: item.default
        ? l(item.label) + t('base.lineHeight.default')
        : l(item.label),
      value: item.value,
      active: editor.value?.isActive({ lineHeight: item.value }),
    }
  })
})

const setLineHeight = ({
  content,
  value,
}: {
  content: string
  value: string
}) => {
  if (!content) {
    return
  }
  editor.value?.chain().focus().setLineHeight(value).run()
}
</script>
