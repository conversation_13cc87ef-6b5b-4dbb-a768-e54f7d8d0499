<template>
  <div style="display: flex" @click="handleMoveBackground">
    <div>
      <menus-button
        ico="movebg"
        style="padding: 0 0 0 15px"
        :text="t('base.backgroundMove')"
        hide-text
      ></menus-button>
    </div>
    <div style="flex: 1" class="fount-bold-text __ellipsis">
      {{ t('base.backgroundMove') }}
    </div>
  </div>
</template>

<script setup lang="ts">
const { editor } = useStore()
const handleMoveBackground = () => {
  editor?.value.chain().focus().setBackgroundSize('del').run()
}
</script>
<style scoped lang="less">
.label {
  margin: 5px 0;
  color: #666;
}
</style>
