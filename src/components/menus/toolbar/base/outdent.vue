<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-27 11:40:46
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\outdent.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="outdent"
      :text="t('base.outdent')"
      shortcut="Shift+Tab"
      hide-text
      @menu-click="editor?.chain().focus().outdent().run()"
  /></template>

  <template v-else>
    <div :class="'fount-bold'" @click="editor?.chain().focus().outdent().run()">
      <div>
        <menus-button
          ico="outdent"
          :text="t('base.outdent')"
          shortcut="Shift+Tab"
          hide-text
        />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.outdent') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
