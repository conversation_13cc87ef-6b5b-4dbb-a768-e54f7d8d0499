<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-22 15:51:16
 * @LastEditTime: 2024-11-27 11:10:21
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\paste.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->

<template>
  <!-- <menus-button
      ico="align-right"
      :text="t('base.align.right')"
      shortcut="Ctrl+Shift+R"
      hide-text
      :menu-active="
        editor?.isActive({ textAlign: 'right' }) ||
        editor?.isActive({ nodeAlign: 'flex-end' })
      "
      :disabled="
        !editor?.can().chain().focus().setTextAlign('right').run() &&
        !editor?.can().chain().focus().setNodeAlign('flex-end').run()
      "
      @menu-click="setAlignRight"
    /> -->
  <t-tooltip :content="t('base.align.paste') + 'Ctrl + V'" theme="light" placement="top" :show-arrow="false"
    destroy-on-close>
    <div :class="'fount-bold'" >
      <div>
        <menus-button ico="paste" :text="t('base.align.paste')" hide-text @menu-click="copyClick" />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.align.paste') }}</div>
    </div>
  </t-tooltip>
</template>

<script setup>
const { editor } = useStore()
const copyClick = async (event) => {
  try {
    const text = await navigator.clipboard.readText()
    if (!text) return false
    //editor?.value.chain().focus().run()
    editor.value
      .chain()
      .focus()
      .insertContent(`${text}`)
      .run()
  } catch (err) {
   
  }
  event.stopPropagation()
}
</script>
<!-- 模块说明 -->
<style lang="less" scoped>
.fount-bold {
  width: 45px;
  height: 45px;
  margin: 5px;
  border-radius: 5px;
  text-align: center;

  &:hover {
    background-color: var(--umo-button-hover-background);
  }

  .fount-bold-text {
    font-size: 12px;
    text-align: center;
    cursor: pointer;
  }
}

.chectbold {
  background-color: var(--umo-button-hover-background);
}
</style>
