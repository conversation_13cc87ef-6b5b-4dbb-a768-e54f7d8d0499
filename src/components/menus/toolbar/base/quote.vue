<template>
  <template v-if="source !== 'tool'">
    <menus-button
      :text="t('base.quote')"
      ico="quote"
      shortcut="Ctrl+Shift+B"
      hide-text
      :menu-active="editor?.isActive('blockquote')"
      :disabled="!editor?.can().chain().focus().toggleBlockquote().run()"
      @menu-click="editor?.chain().focus().toggleBlockquote().run()"
    />
  </template>

  <template v-else>
    <div
      @click="editor?.chain().focus().toggleBlockquote().run()"
      :class="editor?.isActive('blockquote') ? 'chectbold fount-bold' : 'fount-bold'"
    >
      <div>
        <menus-button
          :text="t('base.quote')"
          ico="quote"
          shortcut="Ctrl+Shift+B"
          hide-text
          :disabled="!editor?.can().chain().focus().toggleBlockquote().run()"
        />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.quote') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
