<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-13 15:52:15
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\redo.vue
 * @Description: <menus-button
    ico="redo"
    :text="t('base.redo')"
    shortcut="Ctrl+Y / Ctrl+Shift+Z"
    hide-text
    :disabled="!editor?.can().chain().focus().redo().run()"
    @menu-click="editor?.chain().focus().redo().run()"
  />
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="redo"
      :text="t('base.redo')"
      hide-text
      :disabled="!editor?.can().chain().focus().redo().run()"
      @menu-click="editor?.chain().focus().redo().run()"
  /></template>

  <template v-else>
    <t-tooltip
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
      ><div :class="'fount-bold'" @click="editor?.chain().focus().redo().run()">
        <div>
          <menus-button
            ico="redo"
            hide-text
            :disabled="!editor?.can().chain().focus().redo().run()"
          />
        </div>

        <div class="fount-bold-text __ellipsis">{{ t('base.redo') }}</div>
      </div></t-tooltip
    >
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
