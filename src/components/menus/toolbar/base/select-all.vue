<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="select-all"
      :text="t('base.selectAll')"
      shortcut="Ctrl+A"
      hide-text
      @menu-click="selectAll"
    />
  </template>

  <template v-else>
    <div
      @click="selectAll"
      class="fount-bold"
    >
      <div>
        <menus-button
          ico="select-all"
          :text="t('base.selectAll')"
          shortcut="Ctrl+A"
          hide-text
        />
      </div>

      <div class="fount-bold-text __ellipsis">{{ t('base.selectAll') }}</div>
    </div>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
// 重写全选命令是因为官方重写的后selectAll命令只是利用allSelection，添加了样式，和之前全选样式不一致
const selectAll = () => {
  editor?.value?.chain().focus().command(({ tr, dispatch, state, commands }) => {
    if (dispatch) {
      commands.setTextSelection({
        from: 0,
        to: tr.doc.content.size,
      })
    }
    return true
  }).run()
}
useHotkeys('ctrl+a,command+a', selectAll)
</script>
