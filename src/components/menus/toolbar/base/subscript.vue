<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-27 10:40:01
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\subscript.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->

<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="subscript"
      hide-text
      :menu-active="editor?.isActive('subscript')"
      :disabled="!editor?.can().chain().focus().toggleSubscript().run()"
      @menu-click="editor?.commands.toggleSubscript()"
  /></template>

  <template v-else>
    <t-tooltip
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
    >
      <div
        :class="
          editor?.isActive('subscript') ? 'chectbold fount-bold' : 'fount-bold'
        "
        @click="editor?.commands.toggleSubscript()"
      >
        <div>
          <menus-button
            ico="subscript"
            hide-text
            :menu-active="editor?.isActive('subscript')"
            :disabled="!editor?.can().chain().focus().toggleSubscript().run()"
          />
        </div>

        <div class="fount-bold-text __ellipsis">{{ t('base.subscript') }}</div>
      </div></t-tooltip
    >
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
