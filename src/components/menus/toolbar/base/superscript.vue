<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-27 10:42:33
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\superscript.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->

<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="superscript"
     
      hide-text
      :menu-active="editor?.isActive('superscript')"
      :disabled="!editor?.can().chain().focus().toggleSuperscript().run()"
      @menu-click="editor?.commands.toggleSuperscript()"
  /></template>

  <template v-else>
    <t-tooltip
      :content="t('base.superscript') + 'Ctrl+.'"
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
    >
      <div
        :class="
          editor?.isActive('superscript')
            ? 'chectbold fount-bold'
            : 'fount-bold'
        "
        @click="editor?.commands.toggleSuperscript()"
      >
        <div>
          <menus-button
            ico="superscript"
         
            hide-text
            :menu-active="editor?.isActive('superscript')"
            :disabled="!editor?.can().chain().focus().toggleSuperscript().run()"
          />
        </div>

        <div class="fount-bold-text __ellipsis">
          {{ t('base.superscript') }}
        </div>
      </div></t-tooltip
    >
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
