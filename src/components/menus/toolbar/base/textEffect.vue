<template>
  <template v-if="source !== 'tool'">
    <menus-button
      :text="t('base.textEffect.text')"
      menu-type="dropdown"
      popup-handle="arrow"
      hide-text
     
      overlay-class-name="umo-textEffect-dropdown"
    >
      <icon name="textEffect" class="icon-textEffect" />
      <template #dropmenu>
        <t-dropdown-menu>
          <t-dropdown-item
            class="umo-text-textEffect-menu"
            v-for="item in options"
            :key="item.value"
            :value="item.value"
            :divider="item.divider"
            @click="textEffectChange(item as TextEffectOption)"
          >
            <span>{{ item.label }}</span>
          </t-dropdown-item>
        </t-dropdown-menu>
      </template>
    </menus-button>
  </template>

  <template v-else>
    <t-tooltip
      :content="t('base.textEffect.text')"
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
      ><div
        :class="'fount-bold'"
      >
        <div>
          <menus-button
           
            menu-type="dropdown"
            popup-handle="arrow"
            hide-text
            overlay-class-name="umo-textEffect-dropdown"
          >
            <icon name="textEffect" class="icon-textEffect" />
            <template #dropmenu>
              <t-dropdown-menu>
                <t-dropdown-item
                  class="umo-text-textEffect-menu"
                  v-for="item in options"
                  :key="item.value"
                  :value="item.value"
                  :divider="item.divider"
                  @click="textEffectChange(item as TextEffectOption)"
                >
                  <span>{{ item.label }}</span>
                </t-dropdown-item>
              </t-dropdown-menu>
            </template>
          </menus-button>
        </div>
        <div class="fount-bold-text __ellipsis">
          {{ t('base.textEffect.text') }}
        </div>
      </div></t-tooltip
    >
  </template>
</template>
<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
interface TextEffectOption {
  label: string
  type?: string
}
const options = [
  { label: t('base.textEffect.default'), type: 'default' },
  { label: t('base.textEffect.outline'), type: 'textStroke' },
  { label: t('base.textEffect.shadow'), type: 'textShadow' },
  { label: t('base.textEffect.glow'), type: 'glow' },
]
let textEffect = $ref<TextEffectOption | undefined>()
const textEffectChange = (item: TextEffectOption) => {
  if (item.type == 'textShadow') {
    editor.value?.chain().focus().setTextShadow().run()
  }
  if (item.type == 'textStroke') {
    editor.value?.chain().focus().setTextStroke().run()
  }
  if (item.type == 'glow') {
    editor.value?.chain().focus().setTextGlow().run()
  }
  if (item.type == 'default') {
    editor.value?.chain().focus().unsetTextShadow().run()
    editor.value?.chain().focus().unsetTextStroke().run()
    editor.value?.chain().focus().unsetTextGlow().run()
  }
  textEffect = item
}
const clearFormat = () => {
  editor.value?.chain().focus().unsetHighlight().run()
  editor.value?.chain().focus().unsetColor().run()
  textEffect = {}
}
</script>

<style lang="less" scoped>
.icon-textEffect {
  border-radius: 2px;
  height: 36px;
}


</style>
<style lang="less">
.umo-text-textEffect-dropdown {
  .umo-popup__content {
    .umo-divider {
      margin-top: 8px;
      margin-bottom: 8px;
    }
  }
}

.umo-menu-button .umo-button-icon-arrow{
  height: 36px;
}

.umo-input.umo-size-s{
  height: 36px;
}
.umo-text-textEffect-menu {
  width: 140px;
  border: solid 1px transparent;
  &.umo-clear-format-menu {
    margin-bottom: 0;
  }
  &:hover {
    border-color: var(--umo-primary-color);
    background-color: inherit;
  }
  .umo-dropdown__item-text {
    display: flex;
    align-items: center;
    padding: 2px;
    .umo-icon {
      font-size: 16px;
      margin-right: 5px;
    }
  }
}
</style>