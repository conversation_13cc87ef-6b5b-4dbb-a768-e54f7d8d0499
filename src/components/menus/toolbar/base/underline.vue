<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2024-11-27 10:34:44
 * @FilePath: \dutp-editor\src\components\menus\toolbar\base\underline.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->

<template>
  <template v-if="source !== 'tool'">
    <t-tooltip
      :content="t('base.underline') + 'Ctrl + U'"
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
    >
    <menus-button
      ico="underline"
    
      hide-text
      :menu-active="editor?.isActive('underline')"
      :disabled="!editor?.can().chain().focus().toggleUnderline().run()"
      @menu-click="editor?.chain().focus().toggleUnderline().run()"
  />
  </t-tooltip
  >
</template>

  <template v-else>
    <t-tooltip
      :content="t('base.underline') + 'Ctrl + U'"
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
    >
      <div
        :class="
          editor?.isActive('underline') ? 'chectbold fount-bold' : 'fount-bold'
        "
        @click="editor?.chain().focus().toggleUnderline().run()"
      >
        <div>
          <menus-button
            ico="underline"
           
            hide-text
            :disabled="!editor?.can().chain().focus().toggleUnderline().run()"
          />
        </div>

        <div class="fount-bold-text __ellipsis">{{ t('base.underline') }}</div>
      </div></t-tooltip
    >
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
