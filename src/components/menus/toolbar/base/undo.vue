<template>
  <template v-if="source !== 'tool'">
    <menus-button
      ico="undo"
      :text="t('base.undo')"
      shortcut="Ctrl+Z"
      hide-text
      :disabled="!editor?.can().chain().focus().undo().run()"
      @menu-click="editor?.chain().focus().undo().run()"
  /></template>

  <template v-else>
    <t-tooltip
      :content="t('base.undo') + 'Ctrl+Z'"
      theme="light"
      placement="top"
      :show-arrow="false"
      destroy-on-close
    >
      <div :class="'fount-bold'" @click="editor?.chain().focus().undo().run()">
        <div>
          <menus-button
            ico="undo"
         
            hide-text
            :disabled="!editor?.can().chain().focus().undo().run()"
          />
        </div>
        <div class="fount-bold-text __ellipsis">{{ t('base.undo') }}</div>
      </div></t-tooltip
    >
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
</script>
