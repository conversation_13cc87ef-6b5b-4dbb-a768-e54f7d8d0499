<template>
  <menus-button
    ico="image"
    :text="t('export.image.text')"
    menu-type="dropdown"
    huge
    :select-options="formats"
    @click="saveImage"
  />
</template>

<script setup lang="ts">
import domtoimage from 'dom-to-image-more'
import { saveAs } from 'file-saver'
import { useRoute } from 'vue-router'

import { getChapterInfo } from '@/api/book/chapter.js'
const { toBlob } = domtoimage

const { container, options, page, exportImage } = useStore()
const route = useRoute()
const chapterId = ref(route.query.chapterId)
const bookName = ref('')
const chapterName = ref('')
const formats = [
  { content: t('export.image.png'), value: 'png' },
  { content: t('export.image.jpg'), value: 'jpg' },
]
const getInfo = async () => {
  const { data } = await getChapterInfo(chapterId.value)
  bookName.value = data.bookName
  chapterName.value = data.chapterName
}
onMounted(() => {
  getInfo()
})
const saveImage = async ({
  content,
  value,
}: {
  content: string
  value: string
}) => {
  if (!content) {
    return
  }
  const { zoomLevel } = page.value
  exportImage.value = true
  try {
    page.value.zoomLevel = 100
    await nextTick()
    const node = document.querySelector(
      `${container} .umo-page-content`,
    ) as HTMLElement
    const blob = await toBlob(node, { scale: devicePixelRatio })
    const { title } = options.value.document ?? {}
    const filename = `${bookName.value}-${chapterName.value}`
    // const filename =
    //   title !== '' ? options.value?.document?.title : t('document.untitled')
    saveAs(blob, `${filename}.${value}`)
  } catch {
    const dialog = useAlert({
      theme: 'warning',
      header: t('export.image.error.title'),
      body: t('export.image.error.message'),
      onConfirm() {
        dialog.destroy()
      },
    })
  } finally {
    page.value.zoomLevel = zoomLevel
    exportImage.value = false
  }
}
</script>
