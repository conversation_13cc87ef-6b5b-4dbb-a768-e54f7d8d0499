<template>
  <menus-button
    ico="text"
    :text="t('export.text')"
    huge
    @menu-click="saveTextFile"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'

import { getChapterInfo } from '@/api/book/chapter.js'
const { options, editor } = useStore()
const bookName = ref('')
const chapterName = ref('')
const route = useRoute()
const chapterId = ref(route.query.chapterId)
const getInfo = async () => {
  const { data } = await getChapterInfo(chapterId.value)
  bookName.value = data.bookName
  chapterName.value = data.chapterName
}

const saveTextFile = () => {
  if (!editor.value) {
    return
  }
  const blob = new Blob([editor.value.getText()], {
    type: 'text/plain;charset=utf-8',
  })
  // const { title } = options.value.document ?? {}
  // const filename =
  //   title !== '' ? options.value?.document?.title : t('document.untitled')

  const allName = `${bookName.value}-${chapterName.value}`

  saveAs(blob, `${allName}.txt`)
}

onMounted(() => {
  getInfo()
})
</script>
