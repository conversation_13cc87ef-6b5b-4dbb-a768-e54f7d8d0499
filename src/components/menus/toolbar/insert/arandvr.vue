<template>
  <menus-button
    ico="arandvr"
    :text="t('insert.ARandVR.text')"
    huge
    menu-type="dropdown"
  >
    <template #dropmenu>
      <t-dropdown-menu>
        <t-dropdown-item
          v-for="item in options"
          class="umo-text-image-menu"
          @click="resourceClick(item.type)"
        >
          <span>{{ item.label }}</span>
        </t-dropdown-item>
      </t-dropdown-menu>
    </template>
  </menus-button>

  <modal
    :visible="dialogVisible"
    :header="t('insert.ARandVR.text')"
    width="430px"
    :footer="false"
    @close="dialogVisible = false"
  >
    <div class="umo-web-page-container" style="height: 280px">
      <t-form
        ref="formValidatorStatus"
        :data="formData"
        :rules="rules"
        :label-width="80"
        label-align="top"
        @submit="insertGame"
      >
        <t-form-item :label="t('insert.ARandVR.titltTip')" name="name">
          <t-input
            v-model.trim="formData.name"
            required-mark
            clearable
            :placeholder="t('insert.ARandVR.titleplaceholder')"
            :maxlength="20"
          />
        </t-form-item>

        <t-form-item :label="t('insert.ARandVR.tip')" name="url">
          <t-input
            v-model.trim="formData.url"
            required-mark
            clearable
            :placeholder="t('insert.ARandVR.placeholder')"
          />
          <t-tooltip :content="admin_content" :show-arrow="false">
            <t-icon
              name="error-circle"
              size="16px"
              style="color: #0006; margin-left: 10px"
            />
          </t-tooltip>
        </t-form-item>
        <t-form-item :label="t('insert.ARandVR.color')" name="bgColor">
          <div
            style="width: 100%; height: 32px; border-radius: 3px"
            :style="{ backgroundColor: `${formData.bgColor}` }"
            @click="dialogColorVisible = true"
          ></div>
        </t-form-item>
        <div class="footer">
          <t-button
            style="margin-right: 10px"
            theme="default"
            @click="dialogVisible = false"
            >{{ t('insert.ARandVR.cancel') }}</t-button
          >
          <t-button theme="primary" type="submit">{{
            t('insert.ARandVR.confirm')
          }}</t-button>
        </div>
      </t-form>
    </div>
  </modal>
  <modal
    :visible="dialogColorVisible"
    :header="null"
    width="300px"
    :confirm-btn="null"
    :cancel-btn="null"
    @close="dialogColorVisible = false"
  >
    <color-picker :default-color="formData.bgColor" @change="colorChange" />
  </modal>
  <ResourceLibrary
    v-model:visible="show"
    :file-type="'5'"
    :multiple="true"
    @insert-by-resource="insertByResource"
  />
</template>

<script setup lang="ts">
import { RESOURCE_COVER } from '@/extensions/page/node-names'
const { editor, batchInsert } = useStore()
import { RCType } from '@/enum/extensions/RC'
const admin_content = ref(import.meta.env.VITE_APP_ADMIN_CONTENT)
const dialogVisible = ref(false)
const dialogColorVisible = ref(null)
const formValidatorStatus = ref(null)
const formData = ref({
  name: '',
  url: '',
  bgColor: '#000000',
})
const rules = {
  name: [
    {
      required: true,
      message: t('insert.ARandVR.nameTip'),
      type: 'error',
      trigger: 'blur',
    },
  ],
  url: [
    { required: true, message: t('insert.ARandVR.gameTip'), type: 'error' },
    {
      url: {
        protocols: ['http', 'https', 'ftp'],
        require_protocol: true,
      },
      message: t('insert.ARandVR.gameTip'),
    },
  ],
}

watch(dialogVisible, (val) => {
  dialogVisible.value = val
})
const openDialog = () => {
  formData.value = {
    name: '',
    url: '',
    bgColor: '#000000',
  }
  dialogVisible.value = true
}
const rgbToHex = (rgb) => {
  // 将输入的RGB字符串分割成数组
  rgb = rgb.slice(4)
  const rgbArray = rgb
    .slice(0, rgb.length - 1)
    .split(',')
    .map(Number)
  if (
    rgbArray.length !== 3 ||
    rgbArray.some((val) => isNaN(val) || val < 0 || val > 255)
  ) {
    return null
  }
  // 将每个RGB值转换为十六进制并拼接
  return `#${rgbArray
    .map((val) => {
      const hex = val.toString(16)
      return hex.length === 1 ? `0${hex}` : hex
    })
    .join('')}`
}
const insertGame = () => {
  const validateResult = formValidatorStatus.value.validate((valid) => valid)
  validateResult.then((valid) => {
    if (valid == true) {
      dialogVisible.value = false
      editor.value
        .chain()
        .focus()
        .setResourceCover({
          rcType: RCType.AR,
          url: formData.value.url,
          title: formData.value.name,
          bgColor: formData.value.bgColor,
        })
        .run()
    }
  })
}
const colorChange = (color) => {
  if (color.includes('rgb')) {
    formData.value.bgColor = rgbToHex(color)
  } else {
    formData.value.bgColor = color
  }
  dialogColorVisible.value = false
}

const show = ref(false)

const options = [
  { label: t('insert.ARandVR.local'), type: 'local' },
  { label: t('insert.ARandVR.library'), type: 'library' },
]

const resourceClick = (type: string) => {
  switch (type) {
    case 'local':
      openDialog()
      break
    case 'library':
      show.value = true
      break
  }
}

const insertByResource = (files) => {
  batchInsert(files, RESOURCE_COVER, (file) => ({
    rcType: RCType.AR,
    url: `${file.fileUrl}/?bgColor=ffffff`,
    orgUrl: file.fileUrl,
    size: file.fileSize,
    title: file.name,
    filename: file.name,
    bgColor: '#ffffff',
  }))
}
</script>

<style lang="less" scoped>
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>
