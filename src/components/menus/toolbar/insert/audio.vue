<template>
  <menus-button
    ico="audio"
    :text="t('insert.audio.text')"
    menu-type="dropdown"
    huge
  >
    <template #dropmenu>
      <t-dropdown-menu>
        <t-dropdown-item
          v-for="item in options"
          class="umo-text-image-menu"
          @click="audioClick(item.type)"
        >
          <span>{{ item.label }}</span>
        </t-dropdown-item>
      </t-dropdown-menu>
    </template>
  </menus-button>
  <ResourceLibrary
    v-model:visible="show"
    :file-type="'2'"
    @insert-by-resource="insertByResource"
  />
</template>

<script setup lang="ts">
import { chooseFile } from '@/utils/file'
const { editor } = useStore()

const show = ref(false)

const options = [
  { label: t('insert.audio.HanlinAudio'), type: 'hanlin' },
  { label: t('insert.audio.local'), type: 'local' },
  { label: t('insert.audio.library'), type: 'library' },
]

const audioClick = (type: string) => {
  switch (type) {
    case 'local':
      chooseFile(
        (file) => {
          editor.value
            ?.chain()
            .focus()
            .setAudio({
              src: file.fileUrl,
              size: file.fileSize,
              audioTitle: file.originName,
              name: file.originName,
            })
            .run()
        },
        {
          multiple: false,
          fileType: '.mp3,.wav'
        }
      )
      break
    case 'hanlin':
      chooseFile(
        (file) => {
          editor.value
            ?.chain()
            .focus()
            .setAudioHanlin({
              src: file.fileUrl,
            })
            .run()
        },
        {
          multiple: false,
          fileType: '.mp3,.wav'
        }
      )
      break
    case 'library':
      show.value = true
      break
  }
}

const insertByResource = (file) => {
  editor.value
    ?.chain()
    .focus()
    .setAudio({
      src: file.fileUrl,
      alt: '',
      size: file.fileSize,
      audioTitle: file.name,
      name: file.name,
    })
    .run()
}
</script>
