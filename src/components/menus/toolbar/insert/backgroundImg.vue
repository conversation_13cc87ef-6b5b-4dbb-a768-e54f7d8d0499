<template>
    <menus-button ico="backgroundImg" :text="t('insert.backgroundImg.text')" huge @menu-click="showPopup">
    </menus-button>
    <modal v-model:visible="dialogVisible" attach="body" :header="t('insert.backgroundImg.text')" width="800"
        @confirm="onSubmit">
        <div>
            <t-form :data="formData" label-align="top">
                <div class="layoutGrid">
                    <div class="layout_item">
                        <t-form-item :label="t('insert.backgroundImg.uploadText')" name="text">
                            <t-button @click="uploadImg">{{ t('insert.backgroundImg.uploadText') }}</t-button>
                        </t-form-item>
                    </div>
                    <div v-if="fileData.fileUrl" class="layout_item">
                        <img :src="fileData.fileUrl" style="width: 200px;height: auto;" />
                    </div>
                </div>
                <div class="layoutProportion">
                    <div class="layoutProportion-title">{{ t('insert.backgroundImg.backgroundEqualProportion') }}</div>
                    <div>
                        <t-form-item name="equalProportion">
                            <t-switch v-model="formData.equalProportion" @change="doChangeProportion" /><span
                                style="padding-left:10px;">{{
                                    formData.equalProportion ?
                                        t('insert.backgroundImg.equalProportion') :
                                        t('insert.backgroundImg.freeProportionally') }}</span>
                        </t-form-item>
                    </div>
                </div>
                <!-- <div>
                    <div class="layout_content-title">{{ t('insert.backgroundImg.content') }}</div>
                    <t-form-item name="content">
                        <wangEditor v-if="dialogVisible" :text-content="formData.content" :height-props="400"
                            @update-text-content="doUpdateItemContent" />
                    </t-form-item>
                </div> -->


                <div class="layout_padding">
                    <div class="layout_padding_header">
                        {{ t('insert.backgroundImg.paddingHeader') }}
                    </div>

                    <div class="layout_padding-bg-icon">
                        <t-tooltip :content="t('insert.block.tip')" :overlay-style="{ width: '200px' }"
                            placement="right" show-arrow>
                            <div class="raduis-bg-icon" @click="paddingClick">
                                <LinkIcon size="30" :color="paddingLinkValue ? '#1890ff' : ''" />
                            </div>
                        </t-tooltip>
                    </div>



                    <div class="layout_padding_item">
                        <t-form-item :label="t('insert.backgroundImg.paddingTop')" name="paddingTop">
                            <t-input-number v-model="formData.paddingTop" />
                        </t-form-item>
                    </div>
                    <div class="layout_padding_item">
                        <t-form-item :label="t('insert.backgroundImg.paddingRight')" name="paddingRight">
                            <t-input-number v-model="formData.paddingRight" />
                        </t-form-item>
                    </div>
                    <div class="layout_padding_item">
                        <t-form-item :label="t('insert.backgroundImg.paddingBottom')" name="paddingBottom">
                            <t-input-number v-model="formData.paddingBottom" />
                        </t-form-item>
                    </div>
                    <div class="layout_padding_item">
                        <t-form-item :label="t('insert.backgroundImg.paddingLeft')" name="paddingLeft">
                            <t-input-number v-model="formData.paddingLeft" />
                        </t-form-item>
                    </div>

                </div>

            </t-form>
        </div>
    </modal>
</template>
<script setup>
import { LinkIcon } from 'tdesign-icons-vue-next'
import { ref } from 'vue'

import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { editor } = useStore()
let dialogVisible = $ref(false)
const formData = ref({

    paddingTop: 0,
    paddingRight: 0,
    paddingBottom: 0,
    paddingLeft: 0,
    equalProportion: true,
})
const showPopup = () => {
    formData.value = {
        paddingTop: 0,
        paddingRight: 0,
        paddingBottom: 0,
        paddingLeft: 0,
        equalProportion: true,
    }
    fileData.value = {
    }
    paddingLinkValue.value = false
    dialogVisible = true
}
const fileData = ref({})
// 上传图片
const uploadImg = (e) => {
    console.log(e)
    chooseFile((file) => {
        fileData.value = file
    }, {
        optPreChekck: defaultOptPreChekck
    })
}

const onSubmit = (e) => {
    if (!fileData.value.fileUrl) {
        useMessage('error', t('insert.backgroundImg.uploadImg'))
        return
    }


    editor.value
        ?.chain()
        .focus()
        .setBackgroundImg({
            src: fileData.value.fileUrl,
            size: fileData.value.fileSize,
            imageTitle: fileData.value.originName,
            name: fileData.value.originName,
            linkage: paddingLinkValue.value,
            // content: formData.value.content,
            paddingTop: formData.value.paddingTop,
            paddingRight: formData.value.paddingRight,
            paddingBottom: formData.value.paddingBottom,
            paddingLeft: formData.value.paddingLeft,
            equalProportion: formData.value.equalProportion
        })
        .run()
    dialogVisible = false
}

const paddingLinkValue = ref(false)
const paddingClick = () => {

    paddingLinkValue.value = !paddingLinkValue.value

    watch(() => formData.value.paddingTop, (value) => {
        if (paddingLinkValue.value) {
            formData.value.paddingLeft = value
            formData.value.paddingRight = value
            formData.value.paddingBottom = value
        }
    })

    if (paddingLinkValue.value) return useMessage('success', '内边距已联动')
    if (!paddingLinkValue.value) return useMessage('error', '内边距已取消联动')

}


// 等比缩放
const doChangeProportion = (e) => {
    formData.value.equalProportion = e
}

</script>

<style lang="less" scoped>
.layoutGrid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 10px;
    align-items: center;

}

.layoutProportion {
    margin-top: 20px;
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 10px;
    position: relative;
    font-weight: bold;

    .layoutProportion-title {
        position: absolute;
        top: -10px;
        left: 10px;
        background-color: #fff;
        padding: 0 10px;
        color: #333;
    }
}

.layout_Setting {
    margin-top: 20px;
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 10px;
    position: relative;
    font-weight: bold;

    .layout_header_title {
        position: absolute;
        top: -10px;
        left: 10px;
        background-color: #fff;
        padding: 0 10px;
        color: #333;
    }
}

.layout_padding {
    margin-top: 20px;
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 10px;
    position: relative;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 20px;
    font-weight: bold;

    .layout_padding_header {
        position: absolute;
        top: -10px;
        left: 10px;
        background-color: #fff;
        padding: 0 10px;
        color: #333;
    }

    .layout_padding-bg-icon {
        position: absolute;
        right: 10px;
        top: 35%;
        transform: rotate(135deg);
        cursor: pointer;
    }

    .layout_padding_item {}
}

.layout_content-title {
    font-weight: bold;
    margin: 20px 0 10px;
}
</style>
