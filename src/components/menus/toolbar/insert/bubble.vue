<template>
  <menus-button ico="bubbleNew" :text="t('insert.bubble.text')" huge @menu-click="showPopup">
  </menus-button>
  <modal v-model:visible="dialogVisible" attach="body" :header="t('insert.bubble.text')" width="600" mode="modeless"
    :draggable="true" :close-on-overlay-click="false" :footer="false" :show-overlay="true" :z-index="999">
    <div>
      <t-form ref="formValidatorStatus" :data="formData" :rules="rules" :label-width="120" @reset="onReset"
        @submit="onSubmit">
        <t-form-item name="bubbleType">
          <t-radio-group v-model="formData.bubbleType" default-value="1">
            <t-radio-button value="1">{{
              t('insert.bubble.bubbleText')
            }}</t-radio-button>
            <t-radio-button value="2">{{
              t('insert.bubble.bubbleIcon')
            }}</t-radio-button>
          </t-radio-group>
        </t-form-item>

        <t-form-item v-if="formData.bubbleType == 1" :label="t('insert.bubble.bubbleTitle')" name="bubbleTitle">
          <t-input v-model="formData.bubbleTitle" :placeholder="t('insert.bubble.bubbleTitlePlaceholder')"
            show-limit-number></t-input>
        </t-form-item>
        <t-form-item :label="t('insert.bubble.bubbleUrl')" name="bubbleUrl">
          <t-input v-model="formData.bubbleUrl" :placeholder="t('insert.bubble.bubbleUrlPlaceholder')"></t-input>
        </t-form-item>
        <t-form-item :label="t('insert.bubble.colorgroup')" name="fontColor">
          <t-popup placement="right-bottom" :destroy-on-close="false" attach="body">
            <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
              <div
                :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.bubbleFontColor || '#fff'};margin-right:10px;`">
              </div>
              <div v-text="t('insert.bubble.bubbleFontColor')"></div>
            </div>
            <template #content>
              <div style="padding: 10px">
                <color-picker :default-color="defaultFontColor" @change="colorChange" />
              </div>
            </template>
          </t-popup>
          <!-- <menus-toolbar-base-containerColor /> -->

          <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
            <div class="umo-color-picker-more" style="
                display: flex;
                align-items: center;
                cursor: pointer;
                margin-left: 10px;
              ">
              <div
                :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.bubbleBackgroundColor || '#333'};margin-right:10px;`">
              </div>
              <div v-text="t('insert.bubble.bubbleBackgroundColor')"></div>
            </div>
            <template #content>
              <div style="padding: 10px">
                <color-picker :default-color="defaultColor" @change="backgroundColorChange" />
              </div>
            </template>
          </t-popup>
          <!-- <menus-toolbar-base-containerColor /> -->

          <t-popup v-if="formData.bubbleType == 2" placement="right-bottom" attach="body" :destroy-on-close="false">
            <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
              <div
                :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.bubbleBgColor || '#333'};margin-right:10px;`">
              </div>
              <div v-text="t('insert.bubble.bubbleBgColor')"></div>
            </div>
            <template #content>
              <div style="padding: 10px">
                <color-picker :default-color="defaultColor" @change="bubbleBgColorChange" />
              </div>
            </template>
          </t-popup>
        </t-form-item>
        <t-form-item v-if="formData.bubbleType == 2" :label="t('insert.bubble.bubbleIconSelect')"
          name="bubbleIconSelect">
          <t-radio-group v-model="formData.bubbleIconSelect" size="large" default-value="search-alt"
            @change="doChangeIcon">
            <t-radio-button v-for="item in imgList" :key="item.name" :value="item.name">
              <img :src="item.url" style="
                  width: 20px;
                  height: 20px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                " />
            </t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item :label="t('insert.bubble.bubbleContentSelect')" name="bubbleContentSelect">
          <t-radio-group default-value="1" @change="doChangeContent" v-model="formData.bubbleContentSelect">
            <t-radio-button value="1">{{ t('insert.bubble.bubbleContentText') }}</t-radio-button>
            <t-radio-button value="2">{{ t('insert.bubble.bubbleContentImage') }}</t-radio-button>
          </t-radio-group>
        </t-form-item>
        <t-form-item v-if="formData.bubbleContentSelect == '1'" :label="t('insert.bubble.bubbleContent')"
          name="bubbleContent" required-mark>
          <t-textarea v-model="formData.bubbleContent" :placeholder="t('insert.bubble.bubbleContentTxtPlaceholder')"
            :maxlength="1000" :autosize="{ minRows: 4, maxRows: 4 }" show-limit-number></t-textarea>
        </t-form-item>
        <t-form-item v-else>
          <div class="bubble-content-image">
            <div class="bubble-content-image-button">
              <t-button @click="doUploadImage">上传图片</t-button>
            </div>
            <div class="bubble-content-image-preview">
              <img v-if="formData.bubbleContentImage" :src="formData.bubbleContentImage" alt="" />
            </div>
          </div>

        </t-form-item>
        <t-form-item>
          <t-tag theme="warning">{{ t('insert.bubble.notice') }}</t-tag>
        </t-form-item>
        <div class="bottom-button">
          <t-button type="submit" theme="primary">{{
            t('insert.bubble.submit')
          }}</t-button>
          <t-button theme="default" style="margin-left: 10px" @click="dialogVisible = false">{{
            t('insert.bubble.cancel') }}
          </t-button>
        </div>
      </t-form>
    </div>
  </modal>
</template>

<script setup>
import { ref } from 'vue'

import commentAltDots from '@/assets/icons/comment-alt-dots.svg'
import editbubble from '@/assets/icons/editbubble.svg'
import interactive from '@/assets/icons/interactive.svg'
import searchAlt from '@/assets/icons/search-alt.svg'
import twitch from '@/assets/icons/twitch.svg'
import { getSelectionText } from '@/extensions/selection'
import { chooseFile, defaultOptPreChekck } from '@/utils/file'

// import wangEditor from '@/components/wangEditor/index.vue'
const dialogVisible = ref(false)
const defaultFontColor = ref('#fff')
const { editor } = useStore()
const formData = ref({
  bubbleType: '1',
  bubbleTitle: '',
  bubbleUrl: '',
  bubbleContent: '',
  bubbleIconSelect: 'twitch',
  bubbleFontColor: '',
  bubbleBackgroundColor: '',
  bubbleBgColor: '',
  bubbleContentSelect: '1',
})
const formValidatorStatus = ref(null)
const defaultColor = ref('#333')
const rules = {
  bubbleTitle: [
    {
      required: true,
      message: t('insert.bubble.bubbleTitlePlaceholder'),
      type: 'error',
      trigger: 'blur',
    },
  ],
  bubbleContent: [
    {
      required: true,
      message: t('insert.bubble.bubbleContentPlaceholder'),
      type: 'error',
      trigger: 'blur',
    },
  ],
}

// const doUpdateItemContent = (cellTextContentVal) => {
//   formData.value.bubbleContent = cellTextContentVal
// }

const imgList = [
  {
    name: 'twitch',
    url: twitch,
  },
  {
    name: 'search-alt',
    url: searchAlt,
  },
  {
    name: 'interactive',
    url: interactive,
  },
  {
    name: 'editbubble',
    url: editbubble,
  },
  {
    name: 'comment-alt-dots',
    url: commentAltDots,
  },
]

const showPopup = (item) => {
  const test = getSelectionText(editor?.value) || ''


  formData.value = {
    bubbleType: '1',
    bubbleTitle: test || '',
    bubbleUrl: '',
    bubbleContentSelect: '1',
    bubbleContent: '',
    bubbleFontColor: defaultFontColor.value,
    bubbleIconSelect: 'twitch',
    bubbleBackgroundColor: defaultColor.value,
    bubbleBgColor: defaultColor.value,
    bubbleContentSelect: '1',
  }



  const bubbleShowL = localStorage.getItem('bubbleShow')
  if (bubbleShowL != '1') {
    dialogVisible.value = true
  }


}

const onReset = (firstFormItem) => {
  formValidatorStatus.value.resetFields()
}

const doChangeContent = (checkedValues) => {
  console.log(checkedValues)
  formData.value.bubbleContentSelect = checkedValues
}



const doUploadImage = () => {
  chooseFile((file) => {
    formData.value.bubbleContentImage = file.fileUrl
    console.log(formData.value.bubbleContentImage)
  }, {
    optPreChekck: defaultOptPreChekck
  })
}


const doChangeIcon = (value) => {
  formData.value.bubbleIconSelect = value
}

const onSubmit = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    if (formData.value.bubbleUrl != '') {
      if (
        formData.value.bubbleUrl.startsWith('http://') ||
        formData.value.bubbleUrl.startsWith('https://') ||
        formData.value.bubbleUrl.startsWith('ftp://') ||
        formData.value.bubbleUrl.startsWith('ftps://') ||
        formData.value.bubbleUrl.startsWith('www.') ||
        formData.value.bubbleUrl.startsWith('mailto://')
      ) {
        if (formData.value.bubbleUrl.startsWith('www.')) {
          formData.value.bubbleUrl = `https://${formData.value.bubbleUrl}`
        }
      } else {
        useMessage('error', t('insert.bubble.bubbleUrlPlaceholder'))
        return false
      }
    }

    if (!formData.value.bubbleTitle && formData.value.bubbleType == '1') return useMessage('error', t('insert.bubble.bubbleTitlePlaceholder'))
    if (formData.value.bubbleContentSelect == "1" && !formData.value.bubbleContent) return useMessage('error', t('insert.bubble.bubbleContentPlaceholder'))
    if (formData.value.bubbleContentSelect == "2" && !formData.value.bubbleContentImage) return useMessage('error', t('insert.bubble.bubbleContentImagePlaceholder'))
    editor.value
      ?.chain()
      .focus()
      .setBubbleInline({
        bubbleType: formData.value.bubbleType,
        bubbleTitle: formData.value.bubbleTitle,
        bubbleUrl: formData.value.bubbleUrl,

        bubbleFontColor: formData.value.bubbleFontColor,
        bubbleBackgroundColor: formData.value.bubbleBackgroundColor,
        bubbleIconSelect: formData.value.bubbleIconSelect,
        bubbleBgColor: formData.value.bubbleBgColor,
        bubbleContentSelect: formData.value.bubbleContentSelect,
        bubbleContent: formData.value.bubbleContentSelect == '1' ? formData.value.bubbleContent : '',
        bubbleContentImage: formData.value.bubbleContentSelect == '2' ? formData.value.bubbleContentImage : '',
      })
      .run()
    dialogVisible.value = false
  } else {
    MessagePlugin.warning(firstError)
  }
}

const colorChange = (value) => {
  console.log(value)
  formData.value.bubbleFontColor = value
}

const backgroundColorChange = (value) => {
  formData.value.bubbleBackgroundColor = value
}

const bubbleBgColorChange = (value) => {
  formData.value.bubbleBgColor = value
}
</script>

<style scoped lang="less">
.bottom-button {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

::v-deep(.umo-input--auto-width) {
  width: fit-content;
  min-width: 100px;
}

.umo-color-picker-more {
  margin-right: 10px;
}

.bubble-content-image {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  .bubble-content-image-button {}

  .bubble-content-image-preview {
    width: 100px;
    height: 100px;
    margin-left: 10px;

    img {
      width: 100%;
      object-fit: cover;
    }
  }
}
</style>
