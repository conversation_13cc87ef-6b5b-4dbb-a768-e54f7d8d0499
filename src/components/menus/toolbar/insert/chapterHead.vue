<template>
  <menus-button ico="chapterHeader" :text="t('insert.chapterHead.text')" huge @menu-click="submit">
  </menus-button>
</template>
<script setup>
import { getSelectionText } from '@/extensions/selection'
const dialogVisible = ref(false)
const formData = ref({})

const { editor } = useStore()
const submit = () => {
  const test = getSelectionText(editor?.value) || ''
  if (test) {
    editor.value
      ?.chain()
      .focus()
      .setChapterHeader({
        title: test,
      })
      .run()
    editor.value?.commands.deleteSelectionNode()
  } else {
    editor.value
      ?.chain()
      .focus()
      .setChapterHeader({
        title: '',
      })
      .run()
  }


  dialogVisible.value = false
}
</script>

<style lang="less" scoped>
.footer-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  button {
    margin-left: 10px;
  }
}
</style>
