<template>
  <div class="container">
    <!-- <div class="img-link">
      <LinkIcon size="24" style="color:#43b3ef;cursor: pointer;" @click="toLink" />
    </div> -->
    <div class="image-body">
      <img class="img-view" :src="imgUrl" alt="" />
    </div>
    <div v-if="isShowImageTitle == '1'" class="img-header" @click="toLink">
      {{ imgTitle }}
    </div>
    <div class="img-describe">
      {{ imgDescribe }}
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  imgUrl: {
    type: String,
    default: '',
  },
  imgTitle: {
    type: String,
    default: '',
  },
  linkAddress: {
    type: String,
    default: '',
  },
  imgDescribe: {
    type: String,
    default: '',
  },
  isShowImageTitle: {
    type: String,
    default: '0',
  },
})
const toLink = () => {
  if (props.linkAddress) {
    const url = props.linkAddress
    if (url) {
      window.open(url, '_blank')
    }
  }
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  .img-link {
    position: absolute;
    top: 5px;
    left: 5px;
  }
  .image-body {
    margin-top: 10px;
    width: 85%;
    display: flex;
    justify-content: center;
    .img-view {
      width: 100%;
      height: auto;
      object-fit: contain;
    }
  }
  .img-header {
    width: 100%;
    text-align: center;
    margin-top: 10px;
    cursor: pointer;
  }
  .img-describe {
    width: 100%;
    text-align: center;
    font-weight: bold;
    margin-top: 10px;
  }
}
</style>
