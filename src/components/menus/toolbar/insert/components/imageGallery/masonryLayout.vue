<template>
  <div>
    <div v-if="isSwiperTitle == '1'">
      <t-swiper :duration="300" :interval="intervalNumber" :navigation="{ placement: 'outside' }" style="width: 100%"
        :autoplay="true">
        {{ columnContents }}
        <t-swiper-item v-for="(column, index1) in columnContents" :key="index1" style="width: 100%">
          <t-image-viewer :images="imgList" :default-index="index1" :close-on-overlay="true" attach="body"
            @close="imgClose">
            <template #trigger="{ open }">
              <div class="tdesign-demo-image-viewer__ui-image">
                <img :src="column.src" style="
                    border-radius: 5px;
                    width: 100%;
                    height: 300px;
                    object-fit: contain;
                  " />
                <div v-if="isShowImgTitle == '1'" style="
                    position: absolute;
                    bottom: 0;
                    width: 100%;
                    left: 0;
                    height: 40px;
                    background-color: rgba(0, 0, 0, 0.5);
                    color: #fff;
                    justify-content: center;
                    align-items: center;
                    display: flex;
                  ">
                  {{ /\.[^.]+$/.test(column.imageTitle) ? column.imageTitle.replace(/\.[^/.]+$/, '') : column.imageTitle }}
                </div>
                <div class="tdesign-demo-image-viewer__ui-image--hover" @click="open">
                  <span>
                    <BrowseIcon size="1.4em" />
                    {{ preview ? '预览' : t('insert.image.preview') }}
                  </span>
                </div>
              </div>
            </template>
          </t-image-viewer>
        </t-swiper-item>
      </t-swiper>
    </div>
    <div v-else ref="container" class="container">
      <div v-for="(column, index1) in columnContents" :key="index1" class="item">
        <div v-for="(image, index2) in column" :key="index2" style="margin-top: 10px">
          <t-image-viewer :images="[image?.src]" :show-overlay="true" :close-on-overlay="true">
            <template #trigger="{ open }">
              <div class="tdesign-demo-image-viewer__ui-image">
                <img :src="image.src" :alt="image.imageTitle" style="border-radius: 5px" />
                <div class="tdesign-demo-image-viewer__ui-image--hover" @click="open">
                  <span>
                    <BrowseIcon size="1.4em" />
                    {{ preview ? '预览' : t('insert.image.preview') }}
                  </span>
                </div>
              </div>
            </template>
          </t-image-viewer>

          <div class="caption">
            <text v-if="isShowImgTitle == '1'" style="margin-left: 10px">{{
              image.imageTitle.replace(/\.[^/.]+$/, '')
            }}</text>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { BrowseIcon } from 'tdesign-icons-vue-next'
import { onMounted, ref, watch } from 'vue'

const props = defineProps({
  images: {
    type: Array,
    required: true,
    default: () => [],
  },
  columns: {
    type: Number,
    required: true,
    default: 4,
  },
  isShowNo: {
    type: String,
    default: '1',
  },
  isShowImgTitle: {
    type: String,
    default: '1',
  },
  isSwiperTitle: {
    type: String,
    default: '1',
  },
  preview: {
    type: Boolean,
    default: false,
  },
  interval: {
    type: Number,
    default: 2,
  },
})
// 用于存储每列的图片内容
const columnContents = ref([])
const imgList = ref([])

// 图片布局计算
const organizeImages = () => {
  if (props.isSwiperTitle == '1') {
    columnContents.value = props.images
    imgList.value = props.images.map((item) => item.src)
  } else {
    columnContents.value = Array.from({ length: props.columns }, () => [])
    props.images.forEach((image, index) => {
      const columnIndex = index % props.columns // 按列计算分配
      columnContents.value[columnIndex].push(image)
    })
  }
}

const intervalNumber = computed(() => {
  return props.interval * 1000
})

onMounted(() => {
  organizeImages()
})

const imgClose = () => {
  console.log('imgClose')
}

watch(
  () => [props.images, props.columns, props.isSwiperTitle],
  () => {
    organizeImages()
  },
  { deep: true },
)
</script>
<style scoped>
.umo-image-viewer-preview-image .umo-image-viewer__modal-mask {
  background-color: var(--td-mask-active) !important;
}

.umo-image-viewer-preview-image .umo-image-viewer__modal-pic {
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}
</style>
<style lang="less" scoped>
.container {
  width: 98%;
  column-gap: 5px;
  /* 列间距 */
  margin: 0 auto;
  padding: 5px;
  display: flex;
}

.item {
  flex: 1;
  margin-bottom: 15px;
  break-inside: avoid;
  overflow: hidden;
  text-align: center;
}

//.item div {
//  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
//  border-radius: 8px;
//}

.item img {
  display: block;
  width: 100%;
  height: auto;
}

.item .caption {
  text-align: center;
  font-size: 14px;
  color: #555;
}

.tdesign-demo-image-viewer__ui-image {
  width: 100%;
  height: 100%;
  display: inline-flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: var(--td-radius-small);
  overflow: hidden;
}

.tdesign-demo-image-viewer__ui-image--hover {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--td-text-color-anti);
  line-height: 22px;
  transition: 0.2s;
}

.tdesign-demo-image-viewer__ui-image:hover .tdesign-demo-image-viewer__ui-image--hover {
  opacity: 1;
  cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--img {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  cursor: pointer;
  position: absolute;
}

.tdesign-demo-image-viewer__ui-image--footer {
  padding: 0 16px;
  height: 56px;
  width: 100%;
  line-height: 56px;
  font-size: 16px;
  position: absolute;
  bottom: 0;
  color: var(--td-text-color-anti);
  background-image: linear-gradient(0deg,
      rgba(0, 0, 0, 0.4) 0%,
      rgba(0, 0, 0, 0) 100%);
  display: flex;
  box-sizing: border-box;
}

.tdesign-demo-image-viewer__ui-image--title {
  flex: 1;
}

.tdesign-demo-popup__reference {
  margin-left: 16px;
}

.tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
  cursor: pointer;
}

.tdesign-demo-image-viewer__base {
  margin: 10px;
  border: 4px solid var(--td-bg-color-secondarycontainer);
  border-radius: var(--td-radius-medium);
}
</style>
