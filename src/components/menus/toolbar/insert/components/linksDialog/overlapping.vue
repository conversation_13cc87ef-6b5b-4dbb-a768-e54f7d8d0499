<template>
  <modal :visible="isShow" icon="link" :header="t('insert.link.crossReferencing')" width="800px" draggable
    destroy-on-close :confirm-btn="update ? t('insert.link.ok') : t('insert.link.insert')" :top="50"
    @confirm="insertLink" @close="() => close()">
    <div class="umo-link-container">
      <t-form label-width="150px">
        <t-form-item :label="t('insert.link.quoteType')">
          <t-select v-model="type" clearable :status="error.type ? 'error' : 'default'" @change="onChange">
            <t-option v-for="(ele, i) in option" :key="i" :label="ele.label" :value="ele.value" />
          </t-select>
        </t-form-item>

        <t-form-item :label="t('insert.link.hrefTitle')">
          <t-input v-model="linkName" :status="error.text ? 'error' : 'default'" type="url" :maxlength="30" clearable
            :placeholder="t('insert.link.hrefTitle')" />
        </t-form-item>

        <t-form-item :label="t('insert.link.quoteContent')">
          <div class="umo-link-table">
            <div class="header">
              <t-input v-model="searchVal" clearable :placeholder="t('newToc.placeholder')"
                style="width: 250px; margin-right: 10px" :maxlength="30" show-limit-number @change="searchIptChange" />
              <t-button @click="search">{{
                t('insert.link.quoteSerarch')
              }}</t-button>
            </div>
            <div class="content">
              <t-radio-group v-model="radioVal" @change="handleChange">
                <t-radio v-for="ele in selectData" :key="ele.id" :value="ele.id">
                  {{ ele.name || '未知图片' }}</t-radio>
              </t-radio-group>
            </div>
          </div>
        </t-form-item>
      </t-form>
    </div>
  </modal>
</template>

<script setup>
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false,
  },
  close: {
    type: Function,
    default: () => () => { },
  },
  text: {
    type: String,
    default: '',
  },
  attrs: {
    type: Object,
    default: null,
  },
  href: {
    type: String,
    default: '',
  },
  quoteType: {
    type: String,
    default: '',
  },
  update: Function,
})

const { editor } = useStore()
const rowSelectionAllowUncheck = ref(false)
let selectData = $ref([])
let searchData = $ref([])
let isShow = $ref(false)
let searchVal = $ref('')

let selectValId = $ref('')
let linkName = $ref(props?.text)
let href = $ref(props?.href)

let type = $ref('')
let radioVal = $ref('')
const error = $ref({ text: false, type: false })
const option = [
  {
    label: t('insert.link.Image'),
    value: 'imageLayout,image,imageIcon,imageInLine',
  },
  {
    label: t('insert.link.3d'),
    value: 'resourceCover_0',
  },
  {
    label: t('insert.link.ar'),
    value: 'resourceCover_1',
  },
  {
    label: t('insert.link.simulation'),
    value: 'resourceCover_3',
  },
  {
    label: t('insert.link.game'),
    value: 'resourceCover_4',
  },
  {
    label: t('insert.link.teachingResources'),
    value: 'resourceCover_5',
  },
  {
    label: t('insert.link.audio'),
    value: 'audio',
  },
  {
    label: t('insert.link.video'),
    value: 'video',
  },
]

const handleChange = (v) => {
  selectValId = v
  const test = selectData.find((ele) => ele.id === v)
  href = test?.href || ''
  linkName = test?.name || '公式标题'
}

const insertLink = () => {
  if (type === '') {
    error.type = true
    return
  }
  if (linkName === '') {
    error.text = true
    return
  }
  error.text = false
  error.type = false

  if (props.update) {
    props.update({
      quoteType: type || props.quoteType,
      title: linkName,
      href:
        selectData.find((ele) => ele.id === selectValId)?.src ||
        selectData.find((ele) => ele.id === selectValId)?.url,
      attrs: searchData.find((ele) => ele.id === radioVal),
    })
  } else {
    editor.value?.commands.setLinks({
      title: linkName,
      type: 'crossReferencing',
      href:
        selectData.find((ele) => ele.id === selectValId)?.src ||
        selectData.find((ele) => ele.id === selectValId)?.url,
      quoteType: type,
      attrs: searchData.find((ele) => ele.id === radioVal),
    })
    props.close()
  }
}

watch(
  () => props.dialogVisible,
  (val) => {

    isShow = val
    error.text = false
    error.type = false
    type = props?.quoteType
    linkName = props?.text
    href = props?.href
    radioVal = props.attrs?.id
    const validQuoteTypes = ['imageLayout', 'image', 'imageIcon', 'imageInLine']

    if (validQuoteTypes.includes(props?.quoteType)) {
      onChange('imageLayout,image,imageIcon,imageInLine')
    } else {
      onChange(props?.quoteType)
    }

  },
)

const onChange = (v) => {
  if (!v) {
    selectData = []
    searchData = []
    return
  }

  const jsonData = editor.value.getJSON()
  const _arr = []

  // 递归遍历所有节点
  function traverse(nodes, pageNumber) {
    if (!Array.isArray(nodes)) return
    nodes.forEach((node) => {
      // 处理 resourceCover 特殊类型
      if (v.startsWith('resourceCover_') && node.attrs && v === `resourceCover_${node.attrs.rcType}`) {
        _arr.push({
          name: node.attrs.title || node.attrs.name,
          id: node.attrs.id,
          src: node.attrs.src,
          url: node.attrs.url,
          pageNumber,
        })
      } else if (node.type && v.split(',').includes(node.type)) {
        _arr.push({
          name: node.attrs?.imageTitle
            ? node.attrs.imageTitle
            : node.type === 'image'
              ? '公式标题'
              : node.attrs?.name
                ? node.attrs.name
                : '未知图片',
          id: node.attrs?.id,
          src: node.attrs?.src,
          url: node.attrs?.url,
          pageNumber,
        })
      }
      // 递归子节点
      if (node.content) {
        traverse(node.content, pageNumber)
      }
    })
  }

  // 第一层 page 需要带上页码
  jsonData?.content?.forEach((ele, i) => {
    traverse([ele], i + 1)
  })

  selectData = [..._arr]
  searchData = [..._arr]
}

/**
 * @list 原数组
 * @search 匹配的文字
 * @fieldName 被匹配对象字段名
 */
const fuzzySearch = (list, search, fieldName) => {
  if (!search) return list
  const data = []
  if (list.length != 0 && search) {
    const str = `S*${search}S*`
    const reg = new RegExp(str, 'i') //不区分大小写
    list.map((item) => {
      //item是一个对象,name循环这个对象的每一项进行模糊查询
      if (typeof item === 'object') {
        if (reg.test(item[fieldName])) {
          data.push(item)
        }
      } else {
        if (reg.test(item)) {
          data.push(item)
        }
      }
    })
  }
  return data
}

const searchIptChange = (v) => {
  if (!v) {
    selectData = [...searchData]
    radioVal = ''
  }
}
//搜索
const search = () => {
  selectData = fuzzySearch(searchData, searchVal, 'name')
  radioVal = ''
  searchVal = ''
}
</script>

<style lang="less" scoped>
.umo-link-table {
  width: 100%;
  max-height: 350px;
  border: 1px solid var(--umo-border-color);
  border-radius: 5px;
  padding: 10px;
  box-sizing: border-box;

  .header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .content {
    height: 290px;
    overflow-y: auto;
    scrollbar-width: none;
    /* 针对 Firefox */
    -ms-overflow-style: none;
    /* 针对 Internet Explorer 和 Edge */
  }
}
</style>
