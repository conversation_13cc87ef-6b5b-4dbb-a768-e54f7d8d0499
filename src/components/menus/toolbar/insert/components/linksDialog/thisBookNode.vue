<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-02 11:49:43
 * @LastEditTime: 2024-12-11 16:02:05
 * @FilePath: \dutp-editor\src\components\menus\toolbar\insert\components\linksDialog\thisBookNode.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->

<!-- 模块说明 -->
<style lang="less" scoped></style>

<template>
  <modal
    :visible="isShow"
    icon="link"
    :header="t('insert.link.title1')"
    width="420px"
    draggable
    destroy-on-close
    :confirm-btn="update ? t('insert.link.ok') : t('insert.link.insert')"
    @confirm="insertLink"
    @close="() => close()"
  >
    <div class="umo-link-container">
      <t-form label-align="top">
        <t-form-item :label="t('insert.link.selectDirectory')">
          <t-cascader
            v-model.trim="href"
            :status="error.href ? 'error' : 'default'"
            :placeholder="t('insert.link.selectDirectory')"
            clearable
            :checkStrictly="true"
            :filterable="true"
            :keys="{ label: 'name', value: 'id' }"
            :options="chapter"
          />
        </t-form-item>
        <t-form-item :label="t('insert.link.hrefTitle')">
          <t-input
            v-model="text"
            :status="error.text ? 'error' : 'default'"
            type="url"
            clearable
            :placeholder="t('insert.link.hrefTitle')"
          />
        </t-form-item>
      </t-form>
    </div>
  </modal>
</template>

<script setup>
import { getChapterCatalogue } from '@/api/chapterContent'
const props = defineProps({
  dialogVisible: Boolean,
  close: {
    type: Function,
    default: () => () => {},
  },
  update: Function,
  text: {
    type: String,
    default: '',
  },
  href: {
    type: String,
    default: '',
  },
})
const { editor, chapterId } = useStore()
let chapter = $ref([])

let isShow = $ref(false)
let text = $ref('')
let href = $ref('')
const error = $ref({ text: false, href: false })
const insertLink = () => {

  if (href === '') {
    error.href = true
    return
  }
  if (text === '') {
    error.text = true
    return
  }
  error.text = false
  error.href = false
  if (props.update) {
    props.update({ title: text, href })
  } else {
    editor.value?.commands.setLinks({
      title: text,
      type: 'chaptersInThisBook',
      href,
    })
    props.close()
  }
}

const chapterCatalogue = async () => {
  const res = await getChapterCatalogue(chapterId.value)
  if (res.code === 200) {
    chapter = res.data
  }
 
}

watch(
  () => props.dialogVisible,
  (val) => {
    if (val) chapterCatalogue()
    isShow = val
    error.text = false
    error.href = false
    text = props.text
    href = props.href
  },
)
</script>
