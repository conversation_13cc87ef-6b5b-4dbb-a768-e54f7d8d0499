<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-02 10:46:12
 * @LastEditTime: 2024-12-02 11:51:09
 * @FilePath: \dutp-editor\src\components\menus\toolbar\insert\components\linksDialog\web.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<template>
  <modal
    :visible="isShow"
    icon="link"
    :header="t('insert.link.title2')"
    width="420px"
    draggable
    destroy-on-close
    :confirm-btn="update ? t('insert.link.ok') : t('insert.link.insert')"
    @confirm="insertLink"
    @close="() => close()"
  >
    <div class="umo-link-container">
      <t-form label-align="top">
        <t-form-item :label="t('insert.link.hrefText')">
          <t-input
            v-model.trim="text"
            :status="error.text ? 'error' : 'default'"
            :placeholder="t('insert.link.hrefTextTip')"
            clearable
            maxlength="500"
            show-limit-number
          />
        </t-form-item>
        <t-form-item :label="t('insert.link.href')">
          <t-input
            v-model="href"
            :status="error.href ? 'error' : 'default'"
            type="url"
            clearable
            :placeholder="t('insert.link.hrefTip')"
          />
        </t-form-item>
      </t-form>
    </div>
  </modal>
</template>

<script setup>
const props = defineProps({
  dialogVisible: Boolean,
  close: {
    type: Function,
    default: () => () => {},
  },
  update: Function,
  text: {
    type: String,
    default: '',
  },
  href: {
    type: String,
    default: '',
  },
})
const { editor } = useStore()

let isShow = $ref(false)
let text = $ref('')
let href = $ref('')
const error = $ref({ text: false, href: false })
const insertLink = () => {
  if (text === '') {
    error.text = true
    return
  }
  if (
    !href.startsWith('http://') &&
    !href.startsWith('https://') &&
    !href.startsWith('ftp://') &&
    !href.startsWith('ftps://') &&
    !href.startsWith('www.') &&
    !href.startsWith('mailto://')
  ) {
    error.href = true
    return
  }
  error.text = false
  error.href = false
  if (href.startsWith('www.')) {
    href = 'https://' + href;
  }
  if (props.update) {
    props.update({ title: text, href })
  } else {
    editor.value?.commands.setLinks({ title: text, type: 'websiteLink', href })
    props.close()
  }
}

watch(
  () => props.dialogVisible,
  (val) => {
    isShow = val
    error.text = false
    error.href = false
    text = props.text
    href = props.href
  },
)
</script>

<style lang="less" scoped></style>
