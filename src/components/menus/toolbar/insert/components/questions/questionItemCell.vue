<template>
  <div class="question-cell-con">
    <div class="label">
      <div class="cell-label">
        <span>{{ label }}</span>
        <span v-if="required" class="required">*</span>
      </div>
    
    </div>

    <div class="cell-info">
      <div class="cell-content">
        <slot name="default" />
      </div>
    </div>
  </div>

</template>

<script setup name="questionItemCell">
defineProps({
  label: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
})
</script>

<style lang="less" scoped>
@import '@/assets/styles/_common.less';
.question-cell-con {
  width: 100%;
  display: flex;
  // padding: 15px 0px;
  margin-bottom: 20px;
  .label {
    width: 100px;
  }
  .cell-info {
    width: 100%;
    .base-flex-row;
    justify-content: flex-start;
    align-items: flex-start;
    .cell-label {
      display: flex;
      width: 100px;
      background-color: red;
    }
    .cell-content {
      flex: 1;
    }
  }
}
</style>
