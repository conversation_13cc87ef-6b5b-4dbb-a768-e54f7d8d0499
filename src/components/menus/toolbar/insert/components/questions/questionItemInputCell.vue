<template>
  <div class="question-cell-con">
    <div class="cell-info">
      <div class="cell-label">
        <span>{{ props.label }}</span>
        <span v-if="props.required" class="required">*</span>
      </div>
      <div class="cell-content">
        <wangEditor
          v-if="canLoadRichTextEditorWang"
          :height-props="editorTextbarConfig?.height"
          :max-height-props="editorTextbarConfig?.maxHeight"
          :text-content="itemContent"
          :toolbar-config="editorToolbarConfig"
          :placeholder="props.placeholder"
          @update-text-content="doUpdateItemContent"
          @on-editor-created="(editor) => emit('onEditorCreated', editor)"
        />
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

import wangEditor from '@/components/wangEditor/index.vue'

const emit = defineEmits(['updateItemStem', 'onEditorCreated'])
const props = defineProps({
  editorToolbarConfig: {
    type: Object,
    default: null,
  },
  editorTextbarConfig: {
    type: Object,
    default: null,
  },
  label: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  itemContent: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '请输入内容...',
  },
})

// computed 也是只读属性
/*let localTextContent = computed(() => {
  return props.textContent
})*/

const localItemContent = ref('')
const canLoadRichTextEditorWang = ref(false)

const doUpdateItemContent = (cellTextContentVal) => {
  emit('updateItemStem', cellTextContentVal)
}
// 回显数据
onMounted(() => {
  localItemContent.value = props.itemContent
  canLoadRichTextEditorWang.value = true
})

/*const initPageData = async () => {
  localItemContent.value = props.itemContent
  await nextTick()
  showRichEditorFlag.value = true
}*/
/*watch(
  () => cellTextContent,
  (v) => {
    // 发送更新通知的时候注意要注意要使用 ref 的 value！！！
    emit('update:textContent', v.value)
  },
  { deep: true }, // { immediate: true, deep: true },
)*/
</script>

<style lang="less" scoped>
@import '@/assets/styles/_common.less';
.question-cell-con {
  width: 100%;
  padding: 15px 0px;
  margin-bottom: 20px;

  .cell-info {
    width: 100%;
    .base-flex-row;
    justify-content: flex-start;
    align-items: flex-start;
    .cell-label {
      width: 100px;
    }
    .cell-content {
      flex: 1;
    }
  }
}
</style>
