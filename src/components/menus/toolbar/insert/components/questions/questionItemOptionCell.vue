<template>
  <nav class="choiceQuestion-main-header">
    <span>{{ t('insert.questions.options') }}{{ OPTION_EN[optionIndex] }}</span>
    <div class="option-nav-item-btn">
      <slot name="markAsRightAnswer" />
      <slot name="removeThisOptionCell" />
    </div>
  </nav>
  <wangEditor
    v-if="canLoadRichTextEditorWang"
    :text-content="textContent"
    :toolbar-config="editorToolbarConfig"
    @update-text-content="doUpdateItemContent"
  />
</template>

<script setup name="questionItemOptionCell">
import { ref } from 'vue'

import wangEditor from '@/components/wangEditor/index.vue'
import { OPTION_EN } from '@/utils/quetions-utils.ts'

const props = defineProps({
  editorToolbarConfig: {
    type: Object,
    default: null,
  },
  optionIndex: {
    type: Number,
    default: 0,
  },
  textContent: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['updateItemStem', 'updateItemOption'])

const canLoadRichTextEditorWang = ref(false)

// hook
onMounted(() => {
  canLoadRichTextEditorWang.value = true
})

const doUpdateItemContent = (cellTextContentVal) => {
  emit('updateItemOption', cellTextContentVal)
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/_common.less';
.choiceQuestion-main-header {
  width: calc(100% - 12px);
  border: 1px solid rgb(222, 222, 222);
  background-color: rgb(239, 239, 239);
  padding: 5px 5px;
  .base-flex-row;
  justify-content: space-between;
  align-items: center;
}
</style>
