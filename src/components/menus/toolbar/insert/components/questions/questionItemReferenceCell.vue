<!-- 添加知识点组件，题目参考了教材的哪个章节和哪个小节 -->
<template>
  <t-form-item
    :label="t('forms.fields.knowledgeReference')"
    name="chapterReference"
    :initial-data="chapterAnchor.id"
    class="question-item-refer"
  >
    <div class="cell-content">
      <t-button
        size="small"
        @click="showChapterPopup"
        v-text="t('base.add')"
      ></t-button>
    </div>
    <p>{{ chapterAnchor.label }}</p>
  </t-form-item>
  <menus-toolbar-modal-chapter
    :visibility="showChapterReferPopup"
    :params="parentParams"
    @close="chapterReferPopupClose()"
  />
</template>

<script setup name="QuestionItemReferenceCell">
const { chapterId } = useStore()
let showChapterReferPopup = $ref(false)
const props = defineProps({
  label: {
    type: String,
    default: '',
  },
  params: {
    type: Object,
    default: {},
  },
  // 参考章节小节
  chapterAnchor: {
    type: Object,
    default: {
      id: '',
      label: '',
    },
  },
})
const parentParams = {
  chapterId: chapterId.value,
}
const showChapterPopup = () => {
  showChapterReferPopup = true
}
const chapterReferPopupClose = () => {
  
  showChapterReferPopup = false
}

</script>

<style lang="less" scoped>
@import '@/assets/styles/_common.less';
.question-item-refer {
  width: 100%;
  padding: 15px 0px;
  margin-bottom: 20px;
  :deep(.umo-form__label--top) {
    float: left;
  }
  :deep(.umo-form__controls-content) {
    display: block;
  }
  .cell-info {
    width: 100%;
    .base-flex-row;
    justify-content: flex-start;
    align-items: flex-start;
    .cell-label {
      margin-right: 20px;
    }
    .cell-content {
      flex: 1;
    }
  }
}
</style>
