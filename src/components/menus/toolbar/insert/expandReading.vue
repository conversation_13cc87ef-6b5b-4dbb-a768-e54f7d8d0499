<template>
  <menus-button ico="expandReading" :text="t('insert.expandReading')" huge @menu-click="showPopup">
  </menus-button>
  <t-dialog v-model:visible="dialogVisible" attach="body" :header="t('insert.expandReading')" width="20%"
    :close-on-overlay-click="false" @opened="onOpen" @confirm="onSubmit" @close="onClose" @cancel="onClose">
    <div>
      <t-form ref="formValidatorStatus" :data="formData">
        <t-form-item :label="t('rc.form.title')" name="name" :rules="[
          {
            required: true,
            message: t('rc.form.titleRequired'),
            type: 'error',
          },
        ]">
          <t-input v-model="formData.name" :placeholder="t('rc.form.titlePlaceholder')" :maxlength="100" />
        </t-form-item>

        <t-form-item :label="t('insert.fileDialog.isExpand')" name="isExpand">
          <t-switch v-model="formData.isExpand"></t-switch>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script setup>
import { ref } from 'vue'
const { options } = useStore()
// import RichTextEditor from '@/components/editor/index.vue'
const emit = defineEmits(['updateItemStem'])
const dialogVisible = ref(false)
const { editorWang, togglePopup, editor } = useStore()

const formData = ref({
  name: '',
  isShowTitle: '1',
  readingContent: '',
  isExpand: true,
})
const formValidatorStatus = ref(null)

const onClose = () => {
  dialogVisible.value = false
}

const onOpen = () => {
  formData.value.name = ''
  nextTick(() => {
    formValidatorStatus.value.clearValidate()
  })
}

const showPopup = (item) => {
  dialogVisible.value = true
}

onMounted(() => {
  formValidatorStatus.value.validate()
})

async function onSubmit() {
  const valid = await formValidatorStatus.value.validate({
    showErrorMessage: true,
  })
  if (valid === true) {
    editor.value
      .chain()
      .focus()
      .setExtendedReading({
        name: formData.value.name,
        isExpand: formData.value.isExpand,
      })
      .run()

    dialogVisible.value = false
  }
}
</script>
