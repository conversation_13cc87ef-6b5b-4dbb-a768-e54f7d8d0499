<template>
  <menus-button
    ico="file"
    :text="t('insert.file')"
    huge
    @menu-click="insertFile"
  />
</template>

<script setup lang="ts">
import { chooseFile } from '@/utils/file'
const { editor } = useStore()
const insertFile = () => {
  chooseFile(
    (file) => {
      editor.value?.chain().focus().setFile({
        url: file.fileUrl,
        name: file.originName,
        size: file.fileSize,
      })
    },
    {
      multiple: false,
      fileType: '*'
    }
  )
}
</script>
