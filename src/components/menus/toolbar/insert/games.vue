<template>
  <menus-button
    ico="games"
    :text="t('insert.games.text')"
    huge
    @menu-click="openDialog"
  />
  <modal
    :visible="dialogVisible"
    :header="t('insert.games.text')"
    width="400px"
    :confirm-btn="t('insert.games.insert')"
    @confirm="insertGame"
    @close="dialogVisible = false"
  >
    <div class="umo-web-page-container" style="height: 170px">
      <t-form
        ref="formValidatorStatus"
        :data="formData"
        :rules="rules"
        :label-width="80"
        label-align="top"
      >
        <t-form-item :label="t('insert.games.titltTip')" name="name">
          <t-input
            v-model.trim="formData.name"
            required-mark
            clearable
            :placeholder="t('insert.games.titleplaceholder')"
            :maxlength="20"
          />
        </t-form-item>

        <t-form-item :label="t('insert.games.tip')" name="url">
          <t-input
            v-model.trim="formData.url"
            required-mark
            clearable
            :placeholder="t('insert.games.placeholder')"
          />
          <t-tooltip :content="admin_content" :show-arrow="false">
            <t-icon
              name="error-circle"
              size="16px"
              style="color: #0006; margin-left: 10px"
            />
          </t-tooltip>
        </t-form-item>
      </t-form>
    </div>
  </modal>

  <!-- 
    @click="" -->
</template>

<script setup>
import { ref } from 'vue'
const admin_content = ref(import.meta.env.VITE_APP_ADMIN_CONTENT)
import { RCType } from '@/enum/extensions/RC'
const { editor } = useStore()
const dialogVisible = ref(false)

const formValidatorStatus = ref(null)
const formData = ref({
  name: '',
  url: '',
})

const rules = {
  name: [
    {
      required: true,
      message: t('insert.games.nameTip'),
      type: 'error',
      trigger: 'blur',
    },
  ],
  url: [
    { required: true, message: t('insert.games.gameTip'), type: 'error' },
    {
      url: {
        protocols: ['http', 'https', 'ftp'],
        require_protocol: true,
      },
      message: t('insert.games.gameTip'),
    },
  ],
}

watch(dialogVisible, (val) => {
  dialogVisible.value = val
})

const openDialog = () => {
  formData.value = {
    name: '',
    url: '',
  }
  dialogVisible.value = true
}

const insertGame = () => {
  const validateResult = formValidatorStatus.value.validate((valid) => valid)

  validateResult.then((valid) => {
    if (valid == true) {
      dialogVisible.value = false
      editor.value
        .chain()
        .focus()
        .setResourceCover({
          rcType: RCType.GAME,
          url: formData.value.url,
          title: formData.value.name,
        })
        .run()
    }
  })
}
</script>
