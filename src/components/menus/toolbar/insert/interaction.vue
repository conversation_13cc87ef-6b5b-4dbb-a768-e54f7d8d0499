<template>
  <menus-button
    ico="interaction"
    menu-type="dropdown"
    :text="t('insert.interaction.text')"
    huge
  >
    <template #dropmenu>
      <t-dropdown-menu>
        <t-dropdown-item
          v-for="item in interactionOptions"
          :key="item.value"
          :value="item.value"
          @click="
            editor
              ?.chain()
              .focus()
              .setInteraction({
                interactionType: item.interactionType,
              })
              .run()
          "
        >
          <span>{{ item.label }}</span>
        </t-dropdown-item>
      </t-dropdown-menu>
    </template>
  </menus-button>
</template>

<script setup>
import { InteractionType } from '@/enum/extensions/interaction'
const { editor } = useStore()
const interactionOptions = [
  {
    label: t('insert.interaction.options.vote'),
    value: 'vote',
    interactionType: InteractionType.VOTE,
  },
  {
    label: t('insert.interaction.options.wordCloud'),
    value: 'wordCloud',
    interactionType: InteractionType.WORD_CLOUD,
  },
  {
    label: t('insert.interaction.options.discussion'),
    value: 'discussion',
    interactionType: InteractionType.DISCUSSION,
  },
  {
    label: t('insert.interaction.options.imageWaterfall'),
    value: 'imageWaterfall',
    interactionType: InteractionType.IMAGE_WATER_FALL,
  },
]
// const handleSelect = (item) => {
//   editor.value
//     ?.chain()
//     .focus()
//     .setInteraction({
//       interactionType: item.interactionType,
//     })
//     .run()
// }
</script>
