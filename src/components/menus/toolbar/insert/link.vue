<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2025-02-25 14:39:48
 * @FilePath: \dutp-editor\src\components\menus\toolbar\insert\link.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<template>
  <menus-button
    ico="link"
    :text="t('insert.link.text')"
    menu-type="dropdown"
    :huge="isHuge"
    :trigger="isTirgger"
    :disabled="isDisabled"
    :tooltip="isTooltip"
    :placement="isPlacement"
  >
    <template #dropmenu>
      <t-dropdown-menu>
        <t-dropdown-item
          v-for="item in options"
          :key="item.type"
          class="umo-text-image-menu"
          @click="openLinkDialog(item.type)"
        >
          <span>{{ t(item.label) }}</span>
        </t-dropdown-item>
      </t-dropdown-menu>
    </template>
  </menus-button>
  <Web
    :dialog-visible="dialogIsShow.web"
    :text="text"
    :close="() => (dialogIsShow.web = false)"
  />
  <BookNode
    :dialog-visible="dialogIsShow.bookNode"
    :close="() => (dialogIsShow.bookNode = false)"
  />
  <Overlapping
    :dialog-visible="dialogIsShow.overlapping"
    :close="() => (dialogIsShow.overlapping = false)"
  />
  <ResourceLibrary
    v-model:visible="dialogIsShow.resourceLibrary"
    :is-link="true"
    :multiple="true"
    @insert-by-resource="insertByResource"
  />
</template>

<script setup lang="ts">
import Overlapping from './components/linksDialog/overlapping.vue'
import BookNode from './components/linksDialog/thisBookNode.vue'
import Web from './components/linksDialog/web.vue'
import { getSelectionText } from '@/extensions/selection'
const { editor, batchInsert } = useStore()
const dialogIsShow = reactive({
  web: false,
  bookNode: false,
  overlapping: false,
  resourceLibrary: false,
})
const props = defineProps({
  isHuge: {
    type: Boolean,
    default: false,
  },
  isTirgger: {
    type: String,
    default: 'click',
  },
  isDisabled: {
    type: Boolean,
    default: false,
  },
  isTooltip: {
    type: [String, Boolean],
    default: undefined,
  },
  isPlacement: {
    type: String,
    default: 'bottom-left',
  },
})
const text = ref('');
const options = [
  { label: 'insert.link.websiteLink', type: 'websiteLink' },
  { label: 'insert.link.crossReferencing', type: 'crossReferencing' },
  { label: 'insert.link.resourceLibrary', type: 'resourceLibrary' },
]

const openLinkDialog = (type: string) => {
  switch (type) {
    case 'chaptersInThisBook':
      dialogIsShow.bookNode = true
      break

    case 'websiteLink':
      text.value = getSelectionText(editor?.value) || ''
      dialogIsShow.web = true
      break

    case 'crossReferencing':
      dialogIsShow.overlapping = true
      break

    case 'resourceLibrary':
      dialogIsShow.resourceLibrary = true

      break

    default:
      break
  }
}

const handleCloseOverlappingDialog = (data) => {

  dialogIsShow.overlapping = data
}

const resourceLibraryClose = (data) => {
  dialogIsShow.resourceLibrary = data
}

const insertByResource = (files) => {
  batchInsert(files, 'links', (file) => ({
    title: file.title,
    type: 'resourceLibrary',
    href: file.fileUrl,
    attrs: file,
  }))
}
</script>

<style lang="less" scoped>
.umo-link-container {
  padding: 2px;
  :deep(.umo-form__item) {
    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }
}
</style>
