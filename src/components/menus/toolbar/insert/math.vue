<template>
  <menus-button ico="math" :text="t('insert.math')" menu-type="popup" huge :popup-visible="popupVisible"
    @toggle-popup="togglePopup">
    <template #content>
      <div class="formula">
        <div class="formula-item" @click="addChemistryFormula">
          {{ t('insert.chemicalFormula') }}
        </div>
        <div class="formula-item" @click="addLetexFormula">
          {{ t('insert.latexFormula') }}
        </div>
        <div class="formula-item" @click="addMathFormula">
          {{ t('insert.mathFormula') }}
        </div>
      </div>
    </template>
    <formula-dialog :visibility="formulaDialogVisibility" :provider-name="formulaProviderName"
      :formula-type="formulaType" @confirm="confirmOnFormulaEditorDialog"
      @cancel="formulaDialogVisibility = false"></formula-dialog>
  </menus-button>
</template>
<script setup lang="ts">
import FormulaDialog from '@/components/menus/toolbar/modal/formulaDialog.vue'
let formulaDialogVisibility = $ref(false)
let formulaProviderName = $ref('')
let formulaType = $ref('')
const { editor } = useStore()
const { popupVisible, togglePopup } = usePopup()
function addLetexFormula() {
  formulaProviderName = 'latex'
  formulaType = 'latex'
  formulaDialogVisibility = true
  popupVisible.value = false
}
function addMathFormula() {
  formulaProviderName = 'wiris'
  formulaType = 'math'
  formulaDialogVisibility = true
  popupVisible.value = false
}
function addChemistryFormula() {
  formulaType = 'chem'
  formulaDialogVisibility = true
  formulaProviderName = 'wiris'
  popupVisible.value = false
}

function confirmOnFormulaEditorDialog({
  url,
  width,
  height,
  name,
  langData,
  providerName,
  formulaType,
  isLine,
}) {
  formulaDialogVisibility = false

  if (isLine) {
    editor.value
      .chain()
      .focus()
      .setFormulaInLine({
        //       src: file.fileUrl,
        //       size: file.fileSize,
        //       imageTitle: file.name,
        //       name: file.name,
        src: url,
        width,
        height,
        imageTitle: name,
        isShowImageTitle: '0',
      })
      .run()
  } else {
    editor.value
      .chain()
      .focus()
      .setImage({
        width,
        height,
        src: url,
        name,
        imagetTitle: name,
        content: langData,
        isShowImageTitle: '0',
        providerName,
        formulaType,
      })
      .run()
  }
}
</script>
<style lang="less" scoped>
.formula {
  text-align: left;
  font-size: 14px;

  .formula-item {
    width: 100%;
    display: flex;
    align-items: left;
    line-height: 30px;
    border-radius: 5px;
    padding: 0 5px;
    box-sizing: border-box;

    &:hover {
      cursor: pointer;
      background-color: #f6f6f6;
    }
  }
}
</style>
