<template>
  <menus-button
    ico="paperWarrapper                                                                                                    "
    menu-type="dropdown"
    :text="t('insert.paperWarrapper.text')"
    huge
    :trigger="isTirgger"
    :disabled="isDisabled"
    :tooltip="isTooltip"
    :placement="isPlacement"
  >
    <template #dropmenu>
      <t-dropdown-menu>
        <t-dropdown-item
          v-for="item in options"
          :key="item.value"
          class="umo-text-image-menu"
          :value="item.value"
          @click="imageChange(item)"
        >
          <span>{{ item.label }}</span>
        </t-dropdown-item>
      </t-dropdown-menu>
    </template>
  </menus-button>
  <ResourceLibrary
    v-model:visible="show"
    :file-type="'1'"
    @insert-by-resource="insertByResource"
  />
</template>

<script setup>
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { editor } = useStore()
const props = defineProps({
  isHuge: {
    type: Boolean,
    default: false,
  },
  isTirgger: {
    type: String,
    default: 'click',
  },
  isDisabled: {
    type: Boolean,
    default: false,
  },
  isTooltip: {
    type: [String, Boolean],
    default: undefined,
  },
  isPlacement: {
    type: String,
    default: 'bottom-left',
  },
})
const options = [
  {
    label: t('insert.paperWarrapper.upload'),
    type: 'layoutImages',
  },
  {
    label: t('insert.paperWarrapper.ResourceLibrary'),
    type: 'resourceLibraryImages',
  },
]
let show = $ref(false)
const imageChange = (item) => {
  if (item.type === 'layoutImages') {
    const element = document.querySelector('.umo-block-menu-hander')
    if (element) {
      element.style.display = 'none'
    }
    chooseFile((file) => {
      editor.value
        ?.chain()
        .focus()
        .setPaperWrapping({
          src: file.fileUrl,
          name: file.originName,
          size: file.fileSize,
        })
        .run()
    }, {
    optPreChekck: defaultOptPreChekck
  })
  } else {
    show = true
  }
}

const insertByResource = (file) => {
  editor.value.chain().focus().setPaperWrapping({ src: file.fileUrl }).run()
}
</script>
