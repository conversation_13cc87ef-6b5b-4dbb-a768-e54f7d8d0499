<template>
  <menus-button
    ico="psychologyHealth"
    :text="t('insert.psychologyHealth.text')"
    huge
    @menu-click="dialogVisible = true"
  />
  <t-dialog
    :visible="dialogVisible"
    :header="t('insert.psychologyHealth.text')"
    :destroyOnClose="true"
    width="40%"
    @close="dialogVisible = false"
    @confirm="onConfirm"
  >
    <template-psychology-health-select @update="onUpdateWithSelectedQuiz" />
  </t-dialog>
</template>
<script setup>

// const { chapterId,bookId } = useStore()
const { batchInsert } = useStore()
let selectedQuiz = []
let dialogVisible = $ref(false)
const props = defineProps({
  chapterId: {
    type: String,
    default: null,
  },
  bookId: {
    type: String,
    default: null,
  },
})
function onUpdateWithSelectedQuiz(quizList) {
  selectedQuiz = quizList
}
function onConfirm() {
  if (!selectedQuiz.length) {
    return MessagePlugin.error('您还没选择任何心理测试量表')
  }
  dialogVisible = false
  batchInsert(selectedQuiz, 'psychologyHealth', file => {
    return {
      psychologyHealth: file
    }
  });
}
</script>
<style lang="less" scoped></style>
