<template>
  <menus-button
    ico="remark"
    :text="t('insert.remark.text')"
    huge
    :popup-visible="remarkVisible"
    @toggle-popup="toggleRemarkPopup"
    @menu-click="showPopup"
  >
  </menus-button>
  <t-dialog
    v-model:visible="dialogVisible"
    attach="body"
    :header="t('insert.remark.title')"
    width="20%"
    :close-on-overlay-click="false"
    @opened="onOpen"
    @confirm="onSubmit"
    @close="onClose"
    @cancel="onClose"
  >
    <div>
      <t-form
        ref="formValidatorStatus"
        :data="formData"
        :rules="rules"
        :label-width="120"
        label-align="top"
        @reset="onReset"
      >
        <t-form-item :label="t('insert.remark.text')" name="remark">
          <t-textarea
            v-model="formData.remark"
            :placeholder="t('insert.remark.placeholder')"
            name="remark"
            maxlength="200"
            :autosize="{ minRows: 5, maxRows: 7 }"
          />
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script setup>
import { MessagePlugin } from 'tdesign-vue-next'
import { onMounted, ref } from 'vue'

import { addUserCommon } from '@/api/book/userCommon'
import { getSelectionText } from '@/extensions/selection'
const dialogVisible = ref(false)
const { editorWang, editor, chapterId, getCommentList } = useStore()
const formData = ref({
  remark: '',
})
const remarkVisible = ref(false)

const formValidatorStatus = ref(null)
const rules = {
  remark: [
    {
      required: true,
      message: t('insert.remark.placeholder'),
      type: 'error',
      trigger: 'blur',
    },
  ],
}
const onClose = () => {
  remarkVisible.value = false
}

const onOpen = () => {
  formData.value.remark = ''
  nextTick(() => {
    formValidatorStatus.value.clearValidate()
  })
}

const toggleRemarkPopup = (val) => {}

const showPopup = (item) => {
  remarkVisible.value = true
}

const onReset = (firstFormItem) => {
  formValidatorStatus.value.resetFields()
}

onMounted(() => {
  formValidatorStatus.value.validate()
})

watch(
  () => remarkVisible.value,
  (val) => {
    dialogVisible.value = val
  },
)
const onSubmit = () => {
  const validateResult = formValidatorStatus.value.validate()
  validateResult.then((valid) => {
    if (valid === true) {
      const text = getSelectionText(editor.value)


      if (!text) {
        return MessagePlugin.warning('请先选择内容后备注')
      }
      const dataId = new Date().getTime()
      editor.value?.commands.setInputCommon({ id: dataId })
      const obj = {
        chapterId: chapterId.value,
        dataId,
        remarkContent: formData.value.remark,
      }

      addUserCommon(obj).then((res) => {
        if (res.code === 200) {
          MessagePlugin.success('备注成功')
          saveContentMethod()
          getCommentList().then((res) => {})
        }
      })
      // editor.value?.commands.insertContentAt(editor.value.state.selection.anchor, { type: 'remarkInline', attrs: { 'remarkContent': formData.value.remark } })
      remarkVisible.value = false
    }
  })
}

const saveContentMethod = inject('saveContent')
</script>
