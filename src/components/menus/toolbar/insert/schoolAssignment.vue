<template>
  <menus-button ico="schoolAssignment" :text="t('insert.schoolAssignment.text')" huge
    @menu-click="dialogVisible = true" />
  <t-dialog :visible="dialogVisible" :header="t('insert.schoolAssignment.text')" :destroy-on-close="true" width="40%"
    @close="dialogVisible = false" @confirm="onConfirm">
    <template-paper-select v-if="dialogVisible" paper-type="2" @update="onUpdateWithSelectedQuiz" />
  </t-dialog>
</template>
<script setup>
const { batchInsert } = useStore()
let selectedQuiz = []
let dialogVisible = $ref(false)
function onUpdateWithSelectedQuiz(quizList) {
  selectedQuiz = quizList
}
function onConfirm() {
  if (!selectedQuiz.length) {
    return MessagePlugin.error('您还没选择任何作业')
  }
  dialogVisible = false
  batchInsert(selectedQuiz, 'papers', (file) => {
    return {
      paperInfo: file,
      type: "paper"
    }
  })
}
</script>
<style lang="less" scoped></style>
