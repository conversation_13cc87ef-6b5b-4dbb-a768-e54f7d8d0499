<template>
  <menus-button
    ico="simulation"
    :text="t('insert.simulation.text')"
    huge
    menu-type="dropdown"
  >
    <template #dropmenu>
      <t-dropdown-menu>
        <t-dropdown-item
          v-for="item in options"
          class="umo-text-image-menu"
          @click="resourceClick(item.type)"
        >
          <span>{{ item.label }}</span>
        </t-dropdown-item>
      </t-dropdown-menu>
    </template>
  </menus-button>

  <modal
    :visible="dialogVisible"
    :header="t('insert.simulation.text')"
    width="400px"
    :footer="false"
    @close="dialogVisible = false"
  >
    <div class="umo-web-page-container">
      <t-form
        ref="formValidatorStatus"
        :data="formData"
        :rules="rules"
        :label-width="80"
        label-align="top"
        @submit="insertGame"
      >
        <t-form-item :label="t('insert.simulation.titltTip')" name="name">
          <t-input
            v-model.trim="formData.name"
            required-mark
            clearable
            :placeholder="t('insert.simulation.titleplaceholder')"
            :maxlength="20"
          />
        </t-form-item>

        <t-form-item :label="t('insert.simulation.tip')" name="url">
          <t-input
            v-model.trim="formData.url"
            required-mark
            clearable
            :placeholder="t('insert.simulation.placeholder')"
          />
          <t-tooltip :content="admin_content" :show-arrow="false">
            <t-icon
              name="error-circle"
              size="16px"
              style="color: #0006; margin-left: 10px"
            />
          </t-tooltip>
        </t-form-item>

        <div style="display: flex; justify-content: flex-end; margin-top: 10px">
          <t-button
            theme="default"
            style="margin-right: 10px"
            @click="dialogVisible = false"
            >{{ t('insert.simulation.cancel') }}</t-button
          >

          <t-button theme="primary" type="submit"
            >{{ t('insert.simulation.confirm') }}
          </t-button>
        </div>
      </t-form>
    </div>
  </modal>
  <ResourceLibrary
    v-model:visible="show"
    :file-type="'4'"
    :multiple="true"
    @insert-by-resource="insertByResource"
  />
</template>
<!-- @menu-click="editor.chain().focus().setResourceCover({ rcType:RCType.VIRTUAKL}).run()" -->
<script setup lang="ts">
import { RESOURCE_COVER } from '@/extensions/page/node-names'

const { editor, batchInsert } = useStore()
import { RCType } from '@/enum/extensions/RC'
const admin_content = ref(import.meta.env.VITE_APP_ADMIN_CONTENT)
const dialogVisible = ref(false)
const formValidatorStatus = ref(null)
const formData = ref({
  name: '',
  url: '',
})
const rules = {
  name: [
    {
      required: true,
      message: t('insert.simulation.nameTip'),
      type: 'error',
      trigger: 'blur',
    },
  ],
  url: [
    {
      required: true,
      message: t('insert.simulation.simulationTip'),
      type: 'error',
    },
    {
      url: {
        protocols: ['http', 'https', 'ftp'],
        require_protocol: true,
      },
      message: t('insert.simulation.simulationTip'),
    },
  ],
}

watch(dialogVisible, (val) => {
  dialogVisible.value = val
})
const insertGame = () => {
  const validateResult = formValidatorStatus.value.validate((valid) => valid)
  validateResult.then((valid) => {
    if (valid == true) {
      dialogVisible.value = false
      editor.value
        .chain()
        .focus()
        .setResourceCover({
          rcType: RCType.VIRTUAKL,
          url: formData.value.url,
          title: formData.value.name,
        })
        .run()
    }
  })
}

const openDialog = () => {
  formData.value = {
    name: '',
    url: '',
  }
  dialogVisible.value = true
}

const show = ref(false)

const options = [
  { label: t('insert.simulation.local'), type: 'local' },
  { label: t('insert.simulation.library'), type: 'library' },
]

const resourceClick = (type: string) => {
  switch (type) {
    case 'local':
      openDialog()
      break
    case 'library':
      show.value = true
      break
  }
}

const insertByResource = (files) => {
  batchInsert(files, RESOURCE_COVER, (file) => ({
    rcType: RCType.VIRTUAKL,
    url: file.fileUrl,
    size: file.fileSize,
    title: file.name,
    filename: file.name,
  }))
}
</script>
