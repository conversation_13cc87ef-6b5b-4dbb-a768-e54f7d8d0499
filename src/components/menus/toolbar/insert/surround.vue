<template>
  <menus-button ico="float" :text="t('insert.surround.text')" huge @menu-click="showPopup">
  </menus-button>

  <modal v-model:visible="dialogVisible" attach="body" :header="t('insert.surround.text')"
    :close-on-overlay-click="false" width="600px" @confirm="insertSurround">
    <t-form-item :label="t('insert.surround.surroundTitle')" name="text">
      <t-input v-model="dataForm.text" :placeholder="t('insert.surround.titleplaceholder')" />
    </t-form-item>
    <t-form :data="dataForm" label-align="top">
      <t-form-item :label="t('insert.surround.type')" name="surroundType">
        <t-radio-group v-model="dataForm.surroundType">
          <t-radio v-for="item in surroundList" :key="item.value" :value="item.value">{{ item.label }}</t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item :label="t('insert.surround.spacing')" name="spacing">
        <t-input-number v-model="dataForm.spacing" :min="0"></t-input-number>
      </t-form-item>
      <t-form-item :label="t('insert.surround.isShowImageTitle')" name="isShowImageTitle">
        <t-radio-group v-model="dataForm.isShowImageTitle">
          <t-radio value="1">{{ t('insert.image.yes') }}</t-radio>
          <t-radio value="0">{{ t('insert.image.no') }}</t-radio>
        </t-radio-group>
      </t-form-item>


      <t-form-item :label="t('insert.surround.isShowNo')" name="isShowNo">
        <t-radio-group v-model="dataForm.isShowNo">
          <t-radio value="1">{{ t('insert.image.yes') }}</t-radio>
          <t-radio value="0">{{ t('insert.image.no') }}</t-radio>
        </t-radio-group>
      </t-form-item>
      <t-form-item :label="t('insert.surround.color')" name="color">
        <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
          <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
            <div
              :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${dataForm.color || '#333'};margin-right:10px;`">
            </div>
            <div v-text="t('insert.surround.color')"></div>
          </div>
          <template #content>
            <div style="padding: 10px">
              <color-picker :default-color="dataForm.color" @change="backgroundColorChange" />
            </div>
          </template>
        </t-popup>

      </t-form-item>

      <t-form-item :label="t('insert.surround.imageUrl')" name="imageUrl">
        <t-button @click="handleClickUpload">{{
          t('insert.surround.uploadImage')
        }}</t-button>

        <div v-if="dataForm.imageUrl" class="block-img" @mousedown="backgroundImageShow = true"
          @mouseleave="backgroundImageShow = false">
          <img :src="dataForm.imageUrl" style="object-fit: cover" />
          <div v-show="backgroundImageShow" class="block-delect" @click="backgroundDelclick">
            <DeleteIcon color="#fff" />
          </div>
        </div>
      </t-form-item>


    </t-form>
  </modal>
</template>
<script lang="ts" setup>
import { DeleteIcon } from 'tdesign-icons-vue-next'

import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { editor } = useStore()
let dialogVisible = $ref(false)
let dataForm = $ref({
  color: '#333',
})

const backgroundImageShow = $ref(false)
const showPopup = () => {
  dataForm = {
    color: '#333',
    surroundType: 'rightTop',
    spacing: 0,
    isShowImageTitle: '1',
    isShowNo: '1',
    imageUrl: '',
  }
  dialogVisible = true
}

const surroundList = [
  {
    value: 'rightTop',
    label: t('insert.surround.rightTop'),
  },
  {
    value: 'leftTop',
    label: t('insert.surround.leftTop'),
  },
]

const handleClickUpload = () => {
  chooseFile((file) => {
    dataForm.imageUrl = file.fileUrl
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

const insertSurround = () => {
  if (!dataForm.surroundType)
    return useMessage('error', t('insert.surround.typeTip'))
  if (!dataForm.imageUrl)
    return useMessage('error', t('insert.surround.imgTip'))
  console.log(dataForm)
  editor.value
    .chain()
    .focus()
    .setSurround({
      ...dataForm,
    })
    .run()
  dialogVisible = false
}

const backgroundColorChange = (value) => {
  dataForm.color = value
}

const backgroundDelclick = () => {
  dataForm.imageUrl = ''
}
</script>

<style lang="less" scoped>
.block-img {
  width: 100px;
  height: 100px;
  position: relative;
  cursor: pointer;
  margin-left: 30px;

  img {
    width: 100%;
  }

  .block-delect {
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;

    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
}
</style>
