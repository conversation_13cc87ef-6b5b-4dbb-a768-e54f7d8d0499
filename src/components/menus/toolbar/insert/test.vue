<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-16 14:41:41
 * @LastEditTime: 2024-11-16 15:10:42
 * @FilePath: \dutp-editor\src\components\menus\toolbar\insert\test.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->
<!-- 模块说明 -->
<style lang="scss" scoped></style>

<template>
  <t-button @click="onClick">测试</t-button>
</template>

<script setup>
const { editor } = useStore()
const onClick = () => {
  editor?.value.chain().focus().setButtons({}).run()
}
</script>
