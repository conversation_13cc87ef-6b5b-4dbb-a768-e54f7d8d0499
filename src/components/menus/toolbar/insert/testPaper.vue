<template>
  <menus-button ico="testPaper" :text="t('insert.testPaper.text')" huge @menu-click="dialogVisible = true" />
  <t-dialog :visible="dialogVisible" :header="t('insert.testPaper.text')" :destroy-on-close="true" width="40%"
    @close="dialogVisible = false" @confirm="onConfirm">
    <template-paper-select paper-type="1" @update="onUpdateWithSelectedQuiz" />
  </t-dialog>
</template>
<script setup>
const { batchInsert } = useStore()
let selectedQuiz = []
let dialogVisible = $ref(false)
function onUpdateWithSelectedQuiz(quizList) {
  console.log('quizList', quizList)
  selectedQuiz = quizList
}
function onConfirm() {
  if (!selectedQuiz.length) {
    return MessagePlugin.error('您还没选择任何试卷')
  }
  dialogVisible = false
  // console.log('~~~~~~~~', selectedQuiz)
  batchInsert(selectedQuiz, 'papers', (file) => {
    return {
      paperInfo: file,
      type: 'testPaper'
    }
  })
}
</script>
<style lang="less" scoped></style>
