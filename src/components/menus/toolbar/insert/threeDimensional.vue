<template>
  <menus-button
    ico="threeDimensional"
    :text="t('insert.threeDimensional.text')"
    menu-type="dropdown"
    huge
  >
    <template #dropmenu>
      <t-dropdown-menu>
        <t-dropdown-item
          class="umo-text-image-menu"
          v-for="item in options"
          @click="resourceClick(item.type)"
        >
          <span>{{ item.label }}</span>
        </t-dropdown-item>
      </t-dropdown-menu>
    </template>
  </menus-button>
  <ResourceLibrary
    v-model:visible="show"
    :fileType="'6'"
    :multiple="true"
    @insertByResource="insertByResource"
  />
</template>

<script setup lang="ts">
import { RESOURCE_COVER } from '@/extensions/page/node-names'
import { Base64 } from 'js-base64'
import { RCType } from '@/enum/extensions/RC'

const { editor, batchInsert } = useStore()

const show = ref(false)

const options = [
  { label: t('insert.threeDimensional.local'), type: 'local' },
  { label: t('insert.threeDimensional.library'), type: 'library' },
]

const resourceClick = (type: string) => {
  switch (type) {
    case 'local':
      editor.value
        .chain()
        .focus()
        .setResourceCover({ rcType: RCType.MOEL_3D })
        .run()
      break
    case 'library':
      show.value = true
      break
  }
}

const insertByResource = (files) => {
  batchInsert(files, RESOURCE_COVER, (file) => ({
    rcType: RCType.MOEL_3D,
    url:
      import.meta.env.VITE_ONLINE_PREVIEW +
      encodeURIComponent(Base64.encode(file.fileUrl)),
    size: file.fileSize,
    title: file.name,
    filename: file.name,
  }))
}
</script>
