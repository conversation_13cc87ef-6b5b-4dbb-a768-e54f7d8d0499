<template>
  <menus-button
    ico="training"
    :text="t('insert.training.text')"
    huge
    @menu-click="openDialog"
  />
  <modal
    :visible="dialogVisible"
    :header="t('insert.training.text')"
    width="400px"
    :confirm-btn="t('insert.training.insert')"
    @close="dialogVisible = false"
    @confirm="insertTraining"
  >
    <div class="umo-web-page-container">
      <t-form
        ref="formValidatorStatus"
        :data="formData"
        :rules="rules"
        :label-width="80"
        label-align="top"
      >
        <t-form-item :label="t('insert.training.titltTip')" name="name">
          <t-input
            v-model.trim="formData.name"
            required-mark
            clearable
            :placeholder="t('insert.training.titleplaceholder')"
            :maxlength="20"
          />
        </t-form-item>

        <t-form-item :label="t('insert.training.tip')" name="url">
          <t-input
            v-model.trim="formData.url"
            required-mark
            clearable
            :placeholder="t('insert.training.placeholder')"
          />

          <t-tooltip :content="admin_content" :show-arrow="false">
            <t-icon
              name="error-circle"
              size="16px"
              style="color: #0006; margin-left: 10px"
            />
          </t-tooltip>
        </t-form-item>
      </t-form>
    </div>
  </modal>
</template>

<script setup>
import { ref } from 'vue'

import { RCType } from '@/enum/extensions/RC'
const { editor } = useStore()
const dialogVisible = ref(false)
const formValidatorStatus = ref(null)
const admin_content = ref(import.meta.env.VITE_APP_ADMIN_CONTENT)
const formData = ref({
  name: '',
  url: '',
})

const rules = {
  name: [
    {
      required: true,
      message: t('insert.training.nameTip'),
      type: 'error',
      trigger: 'blur',
    },
  ],
  url: [
    { required: true, message: t('insert.training.gameTip'), type: 'error' },
    {
      url: {
        protocols: ['http', 'https', 'ftp'],
        require_protocol: true,
      },
      message: t('insert.training.gameTip'),
    },
  ],
}

watch(dialogVisible, (val) => {
  dialogVisible.value = val
})

const insertTraining = () => {
  const validateResult = formValidatorStatus.value.validate((valid) => valid)

  validateResult.then((valid) => {
    if (valid == true) {
      dialogVisible.value = false
      editor.value
        .chain()
        .focus()
        .setResourceCover({
          rcType: RCType.TRAINING,
          url: formData.value.url,
          title: formData.value.name,
        })
        .run()
    }
  })
}

const openDialog = () => {
  formData.value = {
    name: '',
    url: '',
  }
  dialogVisible.value = true
}
</script>
