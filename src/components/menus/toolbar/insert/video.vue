<template>
  <menus-button
    ico="video"
    :text="t('insert.video.text')"
    menu-type="dropdown"
    huge
    :placement="isPlacement"
  >
    <template #dropmenu>
      <t-dropdown-menu>
        <t-dropdown-item
          v-for="item in options"
          class="umo-text-image-menu"
          @click="videoClick(item.type)"
        >
          <span>{{ item.label }}</span>
        </t-dropdown-item>
      </t-dropdown-menu>
    </template>
  </menus-button>
  <ResourceLibrary
    v-model:visible="show"
    :file-type="'3'"
    @insert-by-resource="insertByResource"
  />
</template>

<script setup lang="ts">
import { chooseFile } from '@/utils/file'
const { editor } = useStore()

const props = defineProps({
  isPlacement: {
    type: String,
    default: 'bottom-left',
  },
})

const show = ref(false)

const options = [
  { label: t('insert.video.local'), type: 'local' },
  { label: t('insert.video.library'), type: 'library' },
]

const videoClick = (type: string) => {
  switch (type) {
    case 'local':
      chooseFile(
        (file) => {
          editor.value
            ?.chain()
            .focus()
            .setVideo({
              src: file.fileUrl,
              size: file.fileSize,
              videoTitle: file.originName,
              name: file.originName,
            })
            .run()
        },{
          multiple: false,
          fileType: '.mp4',
          fileSize: 200,
        }
      )
      break
    case 'library':
      show.value = true
      break
  }
}

const insertByResource = (file) => {
  editor.value
    ?.chain()
    .focus()
    .setVideo({
      src: file.fileUrl,
      size: file.fileSize,
      videoTitle: file.name,
      name: file.name,
    })
    .run()
}
</script>
