<template>
  <t-drawer
    v-model:visible="showOtherActionPopup"
    attach="body" size="70%"
    mode="overlay"
    class="pick-question-dialog"
    :footer="false"
    :header="'从题库导入'"
    @close="hiddenPopup">
    <div class="search-con">
      <div
        class="search-con-left">
        <label
          class="label-sty">类型：</label>
        <t-select
          v-model="inputQuestionType"
          class="input-sty"
          clearable>
          <t-option
            v-for="(item, index) in questionTypeOptions"
            :key="index"
            :label="item.label"
            :value="item.value" />
        </t-select>
        <label
          class="label-sty">题干：</label>
        <t-input
          v-model="inputQuestionStem"
          class="input-sty"
          clearable />
        <t-button
          class="search-btn"
          theme="primary"
          @click="clickHandler">搜索</t-button>
        <t-button
          class="reset-btn">重置</t-button>
      </div>
    </div>
    <div
      class="question-bank-body">
      <div
        class="question-bank-left-nav">
        <!-- 个人题库 -->
        <t-tree
          v-model:actived="checkedKeys4PersonalQuestions"
          :data="folderHierachyForPersonalQuestions"
          :expand-on-click-node="false"
          :activable="true"
          :expand-all="true"
          class="questions-hierachy-tree"
          @click="clickHandler($event, 'p')" />
        <!-- 教材题库 -->
        <t-tree
          v-model:actived="checkedKeys4BookQuestions"
          :data="folderHierachyForBookQuestions"
          :expand-on-click-node="false"
          :activable="true"
          :expand-all="true"
          class="questions-hierachy-tree"
          @click="clickHandler($event, 'b')" />
        <!-- 以防万一有折叠组件需求 -->
        <!-- <menus-toolbar-base-collapseItem :data="folderHierachyForPersonalQuestions" @click="clickHandler">
      </menus-toolbar-base-collapseItem> -->
      </div>
      <div
        class="question-bank-right-list">
        <div
          class="data-list-con"
          :class="{
            'empty-question-list': loadedQuestions.questionList.length <= 0,
          }">
          <template
            v-if="loadedQuestions.questionList.length <= 0">
            <t-space
              direction="vertical"
              :align="'center'"
              style="margin-top: 10%">
              <t-empty />
            </t-space>
          </template>
          <template
            v-for="(item, index) in loadedQuestions.questionList"
            :key="item.questionId">
            <!-- 判断题 模板 -->
            <template-questions-onview-trueOrFalseQuestion
              v-if="isTrueOrFalseQuestion(item.questionType)"
              :data="item"
              :options="item.options"
              :config="{ contentLabel: true, hasAnswer: true }">
              <template
                #selection>
                <span>{{ index + 1 }}.
                </span><t-checkbox
                  :value="isQuestionSelected(item)"
                  @change="questionCheckedChange($event, item)" />
              </template>
            </template-questions-onview-trueOrFalseQuestion>
            <!-- 单选多选题 模板 -->
            <template-questions-onview-optionSelectQuestion
              v-if="isOptionSelectQuestionType(item.questionType)"
              :data="item"
              :options="item.options"
              :config="{ contentLabel: true, hasAnswer: true }">
              <template
                #selection>
                <span>{{ index + 1 }}.
                </span><t-checkbox
                  :value="isQuestionSelected(item)"
                  @change="questionCheckedChange($event, item)" />
              </template>
            </template-questions-onview-optionSelectQuestion>
            <!-- 填空题 模板 -->
            <template-questions-onview-fillinBlanksQuestion
              v-if="isFillinBlanksQuestion(item.questionType)"
              :data="item"
              :config="{ contentLabel: true, hasAnswer: true }">
              <template
                #selection>
                <span>{{ index + 1 }}.
                </span><t-checkbox
                  :value="isQuestionSelected(item)"
                  @change="questionCheckedChange($event, item)" />
              </template>
            </template-questions-onview-fillinBlanksQuestion>
            <!-- 排序题 模板 -->
            <template-questions-onview-sortingQuestion
              v-if="isSortingQuestion(item.questionType)"
              :data="item"
              :options="item.options"
              :config="{ contentLabel: true, hasAnswer: true }">
              <template
                #selection>
                <span>{{ index + 1 }}.
                </span><t-checkbox
                  :value="isQuestionSelected(item)"
                  @change="questionCheckedChange($event, item)" />
              </template>
            </template-questions-onview-sortingQuestion>
            <!-- 连线题 模板 -->
            <template-questions-onview-matchingQuestion
              v-if="isMatchingQuestions(item.questionType)"
              :data="item"
              :left-option="formatToLeftOption(item)"
              :right-option="formatToRightOption(item)"
              :matching-result="formatToMatchResult(item)"
              :config="{ contentLabel: true, hasAnswer: true }">
              <template
                #selection>
                <span>{{ index + 1 }}.
                </span><t-checkbox
                  :value="isQuestionSelected(item)"
                  @change="questionCheckedChange($event, item)" />
              </template>
            </template-questions-onview-matchingQuestion>
            <!-- 编程题 模板 -->
            <template-questions-onview-programmingQuestion
              v-if="isProgrammingQuestion(item.questionType)"
              :data="item"
              :config="{ contentLabel: true, hasAnswer: true }">
              <template
                #selection>
                <span>{{ index + 1 }}.
                </span><t-checkbox
                  :value="isQuestionSelected(item)"
                  @change="questionCheckedChange($event, item)" />
              </template>
            </template-questions-onview-programmingQuestion>

            <!-- 简答题 模板 -->
            <template-questions-onview-answerInShortQuestion
              v-if="item.questionType === 6"
              :data="item"
              :config="{ contentLabel: true, hasAnswer: true }">
              <template
                #selection>
                <span>{{ index + 1 }}.
                </span><t-checkbox
                  :value="isQuestionSelected(item)"
                  @change="questionCheckedChange($event, item)" />
              </template>
            </template-questions-onview-answerInShortQuestion>
          </template>
        </div>
        <t-pagination
          v-show="loadedQuestions.questionList.length > 0"
          v-model:current="loadedQuestions.pageNum"
          v-model:page-size="loadedQuestions.pageSize"
          style="margin-top: 10px"
          :total="loadedQuestions.total"
          @change="queryQuestionByPagination" />
      </div>
    </div>
    <div class="bottom-con">
      <t-button
        class="cale-btn"
        theme="default"
        variant="outline"
        @click="hiddenPopup">取消</t-button>
      <t-button
        class="sub-btn"
        @click="doConfirm">确定</t-button>
    </div>
  </t-drawer>
</template>

<script setup>
import { listBookQuestionWithOptions } from '@/api/book/bookQuestion'
import { getPersonalQuestionLibrary } from '@/api/book/bookQuestionFolder'
import { listUserQuestionWithOptions } from '@/api/book/userQuestion'
import {
  isFillinBlanksQuestion,
  isMatchingQuestions,
  isOptionSelectQuestionType,
  isProgrammingQuestion,
  isSortingQuestion,
  isTrueOrFalseQuestion,
} from '@/utils/questionTypeUtil'
import { uuid } from '@/utils/quetions-utils'
import { questionTypeOptions } from '@/utils/quetions-utils.ts'

// 定义组件
defineOptions({
  name: 'AddQuestionFromResourceBank',
})
const emits = defineEmits(['change', 'cancel', 'confirm'])
const props = defineProps({
  visibility: {
    type: Boolean,
    default: false,
  },
  closeOnOverlayClick: {
    type: Boolean,
    default: true,
  },
  bookId: {
    type: String,
    default: '',
    required: true,
  },
})

// 业务数据
let checkedKeys4PersonalQuestions = $ref([])
let checkedKeys4BookQuestions = $ref([])
const inputQuestionType = $ref('')
const inputQuestionStem = $ref('')
const rootId4PersonalQuestions = uuid()
const rootId4BookQuestions = uuid()
let showOtherActionPopup = $ref(false)
let folderHierachyForBookQuestions = $ref([])
let folderHierachyForPersonalQuestions = $ref([])
const loadedQuestions = $ref({
  questionList: [],
  pageSize: 10,
  pageNum: 1,
  total: 0,
  currentLoadResourceType: '',
})
let currentSelectedNode = $ref('')
let currentSelectedNodeBookId = $ref('')
let selectedQuestions = $ref([])

// 方法
function formatToLeftOption(item) {
  const result = item.options?.filter((qo) => qo.optionPosition === 1) ?? []
  return result
}
function formatToRightOption(item) {
  return item.options?.filter((qo) => qo.optionPosition === 2) ?? []
}
function formatToMatchResult(questionItem) {
  let connectionOpts = []
  try {
    if (typeof questionItem.rightAnswer === 'string') {
      connectionOpts = JSON.parse(questionItem.rightAnswer)
    }
    return connectionOpts.map(({ source, target }) => {
      return { source, target }
    })
  } catch (err) {}
  return connectionOpts
}
function isQuestionSelected(item) {
  return !!selectedQuestions.find((sq) => sq.questionId === item.questionId)
}

function questionCheckedChange(checked, item) {
  if (checked) {
    selectedQuestions.push(item)
  } else {
    let idx = selectedQuestions.findIndex(i => i.questionId === item.questionId)
    if (idx > -1) {
      selectedQuestions.splice(idx, 1)
    }
  }

}
const hiddenPopup = () => {
  showOtherActionPopup = false
  emits('cancel', false)
}

function doConfirm() {
  showOtherActionPopup = false
  emits('confirm', selectedQuestions)
}

function queryQuestions() {
  const resourceType = loadedQuestions.currentLoadResourceType
  if (resourceType === 'p') {
    checkedKeys4BookQuestions = []
    // 查询个人题目
    listUserQuestionWithOptions({
      questionType: inputQuestionType,
      folderId: currentSelectedNode,
      questionContent: inputQuestionStem,
      pageSize: loadedQuestions.pageSize,
      pageNum: loadedQuestions.pageNum,
    }).then((questions) => {
      loadedQuestions.total = questions.total
      loadedQuestions.questionList = questions.rows.map((questionItem) => {
        return {
          ...questionItem,
          // 保留属性
          _bookQuestionResource: false,
        }
      })
    })
  } else if (resourceType === 'b') {
    checkedKeys4PersonalQuestions = []
    // 查询教材题目
    const params = {
      questionType: inputQuestionType,
      questionContent: inputQuestionStem,
      pageSize: loadedQuestions.pageSize,
      pageNum: loadedQuestions.pageNum,
    }

    // 仅当currentSelectedNode是数字时才添加folderId参数
    if (currentSelectedNode && !isNaN(Number(currentSelectedNode))) {
      params.folderId = currentSelectedNode
    }

    // 仅当currentSelectedNodeBookId有值时才添加bookId参数
    if (currentSelectedNodeBookId) {
      params.bookId = currentSelectedNodeBookId
    }

    listBookQuestionWithOptions(params).then((questions) => {
      loadedQuestions.total = questions.total
      loadedQuestions.questionList = questions.rows.map((questionItem) => {
        return {
          ...questionItem,
          // 保留属性
          _bookQuestionResource: true,
        }
      })
    })
  }
}
function queryQuestionByPagination(paginationObject) {
  if (!loadedQuestions.currentLoadResourceType || !currentSelectedNode) {
    return
  }
  loadedQuestions.pageNum = paginationObject.current
  loadedQuestions.pageSize = paginationObject.pageSize
  queryQuestions()
}
function clickHandler(event, type) {
  if (type) {
    loadedQuestions.currentLoadResourceType = type
  }
  if (event.node?.value) {
    currentSelectedNode = event.node.value
  }

  if (!loadedQuestions.currentLoadResourceType || !currentSelectedNode) {
    MessagePlugin.warning('请先选择个人题库或是教材题库')
    return
  }
  currentSelectedNode = event.node?.value || currentSelectedNode

  currentSelectedNodeBookId = event.node?.data?.bookId || ''
  if (
    currentSelectedNode === rootId4BookQuestions ||
    currentSelectedNode === rootId4PersonalQuestions
  ) {
    return
  }
  loadedQuestions.pageNum = 1
  queryQuestions()
}

function folderHierachyFormat(data) {
  const map = {} // 用于存储每个节点的引用
  const tree = [] // 最终的树形结构

  // 遍历数据
  data.forEach((item) => {
    item.label = item.folderName
    item.value = `${item.folderId}`
    // 将当前节点添加到 map 中
    map[item.folderId] = {
      ...item,
      children: [], // 初始化 children 数组
    }

    // 如果当前节点有父节点，则将其添加到父节点的 children 中
    if (item.parentId && item.parentId !== '0' && map[item.parentId]) {
      map[item.parentId].children.push(map[item.folderId])
    } else {
      // 如果当前节点没有父节点，则将其作为根节点
      tree.push(map[item.folderId])
    }
  })

  return tree
}

function resetLoadedQuestionData() {
  loadedQuestions.questionList = []
  loadedQuestions.pageSize = 10
  loadedQuestions.pageNum = 1
  loadedQuestions.total = 0
  loadedQuestions.currentLoadResourceType = ''
  selectedQuestions = []
  checkedKeys4PersonalQuestions = []
  checkedKeys4BookQuestions = []
}

// 获取题库数据
async function getFolders() {
  try {
    const response = await getPersonalQuestionLibrary()
    // 处理新的数据格式
    const processedData = response.data.map((item) => {
      // 移除了对 folders 数组的检查，允许空文件夹
      const folders = item.folders || []
      // 构建树形结构
      const folderMap = new Map()
      folders.forEach((folder) => {
        folder.children = []
        folderMap.set(folder.folderId, folder)
      })

      const rootFolders = []
      folders.forEach((folder) => {
        if (folder.parentId === '0') {
          rootFolders.push(folder)
        } else {
          const parentFolder = folderMap.get(folder.parentId)
          if (parentFolder) {
            parentFolder.children.push(folder)
          }
        }
      })

      return {
        folderName: item.folderName || item.bookName,
        folderId: item.folderId,
        parentId: '0',
        bookId: item.bookId,
        bookName: item.bookName,
        children: rootFolders,
        type: item.folderType,
        icon: 'el-icon-collection',
      }
    })

    return processedData
  } catch (error) {
    console.error('获取题库数据失败:', error)
    return []
  }
}

// 递归转换树结构的属性名
const convertTreeNodeProperties = (node) => {
  // 修改当前节点的属性
  node.value = node.folderId
  node.label = node.folderName

  // 递归处理子节点
  if (node.children && node.children.length > 0) {
    node.children.forEach((child) => convertTreeNodeProperties(child))
  }

  return node
}

// 监听变化
watch(
  () => props.visibility,
  (newVal) => {
    showOtherActionPopup = newVal
    resetLoadedQuestionData()
    if (newVal) {
      getFolders().then((mixFolders) => {
        // 处理所有顶层数据
        mixFolders.forEach((folder) => {
          convertTreeNodeProperties(folder)
        })

        // 按类型分组处理不同类型的文件夹
        const groupedFolders = mixFolders.reduce((acc, folder) => {
          if (!acc[folder.type]) {
            acc[folder.type] = []
          }
          acc[folder.type].push(folder)
          return acc
        }, {})

        // 处理个人题库 (user类型)
        if (groupedFolders.user) {
          const userFolders = groupedFolders.user
          folderHierachyForPersonalQuestions = [
            {
              label: '个人题库',
              value: rootId4PersonalQuestions,
              children: userFolders[0].children,
            },
          ]
        }

        // 处理教材题库 (book类型)
        if (groupedFolders.book) {
          const bookFolders = groupedFolders.book
          folderHierachyForBookQuestions = [
            {
              label: '教材题库',
              value: rootId4BookQuestions,
              children: bookFolders,
            },
          ]
        }
      })
    }
  }
)
</script>
<style lang="less" scoped>
.question-bank-body {
  display: flex;
  flex-direction: row;
  height: 85%;
  .question-bank-left-nav {
    padding-right: 10px;
  }
  .question-bank-right-list {
    flex: 1;
  }
  .data-list-con {
    height: 90%;
    overflow: hidden auto;
    &.empty-question-list {
      text-align: center;
    }
    .data-item {
      margin-bottom: 16px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.title {
  width: 100%;
  padding-bottom: 10px;
  text-align: left;
  margin-bottom: 20px;
  border-bottom: 1px solid #ccc;
}
.search-con {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  .search-con-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .label-sty {
      width: auto;
    }
    .input-sty {
      width: 200px;
      margin-right: 30px;
    }
    .search-btn {
      margin-right: 10px;
    }
    .reset-btn {
      color: #7d8590;
      background-color: white;
      border: 1px solid #ccc;
      margin-right: 150px;
    }
  }
  .search-con-right {
    .import-btn {
      margin-right: 20px;
    }
  }
}
.bottom-con {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  align-items: center;
  .cale-btn {
    margin-right: 20px;
  }
}
</style>
<style lang="less">
.pick-question-dialog {
  .narrow-scrollbar {
    overflow: hidden;
  }
  .data-list-con {
    .data-item {
      .data-item-em {
        img {
          max-width: 50%;
        }
      }
    }
  }
}
.question-bank-left-nav {
  overflow: auto;
  .umo-tree__item.umo-is-active {
    border-right: 1px solid blue;
  }
}
</style>
