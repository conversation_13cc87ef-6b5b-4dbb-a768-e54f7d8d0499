<template>
  <modal
    :visible="isShow"
    icon="link"
    :header="t('insert.link.title1')"
    width="420px"
    draggable
    destroy-on-close
    :confirm-btn="t('insert.link.insert')"
    @confirm="confirmHandler"
    @close="closeHandler"
  >
    <div class="umo-link-container">
      <t-form label-align="top" :data="formData" :rules="rules" ref="formValidatorStatus">
        <t-form-item :label="t('insert.link.selectDirectory')" name="id">
          <t-cascader
            v-model="formData.id"
            :placeholder="t('insert.link.selectDirectory')"
            value-type="full"
            :keys="{ label: 'name', value: 'id' }"
            :options="allChapterData"
          />
        </t-form-item>
      </t-form>
    </div>
  </modal>
</template>

<script setup lang="ts">
import { getChapterCatalogue } from '@/api/chapterContent'
import { deriverChapterInfoObjectWithMinimumProperty } from '@/utils/questionTypeUtil';
import { type FormProps, type FormInstanceFunctions } from 'tdesign-vue-next';

const emits = defineEmits(['close', 'confirm'])

const props = defineProps({
  chapterId: {
    type: String,
    default: ''
  },
  visibility: Boolean,
})

// 业务数据
const formValidatorStatus: any = ref<FormInstanceFunctions>(null as any);
const formData: any = $ref({
  id: []
});
let currentSelectNode: any = null
const rules: FormProps['rules'] = {
  chapter: [
    {
      required: true,
      message: '必填',
      type: 'error',
    }
  ]
}
let allChapterData = $ref([])

let isShow = $ref(false)

// 方法
const chapterCatalogue = async (chapterId) => {
  const res: any = await getChapterCatalogue(chapterId)
  if (res.code === 200) {
    allChapterData = res.data
    // 接口是通过章节和目录关联构建目录数据，目录级别有catelogId从而形成树形数据，章节级别只有chapterId，所有过滤章节数据得出当前章节对应的数据即可。否则需要磨平chapterId和catelogId的差异。满足多章节的需求。
    // allChapterData = allChapterData.filter(chapterData => chapterData.chapterId === chapterId)
    // if (allChapterData[0]) {
    //   allChapterData[0].catalogId = allChapterData[0].chapterId
    // }
  }
}


function confirmHandler() {
  formValidatorStatus.value.validate().then(result => {
    isShow = false
    let currentLevelChapterInfo: any = null
    const selectedNodeSeqArray: Array<any> = []
    let currentChapterHeading = allChapterData
    for (let i = 0, length = formData.id.length; i < length; i++) {
      const tierInfo = formData.id[i]
      currentLevelChapterInfo = currentChapterHeading.find(chapterInfo => chapterInfo.id === tierInfo)
      if (currentLevelChapterInfo) {
        selectedNodeSeqArray.push(deriverChapterInfoObjectWithMinimumProperty(currentLevelChapterInfo))
        currentChapterHeading = currentLevelChapterInfo.children
      }
    }
    emits('confirm', selectedNodeSeqArray)
  })
}
function closeHandler() {
  isShow = false
  currentSelectNode = null
  formData.id = ''
  emits('close')
}

// hook
watch(
  () => props.visibility,
  (val) => {
    if (!props.chapterId) {
      console.warn('chapter id是查找章节的必要条件')
      return
    }
    if (val) {
      chapterCatalogue(props.chapterId)
      formData.id = []
    }
    isShow = val
  },
)
</script>
<!-- 模块说明 -->
<style lang="less" scoped></style>
