<template>
  <!-- latex formula dialog -->
  <t-dialog :visible="latexDialogVisibility" :header="needT ? t('insert.formulaEdit') : '编辑公式'" width="80%"
    attach="body" placement="center" draggable :confirm-btn="null" :cancel-btn="null" @close="modalClose">
    <div v-if="providerName === 'latex' && latexDialogVisibility" style="position: relative">
      <iframe id="liveEditor" frameborder="0" style="width: 100%; height: 400px; border: 0; outline: none"
        src="https://latexeasy.com/editor"></iframe>
      <div v-show="formula3rdScriptLoading" class="equation-script-loading">
        <t-loading indicator :loading="formula3rdScriptLoading" :prevent-scroll-through="true" :show-overlay="false" />
      </div>
    </div>
    <div class="latex-text">
      <t-checkbox v-model="inlineIsShow">{{ t('insert.formula.tip') }}</t-checkbox>
    </div>
    <div class="dialog-footer">
      <t-button :disabled="formula3rdScriptLoading" @click="confirmOnLatexEditorDialog">{{ t('insert.formula.submit')
      }}</t-button>
      <t-button theme="default" @click="modalClose">{{ t('insert.formula.cancel') }}</t-button>
    </div>
  </t-dialog>
  <!-- wiris formula dialog -->
  <t-dialog :visible="wirisDialogVisibility" :header="needT
    ? formulaType === 'math'
      ? t('insert.mathFormula')
      : t('insert.chemicalFormula')
    : formulaType === 'math'
      ? t('insert.formula.mathText')
      : t('insert.formula.chemicalText')
    " width="80%" attach="body" placement="center" draggable :confirm-btn="null" :cancel-btn="null"
    @close="modalClose">
    <div v-if="providerName === 'wiris'" style="position: relative">
      <div :id="editorContainerId"></div>
      <div v-show="formula3rdScriptLoading" class="equation-script-loading">
        <t-loading indicator :loading="formula3rdScriptLoading" :prevent-scroll-through="true" :show-overlay="false" />
      </div>
    </div>

    <div class="latex-text">
      <t-checkbox v-model="inlineIsShow">{{ t('insert.formula.tip') }}</t-checkbox>
    </div>

    <div class="dialog-footer">
      <t-button :disabled="formula3rdScriptLoading" @click="confirmOnWirisEditorDialog">{{
        t('insert.formula.submit') }}</t-button>
      <t-button theme="default" @click="modalClose">{{ t('insert.formula.cancel') }}</t-button>
    </div>
  </t-dialog>
</template>
<script setup>
import { debounce } from 'lodash'

import { getFormulaImg } from '@/api/book/book'
import { LATEXT_SCRIPT_ID, WIRIS_SCRIPT_ID } from '@/constants/editorIds'
import { uuid } from '@/utils/quetions-utils.ts'
import { loadScript } from '@/utils/scriptLoading'
import { shortId } from '@/utils/short-id'
import { wrs_mathmlEntities, wrs_urlencode } from '@/utils/wirisUtils'

const emits = defineEmits(['confirm', 'cancel'])
const props = defineProps({
  visibility: {
    type: Boolean,
    default: false,
  },
  providerName: {
    type: String,
    default: '',
  },
  formulaType: {
    type: String,
    default: '',
  },
  needT: {
    type: Boolean,
    default: false,
  },
  content: {
    type: String,
    default: '',
  },
})
let latexDialogVisibility = $ref(false)
let wirisDialogVisibility = $ref(false)
let letexeasy = $ref(null)
const { editor } = useStore()
let wirisMathEditor = null
let wirisChemistryEditor = null
let formula3rdScriptLoading = $ref(false)
let encodedMml = $ref('')
const editorContainerId = $ref(`editorContainer_${uuid()}`)
// 是否为行内公式
const inlineIsShow = $ref(false)
const editorConfig = {
  formulaConfig: {
    imageUrlTemplate: 'https://r.latexeasy.com/image.svg?',
    editorMode: 'live',
    editorLiveServer: 'https://latexeasy.com',
  },
}

function modalClose() {
  if (props.providerName === 'latex') {
    latexDialogVisibility = false
  } else {
    wirisDialogVisibility = false
  }
  emits('cancel')
}

function addLatexFormula() {
  latexDialogVisibility = true
  formula3rdScriptLoading = true
  return loadScript({
    id: LATEXT_SCRIPT_ID,
    src: `${editorConfig.formulaConfig.editorLiveServer}/vendor/LatexEasyEditor/editor/sdk.js`,
  })
    .then(() => {
      const le = new window.LatexEasy(document.getElementById('liveEditor'))
      letexeasy = le
      letexeasy.on('ready', () => {
        if (props.content) {
          letexeasy.call('set.latex', {
            latex: props.content,
          })
        }
      })
      le.init()
    })
    .catch((e) => {
      console.error(e)
    })
    .finally(() => {
      setTimeout(() => {
        formula3rdScriptLoading = false
      }, 1000)
    })
}
function addWirisMathFormula() {
  wirisDialogVisibility = true
  loadWirisEditorOn().then(() => {
    if (!wirisMathEditor) {
      wirisMathEditor = window.com.wiris.jsEditor.JsEditor.newInstance({
        language: 'zh',
      })
      // 注册事件监听
      addEditorContentChangeListener(
        wirisMathEditor,
        mathEditorContentChangeHandler,
      )
    }
    wirisMathEditor.insertInto(document.querySelector(`#${editorContainerId}`))
    // if (props.content) {
    wirisMathEditor?.setMathML('<math xmlns="http://www.w3.org/1998/Math/MathML"/>')
    // }
  })
}

function addWirisChemistryFormula() {
  wirisDialogVisibility = true
  loadWirisEditorOn().then(() => {
    if (!wirisChemistryEditor) {
      wirisChemistryEditor = window.com.wiris.jsEditor.JsEditor.newInstance({
        language: 'zh',
        toolbar: 'chemistry',
      })
      // 注册事件监听
      addEditorContentChangeListener(
        wirisChemistryEditor,
        chemistryEditorContentChangeHandler,
      )
    }
    wirisChemistryEditor.insertInto(document.querySelector(`#${editorContainerId}`))
    wirisChemistryEditor?.setMathML('<math xmlns="http://www.w3.org/1998/Math/MathML"/>')
  })
}

function confirmOnLatexEditorDialog() {
  formula3rdScriptLoading = true
  letexeasy.call('get.latex', {}, function (data) {
    if (!editor.value) return
    const params = new URLSearchParams()
    params.append('query', data.latex)
    formulaImgGenerator(
      {
        langData: params.toString().split('=')[1],
        providerName: 'latex',
      },
      debounce((resp) => {
        formula3rdScriptLoading = false
        latexDialogVisibility = false
        emits('confirm', {
          ...resp,
          providerName: props.providerName,
          formulaType: props.formulaType,
          langData: data.latex,
          isLine: inlineIsShow,
        })
      }, 1500),
    )
  })
}

function confirmOnWirisEditorDialog() {
  let mathML = ''
  formula3rdScriptLoading = true
  if (props.formulaType === 'math') {
    mathML = wirisMathEditor.getMathML()
  } else if (props.formulaType === 'wiris') {
    mathML = wirisChemistryEditor.getMathML()
  }
  formulaImgGenerator(
    {
      langData: encodedMml,
      providerName: 'wiris',
    },
    debounce((resp) => {
      if (!resp) {
        return
      }
      formula3rdScriptLoading = false
      wirisDialogVisibility = false

      emits('confirm', {
        ...resp,
        providerName: props.providerName,
        formulaType: props.formulaType,
        langData: mathML,
        isLine: inlineIsShow,
      })
    }, 500),
  )
}

function formulaImgGenerator(param, callbackFn) {
  getFormulaImg(param)
    .then((resp) => {
      const { fileUrl, fileName } = resp.data || {}
      const img = document.createElement('img')
      const shortimgId = `img${shortId()}`
      img.id = `${shortimgId}`
      const imgLoadedHandler = () => {
        const previewImg = document.querySelector(`#${shortimgId}`)
        previewImg.setAttribute('crossorigin', 'anonymous')
        const imgWidth = parseInt(getComputedStyle(previewImg).width)
        const imgHeight = parseInt(getComputedStyle(previewImg).height)
        callbackFn({
          url: fileUrl,
          width: imgWidth,
          height: imgHeight,
          name: fileName || `uuc${shortId()}`,
        })
      }
      img.onload = () => {
        imgLoadedHandler()
      }
      img.onerror = () => {
        callbackFn(null)
      }

      img.src = fileUrl

      img.style.visibility = 'hidden'
      img.setAttribute('dataCode', 'code')

      document.body.appendChild(img)
    })
    .catch((e) => e)
}

function mathEditorContentChangeHandler(source) {
  encodedMml = wrs_urlencode(wrs_mathmlEntities(source.getMathML()))
}
function chemistryEditorContentChangeHandler(source) {
  encodedMml = wrs_urlencode(wrs_mathmlEntities(source.getMathML()))
}

function loadWirisEditorOn() {
  formula3rdScriptLoading = true
  return loadScript({
    src: `https://www.wiris.net/demo/editor/editor`,
    id: WIRIS_SCRIPT_ID,
  })
    .then(() => { })
    .catch((e) => {
      alert('下载文件出现错误，请联系管理员!')
      console.error(e)
    })
    .finally(() => {
      formula3rdScriptLoading = false
    })
}
function addEditorContentChangeListener(editorInstance, callback, delay) {
  let timeoutId = null
  var resetPromise = function (args) {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(function () {
      timeoutId = null
      callback(args)
    }, delay || 500)
  }

  editorInstance.getEditorModel().addEditorListener({
    contentChanged: resetPromise,
    styleChanged: resetPromise,
    caretPositionChanged() { },
    clipboardChanged() { },
    transformationReceived() { },
  })
}

function removeEditorChangeListener(editorInstance, handler) {
  editorInstance?.getEditorModel()?.removeEditorChangeListener?.(handler)
}

onUnmounted(() => {
  removeEditorChangeListener(wirisMathEditor, mathEditorContentChangeHandler)
  removeEditorChangeListener(
    wirisChemistryEditor,
    chemistryEditorContentChangeHandler,
  )
  wirisChemistryEditor = null
  wirisMathEditor = null
})

watch(
  () => [props.providerName, props.formulaType, props.visibility],
  ([nValue4ProviderName, nValue4FormulaType, nValue4Visibility]) => {
    if (!nValue4Visibility) {
      latexDialogVisibility = false
      wirisDialogVisibility = false
      return
    }
    if (nValue4ProviderName === 'latex' && nValue4Visibility) {
      latexDialogVisibility = nValue4Visibility
      addLatexFormula()
    } else if (
      nValue4ProviderName === 'wiris' &&
      (nValue4FormulaType === 'math' || nValue4FormulaType === 'chem') &&
      nValue4Visibility
    ) {
      wirisDialogVisibility = nValue4Visibility
      if (nValue4FormulaType === 'math') {
        addWirisMathFormula()
      } else {
        addWirisChemistryFormula()
      }
    }
  },
)
</script>
<style lang="less" scoped>
.equation-script-loading {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.latex-text {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin: 20px 0 0;

  button {
    margin-left: 10px;
  }
}
</style>
