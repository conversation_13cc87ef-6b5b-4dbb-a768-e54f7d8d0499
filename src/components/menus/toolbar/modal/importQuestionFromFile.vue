<template>
  <modal
    dialogClassName="import-question-from-file"
    v-model:visible="showImportPopup"
    :footer="false"
    attach="body"
    width="30%"
    :header="'导入文件'"
    @close="closeHandler"
    :closeOnOverlayClick="false"
  >
    <t-space direction="vertical">
      <t-radio-group default-value="1" v-model="selectedImportType">
        <t-radio value="1">{{ t('insert.questions.generalQuestion') }}</t-radio>
        <t-radio value="2">{{
          t('insert.questions.matchingQuestion')
        }}</t-radio>
      </t-radio-group>
      <div v-if="selectedImportType === '1'">
        <t-upload
          ref="uploadRef"
          :auto-upload="true"
          :theme="'file'"
          :data="{ extra_data: 123, file_name: 'certificate' }"
          :abridge-name="[10, 8]"
          :request-method="requestMethod4GeneralQuestion"
          accept=".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          draggable
        />
      </div>
      <div v-else-if="selectedImportType === '2'">
        <t-upload
          ref="uploadRef"
          :auto-upload="true"
          :theme="'file'"
          :data="{ extra_data: 123, file_name: 'certificate' }"
          :abridge-name="[10, 8]"
          :request-method="requestMethod4MatchingQuestion"
          accept=".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          draggable
        />
      </div>
      <div>
        <label>仅允许导入xls、xlsx格式文件。</label>
        <t-link
          v-if="selectedImportType === '1'"
          theme="primary"
          @click="downloadFile($event, '1')"
          >{{ t('file.downloadGeneralQuestionTmpl') }}</t-link
        >
        <t-link
          v-if="selectedImportType === '2'"
          theme="primary"
          @click="downloadFile($event, '2')"
          >{{ t('file.downloadMatchingQuestionTmpl') }}</t-link
        >
      </div>
    </t-space>
    <div class="footer-content">
      <t-button
        class="cancel-btn"
        theme="default"
        variant="outline"
        @click="doCancel"
        >取消</t-button
      >
      <t-button @click="doConfirm">确定</t-button>
    </div>
  </modal>
</template>
<script lang="ts" setup>
import { type UploadProps } from 'tdesign-vue-next'
import { readExcelFile } from '@/utils/file'
import * as XLSX from 'xlsx'
import { importQuestions } from '@/api/book/bookQuestion'
import { initTrueOrFalseQuestionOptions } from '@/utils/questionTypeUtil'
import { queryBookResourceDefaultFolder } from '@/components/menus/toolbar/insert/questions/question-folder-mixins'
import {
  validateMandatoryFields,
  QUESTION_TYPE_MAPPER,
  TMPLT_CODE,
  validateMatchingQuestionItemTmpl,
  validateGeneralQuestionItemTmpl,
} from './importQuestionUtil'

const emits = defineEmits(['change', 'cancel', 'confirm'])
const props = defineProps({
  visibility: {
    type: Boolean,
    default: false,
  },
  bookId: {
    type: String,
    default: '',
    required: true,
  },
  chapterId: {
    type: String,
    default: '',
    required: true,
  },
})
const uploadRef = $ref<any>()

// 业务数据
const selectedImportType = $ref('1')
let showImportPopup = $ref(false)
let allQualifedQuestions: Array<Record<string, any>> = [] // 收集所有题目数据
const questionTypes = [
  { value: 1, label: '单选题' },
  { value: 2, label: '多选题' },
  { value: 3, label: '填空题' },
  { value: 4, label: '排序题' },
  { value: 5, label: '连线题' },
  { value: 6, label: '简答题' },
  { value: 7, label: '判断题' },
  { value: 8, label: '编程题' },
]
let defaultFolderData: { [key: string]: any } = $ref({})

// 方法
function downloadFile(event: any, type: string) {
  let filePath = ''
  if (type === '1') {
    filePath = '/files/普通题模版.xlsx'
  } else if (type === '2') {
    filePath = '/files/连线题模板.xlsx'
  }
  window.open(filePath, '_blank')
}
function validSelectFile(file: any) {
  const isExcel =
    file.raw.type ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.raw.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    uploadRef.cancelUpload()
    MessagePlugin.error('只能上传Excel文件！')
    return false
  }
  // 检查文件大小
  const isLt10M = file.raw.size / 1024 / 1024 < 10
  if (!isLt10M) {
    uploadRef.cancelUpload()
    MessagePlugin.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}
// 修改题型转换函数，利用已有的 questionTypes 数组
const getQuestionTypeValue = (typeText) => {
  const questionType = questionTypes.find((type) => type.label === typeText)
  return questionType ? questionType.value : 1 // 默认返回单选题类型
}
async function readGeneralQuestionData(file: any) {
  const workbook: any = await readExcelFile(file.raw)
  const worksheet = workbook.Sheets[workbook.SheetNames[0]]
  const rows: Array<any> = XLSX.utils.sheet_to_json(worksheet)

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i]
    // 所有试题的题干、题型数据必须有效
    let invalidCode = validateGeneralQuestionItemTmpl(row)
    if (invalidCode) {
      return Promise.reject(invalidCode)
    }
    invalidCode = validateMandatoryFields(row)
    if (invalidCode) {
      return Promise.reject(invalidCode)
    }

    const questionData = {
      bookId: '', // bookId.value,
      questionType: getQuestionTypeValue(row['题型']),
      questionContent: row['题干'] || '',
      rightAnswer: row['正确答案'] || '',
      analysis: row['答案解析'] || '',
      folderId: 0, // .value.folderId,
      options: [] as Array<any>,
    }

    // 处理普通题型的选项
    if ([1, 2, 4, 7].includes(questionData.questionType)) {
      // 保持原有的选项处理逻辑
      if (questionData.questionType === 7) {
        // 判断题
        if (
          !questionData.rightAnswer ||
          (questionData.rightAnswer != '正确' &&
            questionData.rightAnswer != '错误')
        ) {
          // 不符合判断题答案格式
          return Promise.reject(TMPLT_CODE.TMPL_MATCHING_QUESTION_OPTION_ERROR)
        }
        questionData.options = initTrueOrFalseQuestionOptions().map(
          (optionItem) => {
            return {
              ...optionItem,
              rightFlag:
                questionData.rightAnswer == optionItem.optionContent ? 1 : 0,
              optionId: undefined,
            }
          },
        )
        // questionData.options = initTrueOrFalseQuestionOptions().map((optionItem) => {
        //   return {
        //     ...optionItem,
        //     optionId: undefined
        //   }
        // })
      } else if (questionData.questionType === 4) {
        // 排序题
        const sortedOptions: Array<any> = []
        for (let j = 1; j <= 5; j++) {
          const optionContent = row[`选项${j}`]
          if (optionContent) {
            sortedOptions.push({
              optionContent: optionContent,
              rightFlag: 0,
              optionIndex: j - 1,
              sort: j - 1,
            })
          }
        }
        questionData.options = sortedOptions
      } else {
        // 单选题和多选题
        for (let j = 1; j <= 5; j++) {
          const optionContent = row[`选项${j}`]
          if (optionContent) {
            const rightAnswer = row['正确答案'] || ''
            const rightFlag = rightAnswer.includes(String.fromCharCode(64 + j))

            questionData.options.push({
              optionContent: optionContent,
              rightFlag: rightFlag ? 1 : 0,
              optionIndex: j - 1,
            })
          }
        }
      }
    }
    allQualifedQuestions.push(questionData)
  }
}

async function readMatchingQuestionData(file: any) {
  const workbook: any = await readExcelFile(file.raw)
  const worksheet = workbook.Sheets[workbook.SheetNames[0]]
  const rows: Array<any> = XLSX.utils.sheet_to_json(worksheet)

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i]
    // 所有试题的题干、题型数据必须有效
    let invalidCode = validateMatchingQuestionItemTmpl(row)
    if (invalidCode) {
      return Promise.reject(invalidCode)
    }
    invalidCode = validateMandatoryFields(row, true)
    if (invalidCode) {
      return Promise.reject(invalidCode)
    }

    const questionData: any = {
      questionType: QUESTION_TYPE_MAPPER['连线题'],
      questionContent: row['题干'] || '',
      analysis: row['解析'] || '',
      folderId: 0,
      rightAnswer: '',
      options: [],
    }

    const leftOptions: Array<any> = []
    const rightOptions: Array<any> = []

    // 处理左侧选项
    for (let j = 'A'.charCodeAt(0); j <= 'E'.charCodeAt(0); j++) {
      const letter = String.fromCharCode(j)
      const content = row[`左侧项-${letter}`]
      if (content) {
        leftOptions.push({
          optionContent: content,
          optionPosition: '1',
          sort: j - 'A'.charCodeAt(0),
        })
      }
    }

    // 处理右侧选项
    for (let j = 1; j <= 5; j++) {
      const content = row[`右侧项-${j}`]
      if (content) {
        rightOptions.push({
          optionContent: content,
          optionPosition: '2',
          sort: j - 1,
        })
      }
    }

    // 合并所有选项
    questionData.options = [...leftOptions, ...rightOptions]

    // 处理连线答案
    const connections = (row['选项'] || '').split(',').map((pair: string) => {
      const [left, right] = pair.split('-')
      return {
        source: left.charCodeAt(0) - 'A'.charCodeAt(0),
        target: parseInt(right) - 1,
      }
    })

    // console.log(questionData)
    questionData.rightAnswer = JSON.stringify(connections)
    allQualifedQuestions.push(questionData)
  }
}
const requestMethod4GeneralQuestion: UploadProps['requestMethod'] = (
  file: any,
) => {
  return new Promise(async (resolve, reject) => {
    // 检查文件类型
    const validateResult = validSelectFile(file)
    if (!validateResult) {
      reject({
        status: 'fail',
        response: null,
      })
      return
    }

    allQualifedQuestions = []
    try {
      await readGeneralQuestionData(file)
    } catch (err: any) {
      if (err.toString() == TMPLT_CODE.TMPL_MATCHING_QUESTION_OPTION_ERROR) {
        MessagePlugin.error({
          content:
            '选择题答案格式不正确或者缺失正确答案，请在正确答案列中填写，正确或者错误',
        })
      } else if (err.toString() === TMPLT_CODE.TMPL_CHAOS) {
        MessagePlugin.error({
          content: '模板不正确，请下载普通题模板再重新上传',
        })
      } else {
        MessagePlugin.error({
          content: '模板信息不正确，请检查明确再重新上传',
        })
      }
      return reject({
        status: 'fail',
        response: null,
      })
    }
    MessagePlugin.success({
      content: '解析完毕，请点击确认并上传到教材库',
    })
    resolve({
      status: 'success',
      response: allQualifedQuestions as any,
    })

    resolve({
      status: 'success',
      response: {
        url: 'https://example.com/uploaded-file.xlsx',
      },
    })
  })
}

const requestMethod4MatchingQuestion: UploadProps['requestMethod'] = (
  file: any,
) => {
  return new Promise(async (resolve, reject) => {
    // 检查文件类型
    const validateResult = validSelectFile(file)
    if (!validateResult) {
      reject({
        status: 'fail',
        response: null,
      })
      return
    }
    try {
      allQualifedQuestions = []
      try {
        await readMatchingQuestionData(file)
      } catch (err: any) {
        if (err.toString() === TMPLT_CODE.TMPL_CHAOS) {
          MessagePlugin.error({
            content: '模板不正确，请下载连线题模板再重新上传',
          })
        } else {
          MessagePlugin.error({
            content: '模板信息不正确，请检查明确再重新上传',
          })
        }
        return reject({
          status: 'fail',
          response: null,
        })
      }
      MessagePlugin.success({
        content: '解析完毕，请点击确认并上传到教材库',
      })
      resolve({
        status: 'success',
        response: allQualifedQuestions as any,
      })
    } catch (error) {
      reject({
        status: 'fail',
        response: error,
      })
    }
  })
}

function closeHandler() {
  showImportPopup = false
  emits('cancel')
}
async function doConfirm() {
  if (!props.bookId || !props.chapterId) {
    MessagePlugin.error('bookId或者chapterId缺失')
    return
  }
  try {
    if (!defaultFolderData) {
      MessagePlugin.error('教材默认folder没有生成')
      return
    }
    const mappedBookQuestions = allQualifedQuestions.map((question) => {
      return {
        questionType: question.questionType,
        chapterId: props.chapterId,
        bookId: props.bookId,
        folderId: defaultFolderData.folderId,
        userQuestion: {
          createSource: 1,
          questionType: question.questionType,
          questionContent: question.questionContent,
          rightAnswer: question.rightAnswer,
          analysis: question.analysis,
          disorder: question.disorder,
          sort: '',
          folderId: 0,
          options: question.options,
          codeContent: '',
        },
      }
    })
    const savedQuestionsResp = await importQuestions(mappedBookQuestions)
    if (savedQuestionsResp.data) {
      showImportPopup = false
      // 模拟上传成功
      MessagePlugin.success({
        content: '上传成功',
      })
      emits('confirm', savedQuestionsResp.data)
    }
  } catch (error: any) {
    MessagePlugin.error(error.toString())
  }
}
function doCancel() {
  closeHandler()
}

// 监听变化
watch(
  () => props.visibility,
  (newVal) => {
    showImportPopup = newVal
    if (showImportPopup && props.bookId) {
      queryBookResourceDefaultFolder(props.bookId).then((defaultFolderObj) => {
        defaultFolderData = defaultFolderObj
      })
    }
  },
)

// hook
onMounted(() => {
  showImportPopup = props.visibility
})
</script>
<style lang="less" scoped>
.import-question-from-file {
  .footer-content {
    text-align: right;
    .cancel-btn {
      margin-right: 10px;
    }
  }
}
</style>
