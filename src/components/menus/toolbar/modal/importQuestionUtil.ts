export const TMPLT_CODE = {
  TMPL_CHAOS: '01',
  TMPL_ITEM_MISSING: '02',
  TMPL_QUESTION_TYPE_INVALID: '03',
  TMPL_QUESTION_CONTENT_INVALID: '04',
  TMPL_QUESTION_OPTION_INVALID: '05',
  TMPL_QUESTION_OPTION_RIGHTANSWER_INVALID: '07',
  TMPL_MATCHING_QUESTION_OPTION_INVALID: '09',
  TMPL_MATCHING_QUESTION_OPTION_MISSING: '10',
  TMPL_MATCHING_QUESTION_OPTION_ERROR: '11',
}
const ALPHBET_INDEX_MAPPER = {
  A: 1,
  B: 2,
  C: 3,
  D: 4,
  E: 5,
  F: 6,
  G: 7,
  H: 8,
  I: 9,
  J: 10,
  K: 11,
  L: 12,
  M: 13,
  N: 14,
  O: 15,
  P: 16,
  Q: 17,
  R: 18,
  S: 19,
  T: 20,
  U: 21,
  V: 22,
  W: 23,
  X: 24,
  Y: 25,
  Z: 26,
}
export const QUESTION_TYPE_MAPPER = {
  单选题: 1,
  多选题: 2,
  填空题: 3,
  排序题: 4,
  连线题: 5,
  简答题: 6,
  判断题: 7,
  编程题: 8,
  '1': '单选题',
  '2': '多选题',
  '3': '填空题',
  '4': '排序题',
  '5': '连线题',
  '6': '简答题',
  '7': '判断题',
  '8': '编程题',
}
const LEFT_OPTION_CONTENT_REG = /左侧项-[A-Za-z]/
const RIGHT_OPTION_CONTENT_REG = /右侧项-[0-9]/
const OPTION_CONTENT_REG = /选项[0-9]/
const GENERAL_QUESTION_FIELDS = ['说明', '题干', '题型', '正确答案', '答案解析']
const MATCHING_QUESTION_FIELDS = ['题号', '题干', '解析', '选项']
function rightAnswersExistInOptions(row, optionKeys) {
  const rightAnswerText = row['正确答案'].split('')
  // 选项的内容需要有值
  const rightAnswerNotExists = rightAnswerText.some((rightAnswerChar) => {
    if (ALPHBET_INDEX_MAPPER[rightAnswerChar] > optionKeys.length) {
      return true
    }
    return false
  })
  return !rightAnswerNotExists
}
export function validateGeneralQuestionItemTmpl(row) {
  const keys = Object.keys(row)
  for (let i = 0; i < keys.length; i++) {
    if (
      GENERAL_QUESTION_FIELDS.indexOf(keys[i]) < 0 &&
      !OPTION_CONTENT_REG.test(keys[i])
    ) {
      return TMPLT_CODE.TMPL_CHAOS
    }
  }
}
export function validateMatchingQuestionItemTmpl(row) {
  const keys = Object.keys(row)
  for (let i = 0; i < keys.length; i++) {
    if (
      MATCHING_QUESTION_FIELDS.indexOf(keys[i]) < 0 &&
      !LEFT_OPTION_CONTENT_REG.test(keys[i]) &&
      !RIGHT_OPTION_CONTENT_REG.test(keys[i])
    ) {
      return TMPLT_CODE.TMPL_CHAOS
    }
  }
}
export function validateMandatoryFields(row, matchingQuestion = false) {
  let questionTypeTextValue = row['题型']
  if (matchingQuestion) {
    questionTypeTextValue = QUESTION_TYPE_MAPPER['5']
  }
  if (!questionTypeTextValue || !QUESTION_TYPE_MAPPER[questionTypeTextValue]) {
    return TMPLT_CODE.TMPL_QUESTION_TYPE_INVALID
  }
  if (!row['题干']) {
    return TMPLT_CODE.TMPL_QUESTION_CONTENT_INVALID
  }
  if (
    questionTypeTextValue === QUESTION_TYPE_MAPPER['1'] ||
    questionTypeTextValue === QUESTION_TYPE_MAPPER['2']
  ) {
    if (!row['正确答案']) {
      // 正确答案没有设置或者正确答案设置的不是A~Z字母
      return TMPLT_CODE.TMPL_QUESTION_OPTION_RIGHTANSWER_INVALID
    }

    const optionKeys = Object.keys(row).filter((keyItem) =>
      OPTION_CONTENT_REG.test(keyItem),
    )
    // 正确答案必须都是A~Z字母
    if (!rightAnswersExistInOptions(row, optionKeys)) {
      return TMPLT_CODE.TMPL_QUESTION_OPTION_RIGHTANSWER_INVALID
    }
    // 验证每个选项是否有内容
    for (let i = 1, len = optionKeys.length; i <= len; i++) {
      if (!row['选项' + i]) {
        return TMPLT_CODE.TMPL_QUESTION_OPTION_INVALID
      }
    }
  } else if (questionTypeTextValue === QUESTION_TYPE_MAPPER['5']) {
    if (!row['选项']) {
      return TMPLT_CODE.TMPL_QUESTION_OPTION_RIGHTANSWER_INVALID
    }
    const rightAnswerPairs = row['选项'].split(',')
    // A-2,B-3,C-5,D-4,E-1
    for (let i = 0; i < rightAnswerPairs.length; i++) {
      const sourceAndTarget = rightAnswerPairs[i].split('-')
      if (sourceAndTarget.length !== 2) {
        return TMPLT_CODE.TMPL_QUESTION_OPTION_RIGHTANSWER_INVALID
      }
      if (
        !row['左侧项-' + sourceAndTarget[0]] ||
        !row['右侧项-' + sourceAndTarget[1]]
      ) {
        return TMPLT_CODE.TMPL_QUESTION_OPTION_RIGHTANSWER_INVALID
      }
    }
  }
}
