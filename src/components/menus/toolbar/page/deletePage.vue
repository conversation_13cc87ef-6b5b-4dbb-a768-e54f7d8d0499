<template>
  <menus-button
    ico="deletePage"
    :text="t('page.deletePage.text')"
    huge
    @menu-click="deletePage"
  />
</template>

<script setup lang="ts">
const { editor } = useStore()

const deletePage = () => {
  const dialog = useConfirm({
    theme: 'danger',
    header: t('page.deletePage.deleteConfirm'),
    body: t('page.deletePage.deleteError'),

    onConfirm() {
      editor.value?.chain().focus().deletePage().run()
      dialog.destroy()
    },
    onCancel() {
      dialog.destroy()
    },
  })
}
</script>
