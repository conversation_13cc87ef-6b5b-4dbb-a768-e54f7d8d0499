<template>
    <menus-button ico="gridPattern" :text="t('pageOptions.gridPattern.text')" huge @menu-click="handleClick" />

    <modal :visible="show" attach="body" :prevent-scroll-through="false" placement="center" draggable
        :header="t('pageOptions.gridPattern.text')" destroy-on-close width="600px" @confirm="handleConfirm"
        @close="handleClose">
        <t-form :data="data" label-align="right" label-width="120px">
            <t-form-item :label="t('pageOptions.gridPattern.switch')" name="switch">
                <t-switch v-model="data.switchValue" />
            </t-form-item>
            <t-form-item :label="t('pageOptions.gridPattern.color')" name="color">
                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                    <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                        <div :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${data.backgroundColor};margin-right:10px;`"
                            :class="`${data.backgroundColor == 'transparent' || !data.backgroundColor ? 'transparent' : ''}`">
                        </div>
                        <div class="umo-color-picker-more-menu"
                            :style="`border-bottom: 3px solid ${data.backgroundColor};}`">
                            <span v-text="t('insert.block.backgroundColor')"></span>
                        </div>
                    </div>
                    <template #content>
                        <div style="padding: 10px">
                            <color-picker :default-color="data.backgroundColor" @change="backgroundColorChange" />
                        </div>
                    </template>
                </t-popup>
            </t-form-item>

            <t-form-item :label="t('pageOptions.gridPattern.size')" name="size">
                <t-input-number v-model="data.size" />
            </t-form-item>
        </t-form>

    </modal>
</template>

<script setup lang="ts">
const { getPageLineObj, gridPatternObj } = useStore()
const show = ref(false)
const data = ref(gridPatternObj.value)
const handleClick = () => {
    data.value = gridPatternObj.value
    show.value = true
}
const handleClose = () => {
    show.value = false
}
const handleConfirm = () => {
    getPageLineObj(data.value)
    handleClose()
}

const backgroundColorChange = (color) => {
    data.value.backgroundColor = color
}

</script>