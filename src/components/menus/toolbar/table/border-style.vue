<template>
  <menus-button
    ico="borderStyle"
    :text="t('table.borderStyle.text')"
    menu-type="popup"
    huge
    :disabled="!editor?.can().setCellAttribute('borderStyle', '')"
    :popup-visible="popupVisible"
    @toggle-popup="togglePopup"
  >
    <template #content>
      <div>
        <div class="border-label">{{ t('table.borderStyle.borderWidth') }}</div>
        <div class="border-input">
          <t-input v-model="numValue" type="number" placeholder="1" />
        </div>
        <div class="border-label">{{ t('table.borderStyle.borderCss') }}</div>
        <div>
          <t-select v-model="styleValue" placeholder="实线">
            <t-option value="solid">
              <hr class="border-demo solid"></hr>
            </t-option>
            <t-option value="dashed">
                <hr class="border-demo dashed"></hr>
            </t-option>
            <t-option value="dotted">
                <hr class="border-demo dotted"></hr>
            </t-option>
          </t-select>
        </div>
        <div class="border-btn">
            <t-button theme="default" style="width: 100%;" @click="borderStyleChange()">{{ t('table.borderStyle.borderUse') }}</t-button>
        </div>
      </div>
    </template>
  </menus-button>
</template>

<script setup lang="ts">
const emits = defineEmits(['change'])

const { popupVisible, togglePopup } = usePopup()
const { editor } = useStore()
const numValue = ref(1)
const styleValue = ref('solid')
const borderStyleChange = () => {
  popupVisible.value = false
  const borderStyle= `${numValue.value}px ${styleValue.value}`
  editor.value
    ?.chain()
    .focus()
    .setCellAttribute('borderStyle', borderStyle)
    .run()
}
</script>
<style lang="less" scoped>
.border-demo {
  width: 100%;
  height: 1px;
  margin: 5px 0;
  :global(.umo-select-option span) {
    width: 100%;
    position: relative;
    white-space: nowrap;
    word-wrap: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}
  &.solid {
    border-top: 1px solid #000;
  }
  &.dashed {
    border-top: 1px dashed #000;
  }
  &.dotted {
    border-top: 1px dotted #000;
  }
}
.border-label {
  margin: 10px 0;
}
.border-input {
  width: 100%;
  margin: 10px 0;
}

.border-btn{
  margin-top: 10px;
  width: 100%;
  text-align: center;
}
</style>
