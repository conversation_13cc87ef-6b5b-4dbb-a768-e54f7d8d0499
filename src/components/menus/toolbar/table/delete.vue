<template>
  <menus-button
    ico="table-delete"
    :text="t('table.delete.text')"
    huge
    :disabled="!editor?.can().deleteTable()"
    @menu-click="deleteTable"
  />
</template>

<script setup lang="ts">
import { TABLE_PLUS } from '@/extensions/page/node-names'

const { editor } = useStore()

const deleteTable = () => {
  const dialog = useConfirm({
    theme: 'danger',
    header: t('table.delete.title'),
    body: t('table.delete.message'),
    confirmBtn: {
      theme: 'danger',
      content: t('table.delete.delete'),
    },
    onConfirm() {
      editor.value
        ?.chain()
        .focus()
        .command(({ tr, commands }) => {
          let op = false
          tr.selection.ranges.forEach((range) => {
            const from = range.$from.pos
            const to = range.$to.pos
            tr.doc.nodesBetween(from, to, (node, pos) => {
              if (TABLE_PLUS === node.type.name) {
                commands.deleteNode(TABLE_PLUS)
                op = true
                return false
              }
            })
          })
          if (!op) {
            commands.deleteTable()
          }
          return true
        })
        .run()
      useMessage('success', t('table.delete.success'))
      dialog.destroy()
    },
  })
}
</script>
