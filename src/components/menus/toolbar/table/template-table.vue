<template>
  <menus-button
    ico="table-template"
    :text="t('table.templateTable.text')"
    :tooltip="t('table.templateTable.tip')"
    huge
    @menu-click="openModel"
  />

  <modal
    :visible="visible"
    icon="page-margin"
    :header="t('table.templateTable.text')"
    width="980px"
    :footer="false"
    :show-header="true"
    :is-table="true"
    class="template-table-modal"
    @close="closeModal"
  >
    <template #templateHeader>
      <div class="table-template-form">
        <div class="table-template-form-title">
          {{ t('table.templateTable.text') }}
        </div>
        <div class="table-template-form-content">
          <t-form
            :data="formData"
            layout="inline"
            label-width="0"
            @submit="onSearch"
          >
            <t-form-item>
              <t-input
                v-model="formData.name"
                :placeholder="t('table.templateTable.placeholder')"
                size="large"
              />
              <t-button style="margin: 0 20px" size="large" type="submit">{{
                t('table.templateTable.search')
              }}</t-button>
              <t-button size="large" theme="default" @click="onReset">{{
                t('table.templateTable.reset')
              }}</t-button>
            </t-form-item>
          </t-form>
        </div>
      </div>
    </template>

    <div class="table-template-list">
      <div
        v-for="item in dataList"
        :key="item.templateId"
        class="table-template-item"
      >
        <div class="mask">
          <div class="table-template-item-img-button">
            <t-button style="width: 120px" @click="() => onUse(item)">{{
              t('table.templateTable.use')
            }}</t-button>
          </div>
        </div>

        <div class="table-template-item-img">
          <div class="tdesign-demo-image-viewer__ui-image">
            <img
              alt="test"
              :src="item.coverUrl"
              class="tdesign-demo-image-viewer__ui-image--img"
            />
          </div>
          <div class="table-template-item-info">
            <div class="table-template-item-info-name">
              <i class="icon"></i>
              {{ item.templateName }}
            </div>
          </div>
        </div>
        <!--  -->
      </div>
    </div>
  </modal>
</template>
<script setup>
import { getTemplateList } from '@/api/table/table'

const { editor } = useStore()
const visible = ref(false)
const formData = ref({})

const dataList = ref([])

const getList = () => {
  getTemplateList().then((res) => {
    dataList.value = res.data
  })
}
const openModel = () => {
  getList()
  visible.value = true
}
const closeModal = () => {
  visible.value = false
}

const onReset = () => {
  formData.value = {}
  getList()
}

const onSearch = () => {
  if (!formData.value.name) {
    dataList.value = dataList.value
  } else {
    dataList.value = dataList.value.filter((item) => {
      return item.templateName.includes(formData.value.name)
    })
  }
}

const onUse = (item) => {
  editor.value
    ?.chain()
    .focus()
    .setTablePlus({
      name: item.templateName,
      json: item.templateJson,
      templateId: item.templateId,
    })
    .run()
  // editor.value?.chain().focus().updateTableTemplate(item.templateJson).run()

  visible.value = false
}
</script>
<style lang="less" scoped>
.table-template-form {
  display: flex;
  align-items: center;

  .table-template-form-title {
    padding-right: 190px;
    color: #333;
  }
}

.template-table-modal {
  .umo-dialog__body {
    background-color: #f5f5f5;
    padding: 32px 46px;
  }

  .umo-dialog--default {
    padding: 0;
    /* padding: var(--td-comp-paddingTB-xxl) var(--td-comp-paddingLR-xxl); */
  }

  .umo-dialog:not(.umo-dialog__fullscreen):not(.t-dialog__fullscreen) {
    padding: 0 !important;
  }

  .umo-dialog__header,
  .t-dialog__header {
    background: #fff;
    border-bottom: 1px solid #ddd;
    padding: 15px 32px;
  }
}

.table-template-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin: 20px 0;

  .table-template-item {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-radius: 4px;
    background-color: #fff;
    padding: 18px;
    position: relative;
    cursor: pointer;
    z-index: 1;

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      opacity: 0;
      transition: all 0.3s;
      z-index: 99;

      .table-template-item-img-button {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);

        border-radius: 4px;
      }
    }

    &:hover {
      .mask {
        opacity: 1;
      }
    }

    .table-template-item-info {
      padding-top: 20px;

      .table-template-item-info-name {
        display: flex;
        align-items: center;
        color: #333;
        .icon {
          display: inline-flex;
          width: 16px;
          height: 16px;
          background-image: url('@/assets/images/tableTemplateIcon.svg');
          background-size: contain;
          margin-right: 5px;
        }
      }
    }

    .tdesign-demo-image-viewer__ui-image {
      width: 172px;
      height: 142px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
      }

      .table-template-item-img-mask {
        position: absolute;
        top: 5px;
        right: 0px;
        width: 30px;
        height: 30px;
        cursor: pointer;
      }

      .table-template-item-img-button {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);

        border-radius: 4px;

        span {
          width: 160px;
        }
      }
    }
  }
}
</style>
