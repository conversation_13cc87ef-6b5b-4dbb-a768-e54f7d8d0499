<template>
  <t-dialog
    :visible="visible"
    attach="body"
    :prevent-scroll-through="false"
    :header="header"
    placement="center"
    destroy-on-close
    v-bind="$attrs"
    :footer="footer"
    :mode="mode"
    :close-on-overlay-click="closeOnOverlayClick"
    @close="emit('close', false)"
  >
    <template v-if="showHeader" #header>
      <div v-if="!isTable">
        <icon v-if="$attrs.icon" :name="$attrs.icon as string" />
        <span>{{ $attrs.header }}</span>
      </div>
      <div v-else><slot name="templateHeader" /></div>
    </template>

    <template v-else #header>
      <div>{{ header }}</div>
    </template>
    <slot />
  </t-dialog>
</template>

<script setup lang="ts">
const { options } = useStore()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  showHeader: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
  header: {
    type: String,
    default: '提示',
  },
  footer: {
    type: Boolean,
    default: true,
  },
  isTable: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'modal',
  },
  closeOnOverlayClick: {
    type: Boolean,
    default: true,
  },
})
const { container } = useStore()
const emit = defineEmits(['close', 'closed'])
// const handleClose = () => {
//   console.log("handleClose");
//   emit("close", false);
// }
// 确认

// 方法
function closeHandler() {
  emit('close', false)
}
</script>

<style lang="less">
.umo-dialog__header--fullscreen,
.t-dialog__header--fullscreen {
  height: 40px;
  padding: 0 15px;
  border-bottom: solid 1px var(--umo-border-color);
  position: fixed;
  width: 100%;
  z-index: 999;
  background-color: var(--td-bg-color-container);
  color: var(--td-text-color-primary);
}

.umo-dialog__body--fullscreen--without-footer {
  padding: 0;
}

.umo-dialog__header--fullscreen .umo-dialog__header-content,
.t-dialog__header--fullscreen .umo-dialog__header-content,
.umo-dialog__header--fullscreen .t-dialog__header-content,
.t-dialog__header--fullscreen .t-dialog__header-content {
  justify-content: center !important;
}

.umo-dialog__fullscreen,
.t-dialog__fullscreen {
  border-radius: 0;
  display: flex;
  flex-direction: column;
  background: var(--td-bg-color-container);
  box-shadow: none;
}
</style>
