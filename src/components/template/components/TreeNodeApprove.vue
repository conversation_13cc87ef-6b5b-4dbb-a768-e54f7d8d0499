<template>
  <div class="tree-list">
    <div v-for="item in dataList" :key="item.id" class="tree-item">
      <div class="tree-item-title">
        <div class="tree-item-title-header">
          <ChevronUpIcon
            v-if="!item.isOpen && item.children?.length"
            style="margin-right: 5px; min-width: 20px"
            @click="handleClick(item)"
          />
          <ChevronDownIcon
            v-if="item.isOpen && item.children?.length"
            style="margin-right: 5px; min-width: 20px"
            @click="handleClick(item)"
          />
          <div @click="handleGoTo(item)">
            {{ item.name }}
          </div>
        </div>
        <div>
          <TreeNodeApprove
            v-if="item.isOpen && item.children && item.children.length > 0"
            :data-list="item.isOpen ? item.children : []"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup name="TreeNodeApprove">
import { ChevronDownIcon, ChevronUpIcon } from 'tdesign-icons-vue-next'
const props = defineProps({
  dataList: {
    type: Array,
    required: true,
  },
})

const handleClick = (item) => {
  item.isOpen = !item.isOpen
}
const handleGoTo = (item) => {
  const iframe = document.querySelector('.iframePage');
  if (iframe && iframe.contentWindow) {
    iframe.contentWindow.postMessage(
      {
        type: 'SCROLL_TO_ELEMENT',
        payload: {
          domId: item.domId,
        },
      },
      '*' // 注意：建议指定为 iframe 的 origin
    );
  }
};
</script>

<style lang="less" scoped>
.tree-list {
  .tree-item {
    .tree-item-title {
      font-size: 14px;
      color: #333;
      font-weight: 600;
      padding: 10px 0;

      padding: 10px 20px;

      cursor: pointer;
      .tree-item-title-header {
        display: flex;
        align-items: center;
      }
    }
  }
}
</style>
