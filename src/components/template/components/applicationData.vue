<template>
  <!--应用数据-->
  <div class="app-container">
    <div style="padding: 20px">
      <t-row :gutter="20">
        <t-col :span="1">
          <div class="title-data">全书数据</div>
        </t-col>
        <t-col :span="10"></t-col>
        <t-col :span="1">
          <div class="export-data">
            <t-button
              v-if="
                curUserPermissions.isEditor ||
                curUserPermissions.permissions.includes(2) ||
                curUserPermissions.permissions.includes(1)
              "
              theme="primary"
              @click="handleExport"
              >导出数据</t-button
            >
          </div>
        </t-col>
      </t-row>
    </div>
    <div class="dashboard-container">
      <t-row :gutter="20" class="mb10">
        <t-col :span="3">
          <div class="data-card">
            <div class="data-round">
              <i class="total"></i>
            </div>
            <div class="data-bg">
              <div class="data-title">学习总时长（h）</div>
              <div class="data-value">
                {{ allBookData.studyTotalTimeHour || 0 }}
              </div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="data-card">
            <div class="data-round">
              <i class="video"></i>
            </div>
            <div class="data-bg">
              <div class="data-title">视频学习总时长（h）</div>
              <div class="data-value">
                {{ allBookData.studyVideoTimeHour || 0 }}
              </div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="data-card">
            <div class="data-round">
              <i class="note"></i>
            </div>
            <div class="data-bg">
              <div class="data-title">笔记总条数</div>
              <div class="data-value">
                {{ allBookData.studyNoteQuantity || 0 }}
              </div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="data-card">
            <div class="data-round">
              <i class="highlight"></i>
            </div>
            <div class="data-bg">
              <div class="data-title">高亮总条数</div>
              <div class="data-value">
                {{ allBookData.studyHighlightQuantity || 0 }}
              </div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="data-card">
            <div class="data-round">
              <i class="discuss"></i>
            </div>
            <div class="data-bg">
              <div class="data-title">讨论总条数</div>
              <div class="data-value">
                {{ allBookData.studyDiscussQuantity || 0 }}
              </div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="data-card">
            <div class="data-round">
              <i class="people"></i>
            </div>
            <div class="data-bg">
              <div class="data-title">学习总人数</div>
              <div class="data-value">
                {{ allBookData.studyUserQuantity || 0 }}
              </div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="data-card">
            <div class="data-round">
              <i class="test"></i>
            </div>
            <div class="data-bg">
              <div class="data-title">试题练习总次数</div>
              <div class="data-value">
                {{ allBookData.studyQuestionQuantity || 0 }}
              </div>
            </div>
          </div>
        </t-col>
        <t-col :span="3">
          <div class="data-card">
            <div class="data-round">
              <i class="accuracy"></i>
            </div>
            <div class="data-bg">
              <div class="data-title">
                试题练习平均正确率<t-tooltip
                  content="正确率不包括主观题"
                  placement="top"
                >
                  <HelpCircleIcon style="margin-left: 10px" color="#ff9000" />
                </t-tooltip>
              </div>
              <div class="data-value">
                {{ allBookData.studyQuestionRate || 0 }} %
              </div>
            </div>
          </div>
        </t-col>
      </t-row>
    </div>
    <div style="margin-left: 20px">
      <t-row :gutter="20">
        <t-col :span="2">
          <div class="title-data">章节数据</div>
        </t-col>
      </t-row>
    </div>
    <div class="dashboard-container">
      <t-table
        row-key="key"
        :data="dataList"
        :columns="columns"
        :hover="true"
        lazy-load
      ></t-table>
    </div>
  </div>
</template>

<script setup>
import { HelpCircleIcon } from 'tdesign-icons-vue-next'
import { ref } from 'vue'

import { listChapterData } from '@/api/book/chapterData'
import { download } from '@/request/index'
const { proxy } = getCurrentInstance()
const allBookData = ref({})
const dataList = ref([])

const loading = ref(false)

const props = defineProps({
  bookId: {
    type: String,
    default: null,
  },
  bookName: {
    type: String,
    default: null,
  },
  curUserPermissions: {
    type: Object,
    default: {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    },
  },
})
const queryParams = ref({
  bookId: props.bookId,
})
const columns = [
  { colKey: 'chapterName', title: '章节目录', width: '300', ellipsis: true },
  { colKey: 'studyUserQuantity', title: '学习人数' },
  { colKey: 'studyTotalTimeHour', title: '学习总时长（h）', ellipsis: true },
  { colKey: 'studyVideoTimeHour', title: '视频学习总时长（h）' },
  { colKey: 'studyNoteQuantity', title: '笔记总条数' },
  { colKey: 'studyHighlightQuantity', title: '高亮总条数' },
  { colKey: 'studyDiscussQuantity', title: '讨论总条数' },
  { colKey: 'studyQuestionQuantity', title: '试题练习总次数' },
  { colKey: 'studyQuestionRate', title: '试题练习平均正确率' },
]

function handleExport() {
  download(
    '/book/chapterData/exportAppData',
    {
      ...queryParams.value,
    },
    `${props.bookName}-应用数据.xlsx`,
  )
}

watch(
  () => props.bookId,
  (value) => {
    queryParams.value.bookId = value
    getList()
  },
)

onMounted(() => {
  getList()
})

function getList() {
  loading.value = true
  listChapterData(queryParams.value).then((response) => {
    dataList.value = response.data
    allBookData.value = {
      studyTotalTimeHour: 0,
      studyVideoTimeHour: 0,
      studyNoteQuantity: 0,
      studyHighlightQuantity: 0,
      studyDiscussQuantity: 0,
      studyUserQuantity: 0,
      studyQuestionQuantity: 0,
      studyQuestionRate: 0,
    }

    dataList.value.forEach((e) => {
      if (e.studyTotalTimeHour) {
        allBookData.value.studyTotalTimeHour += e.studyTotalTimeHour
      }
      if (e.studyVideoTimeHour) {
        allBookData.value.studyVideoTimeHour += e.studyVideoTimeHour
      }
      if (e.studyNoteQuantity) {
        allBookData.value.studyNoteQuantity += e.studyNoteQuantity
      }
      if (e.studyHighlightQuantity) {
        allBookData.value.studyHighlightQuantity += e.studyHighlightQuantity
      }
      if (e.studyDiscussQuantity) {
        allBookData.value.studyDiscussQuantity += e.studyDiscussQuantity
      }
      if (e.studyUserQuantity) {
        allBookData.value.studyUserQuantity += e.studyUserQuantity
      }
      if (e.studyQuestionQuantity) {
        allBookData.value.studyQuestionQuantity += e.studyQuestionQuantity
      }
      if (e.studyQuestionRate) {
        allBookData.value.studyQuestionRate += e.studyQuestionRate
      }
    })
    if (dataList.value.length > 0) {
      allBookData.value.studyQuestionRate = (
        allBookData.value.studyQuestionRate / dataList.value.length
      ).toFixed(2)
      allBookData.value.studyTotalTimeHour = allBookData.value.studyTotalTimeHour.toFixed(2)
      allBookData.value.studyVideoTimeHour = allBookData.value.studyVideoTimeHour.toFixed(2)
    }
    loading.value = false
  })
}
</script>

<style scoped lang="less">
.dashboard-container {
  padding: 20px;
}

.title-data {
  font-size: 16px;
  font-weight: 600;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background-color: #428dcb;
    margin-right: 10px;
    border-radius: 3px;
  }
}

.export-data {
  text-align: right;
}

.data-card {
  background-color: #fff6ec;
  /* 背景颜色 */
  border-radius: 8px;
  padding: 30px 35px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;

  .data-round {
    width: 68px;
    height: 68px;
    background-color: #ffb86b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 25px;

    .total {
      display: inline-block;
      background: url('@/assets/images/total.svg') no-repeat;
      width: 36px;
      height: 36px;
      background-size: contain;
    }

    .video {
      display: inline-block;
      background: url('@/assets/images/videoIcon.svg') no-repeat;
      width: 36px;
      height: 36px;
      background-size: contain;
    }

    .note {
      display: inline-block;
      background: url('@/assets/images/note.svg') no-repeat;
      width: 36px;
      height: 36px;
      background-size: contain;
    }

    .highlight {
      display: inline-block;
      background: url('@/assets/images/highlight.svg') no-repeat;
      width: 36px;
      height: 36px;
      background-size: contain;
    }

    .discuss {
      display: inline-block;
      background: url('@/assets/images/discuss.svg') no-repeat;
      width: 36px;
      height: 36px;
      background-size: contain;
    }

    .accuracy {
      display: inline-block;
      background: url('@/assets/images/accuracy.svg') no-repeat;
      width: 36px;
      height: 36px;
      background-size: contain;
    }

    .people {
      display: inline-block;
      background: url('@/assets/images/people.svg') no-repeat;
      width: 36px;
      height: 36px;
      background-size: contain;
    }

    .test {
      display: inline-block;
      background: url('@/assets/images/test.svg') no-repeat;
      width: 36px;
      height: 36px;
      background-size: contain;
    }
  }

  .data-bg {
    text-align: left;

    .data-title {
      font-size: 16px;
      color: #333;
      margin: 0;
    }

    .data-value {
      font-size: 36px;
      font-weight: bold;
      color: #333;
      margin-top: 17px;
      flex-grow: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }
  }
}
</style>
