<template>
    <Modal :visible="visible" header="审批记录" @close="closeModel" width="800px">
        <div>
            <div>教材名称: {{ approveData.name }}</div>
            <div>章节名称: {{ approveData.chapterName }}</div>
            <div class="approve-list">
                <div class="approve-item" v-for="item in approveData.children" :key="item.id">
                    <div class="approve-item-header">
                        <div><span>当前节点:</span>{{ item.currentNode }}</div>
                        <div><span>发起人:</span>{{ item.promoter }}</div>
                        <div><span>发起时间:</span>{{ item.promoterTime }}</div>
                    </div>
                    <div class="approve-item-content">
                        <div class="approve-item-content-header">
                            <div><span>审批状态：</span> <t-tag theme="primary">{{ item.status }}</t-tag></div>
                            <div><span>审批时间:</span>{{item.approveTime  }}</div>
                        </div>
                        <div class="approve-item-content-body">
                            <div><span>审批意见:</span>{{item.remark}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Modal>
</template>
<script setup lang="ts">
import Modal from '@/components/modal.vue';
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
})
const emits = defineEmits(['close'])
const closeModel = () => {
  emits('close',false)
}

const approveData = ref({
  name: '大学外语',
  chapterName:'第一章 顺序的逆序',
  id:1,
  children:[
    {
        id:12,
        currentNode:'章节提交',
        promoter:'张三',
        promoterTime:'2021-01-01 13:00:00',
        status:'1',
        approveTime:'2021-01-01 13:00:00',
        remark:'可以了，干的很好'
    },
    {
        id:13,
        currentNode:'章节提交',
        promoter:'张三',
        promoterTime:'2021-01-01 13:00:00',
        status:'2',
        approveTime:'2021-01-01 13:00:00',
        remark:'可以了，干的很好'
    }
  ]
})
</script>
<style lang="less" scoped>
.approve-list{
    
    margin-top: 10px;
    .approve-item{
       margin-bottom: 20px;
       border: 1px solid #ddd;
       border-radius: 5px;
        .approve-item-header{
            display: grid;
            grid-template-columns: repeat(3,1fr);
            grid-gap:10px;
            background-color: #eee;
            border-bottom: 1px solid #ddd;
            padding:10px;
            border-radius: 5px 5px 0 0;
            span{
                color:#999;
                margin-right:5px;
            }
        }

        .approve-item-content{
            padding:20px;
            .approve-item-content-header{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
                span{
                    color:#999;
                    margin-right:5px;
                }
            }

            .approve-item-content-body{
                span{
                    color:#999;
                    margin-right:5px;
                }
            }
        }
    }
}

</style>