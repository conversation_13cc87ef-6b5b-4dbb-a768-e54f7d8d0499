<template>
  <div>
    <div class="back">
      <div class="back-btn-bg">
        <div class="back-btn" @click="$router.go(-1)">
          <ChevronLeftIcon />
          <span>返回</span>
        </div>
        <div class="back-title">
          当前节点：{{ auditType == 1 ? '章节提交' : '章节撤销申请' }}
        </div>
      </div>
      <div><t-button @click="audit">审批</t-button></div>
    </div>
    <div class="approvePage-main">
      <div class="approvePage-left">
        <TreeNodeApprove :data-list="chapterNodeList" />
      </div>
      <div class="approvePage-right">
        <div style="text-align: center; font-size: 30px; margin: 20px 0">
          {{ chapterName }}
        </div>
        <div v-if="revokedReason">撤销理由：{{ revokedReason }}</div>
<!--        <analysisJson check-tabs="desktop" :page-config-list="pageConfigList" />-->
        <iframe
          :src="href"
          frameborder="0"
          class="iframePage"
        ></iframe>
      </div>
    </div>
    <!-- 审核 -->
    <t-dialog
      v-model:visible="auditOpen"
      header="审核"
      placement="center"
      width="650px"
      :show-in-attached-element="true"
      @confirm="auditFormSubmit"
      @close="auditOpen = false"
    >
      <t-form
        ref="auditFormRef"
        :data="form"
        :rules="{
          chapterStatus: [
            {
              required: true,
              message: '审批结果',
              type: 'error',
              trigger: 'change',
            },
          ],
        }"
        label-width="100px"
      >
        <t-form-item label="审批结果" name="chapterStatus" required>
          <t-select v-model="form.chapterStatus" clearable style="width: 200px">
            <t-option :value="2" label="同意"></t-option>
            <t-option :value="3" label="驳回"></t-option>
          </t-select>
        </t-form-item>
        <t-form-item label="审批意见" name="remark">
          <t-textarea
            v-model="form.remark"
            placeholder="请输入审批意见"
            :maxlength="200"
            show-word-limit
            :autosize="{ minRows: 2, maxRows: 4 }"
          >
          </t-textarea>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup name="approvePage">
import { ChevronLeftIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { useRoute, useRouter } from 'vue-router'
import { auditChapter } from '@/api/book/bookChapterAuditLog.js'
import { getChapterContentInfo } from '@/api/chapterContent'
import { chapterCatalogList } from '@/api/book/chapter.js'
import TreeNodeApprove from '../components/TreeNodeApprove.vue'
const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const auditOpen = ref(false)
const auditType = ref('')
const revokedReason = ref('')
const pageConfigList = ref([])
const chapterNodeList = ref([])
const chapterName = ref('')
const chapterId = ref(null)
const bookId = ref(null)
const form = ref({
  logId: null,
})
const href = ref('')

//#region 生命周期相关

onMounted(() => {
  chapterId.value = route.query.chapterId
  bookId.value = route.query.bookId
  form.value.logId = route.query.logId
  auditType.value = route.query.auditType
  revokedReason.value = route.query.revokedReason
  chapterName.value = route.query.chapterName
  getChapterDetail()
  chapterCatalogue()
  href.value = `${import.meta.env.VITE_READER_PREVIEW_URL + '?k='+ bookId.value + '&cid=' + chapterId.value + '&fromType=2' + '&operationFlag=false'}`;
})

//#endregion

//#region 操作相关

// 获取章节详情
function getChapterDetail() {
  getChapterContentInfo(chapterId.value).then((response) => {
    const { data } = response
    if (data) {
      pageConfigList.value = JSON.parse(data.content).content || []
    } else {
      pageConfigList.value = []
    }
  })
}

// 获取章节目录
async function chapterCatalogue() {
  const res = await chapterCatalogList(chapterId.value)
  if (res.code === 200) {
    chapterNodeList.value = res.data
  }
}

// 审批
function audit() {
  form.value = {
    logId: form.value.logId,
    chapterStatus: null,
    remark: null,
  }
  auditOpen.value = true
}

// 提交审核
function auditFormSubmit() {
  proxy.$refs['auditFormRef']
    .validate({
      showErrorMessage: true,
    })
    .then((valid) => {
      if (valid === true) {
        auditChapter(form.value).then((res) => {
          MessagePlugin.success('审核成功')
          auditOpen.value = false
          router.push({
            path: '/pages/chapter',
          })
        })
      }
    })
}
//#endregion
</script>

<style lang="less" scoped>
.back {
  display: flex;
  width: 100%;
  padding-bottom: 20px;
  justify-content: space-between;
  border-bottom: 1px solid #f1f1f1;

  .back-btn-bg {
    display: flex;
    align-items: center;

    .back-title {
      margin-left: 60px;
      color: #666;
      font-size: 14px;
    }

    .back-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      color: #999;
      font-size: 14px;

      span {
        margin-left: 5px;
      }
    }
  }
}
/* 针对所有结构标签内的h1 */
:deep(h1) {
  font-size: 2em !important; /* 强制覆盖默认值 */
}

.approvePage-main {
  display: flex;

  .approvePage-left {
    width: 300px;

    background-color: #fff;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    height: calc(100vh - 220px);
    border-right: 1px solid #ddd;
  }

  .approvePage-right {
    flex: 1;
    height: calc(100vh - 220px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    background-color: #fafafa;
    .approvePage-page {
      width: 900px;
      margin: 0 auto;
      min-height: calc(100vh - 220px);
      background-color: #fff;
      padding: 20px;
      margin: 20px auto;
    }
    .iframePage {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
