<template>
  <div class="app-container">
    <div class="chapter-header">
      <div class="chapter-header-main">
        <div style="margin-top: 5px">
          <t-checkbox v-model="selectAll" size="large" @change="handleSelectAll">全部章节</t-checkbox>
        </div>
        <div class="chapter-header-right">
          <t-button v-if="curUserPermissions.isEditor ||
            ((curUserPermissions.permissions.includes(2) ||
              curUserPermissions.permissions.includes(1)) &&
              publishStatus == 1)
            " theme="danger" @click="handleDelete">
            <template #icon>
              <DeleteIcon />
            </template>
            删除</t-button>
          <t-button style="margin-left: 20px" theme="default" @click="handleExport">
            <template #icon>
              <FileImportIcon />
            </template>
            导出</t-button>
          <t-tooltip content="主编和书稿联系人、有编写与预览章节权限的人只有在教材从未三审三校流程通过时才可以进行导出对应章节操作">
            <view style="
                margin-left: 20px;
                cursor: pointer;
                border: 1px solid #176eb8;
                border-radius: 5px;
                height: 13px;
                width: 13px;
                padding: 9px;
                display: flex;
                justify-content: center;
                align-items: center;
              ">
              <img src="@/assets/images/helpsIcon.svg" />
            </view>
          </t-tooltip>
        </div>
      </div>
      <div class="chapter-header-btn-group">
        <t-button v-if="(curUserPermissions.isEditor ||
            curUserPermissions.permissions.includes(2) ||
            curUserPermissions.permissions.includes(1)) && 
            publishStatus == 1 &&
            ( stepId == 1 || auditState == null || auditState == 3)
            " theme="primary" @click="addChapter">
          <template #icon>
            <PlusIcon />
          </template>
          新增章节</t-button>
        <t-button v-if="(curUserPermissions.isEditor ||
            curUserPermissions.permissions.includes(2) ||
            curUserPermissions.permissions.includes(1)) &&
            publishStatus == 1 &&
            ( stepId == 1 || auditState == null || auditState == 3)
            " theme="primary" @click="handleDragSort">
          <template #icon>
            <ArrowUpDown1Icon />
          </template>
          章节顺序</t-button>
        <t-button theme="primary" @click="handleRecycle">
          <template #icon>
            <DeleteIcon />
          </template>
          回收站</t-button>
        <t-button theme="primary" @click="openCaptionStyleDialog">
          <template #icon>
            <SettingIcon />
          </template>
          题注样式</t-button>
        <t-button v-if="curUserPermissions.isEditor ||
            curUserPermissions.permissions.includes(2) ||
            curUserPermissions.permissions.includes(1)
            " theme="primary" @click="openBookTempalteDialog">
          <template #icon>
            <SettingIcon />
          </template>
          教材模板</t-button>
      </div>
    </div>

    <div class="chapter-list">
      <div v-if="dataList.length > 0">
        <TreeNode ref="treeNode" :data-list="dataList" :cur-user-permissions="curUserPermissions"
          :is-free="props.isFree" :master-flag="masterFlag" :book-organize="bookOrganize" @refresh="refresh"
          @edit-chapter="editChapter" />
      </div>
      <div v-else class="chapter-list-empty">
        <t-empty />
      </div>
    </div>

    <CharpterMange ref="chapterMange" :is-free="props.isFree" :master-flag="masterFlag" :book-organize="bookOrganize"
      @refresh="refresh" />

    <!-- 回收站 -->
    <modal v-model:visible="isRecycle" header="回收站" width="30%" :confirm-btn="null" :cancel-btn="null">
      <div class="recycleList">
        <div v-if="recycleList.length > 0">
          <div v-for="item in recycleList" :key="item.chapterId" class="recycleList-item">
            <div class="recycleList-item-header">
              <div>{{ item.updateTime }}</div>
              <div class="recycleList-item-header-right" v-if="publishStatus == 1 &&
            ( stepId == 1 || auditState == null || auditState == 3)" @click="handleRecycleChapter(item)">
                恢复
              </div>
            </div>
            <div class="recycleList-item-content">
              <div>{{ item.chapterName }}</div>
              <div class="recycleList-item-content-right" @click="handleRecycleDetail(item)">
                详情
              </div>
            </div>
          </div>
        </div>
        <div v-else class="recycleList-empty">
          <t-empty />
        </div>
      </div>
    </modal>

    <!-- 题注样式 -->
    <modal v-model:visible="captionStyleDialog" header="题注样式" width="500px" :footer="false">
      <div>
        <t-form ref="captionStyleFormRef" :data="captionStyleForm" label-width="120px"
          @submit="handleConfirmCaptionStyle">
          <t-form-item label="图：">
            <t-radio-group v-model="captionStyleForm.imageNumberType">
              <t-radio :value="1" size="large">图1</t-radio>
              <t-radio :value="2" size="large">图1-1</t-radio>
              <!--              <t-radio :value="3" size="large">不显示</t-radio>-->
            </t-radio-group>
          </t-form-item>
          <t-form-item label="表：">
            <t-radio-group v-model="captionStyleForm.tableNumberType">
              <t-radio :value="1" size="large">表1</t-radio>
              <t-radio :value="2" size="large">表1-1</t-radio>
              <!--              <t-radio :value="3" size="large">不显示</t-radio>-->
            </t-radio-group>
          </t-form-item>
          <div class="footer">
            <t-button theme="default" style="margin-right: 10px" @click="captionStyleDialog = false">取消</t-button>
            <t-button v-if="(curUserPermissions.isEditor ||
            curUserPermissions.permissions.includes(2) ||
            curUserPermissions.permissions.includes(1)) &&
            publishStatus == 1 &&
            ( stepId == 1 || auditState == null || auditState == 3)
            " theme="primary" type="submit">
              更新
            </t-button>
          </div>
        </t-form>
      </div>
      <!-- <template #footer> -->

      <!-- </template> -->
    </modal>

    <!-- 拖拽排序 -->
    <modal v-model:visible="dragSortDialog" header="拖拽排序" width="500px" :confirm-btn="null" :cancel-btn="null">
      <div style="display: flex; justify-content: center; align-items: center">
        <VueDraggable v-model="dragSortDataList" group="inner" class="list-group">
          <div v-for="item in dragSortDataList" :key="item.chapterId" class="list-group-item">
            <MenuApplicationIcon style="margin-right: 10px" />
            {{ item.chapterName }}
          </div>
        </VueDraggable>
      </div>

      <div class="dialog-footer">
        <t-button theme="default" style="margin-right: 10px" @click="dragSortDialog = false">取消</t-button>
        <t-button theme="primary" @click="handleConfirmDragSort">
          确定
        </t-button>
      </div>
    </modal>

    <t-dialog v-model:visible="warningVisible" theme="warning" header="警示" :close-on-esc-keydown="false"
      :close-on-overlay-click="false" :body="confirmLoading
            ? '正在保存，请稍后...'
            : '当前章节已有人员正在进行编写，是否要继续进入编写?如进入编写，则前编写人员会被强制退出编辑器，且保存编写的内容。'
            " :on-close="onClickCancel" confirm-btn="编写内容" :confirm-loading="confirmLoading" @cancel="onClickCancel"
      @confirm="onClickConfirm" />

    <t-dialog v-model:visible="warningVisibleStyle" theme="warning" header="警示" :close-on-esc-keydown="false"
      :close-on-overlay-click="false" body="当前教材正在处于题注更新中，无法进入编辑器。" :on-close="onClickCancel" :confirm-btn="null"
      :cancel-btn="`关闭`" @cancel="onClickCancel" />

    <DeleteModal v-model="deleteModalVisible" @confirm="handleConfirmDelete"></DeleteModal>
    <!-- 教材模板 -->
    <t-dialog v-model:visible="bookTemplateDialog" header="教材模板" width="1200px" :confirm-btn="null" :cancel-btn="null">
      <t-card>
        <div class="tool">
          <t-form :data="queryParams" ref="queryRef" layout="inline" label-width="100px">
            <t-form-item prop="modal" label="模板名称">
              <t-input v-model="queryParams.modal" placeholder="请输入模板名称" clearable />
            </t-form-item>

            <t-form-item prop="serialNumber" label="序号">
              <t-input v-model="queryParams.serialNumber" placeholder="请输入序号" clearable />
            </t-form-item>

            <t-form-item prop="type" label="分类">
              <t-select v-model="queryParams.type" placeholder="全部" clearable style="width: 120px">
                <t-option v-for="item in bookTemplateTypeList" :key="item.typeId" :label="item.typeName"
                  :value="item.typeId" />
              </t-select>
            </t-form-item>

            <t-form-item prop="themeColor" label="颜色">
              <t-select v-model="queryParams.themeColor" placeholder="全部" clearable style="width: 100px">
                <t-option v-for="item in bookTemplateThemeList" :key="item.value" :value="item.value"
                  :label="item.label" style="display: flex;justify-content: center;align-items: center;">
                  <div :style="`width: 20px;height: 20px;background-color: ${item.label};`" />
                </t-option>
                <template #valueDisplay="{ value}">
                  <div style="display: flex; align-items: center; justify-content: center">
                    <div v-if="value" :style="`width: 20px;height: 20px;background-color:${getOptionDesc(bookTemplateThemeList, value)};`" />
                    <div v-else style="color: #999999">全部</div>
                  </div>
                </template>
              </t-select>
            </t-form-item>

            <t-form-item prop="sortBy" label="排序方式">
              <t-select v-model="queryParams.sortBy" placeholder="默认" clearable style="width: 120px">
                <t-option v-for="item in bookTemplateSortList" :key="item.value" :label="item.label"
                  :value="item.value" />

              </t-select>
            </t-form-item>

            <t-form-item>
              <t-button theme="primary" @click="handleQuery" style="margin-right: 20px;">
                <template #icon>
                  <SearchIcon />
                </template>搜索</t-button>
              <t-button @click="resetQuery" style="margin-right: 20px;">
                <template #icon>
                  <RefreshIcon />
                </template>重置</t-button>
              <!-- <t-button @click="updateTemplate">
                <template #icon>
                  <CheckIcon />
                </template>保存选中模板</t-button> -->
            </t-form-item>
          </t-form>
        </div>
      </t-card>
      <t-card style="margin-top: 20px">
        <div class="textBookTemplate-content">
          <div v-for="item in templateList" :key="item.templateId" class="textBookTemplate-content-item">
            <div :class="`${item.templateId == activeTemplate ? 'active' : ''}`"></div>
            <div class="textBookTemplate-content-item-img">
              <img :src="item.imgUrl" style="width: 170px; height: 250px" />
              <div class="textBookTemplate-content-item-img-opt-mask">
                <div class="textBookTemplate-content-item-img-opt-mask-bnt" @click="handleUpdateTemplate(item)">
                  选择
                </div>
                <div class="textBookTemplate-content-item-img-opt-mask-bnt" @click="handlePrevieTemplate(item)">
                  预览
                </div>
              </div>
            </div>
            <div class="textBookTemplate-content-item-title"
              :style="`color:${item.templateId == activeTemplate ? '#2979e9' : ''}`">
              {{ item.serialNumber || '' }}{{ item.modal }}
            </div>
          </div>
        </div>
        <t-pagination style="margin-top: 20px" v-show="total > 0" :total="total" v-model:current="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize" @change="getList" />
      </t-card>

    </t-dialog>
    <t-image-viewer :visible="showPreviewUrl" :images="[showPreviewUrl]" @close="showPreviewUrl = null">
    </t-image-viewer>
  </div>
</template>
<script setup name="chapter">
import {
  ArrowUpDown1Icon,
  DeleteIcon,
  FileImportIcon,
  MenuApplicationIcon,
  PlusIcon,
  SettingIcon,
  SearchIcon,
  RefreshIcon,
  CheckIcon,
} from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { VueDraggable } from 'vue-draggable-plus'
import { useRouter } from 'vue-router'

import {
  editCaptionStyle,
  getBookTemplateListHasUse,
  queryCaptionStyle,
  updateBookTemplate,
} from '@/api/book/book.js'
import {
  delChapter,
  exportBookChapter,
  listChapter,
  listForRecycle,
  listForSort,
  recycleChapter,
  updateChapterSort,
} from '@/api/book/chapter.js'
import DeleteModal from '@/components/DeleteModal/index.vue'
import ChapterEditWebSocket from '@/utils/chapterEditWebSocket.js'
import { bookTemplateSortList, bookTemplateThemeList, getOptionDesc } from '@/utils/optionUtil'
import { listBookTemplateTypeNoPage } from "@/api/book/bookTemplateType.js";
import CharpterMange from './charpterMange.vue'
import TreeNode from './TreeNode.vue'
const { proxy } = getCurrentInstance()
const props = defineProps({
  bookId: {
    type: String,
    default: '',
  },
  curUserPermissions: {
    type: Object,
    default: {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    },
  },
  publishStatus: {
    type: Number,
    default: 1,
  },
  stepId: {
    type: String,
    default: 1,
  },
  isFree: {
    type: Boolean,
    default: false,
  },
  masterFlag: {
    type: Number,
    default: 1,
  },
  bookOrganize: {
    type: Number,
    default: 1,
  },
  auditState: {
    type: Number,
    default: null,
  }
})
const emit = defineEmits(['refresh'])
const loading = ref(false)
const showPreviewUrl = ref(false);
const dragSortDialog = ref(false)
const selectAll = ref(false)
const deleteModalVisible = ref(false)
const dataList = ref([])
const confirmLoading = ref(false)
const recycleList = ref([])
const isRecycle = ref(false)
const captionStyleDialog = ref(false)
const captionStyleForm = ref({})
const dragSortDataList = ref([])
const warningVisible = ref(false)
const warningVisibleStyle = ref(false)
const websocket = ref(null)
const router = useRouter()
const editChapterId = ref(null)
let heartbeatInterval = null
const isRe = ref(true)
const templateList = ref([])
const bookTemplateDialog = ref(false)
const activeTemplate = ref(null);
const total = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
});
const bookTemplateTypeList = ref([])
//#region 监听器相关

watch(
  () => props.bookId,
  () => {
    getChapters()
  },
)

//#endregion

//#region 生命周期相关

onMounted(() => {
  getChapters()
  getBookTemplateTypeList()
  isRe.value = true
  websocket.value = new ChapterEditWebSocket()
  websocket.value.connect()
  websocket.value.on('message', handleMessage)
  websocket.value.on('open', () => {
    heartbeatInterval = setInterval(sendHeartbeat, 5000)
  })
  websocket.value.on('close', () => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
    }
    console.log('websocket close, try to reconnect')
    // 延迟 5 秒后重试连接
    setTimeout(reConnect, 5000)
  })
  // websocket.value.on('error', () => {
  //   if (heartbeatInterval) {
  //     clearInterval(heartbeatInterval)
  //   }
  //   console.log('websocket error, try to reconnect')
  //   // 延迟 5 秒后重试连接
  //   setTimeout(reConnect, 5000)
  // })
})

onBeforeUnmount(() => {
  isRe.value = false
  websocket.value.disconnect()
})

const onClickCancel = () => {
  warningVisible.value = false
  warningVisibleStyle.value = false
  confirmLoading.value = false
}

const onClickConfirm = () => {
  websocket.value.sendMessage({
    optCode: 2,
    chapterId: editChapterId.value,
  })
  confirmLoading.value = true
}

// 教材模板分类列表
const getBookTemplateTypeList = () => {
  listBookTemplateTypeNoPage().then((response) => {
    bookTemplateTypeList.value = response.data
  })
}

function handleMessage(message) {
  // 0成功响应心跳 1当前章节正在编辑中 2当前章节被强制关闭 3同意申请编辑章节 4挤退当前编辑人成功 5题注正在更新中 6数据保存完成
  if (message.resultCode == 0) {
    // 心跳响应
  } else if (message.resultCode == 1) {
    // 当前章节正在编辑中
    warningVisible.value = true
    confirmLoading.value = false
  } else if (message.resultCode == 2) {
    // 当前章节被强制关闭
    // 发送消息
    websocket.value.sendMessage({
      optCode: 5,
      chapterId: editChapterId.value,
    })
    // warningVisible.value = true
    confirmLoading.value = false
  } else if (message.resultCode == 3) {
    // 同意申请编辑章节
    warningVisible.value = false
    router.push({
      path: '/editor',
      query: {
        chapterId: editChapterId.value,
        bookId: props.bookId,
        formType: 1,
      },
    })
  } else if (message.resultCode == 4) {
    // 挤退当前编辑人成功
    confirmLoading.value = true
  } else if (message.resultCode == 5) {
    // 当前章节被强制关闭
    warningVisibleStyle.value = true
  } else if (message.resultCode == 6) {
    warningVisible.value = false
    confirmLoading.value = false
    router.push({
      path: '/editor',
      query: {
        chapterId: editChapterId.value,
        bookId: props.bookId,
        formType: 1,
      },
    })
  }
}

// 重连
const reConnect = () => {
  if (isRe.value) {
    websocket.value.connect()
  }
}

// 心跳
const sendHeartbeat = () => {
  const isEditing = false
  websocket.value.sendMessage({
    optCode: 0,
    chapterId: editChapterId.value,
    isEditing,
  })
}

const editChapter = (item) => {
  editChapterId.value = item.chapterId
  websocket.value.sendMessage({
    optCode: 1,
    chapterId: editChapterId.value,
  })
}

//#endregion

//#region 获取数据相关

// 查询教材模板列表
function getList() {
  // loading.value = true;
  queryParams.value.bookId = props.bookId;
  getBookTemplateListHasUse(queryParams.value).then((response) => {
    templateList.value = response.rows;
    templateList.value.forEach((item) => {
      if (item.isUse) {
        activeTemplate.value = item.templateId;
      }
    })
    total.value = response.total;
    bookTemplateDialog.value = true;
    // loading.value = false;
  });
}

// 选中
function handleUpdateTemplate(row) {
  activeTemplate.value = row.templateId;
  updateTemplate()
}

//预览
function handlePrevieTemplate(row) {
  showPreviewUrl.value = row.imgUrl
}


// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

// 重置按钮操作
function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
  }
  handleQuery();
}

// 获取章节列表
function getChapters() {
  loading.value = true
  listChapter({ bookId: props.bookId }).then((response) => {
    dataList.value = response.data
    loading.value = false
  })
}

function handleConfirmBookTemplate() {
  MessagePlugin.success('模板应用成功')
  bookTemplateDialog.value = false
}

//#endregion

//#region 操作相关

// 点击回收站
function handleRecycle() {
  listForRecycle({ bookId: props.bookId }).then((response) => {
    recycleList.value = response.data
    isRecycle.value = true
  })
}

// 点击排序
function handleDragSort() {
  listForSort({ bookId: props.bookId }).then((response) => {
    dragSortDataList.value = response.data
    dragSortDialog.value = true
  })
}

// 确认排序
function handleConfirmDragSort() {
  let sort = 0
  const chapterSortDataList = dragSortDataList.value.map((item) => {
    sort++
    return {
      chapterId: item.chapterId,
      sort,
    }
  })
  updateChapterSort(chapterSortDataList).then((response) => {
    dragSortDialog.value = false
    MessagePlugin.success('排序成功')
    refresh()
  })
}

// 查看回收站详情
function handleRecycleDetail(item) {
  // 跳预览
  router.push({
    path: '/chapterDetail',
    query: {
      chapterId: item.chapterId,
      formType: 1,
      bookId: props.bookId,
      optType: 1,
    },
  })

  // const { chapterId } = item
  // const { bookId } = item
  // window.open(
  //   `${`${import.meta.env.VITE_READER_PREVIEW_URL}?k=${bookId}&cid=${chapterId}`}`,
  // )
}

// 恢复章节
function handleRecycleChapter(item) {
  recycleChapter({
    chapterId: item.chapterId,
  }).then((res) => {
    handleRecycle()
    MessagePlugin.success('恢复成功')
    refresh()
  })
}

// 刷新数据
function refresh() {
  getChapters()
  emit('refresh')
}

// 新建章节
function addChapter() {
  proxy.$refs['chapterMange'].show(props.bookId, null)
}

// 确认删除
function handleConfirmDelete(fn) {
  const selectDataList = proxy.$refs['treeNode'].handleSelect()
  const dataIdList = selectDataList
    .filter((item) => item.chapterStatus == 0 || item.chapterStatus == 3)
    .map((item) => item.chapterId)
  if (dataIdList.length == 0) {
    fn(true)
    MessagePlugin.error('请选择要删除的章节')
    return
  }
  delChapter(dataIdList).then((response) => {
    fn(true)
    MessagePlugin.success('删除成功')
    refresh()
  })
}

// 删除章节
function handleDelete() {
  const selectDataList = proxy.$refs['treeNode'].handleSelect()
  if (selectDataList && selectDataList.length == 0) {
    MessagePlugin.error('请选择要删除的章节')
    return
  }
  const dataIdList = selectDataList
    .filter((item) => item.chapterStatus == 0 || item.chapterStatus == 3)
    .map((item) => item.chapterId)
  if (dataIdList.length == 0) {
    MessagePlugin.error('已提交或者已通过状态下不允许删除的章节')
    return
  }
  deleteModalVisible.value = true
}

// 全选
function handleSelectAll() {
  if (selectAll.value) {
    proxy.$refs['treeNode'].handleCheckAll()
  } else {
    proxy.$refs['treeNode'].handleCancelCheckAll()
  }
}

// 编辑题注
function openCaptionStyleDialog() {
  queryCaptionStyle({ bookId: props.bookId }).then((response) => {
    captionStyleForm.value = response.data
    captionStyleDialog.value = true
  })
}

// 确认题注
function handleConfirmCaptionStyle() {
  const dialog = DialogPlugin.confirm({
    header: '题注样式',
    body: '确认更新题注样式？确定后题注样式将会进入任务中心。\n注：题注样式的调整需要一定的时间，会在凌晨2点进行，更新期间编辑器不可进入。',
    confirmBtn: '确定',
    cancelBtn: '取消',
    onConfirm: () => {
      editCaptionStyle(captionStyleForm.value).then((response) => {
        captionStyleDialog.value = false
        MessagePlugin.success('编辑成功')
        dialog.hide()
      })
    },
  })
}

// 编辑模板
function openBookTempalteDialog() {
  getList()
}

// 更新模板
function updateTemplate() {
  if (!activeTemplate.value) {
    return MessagePlugin.warning("请选择模板");
  }
  updateBookTemplate({
    bookId: props.bookId,
    templateId: activeTemplate.value,
  }).then((res) => {
    getList()
    // bookTemplateDialog.value = false
  })
}

// 导出
function handleExport() {
  const selectDataList = proxy.$refs['treeNode'].handleSelect()
  let dataIdList = []
  if (props.curUserPermissions.isEditor) {
    dataIdList = selectDataList.map((item) => item.chapterId)
    if (dataIdList.length == 0) {
      MessagePlugin.error('请选择要导出的章节')
      return
    }
    // TODO 导出
    exportBookChapter({
      chapterIdList: dataIdList,
    }).then((res) => {
      MessagePlugin.success('已发起导出任务，请到任务中心查看')
    })
    return
  }
  if (
    (props.curUserPermissions.permissions.includes(2) ||
      props.curUserPermissions.permissions.includes(1)) &&
    props.publishStatus == 1
  ) {
    dataIdList = selectDataList
      .filter((item) => item.isViewer == true || item.isEditor == true)
      .map((item) => item.chapterId)
    if (dataIdList.length == 0) {
      MessagePlugin.error('请选择要导出的章节')
      return
    }
    //TODO 导出
    exportBookChapter({
      chapterIdList: dataIdList,
    }).then((res) => {
      MessagePlugin.success('已发起导出任务，请到任务中心查看')
    })
    return
  }
  MessagePlugin.error('无权限，不能导出')
}
//#endregion
</script>
<style lang="less" scoped>
.chapter-header {
  display: flex;
  justify-content: space-between;
  margin: 30px 0;
  align-items: center;

  .chapter-header-main {
    display: flex;
  }

  .chapter-header-right {
    margin-left: 44px;
    display: flex;
    align-items: center;

    .infoIcon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 4px;
      background: #f1c30f;
      background-size: contain;
      color: #fff;
      cursor: pointer;
      margin-left: 10px;

      .infoIcon-text {
        margin-left: 5px;
      }
    }
  }

  .chapter-header-btn-group {
    button {
      margin-left: 10px;
    }
  }
}

.chapter-list {
  padding-bottom: 20px;
  background-color: #fff;

  .el-tree-node__content {
    background-color: #eaeaea;
    padding: 20px 30px;
    height: auto;
    display: block;

    .chapter-list-item {
      padding-left: 60px;
      margin-top: -25px;

      .charter-list-header {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .charter-list-content {
      display: flex;
      justify-content: space-between;
    }
  }
}

.chapter-list-empty {
  height: 270px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.recycleList {
  .recycleList-item {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 20px;

    .recycleList-item-header {
      background-color: #daeaf7;
      padding: 10px 20px;
      border-bottom: 1px solid #ddd;
      display: flex;
      border-radius: 5px 5px 0 0;
      justify-content: space-between;

      .recycleList-item-header-right {
        color: #0966b4;
        cursor: pointer;
      }
    }
  }

  .recycleList-item-content {
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;

    .recycleList-item-content-right {
      color: #0966b4;
      cursor: pointer;
    }
  }
}

.recycleList-empty {
  height: 180px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.list-group {
  width: 100%;

  .list-group-item {
    margin: 10px 0;
    background-color: #0052d9;
    cursor: pointer;
    padding: 15px;

    border-radius: 5px;

    &:hover {
      background-color: #0966b4;
    }
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
}

.textBookTemplate-content {
  display: grid;
  cursor: pointer;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;

  .textBookTemplate-content-item {
    position: relative;
    background-color: #f6f9fa;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: 10px 15px;
    border-radius: 10px;
    border: 2px solid #ddd;
    position: relative;

    .active {
      position: absolute;
      top: 0;
      right: 0;
      width: 0;
      height: 0;

      border-style: solid;
      border-width: 0 0 50px 50px;
      border-radius: 0 0 10px 0;
      border-color: transparent transparent #67c239 transparent;
      transform: rotate(270deg);
      z-index: 9;

      &::after {
        content: "";
        position: absolute;
        width: 20px;
        height: 20px;

        transform: rotate(90deg);
        top: 25px;
        right: 5px;
        background-image: url("@/assets/images/rightIcon.svg");
        background-size: cover;
      }
    }

    .textBookTemplate-content-item-title {
      text-align: center;
      font-weight: bold;
      font-size: 16px;
    }

    .textBookTemplate-content-item-img {
      position: relative;

      &:hover {
        .textBookTemplate-content-item-img-opt-mask {
          opacity: 1;
          // transition: width 0.5s ease-in-out, background-color 1s linear;
        }
      }

      .textBookTemplate-content-item-img-opt-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease-out;
        will-change: opacity;

        .textBookTemplate-content-item-img-opt-mask-bnt {
          color: #fff;
          cursor: pointer;
          margin: 0 10px;
          width: 56px;
          height: 56px;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          background: linear-gradient(135deg, #0966b4 0%, #00b4db 100%);
          font-size: 24px;
          box-shadow: 0 4px 12px rgba(9, 102, 180, 0.3);
          transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);

          &:hover {
            transform: scale(1.15) rotate(5deg);
            box-shadow: 0 6px 16px rgba(9, 102, 180, 0.4);
            background: linear-gradient(135deg, #00b4db 0%, #0966b4 100%);
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }

      .textBookTemplate-content-item-img-check {
        position: absolute;
        width: 20px;
        height: 20px;
        top: 10px;
        right: 10px;
        border-radius: 50%;
        background-color: #fff;
        border: 1px solid #ccc;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }

    .isUse {
      background-color: #68c2ff54;
    }

    .isUse:hover {
      cursor: pointer;
      background-color: #0099ff8e;
    }
  }
}
</style>
