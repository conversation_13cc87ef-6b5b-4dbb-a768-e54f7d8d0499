<template>
  <div>
    <modal v-model:visible="open" :header="title" width="500" align-center destroy-on-close :before-close="handleClose"
      close-on-click-modal :confirm-btn="null" :cancel-btn="null">
      <div>
        <t-form ref="chapterRef" :data="form" label-width="120px" :rules="rules" @submit="submit">
          <t-form-item label="章节标题" name="chapterName">
            <t-input v-model="form.chapterName" placeholder="请输入章节标题" maxlength="500" />  
          </t-form-item>
          <t-form-item v-if="bookOrganize == 1" label="是否允许试读" name="free">
            <t-radio-group v-model="form.free" :disabled="isFree || masterFlag == 3">
              <t-radio :value="2">是</t-radio>
              <t-radio :value="1">否</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item label="编写者" name="editorIds">
            <t-select v-model="form.editorIds" placeholder="请选择编写者" clear multiple>
              <t-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId" />
            </t-select>
          </t-form-item>

          <t-form-item label="查看者" name="viewerIds">
            <t-select v-model="form.viewerIds" placeholder="请选择查看者" clear multiple>
              <t-option v-for="item in userList" :key="item.userId" :label="item.nickName" :value="item.userId" />
            </t-select>
          </t-form-item>
          <div class="dialog-footer">
            <t-button theme="default" style="margin-right: 10px" @click="handleClose">取消</t-button>
            <t-button theme="primary" type="submit"> 确定 </t-button>
          </div>
        </t-form>
      </div>
    </modal>
  </div>
</template>
<script setup>
import { groupUserList } from '@/api/book/bookGroup'
import { addChapter, getChapter, updateChapterInfo } from '@/api/book/chapter'

const { proxy } = getCurrentInstance()

const props = defineProps({
  isFree: {
    type: Boolean,
    default: false,
  },
  masterFlag: {
    type: Number,
    default: 1,
  },
  bookOrganize: {
    type: Number,
    default: 1,
  },
})
const emit = defineEmits('refresh')

const form = ref({})
const rules = reactive({
  chapterName: [
    {
      required: true,
      message: '请输入章节标题',
      type: 'error',
      trigger: 'blur',
    },
  ],
  free: [
    {
      required: true,
      type: 'error',
      message: '请选择是否允许试读',
      trigger: 'change',
    },
  ],
  editorIds: [
    {
      required: true,
      type: 'error',
      message: '请选择编写者',
      trigger: 'change',
    },
  ],
})
const open = ref(false)
const title = ref('')
const userList = ref([])
//#region 获取数据相关

// 获取用户列表
function getUserList(bookId) {
  groupUserList(bookId).then((response) => {
    userList.value = response.data
  })
}

//#endregion

//#region 操作相关

// 关闭事件
function handleClose() {
  open.value = false
}
const chapterRef = ref(null)
// 确认事件
function submit({ validateResult, firstError }) {
  if (validateResult === true) {
    let f = true
    form.value.editorIds.forEach((e) => {
      const tArr = form.value.viewerIds?.filter((o) => o == e) || []
      if (tArr.length > 0) {
        f = false
      }
    })
    if (!f) {
      MessagePlugin.error('编写者和查看者不能重复')
      return
    }
    if (form.value.chapterId != null) {
      updateChapterInfo(form.value).then((response) => {
        MessagePlugin.success('修改成功')
        open.value = false
        emit('refresh')
      })
    } else {
      addChapter(form.value).then((response) => {
        MessagePlugin.success('新增成功')
        open.value = false
        emit('refresh')
      })
    }
  } else {
    MessagePlugin.error(firstError)
  }
}

// 对外暴露打开事件
function show(bookId, chapterId) {
  getUserList(bookId)
  if (chapterId) {
    getChapter(chapterId).then((response) => {
      form.value = response.data
      form.value.editorIds = form.value.editorIds || []
      form.value.viewerIds = form.value.viewerIds || []
      open.value = true
      title.value = '修改章节'
    })
  } else {
    open.value = true
    form.value = {
      bookId,
      chapterName: '',
      free: 1,
      editorIds: [],
      viewerIds: [],
    }

    if (props.masterFlag == 3) {
      // 副教材不允许试读
      form.value.free = 1
    } else if (props.isFree) {
      // 价格为0的教材默认允许试读
      form.value.free = 2
    }
    title.value = '新增章节'
  }
}

//#endregion

//#region 暴露函数相关

defineExpose({
  show,
})

//#endregion
</script>
<style lang="less" scoped></style>
