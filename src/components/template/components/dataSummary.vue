<template>
  <div class="dashboard">
    <div class="dataSummary-title">
      <div class="dataSummary-title-left">
        <span class="dataSummary-title-left-title">数据</span>
        <t-select
          v-model="booksChapterValue"
          style="width: 240px"
          placeholder="全部"
          clearable
          @change="changeChapter"
        >
          <t-option
            v-for="item in booksChapterList"
            :key="item.chapterId"
            :label="item.chapterName"
            :value="item.chapterId"
          >
            {{ item.chapterName }}
          </t-option>
        </t-select>
      </div>

      <div class="dataSummary-title-right">
        <t-button
          v-if="
            curUserPermissions.isEditor ||
            curUserPermissions.permissions.includes(2) ||
            curUserPermissions.permissions.includes(1)
          "
          theme="primary"
          size="large"
          @click="handleExport"
        >
          <template #icon>
            <CloudUploadIcon />
          </template>
          导出</t-button
        >
      </div>
    </div>
    <div class="dataSummary-bar">
      <t-card class="dataSummary-bar-left">
        <div class="dataSummary-bar-left-title">
          <div class="dataSummary-bar-left-title-text">字数统计</div>
        </div>
        <div class="dataSummary-bar-left-content">
          <div class="dataSummary-bar-left-content-left">文字</div>
          <div class="dataSummary-bar-left-content-content">
            {{ dataInfo.wordQuantity || 0 }}
          </div>
        </div>
      </t-card>

      <div class="dataSummary-bar-right">
        <div class="dataSummary-bar-right-barEchars">
          <echartsRef
            :chart-option="pieOption"
            :chart-style="{ width: '143px', height: '143px' }"
          />
        </div>
        <div class="dataSummary-bar-right-barList">
          <!-- <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #1e88e5"
              >
              </span>
              文字
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.wordQuantity || 0 }}
            </div>
          </div> -->
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #26c6da"
              >
              </span>
              图片
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.imageQuanity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #26a69a"
              >
              </span
              >气泡
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.bubbleQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #ffa726"
              >
              </span>
              链接
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{
                dataInfo.outsideLinkQuantity + dataInfo.insideLinkQuantity || 0
              }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #29b6f6"
              >
              </span
              >公式
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.formulaQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #66bb6a"
              >
              </span>
              3D模型
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.threeDimenQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #8e24aa"
              >
              </span>
              AR/VR
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.avrQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #ff7043"
              >
              </span
              >虚拟仿真
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.simulationQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #9ccc65"
              >
              </span>
              试题
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.questionQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #5c6bc0"
              >
              </span>
              游戏
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.gameQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #ff7043"
              >
              </span
              >脚注
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.footnoteQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #43a047"
              >
              </span
              >教学资源
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.resouceQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #fb8c00"
              >
              </span>
              音频
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.audioQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #29b6f6"
              >
              </span>
              视频
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.videoQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #42a5f5"
              >
              </span
              >拓展阅读
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.extQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #1e88e5"
              >
              </span
              >代码块
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{ dataInfo.codeQuantity || 0 }}
            </div>
          </div>
          <div class="dataSummary-bar-right-barList-item">
            <div class="dataSummary-bar-right-barList-item-title">
              <span
                class="dataSummary-bar-right-barList-item-title-left"
                style="background-color: #ff7043"
              >
              </span
              >互动
            </div>
            <div class="dataSummary-bar-right-barList-item-content">
              {{
                dataInfo.interactionVoteQuantity +
                  dataInfo.interactionWordCloudQuantity +
                  dataInfo.interactionDiscussQuantity +
                  dataInfo.interactionImageWaterfallQuantity || 0
              }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="dataStatistics">
      <div class="dataStatistics-left">
        <div class="dataStatistics-left-header">
          <div class="dataStatistics-left-title">
            <div class="dataStatistics-left-title-quantum">
              <div class="dataStatistics-left-title-quantum-small">
                <div class="dataStatistics-left-title-quantum-small-icon"></div>
              </div>
            </div>

            <div class="dataStatistics-left-title-quantum-title">互动统计</div>
          </div>
        </div>
        <div class="dataStatistics-left-content">
          <div class="dataStatistics-left-content-item">
            <div class="dataStatistics-left-content-item-num">
              {{ dataInfo.interactionVoteQuantity || 0 }}
            </div>
            <div class="dataStatistics-left-content-item-title">投票</div>
          </div>
          <div class="dataStatistics-left-content-item">
            <div class="dataStatistics-left-content-item-num">
              {{ dataInfo.interactionWordCloudQuantity || 0 }}
            </div>
            <div class="dataStatistics-left-content-item-title">词云</div>
          </div>
          <div class="dataStatistics-left-content-item">
            <div class="dataStatistics-left-content-item-num">
              {{ dataInfo.interactionDiscussQuantity || 0 }}
            </div>
            <div class="dataStatistics-left-content-item-title">讨论</div>
          </div>
          <div class="dataStatistics-left-content-item">
            <div class="dataStatistics-left-content-item-num">
              {{ dataInfo.interactionImageWaterfallQuantity || 0 }}
            </div>
            <div class="dataStatistics-left-content-item-title">作品墙</div>
          </div>
        </div>
      </div>
      <div class="dataStatistics-right">
        <div class="dataStatistics-right-line">
          <div class="dataStatistics-left-header">
            <div class="dataStatistics-left-title">
              <div class="dataStatistics-left-title-quantum">
                <div class="dataStatistics-left-title-quantum-small">
                  <div
                    class="dataStatistics-left-title-quantum-small-icon"
                  ></div>
                </div>
              </div>

              <div class="dataStatistics-left-title-quantum-title">
                链接统计
              </div>
            </div>
          </div>
          <div class="dataStatistics-left-content">
            <div class="dataStatistics-left-content-item">
              <div class="dataStatistics-left-content-item-num">
                {{ dataInfo.outsideLinkQuantity || 0 }}
              </div>
              <div class="dataStatistics-left-content-item-title">外部链接</div>
            </div>
            <div class="dataStatistics-left-content-item">
              <div class="dataStatistics-left-content-item-num">
                {{ dataInfo.insideLinkQuantity || 0 }}
              </div>
              <div class="dataStatistics-left-content-item-title">内部链接</div>
            </div>
          </div>
        </div>
        <div class="dataStatistics-right-audio">
          <div class="dataStatistics-left-header">
            <div class="dataStatistics-left-title">
              <div class="dataStatistics-left-title-quantum">
                <div class="dataStatistics-left-title-quantum-small">
                  <div
                    class="dataStatistics-left-title-quantum-small-icon"
                  ></div>
                </div>
              </div>

              <div class="dataStatistics-left-title-quantum-title">
                音频总时长(min)
              </div>
            </div>
          </div>
          <div class="dataStatistics-left-content">
            {{ dataInfo.audioTotalDurationSecond || 0 }}
          </div>
        </div>
        <div class="dataStatistics-right-video">
          <div class="dataStatistics-left-header">
            <div class="dataStatistics-left-title">
              <div class="dataStatistics-left-title-quantum">
                <div class="dataStatistics-left-title-quantum-small">
                  <div
                    class="dataStatistics-left-title-quantum-small-icon"
                  ></div>
                </div>
              </div>

              <div class="dataStatistics-left-title-quantum-title">
                视频总时长(min)
              </div>
            </div>
          </div>
          <div class="dataStatistics-left-content">
            {{ dataInfo.videoTotalDurationSecond || 0 }}
          </div>
        </div>
      </div>
    </div>
    <div class="dataSummary-pie">
      <div class="dataSummary-pie-header">
        <div class="dataSummary-pie-header-quantum">
          <div class="dataSummary-pie-header-quantum-small">
            <div class="dataSummary-pie-header-quantum-small-icon"></div>
          </div>
        </div>
        <div class="dataSummary-pie-header-title">题型统计</div>
      </div>
      <div class="dataSummary-pie-content">
        <echartsRef
          :chart-option="barOption"
          :chart-style="{ width: '100%', height: '350px' }"
        />
        <!-- <e-charts :option="pieOption" :init-options="{ minWidth: '1400px', height: '350px', margin: '0 auto' }"
            autoresize></e-charts> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { CloudUploadIcon } from 'tdesign-icons-vue-next'
import { getCurrentInstance, ref } from 'vue'

import { listForSelect } from '@/api/book/chapter'
import { queryDataOverview } from '@/api/book/chapterData'
import echartsRef from '@/components/echarts/index.vue'
import { download } from '@/request/index'
//   import { formatNumber } from "@/utils/optionUtil"
const { proxy } = getCurrentInstance()

const props = defineProps({
  bookId: {
    type: String,
    default: null,
  },
  bookName: {
    type: String,
    default: null,
  },
  curUserPermissions: {
    type: Object,
    default: {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    },
  },
})
const queryParams = ref({
  bookId: props.bookId,
  chapterId: null,
})
const booksChapterValue = ref('')
const booksChapterList = ref([])

const barsList = ref([
  {
    id: 1,
    name: '图片',
    color: '#8880BE',
    value: 23,
  },
  {
    id: 2,
    name: '气泡',
    color: '#52AEB6',
    value: 45,
  },
  {
    id: 3,
    name: '链接',
    color: '#E8926D',
    value: 45,
  },
  {
    id: 4,
    name: '公式',
    color: '#D73251',
    value: 45,
  },
  {
    id: 5,
    name: '3D模型',
    color: '#E8CE6F ',
    value: 45,
  },
  {
    id: 6,
    name: 'AR/VR',
    color: '#89C685',
    value: 45,
  },
  {
    id: 7,
    name: '虚拟仿真',
    color: '#B47ECC',
    value: 45,
  },
  {
    id: 8,
    name: '试题',
    color: '#8E8E8E',
    value: 45,
  },
  {
    id: 9,
    name: '图片',
    color: '#8880BE',
    value: 23,
  },
  {
    id: 10,
    name: '气泡',
    color: '#52AEB6',
    value: 45,
  },
  {
    id: 11,
    name: '链接',
    color: '#E8926D',
    value: 45,
  },
  {
    id: 12,
    name: '公式',
    color: '#D73251',
    value: 45,
  },
  {
    id: 13,
    name: '3D模型',
    color: '#E8CE6F ',
    value: 45,
  },
  {
    id: 14,
    name: 'AR/VR',
    color: '#89C685',
    value: 45,
  },
  {
    id: 15,
    name: '虚拟仿真',
    color: '#B47ECC',
    value: 45,
  },
  {
    id: 16,
    name: '试题',
    color: '#8E8E8E',
    value: 45,
  },
])

const questionList = ref([])
const barOption = ref({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  xAxis: {
    type: 'category',
    data: [
      '单选',
      '多选',
      '填空',
      '排序',
      '连线',
      '简答',
      '判断',
      '编程',
      // '编程-简答',
    ],
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#333',
    },
    axisLine: {
      lineStyle: {
        color: '#E5E6E7',
        width: 1,
      },
    },
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      data: questionList.value,
      type: 'bar',
      barWidth: 30,
      itemStyle: {
        borderRadius: 12,
      },
    },
  ],
  responsive: true,
})

const pieDataList = ref([])
const pieOption = ref({
  tooltip: {
    trigger: 'item',
  },

  series: [
    {
      type: 'pie',
      radius: ['100%', '60%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
        formatter: '{c}',
        fontSize: 22,
        fontWeight: 'bold',
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 22,
          fontWeight: 'bold',
          formatter: '{c}',
        },
      },

      data: pieDataList.value,
    },
  ],
})

// watch(
//     () => props.bookId,
//     (value) => {
//         queryParams.value.bookId = value;
//         getChapterList();
//         getData();
//     }
// );

onMounted(() => {
  getChapterList()
  getData()
})

// 获取章节
function getChapterList() {
  listForSelect(queryParams.value).then((response) => {
    booksChapterList.value = response.data
  })
}

// 切换章节
function changeChapter(item) {
  queryParams.value.chapterId = item
  getData()
}

const dataInfo = ref({})

// 获取数据
function getData() {
  queryDataOverview(queryParams.value).then((res) => {
    dataInfo.value = res.data
    questionList.value = res.data.questionList
    barOption.value.series[0].data = questionList.value
    // nextTick(() => {
    //proxy.$refs["barRef"].setOption(barOption.value);
    // });

    const data = dataInfo.value
    pieDataList.value = []
    // if (data.wordQuantity != null) {
    //   pieDataList.value.push({
    //     name: '文字',
    //     value: data.wordQuantity,
    //     color: '#1E88E5',
    //   })
    // }
    if (data.imageQuantity != null) {
      pieDataList.value.push({
        name: '图片',
        value: data.imageQuantity,
        itemStyle: {
          color: '#26C6DA',
        },
      })
    }
    if (data.bubbleQuantity != null) {
      pieDataList.value.push({
        name: '气泡',
        value: data.bubbleQuantity,
        itemStyle: {
          color: '#26A69A',
        },
      })
    }
    if (data.linkQuantity != null) {
      pieDataList.value.push({
        name: '链接',
        value: data.linkQuantity,
        itemStyle: {
          color: '#FFA726',
        },
      })
    }
    if (data.formulaQuantity != null) {
      pieDataList.value.push({
        name: '公式',
        value: data.formulaQuantity,
        itemStyle: {
          color: '#29B6F6',
        },
      })
    }
    if (data.modelQuantity != null) {
      pieDataList.value.push({
        name: '3D模型',
        value: data.modelQuantity,

        itemStyle: {
          color: '#66BB6A',
        },
      })
    }
    if (data.arQuantity != null) {
      pieDataList.value.push({
        name: 'AR/VR',
        value: data.arQuantity,
        itemStyle: {
          color: '#8E24AA',
        },
      })
    }
    if (data.simulationQuantity != null) {
      pieDataList.value.push({
        name: '虚拟仿真',
        value: data.simulationQuantity,
        itemStyle: {
          color: '#FF7043',
        },
      })
    }
    if (data.questionQuantity != null) {
      pieDataList.value.push({
        name: '试题',
        value: data.questionQuantity,
        itemStyle: {
          color: '#9CCC65',
        },
      })
    }
    if (data.gameQuantity != null) {
      pieDataList.value.push({
        name: '游戏',
        value: data.gameQuantity,
        itemStyle: {
          color: '#5C6BC0',
        },
      })
    }
    if (data.footnoteQuantity != null) {
      pieDataList.value.push({
        name: '脚注',
        value: data.footnoteQuantity,
        itemStyle: {
          color: '#FF7043',
        },
      })
    }
    if (data.resourceQuantity != null) {
      pieDataList.value.push({
        name: '教学资源',
        value: data.resourceQuantity,
        itemStyle: {
          color: '#43A047',
        },
      })
    }
    if (data.audioQuantity != null) {
      pieDataList.value.push({
        name: '音频',
        value: data.audioQuantity,
        itemStyle: {
          color: '#FB8C00',
        },
      })
    }
    if (data.videoQuantity != null) {
      pieDataList.value.push({
        name: '视频',
        value: data.videoQuantity,
        itemStyle: {
          color: '#29B6F6',
        },
      })
    }
    if (data.readingQuantity != null) {
      pieDataList.value.push({
        name: '拓展阅读',
        value: data.readingQuantity,
        itemStyle: {
          color: '#42A5F5',
        },
      })
    }
    if (data.codeQuantity != null) {
      pieDataList.value.push({
        name: '代码块',
        value: data.codeQuantity,

        itemStyle: {
          color: '#1E88E5',
        },
      })
    }
    if (data.interactiveQuantity != null) {
      pieDataList.value.push({
        name: '互动',
        value: data.interactiveQuantity,
        itemStyle: {
          color: '#FF7043',
        },
      })
    }
    pieOption.value.series[0].data = pieDataList.value

    // proxy.$refs["pieRef"].setOption(pieOption.value);
  })
}
// 导出
function handleExport() {
  download(
    '/book/chapterData/exportDataOverview',
    {
      ...queryParams.value,
    },
    `${props.bookName}-数据总览.xlsx`,
  )
  // proxy.download(
  //     "book/chapterData/exportDataOverview",
  //     {
  //         ...queryParams.value,
  //     },
  //     `${props.bookName}-数据总览.xlsx`
  // );
}
</script>

<style scoped lang="less">
.dashboard {
  padding: 20px;

  .dataSummary-title {
    display: flex;
    justify-content: space-between;
    align-content: center;

    .dataSummary-title-left {
      display: flex;
      align-items: center;

      .dataSummary-title-left-title {
        margin-right: 10px;
      }
    }
  }

  .dataSummary-bar {
    margin: 20px 0;
    display: grid;
    grid-template-columns: 272px auto;
    grid-gap: 22px;

    .dataSummary-bar-left {
      padding: 8px 10px;

      .dataSummary-bar-left-title {
        width: 100%;
        height: 85px;
        border-radius: 8px;
        display: flex;
        align-items: center;

        font-size: 24px;
        font-weight: bold;
        color: #0966b4;
        background: url('@/assets/images/fontNumber.png') center no-repeat;
        margin-bottom: 29px;

        .dataSummary-bar-left-title-text {
          padding: 0 20px;
        }
      }

      .dataSummary-bar-left-content {
        .dataSummary-bar-left-content-left {
          display: flex;
          align-items: center;
          margin-bottom: 16px;

          &::before {
            content: '';
            display: block;
            width: 4px;
            height: 12px;
            background: #428dcb;
            border-radius: 5px 5px 5px 5px;
            margin-right: 8px;
          }
        }

        .dataSummary-bar-left-content-content {
          font-size: 30px;
          color: #333;
          font-weight: bold;
        }
      }
    }

    .dataSummary-bar-right {
      padding: 40px 20px;
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid #e8e8e8;
      box-shadow: 0 0px 12px rgba(0, 0, 0, 0.12);
      display: grid;
      grid-template-columns: 143px auto;
      grid-gap: 50px;

      .dataSummary-bar-right-barEchars {
        width: 143px;
        height: 143px;
        margin-top: 16px;
        margin-right: 50px;

        border-radius: 50%;
      }

      .dataSummary-bar-right-barList {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        grid-gap: 50px;

        .dataSummary-bar-right-barList-item {
          display: flex;
          flex-direction: column;

          .dataSummary-bar-right-barList-item-title {
            display: flex;
            align-items: center;
            font-size: 14px;

            .dataSummary-bar-right-barList-item-title-left {
              width: 4px;
              height: 12px;
              border-radius: 5px 5px 5px 5px;
              margin-right: 8px;
              display: inline-block;
            }
          }

          .dataSummary-bar-right-barList-item-content {
            font-weight: bold;
            font-size: 30px;
            color: #333;
            margin-left: 12px;
            margin-top: 16px;
          }
        }
      }
    }
  }

  .dataStatistics {
    display: grid;
    grid-template-columns: 1fr 2fr;
    grid-gap: 20px;

    .dataStatistics-left {
      background: #eaf3ee;
      border-radius: 8px;
      padding: 16px 20px;

      .dataStatistics-left-header {
        .dataStatistics-left-title {
          display: flex;
          align-items: center;

          .dataStatistics-left-title-quantum {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            background: rgba(55, 176, 170, 0.2);
            display: flex;
            justify-content: center;
            align-items: center;

            .dataStatistics-left-title-quantum-small {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: #37b0aa;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            .dataStatistics-left-title-quantum-small-icon {
              background: url('@/assets/images/start.png') no-repeat;
              width: 16px;
              height: 16px;
            }
          }

          .dataStatistics-left-title-quantum-title {
            color: #37b0aa;
            font-size: 16px;
            font-weight: bold;
            padding-left: 12px;
          }
        }
      }

      .dataStatistics-left-content {
        padding: 24px 76px;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 20px;

        .dataStatistics-left-content-item {
          text-align: center;

          .dataStatistics-left-content-item-num {
            font-size: 30px;
            font-weight: bold;
            margin-bottom: 14px;
          }

          .dataStatistics-left-content-item-title {
            font-size: 14px;
            color: #333;
          }
        }
      }
    }

    .dataStatistics-right {
      display: grid;
      grid-gap: 20px;
      grid-template-columns: repeat(3, 1fr);

      .dataStatistics-right-line {
        background: #e6f2ff;
        border-radius: 8px 8px 8px 8px;
        padding: 17px 26px;

        .dataStatistics-left-header {
          .dataStatistics-left-title {
            display: flex;
            align-items: center;

            .dataStatistics-left-title-quantum {
              width: 42px;
              height: 42px;
              border-radius: 50%;
              background: rgba(93, 145, 200, 0.2);
              display: flex;
              justify-content: center;
              align-items: center;

              .dataStatistics-left-title-quantum-small {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #5d91c8;
                display: flex;
                justify-content: center;
                align-items: center;
              }

              .dataStatistics-left-title-quantum-small-icon {
                background: url('@/assets/images/line.png') no-repeat;
                width: 16px;
                height: 16px;
              }
            }

            .dataStatistics-left-title-quantum-title {
              color: #5d91c8;
              font-size: 16px;
              font-weight: bold;
              padding-left: 12px;
            }
          }
        }

        .dataStatistics-left-content {
          padding: 24px 61px;
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          grid-gap: 20px;

          .dataStatistics-left-content-item {
            text-align: center;

            .dataStatistics-left-content-item-num {
              font-size: 30px;
              font-weight: bold;
              margin-bottom: 14px;
            }

            .dataStatistics-left-content-item-title {
              font-size: 14px;
              color: #333;
            }
          }
        }
      }

      .dataStatistics-right-audio {
        background: #f5f3ff;
        border-radius: 8px 8px 8px 8px;
        padding: 17px 26px;

        .dataStatistics-left-header {
          .dataStatistics-left-title {
            display: flex;
            align-items: center;

            .dataStatistics-left-title-quantum {
              width: 42px;
              height: 42px;
              border-radius: 50%;
              background: rgba(137, 133, 211, 0.2);
              display: flex;
              justify-content: center;
              align-items: center;

              .dataStatistics-left-title-quantum-small {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #8985d3;
                display: flex;
                justify-content: center;
                align-items: center;
              }

              .dataStatistics-left-title-quantum-small-icon {
                background: url('@/assets/images/audio.png') no-repeat;
                width: 16px;
                height: 16px;
              }
            }

            .dataStatistics-left-title-quantum-title {
              color: #8985d3;
              font-size: 16px;
              font-weight: bold;
              padding-left: 12px;
            }
          }
        }

        .dataStatistics-left-content {
          padding: 24px 76px;
          font-size: 36px;
          font-weight: bold;
          text-align: center;
        }
      }

      .dataStatistics-right-video {
        background: #fbf4e8;
        border-radius: 8px 8px 8px 8px;
        padding: 17px 26px;

        .dataStatistics-left-header {
          .dataStatistics-left-title {
            display: flex;
            align-items: center;

            .dataStatistics-left-title-quantum {
              width: 42px;
              height: 42px;
              border-radius: 50%;
              background: rgba(232, 180, 116, 0.24);
              display: flex;
              justify-content: center;
              align-items: center;

              .dataStatistics-left-title-quantum-small {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #e7b271;
                display: flex;
                justify-content: center;
                align-items: center;
              }

              .dataStatistics-left-title-quantum-small-icon {
                background: url('@/assets/images/video.png') no-repeat;
                width: 16px;
                height: 16px;
              }
            }

            .dataStatistics-left-title-quantum-title {
              color: #e7b271;
              font-size: 16px;
              font-weight: bold;
              padding-left: 12px;
            }
          }
        }

        .dataStatistics-left-content {
          padding: 24px 76px;
          font-size: 36px;
          font-weight: bold;
          text-align: center;
        }
      }
    }
  }

  .dataSummary-pie {
    margin: 24px 0;
    border: 1px solid #e5e6e7;
    border-radius: 8px;
    padding: 20px;

    .dataSummary-pie-header {
      display: flex;
      align-items: center;

      .dataSummary-pie-header-quantum {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        background: rgba(9, 102, 180, 0.2);
        display: flex;
        justify-content: center;
        align-items: center;

        .dataSummary-pie-header-quantum-small {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: #0966b4;
          display: flex;
          justify-content: center;
          align-items: center;

          .dataSummary-pie-header-quantum-small-icon {
            background: url('@/assets/images/ti.png') no-repeat;
            width: 16px;
            height: 16px;
          }
        }
      }

      .dataSummary-pie-header-title {
        font-size: 16px;
        font-weight: bold;
        color: #0966b4;
        padding-left: 12px;
      }
    }

    .dataSummary-pie-content {
      width: 100%;
    }
  }
}
</style>
