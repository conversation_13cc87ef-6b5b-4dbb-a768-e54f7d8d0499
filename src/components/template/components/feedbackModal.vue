<template>
  <div>
    <modal
      v-model:visible="open"
      header="消息提醒"
      width="500"
      align-center
      destroy-on-close
      :before-close="handleClose"
      close-on-click-modal
      :confirm-btn="null"
      :cancel-btn="null"
    >
      您好，您有新的读者反馈信息待处理， {{ info }}
    </modal>
  </div>
</template>
<script setup>
const props = defineProps({
  info: {
    type: String,
    default: false,
  },
})

const open = ref(false)
//#region 操作相关

// 关闭事件
function handleClose() {
  open.value = false
}

// 对外暴露打开事件
function show() {
  open.value = true
}

//#endregion

//#region 暴露函数相关

defineExpose({
  show,
})

//#endregion
</script>
<style lang="less" scoped></style>
