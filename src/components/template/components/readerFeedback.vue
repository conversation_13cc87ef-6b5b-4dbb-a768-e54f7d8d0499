<template>
  <!-- 读者反馈 -->
  <div class="app-container">
    <div class="form-box">
      <t-form
        ref="queryRef"
        :data="queryParams"
        layout="inline"
        @reset="onReset"
      >
        <t-form-item label="反馈类型：" name="faultType">
          <t-select
            v-model="queryParams.faultType"
            placeholder="请选择反馈类型"
            clearable
            style="width: 200px"
          >
            <t-option
              v-for="item in feedbackTypeList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></t-option>
          </t-select>
        </t-form-item>
        <t-form-item label="状态：" name="auditStatus">
          <t-select
            v-model="queryParams.auditStatus"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
          >
            <t-option
              v-for="item in feedbackStatusList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></t-option>
          </t-select>
        </t-form-item>

        <t-form-item>
          <t-button
            theme="primary"
            style="margin-right: 20px"
            @click="handleQuery"
          >
            <template #icon>
              <SearchIcon />
            </template>
            搜索</t-button
          >
          <t-button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置</t-button
          >
        </t-form-item>
      </t-form>
    </div>

    <div class="table-box">
      <t-enhanced-table
        row-key="bookId"
        :columns="columns"
        :data="feedbackList"
        :pagination="queryParams"
        @page-change="onPageChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 }}
        </template>
        <template #auditStatus="{ row }">
          <t-tag v-if="row.auditStatus === 0" theme="warning">待审核</t-tag>
          <t-tag v-else-if="row.auditStatus === 1" theme="success"
            >已通过</t-tag
          >
          <t-tag v-else-if="row.auditStatus === 2" theme="danger">已驳回</t-tag>
        </template>

        <template #operation="{ row }">
          <t-button variant="text" @click="handleDetail(row)">
            <template #icon>
              <EditIcon />
            </template>
            反馈详情</t-button
          >
        </template>
      </t-enhanced-table>
    </div>

    <t-dialog
      v-model:visible="dialogVisible"
      attach="body"
      header="反馈详情"
      :footer="false"
      width="800px"
    >
      <div class="dialog-content">
        <div class="dialog-item">
          <span class="dialog-item-label">教材名称</span
          ><span class="dialog-item-value">{{ form.bookName }}</span>
        </div>

        <div class="dialog-item">
          <span class="dialog-item-label">目录位置</span>
          <span class="dialog-item-value">{{ form.chapterLocation }}</span>
        </div>

        <div class="dialog-item">
          <span class="dialog-item-label">页码</span>
          <span class="dialog-item-value">{{
            '第' + form.pageNumber + '页'
          }}</span>
        </div>

        <div class="dialog-text">
          <span class="dialog-item-label">教材内容</span>
          <span class="dialog-item-value">{{ form.faultText }}</span>
        </div>

        <div class="dialog-three">
          <div class="dialog-item">
            <span class="dialog-item-label">反馈人</span>
            <span class="dialog-item-value">{{ form.nickName }}</span>
          </div>
          <div class="dialog-item">
            <span class="dialog-item-label">手机号</span>
            <span class="dialog-item-value">{{ form.phonenumber }}</span>
          </div>
          <div class="dialog-item">
            <span class="dialog-item-label">学校</span>
            <span class="dialog-item-value">{{ form.schoolName }}</span>
          </div>
        </div>

        <div class="dialog-item">
          <span class="dialog-item-label">反馈类型</span
          ><span class="dialog-item-value">{{ form.faultType }}</span>
        </div>

        <div class="dialog-item">
          <span class="dialog-item-label">反馈时间</span>
          <span class="dialog-item-value">{{ form.createTime }}</span>
        </div>

        <div class="dialog-item">
          <span class="dialog-item-label" style="vertical-align: top"
            >反馈图片</span
          >
          <ImageView
            v-for="img in form.images"
            style="width: 100px; height: 150px; margin: 5px"
            :img="img.fileUrl"
            :alt="img.fileUrl"
          ></ImageView>
        </div>

        <div class="dialog-text">
          <div class="dialog-item-label" style="width: 80px">问题描述</div>
          <div>
            {{ form.comment }}
          </div>
        </div>

        <div class="dialog-item status">
          <span class="dialog-item-label">状态</span>
          <t-tag
            :theme="form.auditStatus == 1 ? 'success' : 'warning'"
            style="color: #fff"
            >{{ form.auditStatus == 1 ? '已处理' : '未处理' }}</t-tag
          >
        </div>

        <div class="dialog-item">
          <span class="dialog-item-label" style="vertical-align: top"
            >处理结果</span
          >
          <t-textarea
            v-if="form.auditStatus == 0"
            v-model="content"
            placeholder="请输入内容"
            :autosize="{ minRows: 3, maxRows: 5 }"
            maxlength="200"
          />

          <div v-if="form.auditStatus == 1">{{ form.content }}</div>
        </div>

        <div class="dialog-footer" v-if="form.auditStatus == 0">
          <t-button
            theme="default"
            style="margin-right: 10px"
            @click="closeDialog"
            >取消</t-button
          >
          <t-button
            v-if="curUserPermissions.isEditor"
            theme="primary"
            @click="submitForm"
          >
            转为已处理
          </t-button>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup>
import { EditIcon, RefreshIcon, SearchIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'

import {
  getFeedback,
  listForAdmin,
  updateFeedback,
} from '@/api/message/feedback.js'
import { feedbackStatusList, feedbackTypeList } from '@/utils/quetions-utils'

const { proxy } = getCurrentInstance()
const props = defineProps({
  bookId: {
    type: String,
    default: null,
  },
  curUserPermissions: {
    type: Object,
    default: {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    },
  },
})
const feedbackList = ref([])
const loading = ref(true)
const total = ref(0)
const dialogVisible = ref(false)
const content = ref('')
const form = ref({})
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bookId: null,
    auditStatus: null,
    faultType: null,
    total: 10,
  },
})

const { queryParams } = toRefs(data)
const columns = [
  { colKey: 'index', title: '序号', width: '100' },
  { colKey: 'nickName', title: '反馈人' },
  { colKey: 'phonenumber', title: '反馈人手机号', ellipsis: true },
  { colKey: 'schoolName', title: '反馈人所属学校' },
  { colKey: 'chapterLocation', title: '目录位置' },
  { colKey: 'pageNumber', title: '页码段落' },
  { colKey: 'faultType', title: '反馈类型' },
  { colKey: 'createTime', title: '反馈时间' },
  { colKey: 'auditStatus', title: '状态' },
  { colKey: 'operation', title: '操作' },
]

//#region 监听器相关
watch(
  () => props.bookId,
  () => {
    queryParams.value.bookId = props.bookId
    getList()
  },
)
//#region 生命周期相关

//#region  生命周期相关

onMounted(() => {})

//#endregion

//#endregion 获取数据相关
function getList() {
  loading.value = true
  queryParams.value.bookId = props.bookId
  listForAdmin(queryParams.value).then((response) => {
    feedbackList.value = response.rows
    queryParams.value.total = response.total
    loading.value = false
  })
}

//#endregion

function closeDialog() {
  dialogVisible.value = false
}

function submitForm() {
  updateFeedback({
    feedBackId: form.value.feedBackId,
    auditStatus: 1,
    content: content.value,
  }).then((response) => {
    if (response.code == 200) {
      MessagePlugin.success('处理成功')
    } else {
      MessagePlugin.error(response.msg)
    }

    //proxy.$modal.msgSuccess('处理成功')
    dialogVisible.value = false
    getList()
  })
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

const onPageChange = (pageInfo) => {
  queryParams.value.pageSize = pageInfo.pageSize
  queryParams.value.pageNum = pageInfo.current
  queryParams.value.current = pageInfo.current
  getList()
}

const queryRef = ref(null)

// 查看反馈详情
function handleDetail(row) {
  const _feedBackId = row.feedBackId
  getFeedback(_feedBackId).then((response) => {
    form.value = response.data
    dialogVisible.value = true
  })
}

//#region 操作相关
onMounted(() => {
  getList()
})

const onReset = () => {
  MessagePlugin.success('重置成功')
  getList()
  MessagePlugin.success('重置成功')
  getList()
}
</script>

<style scoped lang="less">
.form-box {
  width: 100%;
  margin: 20px 0;
}

.dialog-content {
  .dialog-item {
    color: #333;
    line-height: 30px;
    padding: 5px 0;

    .dialog-item-label {
      color: #999;
      margin-right: 10px;
    }

    .dialog-item-value {
      color: #333;
    }

    img {
      width: 100px;
      height: 150px;
      margin-right: 10px;
    }
  }

  .dialog-text {
    display: flex;
    line-height: 30px;
    color: #333;
    .dialog-item-label {
      min-width: 60px;
      color: #999;
      margin-right: 15px;
    }

    .dialog-item-value {
      color: #333;
    }
    .label {
      color: #999;
      margin-right: 5px;
    }
  }

  .dialog-three {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
