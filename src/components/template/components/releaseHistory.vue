<template>
  <!-- 读者反馈 -->
  <div class="app-container">
    <div class="form-box">
      <t-form
        :data="queryParams"
        layout="inline"
        ref="queryRef"
        @reset="handleReset"
        @submit="handleQuery"
      >
        <t-form-item label="节点：" name="stepId">
          <t-select
            v-model="queryParams.stepId"
            placeholder="全部"
            style="width: 200px"
            clearable
          >
            <t-option
              v-for="item in bookPublishStepList"
              :key="item.stepId"
              :value="item.stepId"
              :label="item.stepName"
            ></t-option>
          </t-select>
        </t-form-item>
        <t-form-item label="状态：" name="state">
          <t-select
            v-model="queryParams.state"
            placeholder="全部"
            clearable
            style="width: 200px"
          >
            <t-option
              v-for="item in statusList"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></t-option>
          </t-select>
        </t-form-item>

        <t-form-item>
          <t-button @click="handleQuery" style="margin-right: 20px">
            <template #icon>
              <SearchIcon />
            </template>
            搜索</t-button
          >
          <t-button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置</t-button
          >
        </t-form-item>
      </t-form>
    </div>

    <div class="table-box">
      <t-table row-key="key" :data="dataList" :columns="columns" v-model:displayColumns="displayColumns" lazy-load>
        <template #state="{ row }">
          <t-tag type="warning">{{
            getOptionDesc(statusList, row.state)
          }}</t-tag>
        </template>
        <template #operation="{ row }">
          <t-link
            v-if="row.state == 1 && curUserPermissions.permissions.includes(5)"
            @click="handleApprove(row)"
            style="margin-right: 30px"
            >审批</t-link
          >
          <t-link @click="handleDetail(row)">详情</t-link>
        </template>
      </t-table>
    </div>
  </div>
  <!-- 审批模态框 -->
  <modal
    v-if="approvalVisible"
    :visible="approvalVisible"
    :header="`审批`"
    :footer="false"
    width="850px"
    @close="approvalVisible = false"
  >
    <processForm
      v-if="approvalVisible"
      :book-id="bookId"
      :process-id="processId"
      :audit-user-id="auditUserId"
      :version-id="versionId"
      :step-id="stepId"
      :book-organize="props.bookOrganize"
      @closeDialog="handleCloseDialog"
    ></processForm>
  </modal>
</template>

<script setup>
import { statusList, getOptionDesc } from '@/utils/quetions-utils'
import { SearchIcon, RefreshIcon } from 'tdesign-icons-vue-next'
import { listStepNotPage } from '@/api/book/bookPublishStep.js'
import { bookVersionList } from '@/api/book/bookVersion.js'
import { MessagePlugin } from 'tdesign-vue-next'
const { proxy } = getCurrentInstance()
import { useRoute, useRouter } from 'vue-router'
import processForm from './processForm.vue'
import { ref } from 'vue'
import { getBook } from '@/api/book/book.js'
const router = useRouter()
const props = defineProps({
  bookId: {
    type: String,
    default: null,
  },
  bookOrganize: {
    type: Number,
    default: null,
  },
  curUserPermissions: {
    type: Object,
    default: {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    },
  },
})
const approvalVisible = ref(false)
const bookId = ref('')
const processId = ref('')
const auditUserId = ref('')
const versionId = ref('')
const stepId = ref('')
const queryRef = ref(null)
const loading = ref(true)
const total = ref(0)
const dialogVisible = ref(false)
const bookOrganize = ref(props.bookOrganize)
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  bookId: null,
})
const publishSteplList = ref([])
const dataList = ref([])

const columns = [
  { colKey: 'versionNo', title: '版本号', width: '100' },
  { colKey: 'recordNo', title: '录排编号' },
  { colKey: 'stepName', title: '当前节点', ellipsis: true },
  { colKey: 'state', title: '状态' },
  { colKey: 'promoterUserName', title: '发起人' },
  { colKey: 'operation', title: '操作' },
]
const displayColumns = ref(['versionNo', 'recordNo', 'stepName','state','promoterUserName','operation']);

//#region 监听器相关
watch(
  () => props.bookId,
  (value) => {
    queryParams.value.bookId = value
    getList()
  },
)
//#region 生命周期相关

//#region  生命周期相关

onMounted(() => {
  if (bookOrganize.value == 1){
    displayColumns.value = ['versionNo', 'recordNo', 'stepName','state','promoterUserName','operation'];
  }else{
    displayColumns.value = ['versionNo', 'stepName','state','promoterUserName','operation'];
  }
  queryRef.value.validate()
  getPublishStepList()
  getList()
})

// 获取节点
function getPublishStepList() {
  listStepNotPage().then((res) => {
    publishSteplList.value = res.data
  })
}

const bookPublishStepList = computed(() => {
  if (props.bookOrganize == 1) {
    return publishSteplList.value
  } else {
    return publishSteplList.value.filter((item) => item.schoolFlag == 1)
  }
})
//#endregion

//#endregion 获取数据相关
function getList() {
  loading.value = true
  queryParams.value.bookId = props.bookId

  bookVersionList(queryParams.value).then((response) => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

//#endregion

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

// 查看反馈详情
function handleDetail(row) {
  dialogVisible.value = true
  // 审批详情按钮
  router.push({
    path: '/pages/processDetail',
    query: {
      bookId: row.bookId,
      masterFlag: row.masterFlag,
      bookOrganize: row.bookOrganize,
      stepId: row.stepId,
      processId: row.processId,
      auditUserId: row.auditUserId,
      state: row.state,
    },
  })
}

// 表单重置
const handleReset = () => {
  MessagePlugin.success('重置成功')
  getList()
}

const handleApprove = (row) => {
  // 发布审核不跳教材详情页面 直接弹窗审核
  if (row.stepId == 15) {
    if (row.masterFlag == 3) {
      // 查询关联主教材的发版状态
      getBook(row.masterBookId).then((res) => {
        if (res.data.publishStatus !== 2) {
          // 主教材已发布
          MessagePlugin.error(
            '已关联的主教材' + res.data.bookName + '还未发版，副教材不允许发版',
          )
          return
        } else {
          // 主教材未发布
          bookId.value = row.bookId
          processId.value = row.processId
          auditUserId.value = row.auditUserId
          versionId.value = row.versionId
          stepId.value = row.stepId
          bookOrganize.value = row.bookOrganize
          approvalVisible.value = true
        }
      })
    } else {
      // 主教材未发布
      bookId.value = row.bookId
      processId.value = row.processId
      auditUserId.value = row.auditUserId
      versionId.value = row.versionId
      stepId.value = row.stepId
      bookOrganize.value = row.bookOrganize
      approvalVisible.value = true
    }
  } else if (row.stepId == 14) {
    // 发布环节的教材详情页面中所有可填项都是必填
    router.push({
      path: '/pages/bookApproveRelease',
      query: {
        bookId: row.bookId,
        masterFlag: row.masterFlag,
        bookOrganize: row.bookOrganize,
        stepId: row.stepId,
        processId: row.processId,
        auditUserId: row.auditUserId,
        funType: 1,
      },
    })
  } else {
    router.push({
      path: '/pages/bookApprove',
      query: {
        bookId: row.bookId,
        masterFlag: row.masterFlag,
        bookOrganize: row.bookOrganize,
        stepId: row.stepId,
        processId: row.processId,
        auditUserId: row.auditUserId,
        funType: 1,
      },
    })
  }
}
// 关闭审批弹窗
function handleCloseDialog() {
  approvalVisible.value = false
}

//#region 操作相关
</script>

<style scoped lang="less">
.form-box {
  width: 100%;
  margin: 20px 0;
}

.dialog-content {
  .dialog-item {
    color: #333;
    line-height: 30px;
    padding: 5px 0;

    span {
      color: #999;
      margin-right: 20px;

      display: inline-flex;
      width: 56px;
    }

    img {
      width: 100px;
      height: 150px;
      margin-right: 10px;
    }
  }

  .dialog-text {
    display: flex;
    line-height: 30px;
    color: #333;

    .label {
      width: 200px;
      color: #999;
      margin-right: 5px;
    }
  }

  .dialog-three {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;
  }
}
</style>
