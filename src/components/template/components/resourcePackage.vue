<template>
  <!-- 读者反馈 -->
  <div class="app-container">
    <div class="form-box">
      <t-form
        ref="queryRef"
        :data="queryParams"
        layout="inline"
        @reset="onReset"
      >
        <t-form-item label="资源名称：" name="fileName">
          <t-input
            v-model="queryParams.fileName"
            placeholder="请输入资源名称"
            clearable
            style="width: 200px"
          />
        </t-form-item>
        <t-form-item label="下载：" name="download">
          <t-select
            v-model="queryParams.download"
            placeholder="全部"
            clearable
            style="width: 200px"
          >
            <t-option
              v-for="item in downloadOptions"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></t-option>
          </t-select>
        </t-form-item>

        <t-form-item>
          <t-button
            theme="primary"
            style="margin-right: 20px"
            @click="handleQuery"
          >
            <template #icon>
              <SearchIcon />
            </template>
            搜索</t-button
          >
          <t-button theme="default" type="reset">
            <template #icon>
              <RefreshIcon />
            </template>
            重置</t-button
          >
        </t-form-item>
      </t-form>
    </div>

    <div class="tableresource-box">
      <div class="table-header">
        <div class="table-header-left">
          <div class="table-header-all">全部资源</div>
          <div>
            <t-button
              v-if="
                curUserPermissions.isEditor ||
                curUserPermissions.permissions.includes(2) ||
                curUserPermissions.permissions.includes(1)
              "
              theme="primary"
              @click="handleDownload"
              >权限</t-button
            >
          </div>
          <div
            v-if="
              curUserPermissions.isEditor ||
              curUserPermissions.permissions.includes(2) ||
              curUserPermissions.permissions.includes(1)
            "
            style="margin-left: 10px"
          >
            <t-button theme="default" @click="handleDelete()">删除</t-button>
          </div>
        </div>

        <div class="table-header-right">
          <t-popconfirm
            :cancel-btn="null"
            :confirm-btn="null"
            content="可上传100个文件，支持jpg、png、doc、docx、ppt、pptx、pdf、txt、mp3、mp4、zip、rar格式,单个文件大小200M以内"
          >
            <t-button shape="circle" theme="default" style="margin-right: 30px">
              <template #icon>
                <HelpIcon />
              </template>
            </t-button>
          </t-popconfirm>
          <!-- <t-button theme="primary" style="margin-right: 10px">引用</t-button> -->
          <t-button
            v-if="
              (curUserPermissions.isEditor ||
                curUserPermissions.permissions.includes(2) ||
                curUserPermissions.permissions.includes(1)) &&
              (publishStatus == 1 || bookOrganize == 2)
            "
            theme="primary"
            style="margin-right: 10px"
            @click="handleUpload"
            >上传</t-button
          >
        </div>
      </div>
      <t-enhanced-table
        row-key="bookFileId"
        :data="bookFileList"
        :columns="columns"
        lazy-load
        :pagination="queryParams"
        :loading="tableLoading"
        @select-change="handleSelectionChange"
        @page-change="onPageChange"
      >
        <template #name="{ row }">
          <div class="table-fileName">
            <div class="table-fileName-icon">
              <i :class="selectTypeName(row?.extension)"></i>
            </div>
            <div class="table-fileName-name">
              {{ row?.fileName + '.' + row?.extension }}
            </div>
            <div class="table-fileName-tag">
              <t-tag
                v-if="row?.download == 1"
                theme="success"
                variant="light-outline"
                >允许下载</t-tag
              >
              <t-tag v-else theme="default" variant="light-outline"
                >不允许下载</t-tag
              >
            </div>
          </div>
        </template>
        <template #fileSize="{ row }">
          {{ (row?.fileSize / 1024 / 1024).toFixed(2) }}MB
        </template>
        <template #download="{ row }">
          <t-switch
            v-if="
              curUserPermissions.isEditor ||
              curUserPermissions.permissions.includes(2) ||
              curUserPermissions.permissions.includes(1)
            "
            :value="row.download"
            :default-value="1"
            :custom-value="[1, 2]"
            @change="(t) => handleDownloadChange(t, row)"
          ></t-switch>
        </template>
        <template #operation="{ row }">
          <t-button
            v-if="
              curUserPermissions.isEditor ||
              curUserPermissions.permissions.includes(2) ||
              curUserPermissions.permissions.includes(1)
            "
            variant="text"
            @click="handleApprove(row)"
          >
            <template #icon>
              <Edit2Icon />
            </template>
            编辑</t-button
          >
          <t-button
            v-if="
              curUserPermissions.isEditor ||
              curUserPermissions.permissions.includes(2) ||
              curUserPermissions.permissions.includes(1)
            "
            variant="text"
            @click="handleDelete(row)"
          >
            <template #icon>
              <DeleteIcon />
            </template>
            删除</t-button
          >
        </template>
      </t-enhanced-table>
    </div>

    <modal
      v-model:visible="editVisible"
      header="编辑"
      width="700px"
      :footer="false"
    >
      <t-form :data="form" :rules="rules" @submit="onSubmit">
        <t-form-item label="资源名称" name="fileName">
          <t-input
            v-model="form.fileName"
            placeholder="请输入资源名称"
            style="width: 300px"
          />
        </t-form-item>
        <t-form-item label="资源类型" name="download">
          <t-select
            v-model="form.download"
            placeholder="请选择资源类型"
            style="width: 300px"
          >
            <t-option label="允许" :value="1"></t-option>
            <t-option label="不允许" :value="2"></t-option>
          </t-select>
        </t-form-item>
        <t-form-item>
          <t-button
            theme="default"
            style="margin-right: 10px"
            @click="editVisible = false"
            >取消</t-button
          >
          <t-button theme="primary" type="submit">确定</t-button>
        </t-form-item>
      </t-form>
    </modal>

    <modal
      v-model:visible="downloadDialogVisible"
      header="权限"
      width="25%"
      :footer="false"
    >
      <t-form :data="downLoadForm" @submit="submitDownloadForm">
        <t-form-item label="允许下载" name="download">
          <t-select
            v-model="downLoadForm.download"
            placeholder="请选择资源类型"
            style="width: 300px"
          >
            <t-option label="允许" :value="1"></t-option>
            <t-option label="不允许" :value="2"></t-option>
          </t-select>
        </t-form-item>
        <div class="modal-footer">
          <t-button
            theme="default"
            style="margin-right: 10px"
            @click="downloadDialogVisible = false"
            >取消</t-button
          >
          <t-button theme="primary" type="submit">确定</t-button>
        </div>
      </t-form>
    </modal>

    <DeleteModal
      v-model="deleteModalVisible"
      @confirm="handleConfirmDelete"
    ></DeleteModal>
  </div>
</template>

<script setup>
import { RefreshIcon, SearchIcon } from 'tdesign-icons-vue-next'
import { DeleteIcon, Edit2Icon, HelpIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { LoadingPlugin } from 'tdesign-vue-next'

import {
  addBookFile,
  batchUpdateBookFile,
  delBookFile,
  getBookFile,
  listBookFile,
  updateBookFile,
} from '@/api/book/bookFile'
import DeleteModal from '@/components/DeleteModal/index.vue'
import { OssService } from '@/utils/aliOss'
import { downloadOptions } from '@/utils/quetions-utils'
const downloadDialogVisible = ref(false)
const { proxy } = getCurrentInstance()
const editVisible = ref(false)
const form = ref({})
const downLoadForm = ref({})
const props = defineProps({
  bookId: {
    type: String,
    default: null,
  },
  curUserPermissions: {
    type: Object,
    default: {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    },
  },
  bookOrganize: {
    type: Number,
    default: 1,
  },
  publishStatus: {
    type: Number,
    default: 1,
  },
})

const ids = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const content = ref('')
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    bookId: null,
    auditStatus: null,
    faultType: null,
    total: 0,
  },
})
const bookFileList = ref([])
const tableLoading = ref(false)
const rules = {
  fileName: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
  download: [{ required: true, message: '请选择资源类型', trigger: 'change' }],
}

const pagination = ref({
  defaultPageSize: 10,
  total: 0,
  defaultCurrent: 1,
})
const deleteModalVisible = ref(false)
const onSubmit = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    updateBookFile({
      ...form.value,
      fileName: `${form.value.fileName}.${form.value.extension}`,
    }).then((response) => {
      MessagePlugin.success('提交成功')
      editVisible.value = false
      getList()
    })
  } else {
    MessagePlugin.warning(firstError)
  }
}
const { queryParams } = toRefs(data)
const columns = [
  {
    colKey: 'bookFileId',
    type: 'multiple',
    width: 50,
  },
  { colKey: 'name', title: '', width: '50%' },
  { colKey: 'fileSize', title: '' },
  { colKey: 'createTime', title: '' },
  { colKey: 'download', title: '' },
  { colKey: 'operation', title: '', width: '220px' },
]

const handleConfirmDelete = (fn) => {
  delBookFile(delIdList.value).then((response) => {
    fn(true)
    MessagePlugin.success('提交成功')
    getList()
  })
}
// 查询教材资源列表
function getList() {
  tableLoading.value = true
  listBookFile(queryParams.value).then((response) => {
    bookFileList.value = response.rows
    bookFileList.value.forEach((e) => {
      const f = e.fileName.split('.')
      e.extension = f[1]
      e.fileName = f[0]
    })
    queryParams.value.total = response.total
    tableLoading.value = false
  })
}
//#endregion
//#region 监听器相关
watch(
  () => props.bookId,
  () => {
    queryParams.value.bookId = props.bookId
    getList()
  },
)
//#region 生命周期相关

//#region  生命周期相关

onMounted(() => {
  queryParams.value.bookId = props.bookId
  getList()
})
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item)
}

const handleDownload = () => {
  downLoadForm.value = {
    download: 1,
  }
  if (ids.value.length == 0) {
    MessagePlugin.error('请选择要设置权限的教材资源')
    return
  }
  downloadDialogVisible.value = true
}

// 切换权限
function handleDownloadChange(t, item) {
  updateBookFile({
    bookFileId: item.bookFileId,
    download: t,
  }).then((response) => {
    MessagePlugin.success('修改成功')
    getList()
  })
}

// 权限确认
function submitDownloadForm() {
  const fileList = ids.value.map((e) => {
    return {
      bookFileId: e,
      download: downLoadForm.value.download,
    }
  })
  batchUpdateBookFile(fileList).then((res) => {
    MessagePlugin.success('修改成功')
    downloadDialogVisible.value = false
    getList()
  })
}

//#endregion

//#endregion

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1

  getList()
}

const handleApprove = (row) => {
  reset()
  const _bookFileId = row.bookFileId || ids.value
  getBookFile(_bookFileId).then((response) => {
    form.value = response.data
    const f = form.value.fileName.split('.')
    form.value.extension = f[1]
    form.value.fileName = f[0]
    editVisible.value = true
  })
}

const delIdList = ref([])
const handleDelete = (row) => {
  if (row) {
    delIdList.value = [row.bookFileId]
  } else {
    if (ids.value.length == 0) {
      MessagePlugin.warning('请选择要删除的资源')
      return
    }
    delIdList.value = ids.value
  }
  deleteModalVisible.value = true
}

// 表单重置
function reset() {
  form.value = {
    bookFileId: null,
    bookId: null,
    fileType: null,
    fileName: null,
    fileSize: null,
    fileUrl: null,
    delFlag: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
  }
}

const onReset = () => {
  MessagePlugin.success('重置成功')
  getList()
}

const selectTypeName = computed(() => {
  return function (extension) {
    if (!extension) return null

    if (extension === 'doc' || extension === 'docx') {
      return 'doc'
    }

    if (extension === 'pdf') {
      return 'pdf'
    }

    if (extension === 'ppt' || extension === 'pptx') {
      return 'ppt'
    }

    if (extension === 'xls' || extension === 'xlsx') {
      return 'xls'
    }

    if (extension === 'txt') {
      return 'txt'
    }

    if (extension === 'zip') {
      return 'zip'
    }

    if (extension === 'rar') {
      return 'rar'
    }

    if (extension === 'mp3') {
      return 'mp3'
    }

    if (extension === 'mp4' || extension === 'avi' || extension === 'mov') {
      return 'avi'
    }

    if (
      extension === 'jpg' ||
      extension === 'jpeg' ||
      extension === 'png' ||
      extension === 'gif'
    ) {
      return 'jpg'
    }
  }
})

const onPageChange = async (pageInfo) => {
  queryParams.value.pageSize = pageInfo.pageSize
  queryParams.value.pageNum = pageInfo.current
  queryParams.value.current = pageInfo.current
  getList()
}

//#region 操作相关

const acceptList = [
  'doc',
  'docx',

  'pdf',

  'ppt',
  'pptx',

  'xls',
  'xlsx',

  'txt',

  'zip',
  'rar',

  'mp3',
  'mp4',
  'avi',
  'mav',
  'fiv',

  'jpg',
  'jpeg',
  'png',
]
// 上传
const handleUpload = () => {
  if (total.value >= 100) {
    MessagePlugin.error('最多上传100个文件')
    return
  }
  form.value = {
    bookId: props.bookId,
  }
  const InputFile = document.createElement('input')
  InputFile.type = 'file'
  InputFile.click()
  InputFile.addEventListener('change', async (e) => {
    const { files } = e.target

    const fileArr = []
    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      let fileExtension = ''
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
      }
      if (fileExtension && !acceptList.includes(fileExtension)) {
        MessagePlugin.error(`不允许上传${fileExtension}类型的文件`)
        break
      }
      if (file.size / 1024 / 1024 > 200) {
        MessagePlugin.error(`请上传不大于200MB的文件`)
        break
      }
      const loading = LoadingPlugin({
        text: '正在上传文件，请稍候...',
        fullscreen: true,
      })
      const res = await OssService(file)
      loading.hide()
      fileArr.push({
        fileName: file.name,
        fileSize: file.size,
        fileUrl: res.url,
        bookId: props.bookId,
        download: 1,
      })
      MessagePlugin.closeAll()
    }
    fileArr.forEach((e) => {
      addBookFile(e).then((response) => {
        MessagePlugin.success('新增成功')
        getList()
      })
    })
    InputFile.remove && InputFile.remove()
  })
}
</script>

<style lang="less">
.form-box {
  width: 100%;
  margin: 20px 0;
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  top: 45px;

  .table-header-left {
    display: flex;
    position: relative;
    left: 40px;
    z-index: 999;

    .table-header-all {
      padding: 5px 10px;
    }
  }

  .table-header-right {
    z-index: 999;
    margin-right: 40px;
  }
}

.tableresource-box {
  .t-table__content {
    .t-table__header {
      height: 60px;
    }
  }
}

.table-fileName {
  display: flex;
  align-items: center;

  .table-fileName-icon {
    width: 30px;
    height: 30px;
    margin-right: 10px;

    i {
      width: 30px;
      height: 30px;
      display: inline-flex;
      background-size: contain;
    }

    .mp3 {
      background: url('../../../assets/images/muics.svg') no-repeat;
    }

    .doc {
      background: url('../../../assets/images/word.svg') no-repeat;
    }

    .pdf {
      background: url('../../../assets/images/pdf.svg') no-repeat;
    }

    .ppt {
      background: url('../../../assets/images/ppt.svg') no-repeat;
    }

    .xls {
      background: url('../../../assets/images/xls.svg') no-repeat;
    }

    .txt {
      background: url('../../../assets/images/txt.svg') no-repeat;
    }

    .avi {
      background: url('../../../assets/images/video.svg') no-repeat;
    }

    .rar {
      background: url('../../../assets/images/rar.svg') no-repeat;
    }

    .jpg {
      background: url('../../../assets/images/images.svg') no-repeat;
    }
  }

  .table-fileName-name {
    margin: 0 10px;
  }
}

.dialog-content {
  .dialog-item {
    color: #333;
    line-height: 30px;
    padding: 5px 0;

    span {
      display: inline-flex;
    }

    img {
      width: 100px;
      height: 150px;
      margin-right: 10px;
    }
  }

  .dialog-text {
    display: flex;
    line-height: 30px;
    color: #333;

    .label {
      width: 200px;
      color: #999;
      margin-right: 5px;
    }
  }

  .dialog-three {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;
  }
}
</style>
