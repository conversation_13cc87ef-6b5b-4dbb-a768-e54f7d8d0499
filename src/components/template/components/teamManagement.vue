<template>
  <div class="app-container">
    <t-form :data="form" ref="formRef" :rules="rules" label-width="120px" @submit="save">
      <div class="save-btn" v-if="isShowSave && publishStatus == 1">
        <t-button theme="primary" size="large" type="submit"
          v-if="[1, 2, 5, 6].some(p => curUserPermissions.permissions.includes(p))">保存</t-button>
      </div>

      <div class="form-title">作者</div>
      <div class="form-single">
        <t-form-item label="书稿联系人" name="contact">
          <t-select v-model="form.contact" placeholder="请选择" style="width: 200px" clearable filterable :disabled="(![5, 6].some(p => curUserPermissions.permissions.includes(p)))
            || (stepId !== 1 && auditState !== null && auditState !== 3)
            ">
            <t-option v-for="item in userList" :key="item.userId" :label="item.nickName + item.phonenumber"
              :value="item.userId" />
          </t-select>
        </t-form-item>
      </div>

      <div class="form-row">
        <div>
          <t-form-item label="主编" name="editor">
            <t-select v-model="form.editor" multiple :max="20" style="width: 200px" clearable filterable :disabled="(![5, 6].some(p => curUserPermissions.permissions.includes(p)))
              || (stepId !== 1 && auditState !== null && auditState !== 3)
              ">
              <t-option v-for="item in userList" :key="item.userId" :label="item.nickName + item.phonenumber"
                :value="item.userId" />
            </t-select>
          </t-form-item>
        </div>
        <div>
          <t-form-item label="副主编" name="associateEditor">
            <t-select v-model="form.associateEditor" placeholder="请选择" style="width: 200px" multiple :max="20"
              filterable :disabled="(![1, 2, 5, 6].some(p => curUserPermissions.permissions.includes(p)))
                || (stepId !== 1 && auditState !== null && auditState !== 3)
                " clearable>
              <t-option v-for="item in userList" :key="item.userId" :label="item.nickName + item.phonenumber"
                :value="item.userId" />
            </t-select>
          </t-form-item>
        </div>
      </div>

      <div class="form-single">
        <t-form-item label="参编" name="participateCompilation">
          <t-select v-model="form.participateCompilation" multiple :max="20" placeholder="请选择" style="width: 200px"
            clearable filterable :disabled="(![1, 2, 5, 6].some(p => curUserPermissions.permissions.includes(p)))
              || (stepId !== 1 && auditState !== null && auditState !== 3)
              ">
            <t-option v-for="item in userList" :key="item.userId" :label="item.nickName + item.phonenumber"
              :value="item.userId" />
          </t-select>
        </t-form-item>
      </div>

      <div v-if="[5, 6, 7].some(p => curUserPermissions.permissions.includes(p))" class="form-title"
        style="margin-top: 30px">编辑</div>

      <div v-if="[5, 6, 7].some(p => curUserPermissions.permissions.includes(p))" class="form-row">
        <div>
          <t-form-item label="策划编辑" name="planningEditor">
            <t-select v-model="form.planningEditor" placeholder="请选择" style="width: 200px" clearable filterable
              :disabled="(![5, 6].some(p => curUserPermissions.permissions.includes(p)))
                || (stepId !== 1 && auditState !== null && auditState !== 3)
                ">
              <t-option v-for="item in userList.filter((item) => item.roleId == 4)" :key="item.userId"
                :label="item.nickName + item.phonenumber" :value="item.userId" />
            </t-select>
          </t-form-item>
        </div>
        <div>
          <t-form-item label="责任编辑" name="editorInCharge">
            <t-select v-model="form.editorInCharge" multiple :max="20" placeholder="请选择" style="width: 200px" clearable
              filterable :disabled="(![5, 6].some(p => curUserPermissions.permissions.includes(p)))
                || (stepId !== 1 && auditState !== null && auditState !== 3)
                ">
              <t-option v-for="item in userList.filter((item) => item.roleId == 4)" :key="item.userId"
                :label="item.nickName + item.phonenumber" :value="item.userId" />
            </t-select>
          </t-form-item>
        </div>
      </div>
      <div v-if="[5, 6, 7].some(p => curUserPermissions.permissions.includes(p))" class="form-row">
        <div>
          <t-form-item label="编校人员" name="proofreader">
            <t-select v-model="form.proofreader" multiple :max="20" placeholder="请选择" style="width: 200px" clearable
              filterable :disabled="(![5, 6].some(p => curUserPermissions.permissions.includes(p)))
                ">
              <t-option v-for="item in userList.filter((item) => item.roleId == 4)" :key="item.userId"
                :label="item.nickName + item.phonenumber" :value="item.userId" />
            </t-select>
          </t-form-item>
        </div>
      </div>
    </t-form>
  </div>
</template>

<script setup>
import { listUserNotPage } from '@/api/system/user'
import { getBookGroup, updateBookGroup } from '@/api/book/bookGroup'
import { MessagePlugin } from 'tdesign-vue-next'
const { proxy } = getCurrentInstance()

const props = defineProps({
  bookId: {
    type: String,
    default: null,
  },
  isShowSave: {
    type: Boolean,
    default: true,
  },
  curUserPermissions: {
    type: Object,
    default: {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    },
  },
  publishStatus: {
    type: Number,
    default: 1,
  },
  stepId: {
    type: [String, Number],
    default: 1,
  },
  auditState: {
    type: Number,
    default: null,
  }
})
const userList = ref([])
const formRef = ref(null)
const isShowSave = ref(true)
const form = ref({
  contact: null, //书稿联系人
  editor: [], //主编
  associateEditor: [], //副主编
  participateCompilation: [], //参编
  planningEditor: null, //策划编辑
  editorInCharge: [], //责任编辑
  bookId: null,
})

const rules = reactive({
  contact: [
    {
      required: true,
      message: '请选择书稿联系人',
      trigger: 'change',
    },
  ],
  planningEditor: [
    {
      required: true,
      message: '请选择策划编辑',
      trigger: 'change',
    },
  ],
})

//#region 监听器相关

//#endregion

//#region 生命周期相关

//#region  生命周期相关

onMounted(() => {
  getInfo()
  getUserList()
})

//#endregion

//#endregion 获取数据相关

// 获取用户列表
function getUserList() {
  listUserNotPage().then((res) => {
    userList.value = res.data || []
  })
}

//#endregion

//#region 操作相关

// 获取数据
async function getInfo() {
  let res = await getBookGroup(props.bookId)
  form.value = res.data
  form.value.editor = res.data.editor || []
  form.value.associateEditor = res.data.associateEditor || []
  form.value.participateCompilation = res.data.participateCompilation || []
  form.value.editorInCharge = res.data.editorInCharge || []
  // getBookGroup(props.bookId).then((res) => {
  //   console.log(res.data);
  // })
}

// 保存
function save({ validateResult, firstError }) {
  if (validateResult === true) {
    updateBookGroup(form.value).then((res) => {
      if (res.code === 200) {
        MessagePlugin.success('保存成功')
      } else {
        MessagePlugin.error('保存失败')
      }
    })
  } else {
    MessagePlugin.error('保存失败')
  }

  // proxy.$refs["formRef"].validate((valid) => {
  //   if (valid) {
  //
  //   } else {
  //     console.log("error submit!");
  //   }
  // });
}

//#endregion

//#endregion 暴露事件

//   defineExpose({
//     save,
//     getInfo,
//   });
//#endregion
</script>

<style scoped lang="less">
.form-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #666;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background-color: #409eff;
    border-radius: 8px;
    margin-right: 8px;
  }
}

.form-single {}

.form-row {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.save-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
