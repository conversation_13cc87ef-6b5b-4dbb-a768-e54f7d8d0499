<template>
  <div class="app-container">
    <div class="main">
      <div class="book-item">
        <div class="book-label">教育学科分类</div>
        <div class="book-value">{{ bookInfo.subjectName }}</div>
      </div>

      <div class="book-item">
        <div class="book-label">中图分类</div>
        <div class="book-value">{{ bookInfo.bookTypeName }}</div>
      </div>

      <div class="book-item">
        <div class="book-label">专区分类</div>
        <div class="book-value">{{ bookInfo.areaName }}</div>
      </div>

      <div class="book-item">
        <div class="book-label">{{ bookInfo.authorLabel }}</div>
        <div class="book-value">{{ bookInfo.authorValue }}</div>
      </div>

      <div class="book-item">
        <div class="book-label">编辑推荐</div>
        <div class="book-value">{{ bookInfo.recommend }}</div>
      </div>

      <div class="book-item">
        <div class="book-label">版权声明</div>
        <div class="book-value">{{ bookInfo.declaration }}</div>
      </div>

      <div class="book-item">
        <div class="book-label">版权信息</div>
        <div class="book-value">{{ bookInfo.copyright }}</div>
      </div>

      <div class="book-item">
        <div class="book-label">教材简介</div>
        <div class="book-value">
          {{ bookInfo.introduce }}
        </div>
      </div>
      <div class="book-item">
        <div class="book-label">部门</div>
        <div class="book-value">
          {{ bookInfo.deptName }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'

import { getProfile } from '@/api/book/book.js'
const props = defineProps({
  bookId: {
    type: String,
  },
})
const route = useRoute()
const bookInfo = ref({})

//#region 监听器相关

onMounted(() => {
  getBookInfo()
})
//#region 生命周期相关

//#region 获取信息相关

// 获取教材信息
async function getBookInfo() {
  const bookId = props.bookId || route.query.bookId
  const response = await getProfile({
    bookId,
  })
  bookInfo.value = response.data
}

//#endregion
</script>

<style scoped lang="less">
.main {
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  .book-item {
    display: flex;
    font-size: 14px;
    margin-bottom: 20px;
    .book-label {
      font-weight: bold;
      margin-right: 10px;
      width: 120px;
      vertical-align: top;
      &::before {
        content: '';
        font-weight: bold;
        display: inline-block;
        width: 13px;
        height: 13px;
        margin-right: 5px;
        background: url('@/assets/images/bookIcon.svg') no-repeat;
        background-size: contain;
      }
    }
    .book-value {
      flex: 1;
    }
  }
}

// .t-form__label--left {
//   text-align: left;
//   display: flex;
//   align-items: center;

//   label {
//     :global(&::before) {
//       content: '';
//       font-weight: bold;
//       display: inline-block;
// .t-form__label--left {
//   text-align: left;
//   display: flex;
//   align-items: center;

//   label {

//   }
// }

// :global(.t-form__controls-content) {
//   display: flex;
//   align-items: center;
//   margin-left: 30px;
//   min-height: var(--td-comp-margin-xxxl);
// }
</style>
