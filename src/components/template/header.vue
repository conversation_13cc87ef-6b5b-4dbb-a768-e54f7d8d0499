<template>
  <t-header>
    <t-head-menu value="item1" height="120px" class="header-menu">
      <template #logo>
        <!-- <img width="136" class="logo" src="https://www.tencent.com/img/index/menu_logo_hover.png"
                        alt="logo" /> -->
        <div>数字教材编辑端</div>
      </template>

      <div class="toggleBtn" @click="toggleMenu">
        <ViewListIcon />
      </div>

      <!-- <t-menu-item value="item2"> 菜单内容一 </t-menu-item>
                <t-menu-item value="item3"> 菜单内容二 </t-menu-item>
                <t-menu-item value="item4" :disabled="true"> 菜单内容三 </t-menu-item> -->
      <template #operations>
        <t-avatar :image="avatar" size="large" />
        <div class="user-name">
          <t-dropdown :options="options" trigger="click" @click="clickHandler">
            <t-space>
              <t-button ghost variant="text" style="color: #fff">
                {{ nickName }}
                <template #suffix>
                  <t-icon name="chevron-down" size="16"
                /></template>
              </t-button>
            </t-space>
          </t-dropdown>
        </div>
      </template>
    </t-head-menu>
  </t-header>
</template>
<script setup>
import { ViewListIcon } from 'tdesign-icons-vue-next'
import { ref } from 'vue'
const { nickName, avatar, getUserInfo, LoginOut } = useStore()
const emits = defineEmits(['toggleMenu'])
const visible = ref(false)

const toggleMenu = () => {
  emits('toggleMenu', (visible.value = !visible.value))
}

const options = [
  { content: '个人中心', value: 1 },
  { content: '退出登录', value: 2 },
]

onMounted(() => {
  getInfo()
})

const getInfo = () => {
  getUserInfo().then((res) => {})
}

const logout = () => {
  LoginOut().then(() => {
    MessagePlugin.success({
      content: '退出成功',
    })
    window.location.href = '/#/login'
  })
}

const clickHandler = (data) => {
  const { content, value } = data
  if (value === 2) {
    logout()
  }
  if (value === 1) {
    window.location.href = '/#/user/userInfo'
  }
}
</script>

<style lang="less" scoped>
.header-menu {
  background-color: #0966b4;
  color: #fff;

  .user-name {
    padding: 0 26px 0 10px;
    .t-button--variant-text.t-button--ghost {
      border: none;
      background: none;
      color: var(--td-text-color-anti);
    }
  }

  .logout {
    cursor: pointer;
    background: #207bc6;
    border-radius: 4px 4px 4px 4px;
    width: 62px;
    height: 28px;
    line-height: 28px;
    text-align: center;
  }

  .toggleBtn {
    width: 34px;
    height: 34px;
    border-radius: 50%;
    background-color: #207bc6;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}
</style>
