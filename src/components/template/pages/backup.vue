<template>
  <t-radio-group variant="primary-filled" default-value="1" size="large" v-model="selectType" @change="bookTypeChange">
    <t-radio-button value="1">公开教材</t-radio-button>
    <t-radio-button value="2">校本教材</t-radio-button>
  </t-radio-group>
<!--  <t-button style="float: right" theme="primary" @click="additionSetting">补录配置</t-button>-->
  <div class="search-form">
    <t-form ref="searchForm" :data="queryData" :label-width="120" layout="inline" @reset="onReset">
      <t-form-item label="教材名称">
        <t-input v-model="queryData.bookName" placeholder="请输入教材名称" clearable style="width: 200px;"></t-input>
      </t-form-item>
      <t-form-item label="教材编号">
        <t-input v-model="queryData.bookNo" placeholder="请输入教材编号" clearable style="width: 200px;"></t-input>
      </t-form-item>
      <t-form-item v-if="selectType == 1" label="ISBN">
        <t-input v-model="queryData.isbn" placeholder="请输入ISBN" clearable style="width: 200px;"></t-input>
      </t-form-item>
      <t-form-item v-if="selectType == 1" label="ISSN">
        <t-input v-model="queryData.issn" placeholder="请输入ISSN" clearable style="width: 200px;"></t-input>
      </t-form-item>
      <t-form-item label="主/副教材">
        <t-select v-model="queryData.masterFlag" clearable style="width: 200px;">
          <t-option value="" label="全部"></t-option>
          <t-option value="1" label="常规教材"></t-option>
          <t-option value="2" label="主教材"></t-option>
          <t-option value="3" label="副教材"></t-option>
        </t-select>
      </t-form-item>
      <t-form-item label="节点">
        <t-select v-model="queryData.stepId" clearable style="width: 200px;">
          <t-option value="" label="全部"></t-option>
          <t-option :value="item.stepId" :label="item.stepName" v-for="item in filteredList(stepList)" :key="item.stepId" ></t-option>
        </t-select>
      </t-form-item>
      <t-form-item label="状态">
        <t-select v-model="queryData.state" clearable style="width: 200px;">
          <t-option value="" label="全部"></t-option>
          <t-option :value="item.value" :label="item.label" v-for="item in filteredList(statusList)" :key="item.value">{{ item.label }}</t-option>
        </t-select>
      </t-form-item>
      <t-button theme="primary" @click="search" style="margin-right: 20px"
      >查询</t-button
      >
      <t-button theme="default" type="reset">重置</t-button>
    </t-form>
  </div>
  <t-table hover row-key="index"
           :columns="columns"
           :data="tableData"
           v-model:displayColumns="displayColumns"
           :pagination="pagination"
           @select-change="handleSelectionChange"
           @page-change="onPageChange">
  </t-table>

  <t-dialog
    v-model:visible="additionVisible"
    header="补录配置"
    width="700px"
    :on-confirm="handleAddition"
  >
    <div>
      <t-form :model="form" labt-width="120px">
        <t-row  class="mb8">
          <t-col :span="11">
            <t-form-item label="是否开启补录" prop="showAddRecord" required>
              <t-radio-group v-model="form.showAddRecord" default-value="1">
                <t-radio value="1">开启</t-radio>
                <t-radio value="2">关闭</t-radio>
              </t-radio-group>
            </t-form-item>
          </t-col>
        </t-row>
      </t-form>
    </div>
  </t-dialog>
  <!-- 审批模态框 -->
  <modal
    v-if="approvalVisible"
    :visible="approvalVisible"
    :header="`审批`"
    :footer="false"
    width="850px"
    @close="approvalVisible = false"
  >
    <processForm v-if="approvalVisible"
                 :key="approvalVisible ? 'form-open' : 'form-closed'"
                 :book-id="bookId"
                 :process-id="processId"
                 :audit-user-id="auditUserId"
                 :version-id="versionId"
                 :step-id="stepId"
                 :book-organize="bookOrganize"
                 @closeDialog="closeModel"></processForm>
  </modal>
</template>

<script setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
import { MessagePlugin } from 'tdesign-vue-next'

import { getStepById,listStepNotPage } from '@/api/book/bookPublishStep.js'
import { addConfig, delConfig, getConfig, listConfig, updateConfig } from "@/api/book/config";
import { list } from '@/api/book/process.js';
import { mainSubTextbookList, statusList } from '@/utils/quetions-utils'
import processForm from '../components/processForm.vue'
import { getInfo } from '@/api/login'
import { getBook } from '@/api/book/book.js'

const approvalVisible = ref(false)
const stepList = ref([]);
const selectType = ref('1')
const additionVisible = ref(false);
// 检索表单
const searchForm = ref(null);
const bookId = ref('');
const processId = ref('');
const auditUserId = ref('');
const versionId = ref('');
const stepId = ref('');
const bookOrganize = ref('');
const currentUserId = ref('');
// 检索数据
const queryData = ref({
  current: 1,
  pageSize: 20,
  total: 10,
  name: '',
  bookOrganize: '1', // 默认公开教材
})
const form = ref({
  showAddRecord:'1'
});
// 检索
const search = () => {
  queryData.value.pageNum = 1
  queryData.value.current = 1
  getList()
}
// 检索表单重置
function onReset(){
  queryData.value = {
    bookOrganize: selectType.value,
    current: 1,
    pageSize: 20,
    total: 10,
    name: '',
  }
  search();
}

// 表格列
const columns = [
  { colKey: 'bookNo', title: '教材编号', ellipsis: true, width: '100',fixed:'left'},
  { colKey: 'bookName', title: '教材名称', ellipsis: true, width: '200',fixed:'left' },
  { colKey: 'isbn', title: 'ISBN', ellipsis: true, width: '200' },
  { colKey: 'issn', title: 'ISSN', ellipsis: true, width: '200' },
  { colKey: 'stepName', title: '当前节点', ellipsis: true, width: '100' },
  { colKey: 'state', title: '状态', ellipsis: true, width: '100',
    cell: (h, { row, rowIndex }) => {
      return statusList[row.state].label
    },
  },
  { colKey: 'createBy', title: '发起人', ellipsis: true, width: '100' },
  { colKey: 'createTime', title: '发起时间', ellipsis: true, width: '150' },
  { colKey: 'dealUserName', title: '处理人', ellipsis: true, width: '100' },
  { colKey: 'versionNo', title: '版本号', ellipsis: true, width: '100' },
  { colKey: 'masterFlag', title: '主/副教材', ellipsis: true, width: '100',
    cell: (h, { row, rowIndex }) => {
      return mainSubTextbookList[row.masterFlag].label
    }, },
  { colKey: 'recordNo', title: '录排编号', ellipsis: true, width: '100' },
  {
    title: '操作',
    colKey: 'operation',
    fixed: 'right',
    foot: "-",
    ellipsis: true,
    width: 240,
    cell: (h, { row }) => {
      let items = [];
      items = [
        {
          label: '审批',
          color: '#0966B4',
          onClick: (row) => {
            processRow(row)
          },
        },
        {
          label: '审批详情',
          color: '#0966B4',
          onClick: (row) => {
            detailRow(row)
          }
        },
      ]
      return h(
        'div',
        { style: 'display:grid;grid-template-columns:repeat(4,1fr);gap:10px;' },
        items.map((item) => {
          if (
            currentUserId.value == row.userId &&
            ((row.stepId != 16 && row.stepId != 2 && row.state != 2 && row.state != 3)
              || (row.stepId == 2 && row.state == 1)) &&
            item.label == '审批'
          ) {
            return h(
              'div',
              {
                onclick: () => item.onClick(row),
                style: `display:flex;align-items:center;justify-content:start;cursor:pointer;color:${item.color}`,
              },
              item.label,
            )
          } else if (
            item.label == '审批详情'
          ) {
            return h(
              'div',
              {
                onclick: () => item.onClick(row),
                style: `display:flex;align-items:center;justify-content:start;cursor:pointer;color:${item.color}`,
              },
              item.label,
            )
          } else {
            return h('div')
          }
        }),
      )
    }
  },
]
const displayColumns = ref(['bookNo', 'bookName', 'isbn','issn','stepName','state','createBy','createTime','dealUserName','versionNo','masterFlag','recordNo','operation']);
// 分页配置
const pagination = ref({
  defaultPageSize: 20,
  total: 10,
  defaultCurrent: 1,
});

// 表格假数据
const tableData = ref([])

// 查询列表数据
const getList = async () => {
  const res = await list(queryData.value);
  if (res.code == 200) {
    tableData.value = res.rows;
    pagination.value.total = res.total;
  }else{
    MessagePlugin.error(res.msg);
  }
}

// 查询平台的补录设置
function getAddition() {
  listConfig().then(response => {
    form.value = response.rows[0];
  });
}

// 补录配置
function additionSetting(){
  getAddition();
  additionVisible.value = true
}

// 勾选
const handleSelectionChange = (value, ctx) => {
  selectedRowKeys.value = value

}

//#region 操作相关
const onPageChange = (pageInfo) => {
  queryData.value.pageSize = pageInfo.pageSize
  queryData.value.pageNum = pageInfo.current
  queryData.value.current = pageInfo.current
  getList()
}

// 补录确认
function handleAddition(){
  /** 提交按钮 */
  updateConfig(form.value).then(response => {
    MessagePlugin.success('修改成功')
    additionVisible.value = false
  });
}

// 列表审批按钮
function processRow(row){
  // 发布审核不跳教材详情页面 直接弹窗审核
  if(row.stepId == 15){
    if(row.masterFlag == 3){
      // 查询关联主教材的发版状态
      getBook(row.masterBookId).then(res => {
        if (res.data.publishStatus !== 2) {
          // 主教材已发布
          MessagePlugin.error('已关联的主教材'+ res.data.bookName +'还未发版，副教材不允许发版')
          return;
        }else{
          // 主教材未发布
          bookId.value = row.bookId;
          processId.value = row.processId;
          auditUserId.value = row.auditUserId;
          versionId.value = row.versionId;
          stepId.value = row.stepId;
          bookOrganize.value = row.bookOrganize;
          approvalVisible.value = true;
        }
      });
    }else{
      // 主教材未发布
      bookId.value = row.bookId;
      processId.value = row.processId;
      auditUserId.value = row.auditUserId;
      versionId.value = row.versionId;
      stepId.value = row.stepId;
      bookOrganize.value = row.bookOrganize;
      approvalVisible.value = true;
    }
  }else if (row.stepId == 14){
    // 发布环节的教材详情页面中所有可填项都是必填
    router.push({ path: '/pages/bookApproveRelease', query: { bookId: row.bookId,masterFlag:row.masterFlag,bookOrganize:row.bookOrganize,stepId:row.stepId,processId:row.processId,auditUserId:row.auditUserId,funType:2 } })
  }else{
    router.push({ path: '/pages/bookApprove', query: { bookId: row.bookId,masterFlag:row.masterFlag,bookOrganize:row.bookOrganize,stepId:row.stepId,processId:row.processId,auditUserId:row.auditUserId,funType:2 } })
  }
}

// 审批详情按钮
function detailRow(row){
  router.push({ path: '/pages/processDetail', query: { bookId: row.bookId,masterFlag:row.masterFlag,bookOrganize:row.bookOrganize,stepId:row.stepId,processId:row.processId,auditUserId:row.auditUserId,state:row.state,funType:3 } })
}

// 查询审批节点列表  school_flag = 1是校本教材
function getApproveNodeList() {

  const param = {
    schoolFlag: queryData.value.bookOrganize == 2 ? 1 : '',
  }
  listStepNotPage(param).then((res) => {
    stepList.value = res.data;
  });
}

function filteredList(items){
  return items.slice(1); // 移除第一个元素
}

// 公开/校本教材切换
function bookTypeChange(val){
  if (val == 1) {
    displayColumns.value = ['bookNo', 'bookName', 'isbn','issn','stepName','state','createBy','createTime','dealUserName','versionNo','masterFlag','recordNo','operation']
  }else{
    displayColumns.value = ['bookNo', 'bookName','stepName','state','createBy','createTime','dealUserName','versionNo','masterFlag','operation']
  }
  queryData.value.bookOrganize = val
  getList();
  getApproveNodeList();
}

// 关闭审批弹窗
async function closeModel(){
  approvalVisible.value = false;
  getList();
  // window.location.reload();
}

function getUser(){
  getInfo().then((res) => {
    currentUserId.value = res.user.userId;
  })
}

function initData(){
  getUser();
  getList();
  getApproveNodeList();
}

onMounted(() => {
  selectType.value = route.query.bookOrganize == 2 ? '2' : '1';
  queryData.value.bookOrganize = selectType.value;
  bookTypeChange(selectType.value);
  initData();
})
</script>

<style scoped lang="less">
.t-radio-group.t-radio-group--filled {
    border-color: var(--td-bg-color-component);
    padding: 6px;
    border-radius: 8px;
    background-color: #E8F4FE;
    position: relative;
}

.t-radio-group.t-radio-group--primary-filled .t-radio-button.t-is-checked {
    color: #0966B4;
    background-color: #fff;
}
.search-form {
  margin: 20px 0;
}
</style>