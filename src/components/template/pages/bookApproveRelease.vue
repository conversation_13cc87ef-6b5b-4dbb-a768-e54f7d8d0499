<template>
  <div>
    <div class="book-approve-header">
      <div class="book-approve-header-left">
        <t-link theme="primary" @click="handleBack">返回</t-link>
      </div>
      <div class="book-approve-header-node">
        <span>当前节点:</span
        ><span style="margin-left: 10px">{{ stepName }}</span>
      </div>
    </div>
    <div style="margin-top: 20px">
      <t-button size="medium" theme="primary" @click="openApproval"
        >审批</t-button
      >
    </div>
  </div>
  <t-space style="margin-top: 20px" direction="vertical" size="large">
    <t-tabs v-model="stepValue" @change="handleTabsChange">
      <t-tab-panel :value="1" label="基本信息" />
      <t-tab-panel :value="2" label="教材简介" />
      <t-tab-panel :value="3" label="团队成员" />
      <t-tab-panel :value="4" label="章节目录" />
    </t-tabs>
  </t-space>
  <!-- 基本信息-->
  <div v-if="stepValue == 1" class="mt30">
    <t-form
      ref="bookRef"
      :data="form"
      :labt-width="100"
      :rules="rules"
      @submit="onSubmit"
    >
      <t-row class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="教材编号" name="bookNo">
            <span>{{ form.bookNo }}</span>
          </t-form-item>
        </t-col>
        <t-col :span="3" :offset="1">
          <t-form-item label="版本号" name="versionNo">
            <span>{{ form.versionNo }}</span>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="教材名称" name="bookName">
            <t-input
              v-model="form.bookName"
              mexlength="30"
              placeholder="请输入教材名称"
              :disabled="isAmend == 1"
            />
          </t-form-item>
        </t-col>
        <t-col :span="3" :offset="1">
          <t-form-item label="选题号" name="topicNo">
            <t-input
              v-model="form.topicNo"
              mexlength="20"
              placeholder="请输入选题号"
              :disabled="isAmend == 1"
            />
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="教材类型" name="bookOrganize">
            <span>{{ getOptionDesc(bookTypeList, form.bookOrganize) }}</span>
          </t-form-item>
        </t-col>
        <t-col class="mb20" :span="3" :offset="1">
          <t-form-item label="版次" name="edition">
            <t-input
              v-model="form.edition"
              maxlengh="10"
              :disabled="isAmend == 1"
              placeholder="请输入版次"
            />
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="主/副教材" name="masterFlag">
            <span>{{
              getOptionDesc(bookNatureTypeList, form.masterFlag)
            }}</span>
          </t-form-item>
        </t-col>
        <t-col class="mb20" :span="3" :offset="1">
          <t-form-item label="出版单位" name="houseId">
            <t-select v-model="form.houseId" placeholder="出版单位" :disabled="isAmend == 1" filterable>
              <t-option
                v-for="item in publishHouselList"
                :key="item.houseId"
                :label="item.houseName"
                :value="item.houseId"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row v-if="form.masterFlag != 1" class="mb20" :gutter="16">
        <t-col v-if="form.masterFlag == 3" :span="3">
          <t-form-item label="主教材" name="masterBookId">
            <span v-if="!form.masterBookId" style="color: #999999"
              >请搜索主教材</span
            >
            <t-tag
              v-else
              closable
              :disable-transitions="false"
              @close="handleBookClose(null, 3)"
              >{{ '(' + form.masterBookNo + ')' + form.masterBookName }}
            </t-tag>
          </t-form-item>
        </t-col>
        <t-col v-if="form.masterFlag == 2" class="mb20" :span="3">
          <t-form-item label="副教材" name="deputyBookList">
            <span
              v-if="
                !form.deputyBookList ||
                (form.deputyBookList && !form.deputyBookList.length)
              "
              style="color: #999999"
              >请搜索副教材（可多选）</span
            >
            <div v-else>
              <t-tag
                v-for="item in form.deputyBookList"
                :key="item.bookId"
                style="margin-right: 5px"
                closable
                :disable-transitions="false"
                @close="handleBookClose(item, 2)"
                >{{ '(' + item.bookNo + ')' + item.bookName }}
              </t-tag>
            </div>
          </t-form-item>
        </t-col>

        <t-col :span="3" :offset="1" v-if="isAmend != 1">
          <t-form-item label="搜索">
            <div
              style="display: flex; align-items: center; flex-direction: row"
            >
              <t-input
                v-model="form.searchBookNo"
                maxlength="20"
                placeholder="请输入教材编号"
              />
              <t-button
                style="margin-left: 10px"
                theme="primary"
                @click="searchBook"
                >搜索
              </t-button>
            </div>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row v-if="form.bookOrganize == 1" class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="ISBN" name="isbn">
            <t-input
              v-model="form.isbn"
              maxlength="20"
              :disabled="form.masterFlag == 3||isAmend == 1"
              placeholder="请输入ISBN"
            />
          </t-form-item>
        </t-col>
        <t-col class="mb20" :span="3" :offset="1">
          <t-form-item label="ISSN" name="issn">
            <t-input
              v-model="form.issn"
              maxlength="20"
              :disabled="form.masterFlag == 3||isAmend == 1"
              placeholder="请输入ISSN"
            />
          </t-form-item>
        </t-col>
      </t-row>

      <t-row v-if="form.bookOrganize == 1" class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="定价" name="priceCounter">
            <t-input-number
              v-model.number="form.priceCounter"
              style="width: 100%"
              placeholder="请输入定价"
              :disabled="isAmend == 1"
              step-strictly
              :min="0"
              :precision="2"
              :step="0.01"
              :max="999999999"
            />
          </t-form-item>
        </t-col>
        <t-col class="mb20" :span="3" :offset="1">
          <t-form-item label="售价" name="priceSale">
            <t-input-number
              v-model="form.priceSale"
              style="width: 100%"
              placeholder="请输入售价"
              step-strictly
              :min="0"
              :precision="2"
              :step="0.01"
              :max="999999999"
              :disabled="form.masterFlag == 3||isAmend == 1"
            />
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="出版时间" name="publishDate">
            <t-date-picker
              v-model="form.publishDate"
              style="width: 100%"
              :disabled="isAmend == 1"
              type="date"
              clearable
              value-format="YYYY-MM-DD"
              placeholder="请选择出版时间"
            >
            </t-date-picker>
          </t-form-item>
        </t-col>
        <t-col class="mb20" :span="3" :offset="1">
          <t-form-item label="学校" name="schoolIdList">
            <t-select
              v-model="form.schoolIdList"
              placeholder="请选择学校（可多选）"
              clearable
              multiple
              filterable
              :disabled="form.masterFlag == 3||isAmend == 1"
              :multiple-limit="10"
            >
              <t-option
                v-for="item in schoolList"
                :key="item.id"
                :label="item.schoolName"
                :value="item.id"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="中图分类" name="bookType">
            <t-tree-select
              v-if="deptOptions.length > 0"
              v-model="form.bookType"
              placeholder="请选择中图分类"
              :disabled="form.masterFlag == 3||isAmend == 1"
              :data="typeList"
              :popup-props="popupProps"
              :tree-props="treeProps"
            />
          </t-form-item>
        </t-col>
        <t-col class="mb20" :span="3" :offset="1">
          <t-form-item label="教育学科分类" name="subjectId">
            <t-tree-select
              v-model="form.subjectId"
              placeholder="请选择教育学科分类"
              :data="eduSubjectList"
              :disabled="form.masterFlag == 3||isAmend == 1"
              :tree-props="{
                keys: {
                  value: 'subjectId',
                  label: 'subjectName',
                  children: 'children',
                },
              }"
            />
          </t-form-item>
        </t-col>
      </t-row>
      <t-row class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="专区分类" name="areaIdList">
            <t-select
              v-model="form.areaIdList"
              :disabled="isAmend == 1"
              placeholder="请选择专区分类（可多选）"
              clearable
              multiple
              :multiple-limit="5"
            >
              <t-option
                v-for="item in bookAreaList"
                :key="item.areaId"
                :label="item.areaName"
                :value="item.areaId"
              />
            </t-select>
          </t-form-item>
        </t-col>
        <t-col class="mb20" :span="3" :offset="1">
          <t-form-item label="语种" name="languageId">
            <t-select
              :disabled="isAmend == 1"
              v-model="form.languageId"
              placeholder="请选择语种"
              clearable
            >
              <t-option
                v-for="item in languageList"
                :key="item.languageId"
                :label="item.languageName"
                :value="item.languageId"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item v-if="form.bookOrganize == 1" label="部门" name="deptId">
            <t-tree-select
              :disabled="isAmend == 1"
              v-model="form.deptId"
              :data="deptOptions"
              :tree-props="{
                keys: {
                  value: 'id',
                  label: 'label',
                  children: 'children',
                },
              }"
              value-key="id"
              placeholder="请选择归属部门"
              check-strictly
            />
          </t-form-item>
        </t-col>
        <t-col class="mb20" :span="3" :offset="1">
          <t-form-item name="authorLabel">
            <div
              style="display: flex; align-items: center; flex-direction: row"
            >
              <t-input v-model="form.authorLabel" :disabled="isAmend == 1" maxlengh="6"></t-input>
              :
              <t-input v-model="form.authorValue" :disabled="isAmend == 1" maxlengh="30"></t-input>
            </div>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="3">
          <t-form-item label="封面" name="cover">
            <t-upload
              v-model="imageList"
              :disabled="isAmend == 1"
              action="#"
              theme="image"
              :auto-upload="false"
              :show-image-file-name="false"
              :on-select-change="handleImageChange"
              :on-remove="handleImageRemove"
              :files="imageList"
              :show-upload-progress="false"
              :before-upload="() => false"
              accept="image/*"
            >
              <t-button theme="primary">选择图片</t-button>
              <template #tips>
                <div class="upload-tips">支持 jpg/png/gif 等图片格式</div>
              </template>
            </t-upload>
          </t-form-item>
        </t-col>
      </t-row>
      <t-form-item>
        <t-button v-if="isAmend != 1" theme="primary" type="submit">保存</t-button>
      </t-form-item>
    </t-form>
  </div>

  <!-- 教材简介-->
  <div v-if="stepValue == 2" class="mt30">
    <t-form
      ref="bookInfoRef"
      :data="form"
      labt-width="100px"
      :rules="rules"
      @submit="onSubmitAttribute"
    >
      <t-row class="mb20" :gutter="16">
        <t-col :span="6">
          <t-form-item label="版权信息" name="copyright">
            <t-textarea
              v-model="form.copyright"
              placeholder="请输入内容"
              :maxlength="5000"
              show-word-limit
              :autosize="{ minRows: 4, maxRows: 6 }"
            >
            </t-textarea>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="6">
          <t-form-item label="版权声明" name="declaration">
            <t-textarea
              v-model="form.declaration"
              placeholder="请输入内容"
              :maxlength="5000"
              show-word-limit
              :autosize="{ minRows: 4, maxRows: 6 }"
            >
            </t-textarea>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="6">
          <t-form-item label="编辑推荐" name="recommend">
            <t-textarea
              v-model="form.recommend"
              :maxlength="5000"
              show-word-limit
              :autosize="{ minRows: 4, maxRows: 6 }"
              placeholder="请输入内容"
            >
            </t-textarea>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="6">
          <t-form-item label="教材简介" name="introduce">
            <t-textarea
              v-model="form.introduce"
              :maxlength="5000"
              show-word-limit
              :autosize="{ minRows: 4, maxRows: 6 }"
              placeholder="请输入内容"
            >
            </t-textarea>
          </t-form-item>
        </t-col>
      </t-row>
      <t-form-item>
        <t-button theme="primary" type="submit">保存</t-button>
      </t-form-item>
    </t-form>
  </div>
  <!-- 团队成员-->
  <div v-if="stepValue == 3" class="mt30">
    <t-form :model="form" labt-width="120px">
      <div class="form-title">作者</div>
      <t-row class="mb20" :gutter="16">
        <t-col :span="16">
          <t-form-item label="书稿联系人" name="contact" required>
            <t-select
              v-model="form.contact"
              placeholder="请选择"
              style="width: 500px"
              :readonly="true"
              :show-arrow="false"
              :borderless="true"
            >
              <t-option
                v-for="item in userList"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>
      <t-row class="mb20" :gutter="16">
        <t-col :span="16">
          <t-form-item label="主编" name="editor">
            <t-select
              v-model="form.editor"
              placeholder="请选择"
              multiple
              :multiple-limit="10"
              style="width: 500px"
              :readonly="true"
              :show-arrow="false"
              :borderless="true"
            >
              <t-option
                v-for="item in userList"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="16">
          <t-form-item label="副主编" name="associateEditor">
            <t-select
              v-model="form.associateEditor"
              multiple
              placeholder="请选择"
              style="width: 500px"
              :multiple-limit="10"
              :readonly="true"
              :show-arrow="false"
              :borderless="true"
            >
              <t-option
                v-for="item in userList"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="16">
          <t-form-item label="参编" name="participateCompilation">
            <t-select
              v-model="form.participateCompilation"
              multiple
              placeholder="请选择"
              style="width: 500px"
              :multiple-limit="10"
              :readonly="true"
              :show-arrow="false"
              :borderless="true"
            >
              <t-option
                v-for="item in userList"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>
      <div class="form-title">编辑</div>
      <t-row class="mb20" :gutter="16">
        <t-col :span="16">
          <t-form-item label="策划编辑" name="planningEditor" required>
            <t-select
              v-model="form.planningEditor"
              placeholder="请选择"
              style="width: 500px"
              :readonly="true"
              :show-arrow="false"
              :borderless="true"
            >
              <t-option
                v-for="item in userList"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="16">
          <t-form-item label="责任编辑" name="editorInCharge">
            <t-select
              v-model="form.editorInCharge"
              placeholder="请选择"
              multiple
              style="width: 500px"
              :multiple-limit="10"
              :readonly="true"
              :show-arrow="false"
              :borderless="true"
            >
              <t-option
                v-for="item in userList"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb20" :gutter="16">
        <t-col :span="16">
          <t-form-item label="编校人员" name="proofreader">
            <t-select
              v-model="form.proofreader"
              placeholder="请选择"
              multiple
              style="width: 500px"
              :multiple-limit="10"
              :readonly="true"
              :showArrow="false"
              :borderless="true"
            >
              <t-option
                v-for="item in userList || []"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </t-col>
      </t-row>
    </t-form>
  </div>
  <!-- 章节目录-->
  <div v-if="stepValue == 4" class="mt30">
    <processChapter
      :book-id="bookId"
      :process-id="processId"
      :audit-user-id="auditUserId"
      :version-id="versionId"
      :step-id="stepId"
      :is-amend="isAmend"
      :master-flag="masterFlag"
      :book-organize="bookOrganize"
      :cur-user-permissions="curUserPermissions"
    />
  </div>
  <!-- 审批模态框 -->
  <modal
    v-if="approvalVisible"
    :visible="approvalVisible"
    :header="`审批`"
    :footer="false"
    width="850px"
    @close="approvalVisible = false"
  >
    <processForm
      v-if="approvalVisible"
      :book-id="bookId"
      :process-id="processId"
      :audit-user-id="auditUserId"
      :is-amend="isAmend"
      :version-id="versionId"
      :step-id="stepId"
      :book-organize="bookOrganize"
      @close-dialog="handleCloseDialog"
    ></processForm>
  </modal>
</template>

<script setup lang="ts">
import { MessagePlugin } from 'tdesign-vue-next'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { ListbookAreaNotPage } from '@/api/basic/bookArea.js'
import { listNoPage as listPublishHouse } from '@/api/basic/house.js'
import { listSchoolNoPage } from '@/api/basic/school.js'
import { listSubjectNotPage } from '@/api/basic/subject.js'
import { getBook, searchBookOneByBookNo, updateBook } from '@/api/book/book.js'
import {
  addBookAttribute,
  getBookAttribute,
  updateBookAttribute,
} from '@/api/book/bookAttribute.js'
import { getBookGroup } from '@/api/book/bookGroup.js'
import { getStepById } from '@/api/book/bookPublishStep.js'
import { listLanguage } from '@/api/book/language.js'
import { listType } from '@/api/book/type.js'
import { deptTreeSelect, listUserNotPage } from '@/api/system/user.js'
import { OssService } from '@/utils/aliOss'
import {
  bookNatureTypeList,
  bookTypeList,
  getOptionDesc,
} from '@/utils/quetions-utils'

import processChapter from '../components/processChapter.vue'
import processForm from '../components/processForm.vue'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const deptOptions = ref([])
const stepValue = ref(1)
const bookRef = ref(null)
const bookInfoRef = ref(null)
const bookId = ref()
const stepId = ref()
const auditUserId = ref()
const processId = ref()
const stepName = ref()
const masterFlag = ref()
const bookOrganize = ref()
const schoolList = ref([])
const publishSteplList = ref([])
const typeList = ref([])
const languageList = ref([])
const eduSubjectList = ref([])
const bookAreaList = ref([])
const publishHouselList = ref([])
const imageList = ref([])
const userList = ref([])
const approvalVisible = ref(false)
const versionId = ref()
const isAmend = ref();
// 1 从教材列表进入 2 代办中心进入
const funType = ref(2)
const curUserPermissions = ref({
  permissions: [],
  isEditor: false,
  isAuthor: false,
  bookId: null,
})
const data = reactive({
  form: {
    authorLabel: '作者',
    deputyBookList: [],
    deptId: '',
    masterFlag: masterFlag.value,
    bookOrganize: bookOrganize.value,
  },
  rules: {
    bookName: [
      { required: true, message: '教材名称不能为空', trigger: 'blur' },
    ],
    bookOrganize: [
      {
        required: true,
        message: '教材类型不能为空',
        trigger: 'blur',
      },
    ],
    houseId: [
      {
        required: true,
        message: '出版单位不能为空',
        trigger: 'change',
      },
    ],
    edition: [{ required: true, message: '版次不能为空', trigger: 'blur' }],
    masterFlag: [
      { required: true, message: '主/副教材不能为空', trigger: 'blur' },
    ],
    topicNo: [{ required: true, message: '选题号不能为空', trigger: 'blur' }],
    priceCounter: [
      { required: true, message: '定价不能为空', trigger: 'blur' },
    ],
    priceSale: [{ required: true, message: '售价不能为空', trigger: 'blur' }],
    publishDate: [
      { required: true, message: '出版时间不能为空', trigger: 'change' },
    ],
    schoolIdList: [
      {
        required: computed(() => !(form.value.masterFlag == 3 || isAmend.value == 1)),
        message: '学校不能为空',
        trigger: 'change'
      }
    ],
    bookType: [
      { required: computed(() => !(form.value.masterFlag == 3 || isAmend.value == 1)),
         message: '中图分类不能为空', trigger: 'change' },
    ],
    subjectId: [
      { required: computed(() => !(form.value.masterFlag == 3 || isAmend.value == 1)),
         message: '教育学科分类不能为空', trigger: 'change' },
    ],
    areaIdList: [
      { required: true, message: '专区分类不能为空', trigger: 'change' },
    ],
    languageId: [
      { required: true, message: '语种不能为空', trigger: 'change' },
    ],
    copyright: [
      { required: true, message: '版权信息不能为空', trigger: 'blur' },
    ],
    declaration: [
      { required: true, message: '版权声明不能为空', trigger: 'blur' },
    ],
    recommend: [
      { required: true, message: '编辑推荐不能为空', trigger: 'blur' },
    ],
    introduce: [
      { required: true, message: '教材简介不能为空', trigger: 'blur' },
    ],
    deptId: [{ required: true, message: '部门不能为空', trigger: 'change' }],
    cover: [{ required: true, message: '封面图片不能为空', trigger: 'change' }],
  },
})
const { queryParams, form, rules } = toRefs(data)

// 获取教材简介
async function getBookIntroduction(bookId) {
  const res = await getBookAttribute(bookId)
  form.value = res.data
}

// 获取教材信息
async function getBookInfo(bookId) {
  const response = await getBook(bookId)
  form.value = response.data
  form.value.versionNo = response.data.lastVersionNo || ''
  form.value.areaIdList = form.value.areaList.map((item) => item.areaId) || []
  form.value.schoolIdList =
    form.value.schoolList.map((item) => item.schoolId) || []
  form.value.subjectId = form.value.subject?.subjectId
  versionId.value = form.value.lastVersionId
  console.log(versionId.value)
  isAmend.value = response.data.lastVersionId != response.data.versionId ? 1 : 0 // 是否是修正数据 1 是 0 否

  // 封面图片
  const { cover } = form.value
  // 首先将值转为数组
  let list = []
  if (cover) {
    list = Array.isArray(cover) ? cover : cover.split(',')
  }
  // 然后将数组转为对象数组
  imageList.value = list.map((item) => {
    if (typeof item === 'string') {
      item = { name: item, url: item }
    } else {
      // 传入对象
      item = {
        name: item[props.nameKey],
        url: item[props.urlKey],
      }
    }
    return item
  })
}

// 获取学校
function getSchoolList() {
  listSchoolNoPage().then((res) => {
    schoolList.value = res.data
  })
}

// 获取中图分类
function getTypeList() {
  listType().then((res) => {
    typeList.value = res.data
  })
}

// 获取教育学科分类
function getEduSubjectList() {
  listSubjectNotPage().then((res) => {
    eduSubjectList.value = res.data
  })
}

// 获取专区分类
function getBookAreaList() {
  ListbookAreaNotPage().then((res) => {
    bookAreaList.value = res.data
  })
}

// 获取语种
function getLanguageList() {
  listLanguage().then((res) => {
    languageList.value = res.data
  })
}

// 获取出版社列表
function getPublishHouseList() {
  listPublishHouse().then((res) => {
    publishHouselList.value = res.data
  })
}

// 主副教材切换事件
function handleBookMasterFlagChange(masterFlag) {
  form.value.deputyBookList = []
  form.value.masterBookId = null
  form.value.masterBookName = null
  form.value.masterBookNo = null
  form.value.bookType = null
  form.value.bookTypeName = null
  form.value.areaList = []
  form.value.schoolList = []
  form.value.areaIdList = []
  form.value.schoolIdList = []
  form.value.subject = null
  form.value.subjectId = null
  form.value.topSubjectId = null
  form.value.thirdSubjectId = null
  form.value.secondSubjectId = null
  form.value.forthSubjectId = null
  form.value.priceSale = null
  form.value.isbn = null
  form.value.issn = null
}

// tag关闭事件
function handleBookClose(item, masterFlag) {
  if (masterFlag == 2) {
    // 主教材
    form.value.deputyBookList = form.value.deputyBookList.filter(
      (o) => o.bookId != item.bookId,
    )
  } else if (masterFlag == 3) {
    // 副教材
    form.value.masterBookId = null
    form.value.masterBookName = null
    form.value.masterBookNo = null
    form.value.bookType = null
    form.value.bookTypeName = null
    form.value.areaList = []
    form.value.schoolList = []
    form.value.areaIdList = []
    form.value.schoolIdList = []
    form.value.subject = null
    form.value.subjectId = null
    form.value.topSubjectId = null
    form.value.thirdSubjectId = null
    form.value.secondSubjectId = null
    form.value.forthSubjectId = null
    form.value.priceSale = null
    form.value.isbn = null
    form.value.issn = null
  }
}

// 搜索教材
function searchBook() {
  if (
    form.value.masterFlag == 2 &&
    form.value.deputyBookList &&
    form.value.deputyBookList.length >= 5
  ) {
    MessagePlugin.error('副教材不能超过5本')
    return
  }
  searchBookOneByBookNo({
    masterFlag: form.value.masterFlag,
    bookNo: form.value.searchBookNo,
    bookOrganize: form.value.bookOrganize,
  }).then((res) => {

    if (res.data) {
      if (form.value.masterFlag == 2) {
        // 主教材
        form.value.deputyBookList.push(res.data)
      } else if (form.value.masterFlag == 3) {
        // 副教材
        const masterBook = res.data
        form.value.masterBookId = masterBook.bookId
        form.value.masterBookName = masterBook.bookName
        form.value.masterBookNo = masterBook.bookNo
        form.value.bookType = masterBook.bookType
        form.value.bookTypeName = masterBook.bookTypeName
        form.value.areaList = masterBook.areaList
        form.value.areaIdList = masterBook.areaList?.map((o) => o.areaId)
        form.value.schoolList = masterBook.schoolList
        form.value.schoolIdList = masterBook.schoolList?.map((o) => o.schoolId)
        form.value.subject = masterBook.subject
        form.value.subjectId = masterBook.subject.subjectId
        form.value.topSubjectId = masterBook.topSubjectId
        form.value.thirdSubjectId = masterBook.thirdSubjectId
        form.value.secondSubjectId = masterBook.secondSubjectId
        form.value.forthSubjectId = masterBook.forthSubjectId
        form.value.priceSale = 0
        form.value.isbn = masterBook.isbn
        form.value.issn = masterBook.issn
      }
    } else {
      MessagePlugin.error('未查询到教材')
    }
  })
}

const popupProps = {
  overlayStyle: {
    width: '500px',
  },
}

const treeProps = {
  keys: {
    label: 'typeName',
    value: 'typeId',
    children: 'children',
  },
}

function handleImageRemove(file) {
  // 删除图片
  const index = imageList.value.findIndex((item) => item.url === file.url)
  if (index !== -1) {
    imageList.value.splice(index, 1)
  }
  form.value.cover = ''
}

// 图片相关处理函数ch
async function handleImageChange(files) {
  // files是一个数组,包含新添加的文件
  for (const file of files) {
    try {
      // 验证文件类型和大小
      const isImage = file.type.startsWith('image/')
      const isValidSize = file.size <= 5 * 1024 * 1024 // 5MB

      if (!isImage) {
        MessagePlugin.error(`${file.name} 不是图片文件`)
        continue
      }

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过5MB`)
        continue
      }

      // 创建本地预览URL
      const localUrl = URL.createObjectURL(file)

      // 创建新的图片文件对象
      const imageFile = {
        name: file.name,
        url: localUrl,
        file,
        uploadProgress: 0,
      }

      // 添加到图片列表
      imageList.value.push(imageFile)

      // 上传到OSS并获取URL
      const ossUrl = await OssService(file, {
        progressCallback: (progress) => {
          // 更新特定文件的上传进度
          const index = imageList.value.findIndex(
            (item) => item.name === file.name,
          )
          if (index !== -1) {
            imageList.value[index].uploadProgress = Math.floor(progress * 100)
          }
        }
      })

      // 更新图片文件的URL为OSS URL
      const index = imageList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        imageList.value[index].ossUrl = ossUrl.url
        imageList.value[index].uploadProgress = 100
        form.value.cover = imageList.value[index].ossUrl
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      // 从列表中移除失败的文件
      const index = imageList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        imageList.value.splice(index, 1)
      }
    }
  }
}

function removeImage(index) {
  // 释放本地预览URL
  if (imageList.value[index].url) {
    URL.revokeObjectURL(imageList.value[index].url)
  }
  imageList.value.splice(index, 1)
}
// 查询部门下拉树结构
function getDeptTree() {
  deptTreeSelect().then((response) => {
    deptOptions.value = response.data
  })
}

function onSubmit({ validateResult, firstError }) {
  if (validateResult === true) {
    if ((form.value.masterFlag != 3 && isAmend.value != 1) && (!form.value.isbn && !form.value.issn && form.value.bookOrganize == 1)) {
      MessagePlugin.error('ISSN或ISBN必须填写一个')
      return
    }
    if (form.value.masterFlag == 2 && form.value.deputyBookList.length == 0) {
      MessagePlugin.error('副教材不能为空')
      return
    } else if (form.value.masterFlag == 3 && !form.value.masterBookId) {
      MessagePlugin.error('主教材不能为空')
      return
    }
    if (!form.value.authorLabel || !form.value.authorValue) {
      MessagePlugin.error('自定义字段必须填写完整')
      return
    }

    // 基础信息
    form.value.areaList = form.value.areaIdList.map((item) => {
      return {
        areaId: item,
      }
    })
    form.value.schoolList = form.value.schoolIdList.map((item) => {
      return {
        schoolId: item,
      }
    })
    if (
      form.value.priceSale &&
      form.value.priceCounter &&
      form.value.priceSale > form.value.priceCounter
    ) {
      MessagePlugin.error('售价不能大于定价')
      return
    }
    // 获取四级教育学科分类
    if (form.value.subjectId) {
      for (const item1 of eduSubjectList.value) {
        if (item1.subjectId == form.value.subjectId) {
          form.value.topSubjectId = item1.subjectId
          break
        }
        for (const item2 of item1.children) {
          if (item2.subjectId == form.value.subjectId) {
            form.value.topSubjectId = item1.subjectId
            form.value.secondSubjectId = item2.subjectId
            break
          }
          for (const item3 of item2.children) {
            if (item3.subjectId == form.value.subjectId) {
              form.value.topSubjectId = item1.subjectId
              form.value.secondSubjectId = item2.subjectId
              form.value.thirdSubjectId = item3.subjectId
              break
            }
            for (const item4 of item3.children) {
              if (item4.subjectId == form.value.subjectId) {
                form.value.topSubjectId = item1.subjectId
                form.value.secondSubjectId = item2.subjectId
                form.value.thirdSubjectId = item3.subjectId
                form.value.forthSubjectId = item4.subjectId
                break
              }
            }
          }
        }
      }
    }
    form.value.bookId = bookId.value


    updateBook(form.value).then((response) => {
      MessagePlugin.success('修改成功')
    })
  }
}

// 获取用户列表
function getUserList() {
  listUserNotPage().then((res) => {
    userList.value = res.data || []
  })
}

// 获取团队信息
async function getBookUserInfo() {
  const res = await getBookGroup(bookId.value)
  form.value.contact = res.data.contact || ''
  form.value.editor = res.data.editor || []
  form.value.associateEditor = res.data.associateEditor || []
  form.value.participateCompilation = res.data.participateCompilation || []
  form.value.editorInCharge = res.data.editorInCharge || []
  form.value.planningEditor = res.data.planningEditor || ''
  form.value.proofreader = res.data.proofreader || [];
}

function onSubmitAttribute({ validateResult, firstError }) {
  if (validateResult === true) {
    // 教材简介
    if (form.value.attributeId != null) {
      updateBookAttribute(form.value).then((response) => {
        MessagePlugin.success('修改成功')
      })
    } else {
      addBookAttribute(form.value).then((response) => {
        form.value.attributeId = response.data
        MessagePlugin.success('修改成功')
      })
    }
  }
}

// 获取当前节点
function getStepInfo() {
  getStepById(stepId.value).then((res) => {
    stepName.value = res.data.stepName
  })
}

// 页签改变时
function handleTabsChange(tabsValue) {
  if (tabsValue == '1') {
    getBookInfo(bookId.value)
  } else if (tabsValue == '2') {
    getBookIntroduction(bookId.value)
  } else if (tabsValue == '3') {
    getBookUserInfo()
  }
}

// 打开审批弹窗
async function openApproval() {
  stepValue.value = 1
  await getBookInfo(bookId.value)
  bookRef.value.validate().then((result) => {
    if (result == true) {
      if ((form.value.masterFlag != 3 && isAmend.value != 1) && (!form.value.isbn && !form.value.issn && form.value.bookOrganize == 1)) {
        MessagePlugin.error('ISSN或ISBN必须填写一个')
        return
      }
      if (form.value.masterFlag == 2 && form.value.deputyBookList.length == 0) {
        MessagePlugin.error('副教材不能为空')
        return
      } else if (form.value.masterFlag == 3 && !form.value.masterBookId) {
        MessagePlugin.error('主教材不能为空')
        return
      }
      if (!form.value.authorLabel || !form.value.authorValue) {
        MessagePlugin.error('自定义字段必须填写完整')
        return
      }
      approvalVisible.value = true
    } else {
      MessagePlugin.error('请填写全部信息并保存后再进行审批')
    }
  })
}

// 关闭审批弹窗
function handleCloseDialog() {
  approvalVisible.value = false
}

// 返回
function handleBack() {
  if (funType.value == 1) {
    router.push({
      path: '/pages/detail',
      query: { bookId: bookId.value },
    })
  } else if (funType.value == 2) {
    router.push({ path: '/pages/backup',
      query: { bookOrganize: bookOrganize.value },
    })
  }
}

// 页面初始化
async function initData() {
  bookId.value = route.query.bookId
  processId.value = route.query.processId
  masterFlag.value = route.query.masterFlag
  bookOrganize.value = route.query.bookOrganize
  stepId.value = route.query.stepId
  auditUserId.value = route.query.auditUserId
  funType.value = route.query.funType || 2
  await getBookInfo(bookId.value)
  getDeptTree()
  getPublishHouseList()
  getSchoolList()
  getTypeList()
  getEduSubjectList()
  getBookAreaList()
  getLanguageList()
  getBookUserInfo()
  getUserList()
  getStepInfo()
  stepValue.value = route.query.stepValue || 1
}

initData()
</script>

<style scoped lang="less">
.mb20 {
  margin-bottom: 20px;
}

.mt30 {
  margin-top: 30px;
}

.form-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #666;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background-color: #409eff;
    border-radius: 8px;
    margin-right: 8px;
  }
}
.book-approve-header {
  width: 100%;
  height: 30px;
  display: flex;
  .book-approve-header-left {
    width: 60px;
    height: 30px;
  }
  .book-approve-header-node {
    width: 200px;
    height: 30px;
  }
}
.footerBtn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
