<template>
  <div>
    <div class="search-form">
      <t-form
        ref="searchForm"
        @reset="resetQuery"
        :data="queryParams"
        :label-width="120"
        layout="inline"
      >
        <t-form-item label="教材名称" name="bookName">
          <t-input
            v-model="queryParams.bookName"
            placeholder="请输入教材名称"
            clearable
            maxlength="30"
            style="width: 200px"
          ></t-input>
        </t-form-item>
        <t-form-item label="教材编号" name="bookNo">
          <t-input
            v-model="queryParams.bookNo"
            placeholder="请输入教材编号"
            clearable
            maxlength="20"
            style="width: 200px"
          ></t-input>
        </t-form-item>
        <t-form-item label="ISBN" name="isbn">
          <t-input
            v-model="queryParams.isbn"
            placeholder="请输入ISBN"
            clearable
            maxlength="40"
            style="width: 200px"
          ></t-input>
        </t-form-item>
        <t-form-item label="ISSN" name="issn">
          <t-input
            v-model="queryParams.issn"
            placeholder="请输入ISSN"
            clearable
            maxlength="40"
            style="width: 200px"
          ></t-input>
        </t-form-item>
        <t-form-item label="教材类型" name="bookOrganize">
          <t-select
            v-model="queryParams.bookOrganize"
            clearable
            filterable
            placeholder="全部"
            style="width: 200px"
          >
            <t-option :value="1" label="公开教材"></t-option>
            <t-option :value="2" label="校本教材"></t-option>
          </t-select>
        </t-form-item>

        <t-form-item label="章节名称" name="chapterName">
          <t-input
            v-model="queryParams.chapterName"
            placeholder="请输入章节名称"
            clearable
            maxlength="40"
            style="width: 200px"
          ></t-input>
        </t-form-item>

        <t-form-item label="状态" name="chapterStatus">
          <t-select
            v-model="queryParams.chapterStatus"
            clearable
            style="width: 200px"
          >
            <t-option
              :value="item.value"
              :label="item.label"
              v-for="item in chapterAuditStatusList"
              :key="item.value"
              >{{ item.label }}</t-option
            >
          </t-select>
        </t-form-item>
        <t-form-item label="节点" name="auditType">
          <t-select
            v-model="queryParams.auditType"
            clearable
            style="width: 200px"
          >
            <t-option :value="1" label="章节提交"></t-option>
            <t-option :value="2" label="章节撤销申请"></t-option>
          </t-select>
        </t-form-item>
        <t-form-item>
          <t-space size="small">
            <t-button theme="primary" @click="handleQuery">搜索</t-button>
            <t-button theme="default" variant="base" type="reset"
              >重置</t-button
            >
          </t-space>
        </t-form-item>
      </t-form>
    </div>

    <div class="table">
      <t-table
        :columns="columns"
        :data="dataList"
        row-key="chapterId"
        lazy-load
        :pagination="queryParams"
        @page-change="onPageChange"
      >
        <template #auditType="{ row }">
          <t-tag v-if="row.auditType === 1">章节提交</t-tag>
          <t-tag v-if="row.auditType === 2">章节撤销申请</t-tag>
        </template>

        <template #chapterStatus="{ row }">
          <t-tag>{{
            getOptionDesc(chapterAuditStatusList, row.chapterStatus)
          }}</t-tag>
        </template>
        <template #bookOrganize="{ row }">
          <t-tag v-if="row.bookOrganize == 1"> 公开教材</t-tag>
          <t-tag v-if="row.bookOrganize == 2">校本教材</t-tag>
        </template>
        <template #opt="{ row }">
          <t-button
            variant="text"
            v-if="row.chapterStatus === 1"
            @click="handleAudit(row)"
            >审批</t-button
          >
          <t-button variant="text" theme="primary" @click="handleAuditList(row)"
            >审批记录</t-button
          >
        </template>
      </t-table>
    </div>
    <Modal
      v-model:visible="auditVisible"
      header="审批记录"
      width="800px"
      :confirm-btn="null"
      :cancel-btn="null"
    >
      <div>
        <div>教材名称: {{ form.bookName }}</div>
        <div>章节名称: {{ form.chapterName }}</div>
        <div class="approve-list">
          <div
            class="approve-item"
            v-for="item in auditHistoryList"
            :key="item.logId"
          >
            <div class="approve-item-header">
              <div>
                <span>当前节点:</span
                ><t-tag v-if="item.auditType === 1">章节提交</t-tag>
                <t-tag v-if="item.auditType === 2">章节撤销申请</t-tag>
              </div>
              <div><span>发起人:</span>{{ item.nickName }}</div>

              <div><span>发起时间:</span>{{ item.createTime }}</div>
            </div>
            <div class="approve-item-content">
              <div class="approve-item-content-header">
                <div>
                  <span>审批状态：</span>
                  <t-tag>{{
                    getOptionDesc(chapterAuditStatusList, item.chapterStatus)
                  }}</t-tag>
                </div>
                <div><span>审批人:</span>{{ item.auditNickName }}</div>
                <div><span>审批时间:</span>{{ item.auditTime }}</div>
              </div>
              <div class="approve-item-content-body">
                <div><span>审批意见:</span>{{ item.remark }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup name="chapter">
import { ref } from 'vue'
import { chapterAuditStatusList } from '@/utils/quetions-utils'
import approveList from '../components/approveList.vue'
import {
  chapterAuditLogList,
  chapterAuditHistoryList,
} from '@/api/book/bookChapterAuditLog'
import {
  bookNatureTypeList,
  bookShelfStatusOptions,
  getOptionDesc,
  bookTypeList,
  bookPublishStatusOptions,
  bookSchoolStatusOptions,
} from '@/utils/optionUtil.js'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const queryParams = ref({
  current: 1,
  pageSize: 10,
  total: 10,
})
const auditVisible = ref(false)
const listShow = ref(false)
const auditHistoryList = ref([])
const form = ref({})
const columns = [
  {
    colKey: 'chapterName',
    title: '章节名称',
    width: 150,
    ellipsis: true,
    fixed: 'left',
  },
  {
    colKey: 'bookNo',
    title: '教材编号',
    width: 150,
    ellipsis: true,
    fixed: 'left',
  },
  {
    colKey: 'bookName',
    title: '教材名称',
    width: 150,
    ellipsis: true,
    fixed: 'left',
  },
  { colKey: 'auditType', width: 120, ellipsis: true, title: '节点' },
  { colKey: 'chapterStatus', width: 110, ellipsis: true, title: '状态' },
  { colKey: 'nickName', width: 110, ellipsis: true, title: '发起人' },
  { colKey: 'createTime', width: 200, ellipsis: true, title: '发起时间' },
  { colKey: 'isbn', width: 200, ellipsis: true, title: 'ISBN' },
  { colKey: 'issn', width: 200, ellipsis: true, title: 'ISSN' },
  { colKey: 'bookOrganize', width: 110, ellipsis: true, title: '教材类型' },
  {
    colKey: 'opt',
    width: 200,
    ellipsis: true,
    title: '操作',
    fixed: 'right',
    cell: 'ellipsis',
  },
]
const dataList = ref([])

//#region 生命周期相关

onMounted(() => {
  getList()
})

//#endregion

//#region 获取数据相关

const getList = () => {
  chapterAuditLogList(queryParams.value).then((res) => {
    dataList.value = res.rows
    queryParams.value.total = res.total
  })
}
//#endregion

//#region 操作相关

// 分页变化
const onPageChange = (pageInfo) => {
  queryParams.value.pageSize = pageInfo.pageSize
  queryParams.value.pageNum = pageInfo.current
  queryParams.value.current = pageInfo.current
  getList()
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.value.pageNum = 1
  queryParams.value.current = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  handleQuery()
}

// 跳转审核
const handleAudit = (row) => {
  router.push({
    path: '/pages/approvePage',
    query: {
      bookId: row.bookId,
      chapterId: row.chapterId,
      logId: row.logId,
      chapterName: row.chapterName,
      auditType: row.auditType,
      revokedReason: row.revokedReason || '',
    },
  })
}

// 打开审核列表
const handleAuditList = (row) => {
  form.value = {
    bookName: row.bookName,
    chapterName: row.chapterName,
  }
  chapterAuditHistoryList(row.chapterId).then((res) => {
    auditHistoryList.value = res.data
    auditVisible.value = true
  })
}

// 关闭审核列表
const closeShow = () => {
  listShow.value = false
}
//#endregion
</script>

<style lang="less" scoped>
.search-form {
  margin: 20px 0;
}
.approve-list {
  margin-top: 10px;
  .approve-item {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    .approve-item-header {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 10px;
      background-color: #eee;
      border-bottom: 1px solid #ddd;
      padding: 10px;
      border-radius: 5px 5px 0 0;
      span {
        color: #999;
        margin-right: 5px;
      }
    }

    .approve-item-content {
      padding: 20px;
      .approve-item-content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        span {
          color: #999;
          margin-right: 5px;
        }
      }

      .approve-item-content-body {
        span {
          color: #999;
          margin-right: 5px;
        }
      }
    }
  }
}
</style>
