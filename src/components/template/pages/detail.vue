<template>
  <div class="app-container">
    <div class="back-icon" @click="handleBack"><ChevronLeftIcon /> 返回</div>

    <div class="book-info-container" :style="isShow ? '' : 'margin-bottom:0;'">
      <div class="book-info-left" :style="isShow ? '' : 'height:auto;'">
        <img
          v-if="isShow"
          :src="
            bookInfo.cover ||
            'http://dutp-test.oss-cn-beijing.aliyuncs.com/1741232140222.png'
          "
          alt="Book Cover"
          @click="handleUpdateCover"
        />

        <!-- <img
          src="../../../assets/icons/twitch.svg"
          style="width: 20px; height: 20px; color: red"
        /> -->
      </div>
      <div class="book-info-right" :style="isShow ? '' : 'margin-left:-200px'">
        <div class="book-info-title">
          <div class="book-info-title-left">
            <h2>{{ bookInfo.bookName }}</h2>
            <div class="book-info-tags">
              <t-tag
                v-if="bookInfo.bookOrganize == 1"
                type="info"
                color="#FBEFDA"
                style="margin-right: 8px; color: #e37318"
                >公开教材</t-tag
              >
              <t-tag
                v-if="bookInfo.bookOrganize == 2"
                type="info"
                color="#E5DBFF"
                style="margin-right: 8px; color: #4800ff"
                >校本教材</t-tag
              >

              <t-tag
                v-if="bookInfo.bookOrganize == 1 && bookInfo.publishStatus == 1"
                type="info"
                color="#EAEEF2"
                style="margin-right: 8px; color: #007dff"
                >未出版</t-tag
              >
              <t-tag
                v-if="bookInfo.bookOrganize == 1 && bookInfo.publishStatus == 2"
                type="info"
                color="#FFEFD8"
                style="margin-right: 8px; color: #ff9600"
                >已出版</t-tag
              >
              <t-tag
                v-if="bookInfo.bookOrganize == 2 && bookInfo.publishStatus == 1"
                type="info"
                color="#FFEFD8"
                style="margin-right: 8px; color: #ff9600"
                >已创建</t-tag
              >
              <t-tag
                v-if="bookInfo.bookOrganize == 2 && bookInfo.publishStatus == 2"
                type="info"
                color="#DEF0FF"
                style="margin-right: 8px; color: #008bff"
                >已完成</t-tag
              >

              <t-tag
                v-if="bookInfo.bookOrganize == 1 && bookInfo.shelfState == 1"
                type="info"
                color="#EAEEF1"
                style="margin-right: 8px; color: #008ceb"
                >已上架</t-tag
              >
              <t-tag
                v-if="bookInfo.bookOrganize == 1 && bookInfo.shelfState == 2"
                type="info"
                color="#EAEEF1"
                style="margin-right: 8px; color: #008ceb"
                >待上架</t-tag
              >
              <t-tag
                v-if="bookInfo.bookOrganize == 1 && bookInfo.shelfState == 3"
                type="info"
                color="#EAEEF1"
                style="margin-right: 8px; color: #008ceb"
                >已召回</t-tag
              >
              <t-tag
                v-if="bookInfo.bookOrganize == 1 && bookInfo.shelfState == 4"
                type="info"
                color="#E4F1ED"
                style="margin-right: 8px; color: #00eba4"
                >即将上架</t-tag
              >
            </div>
          </div>
          <div v-if="!isShow" class="book-info-title-right">
            <div class="book-info-title-btn">
              <t-button variant="outline" @click="showToggle">
                <template #icon>
                  <ChevronDownDoubleIcon />
                </template>
              </t-button>
            </div>
          </div>
        </div>
        <div v-if="isShow" class="book-info">
          <div v-if="bookInfo.bookOrganize == 1" class="book-info-price">
            <div class="book-info-price-discount">{{ bookInfo.priceSale }}</div>
            <div class="book-info-price-original">
              {{ bookInfo.priceCounter }}
            </div>
          </div>
          <div class="book-info-description">
            <div class="book-info-description-left">
              <div class="book-info-description-item">
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">教材编号</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.bookNo
                  }}</span>
                </div>
                <div
                  v-if="
                    (bookInfo.isbn || !bookInfo.issn) &&
                    bookInfo.bookOrganize == 1
                  "
                  class="book-info-description-item-panel"
                >
                  <span class="book-info-description-item-label">ISBN</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.isbn
                  }}</span>
                </div>
                <div
                  v-if="bookInfo.issn && bookInfo.bookOrganize == 1"
                  class="book-info-description-item-panel"
                >
                  <span class="book-info-description-item-label">ISSN</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.issn
                  }}</span>
                </div>
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">版次</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.edition
                  }}</span>
                </div>
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">版本号</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.versionNo
                  }}</span>
                </div>
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">选题号</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.topicNo
                  }}</span>
                </div>
              </div>
              <div class="book-info-description-item">
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">主/副教材</span
                  ><span class="book-info-description-item-value">{{
                    getOptionDesc(bookNatureTypeList, bookInfo.masterFlag)
                  }}</span>
                </div>
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">语种</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.languageName
                  }}</span>
                </div>
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label"
                    >出版社单位</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.houseName
                  }}</span>
                </div>
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">出版时间</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.publishDate
                  }}</span>
                </div>
                <div class="book-info-description-item-panel">
                  <span class="book-info-description-item-label">创建时间</span
                  ><span class="book-info-description-item-value">{{
                    bookInfo.createTime
                  }}</span>
                </div>
              </div>
            </div>
            <div class="book-info-description-right">
              <div class="book-info-description-item-panel">
                <span class="book-info-description-item-label">学校</span
                ><span class="book-info-description-item-value">{{
                  bookInfo.schoolName
                }}</span>
              </div>

              <div class="book-info-description-item-panel">
                <span class="book-info-description-item-label">关联教材</span
                ><span class="book-info-description-item-value">{{
                  bookInfo.deputyBookName
                }}</span>
              </div>
            </div>
          </div>

          <div class="book-tools">
            <div class="book-tool-item">
              <div class="book-tool-item-label">完成度</div>
              <div class="book-tool-item-value">
                <t-progress
                  :percentage="bookInfo.completeRate"
                  style="width: 270px"
                />
              </div>
            </div>
            <div class="book-tool-btngroup">
              <div>
                <t-button
                  v-if="
                    (bookInfo.auditState == null || bookInfo.auditState == 3) &&
                    curUserPermissions.permissions.includes(1)
                  "
                  variant="outline"
                  @click="finalizedDialogVisible = true"
                >
                  <template #icon>
                    <SendIcon />
                  </template>
                  定稿提交</t-button
                >
              </div>
              <div>
                <t-button
                  v-if="bookInfo.masterFlag != 3 && curUserPermissions.isEditor"
                  variant="outline"
                  @click="goToPushBook"
                >
                  <template #icon>
                    <BookIcon />
                  </template>

                  推送教材</t-button
                >
              </div>
              <div>
                <t-button
                  v-if="
                    (curUserPermissions.permissions.includes(1) ||
                      curUserPermissions.permissions.includes(2)) &&
                    (bookInfo.stepId == 1 || bookInfo.auditState == null || bookInfo.auditState == 3)
                  "
                  variant="outline"
                  @click="importBook"
                >
                  <template #icon>
                    <Download1Icon />
                  </template>

                  导入</t-button
                >
              </div>
              <!-- <div>
                <t-button
                  v-if="
                    curUserPermissions.permissions.includes(1) ||
                    curUserPermissions.permissions.includes(2) ||
                    curUserPermissions.permissions.includes(5) ||
                    curUserPermissions.permissions.includes(6)
                  "
                  variant="outline"
                  @click="handlePreview"
                >
                  <template #icon>
                    <SearchIcon />
                  </template>
                  预览</t-button
                >
              </div> -->
              <div>
                <t-button
                  v-if="
                    curUserPermissions.isEditor ||
                    ((curUserPermissions.permissions.includes(2) ||
                      curUserPermissions.permissions.includes(1)) &&
                      bookInfo.publishStatus == 1)
                  "
                  variant="outline"
                  @click="exportBook"
                >
                  <template #icon>
                    <Upload1Icon />
                  </template>

                  导出</t-button
                >
              </div>

              <div>
                <t-button variant="outline" @click="showToggle">
                  <template #icon>
                    <ChevronUpDoubleIcon />
                  </template>
                </t-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <t-dialog
    v-model:visible="finalizedDialogVisible"
    width="500"
    header="定稿提交"
    @confirm="finalizedSubmit"
    @close="finalizedDialogVisible = false"
  >
    <div>
      <div>确认将教材提交给策划编辑？</div>
      <div style="color: red">
        确认后将会立即导出教材文件，并进入到来稿确认环节，教材内所有功能均不可用。
      </div>
    </div>
  </t-dialog>
  <t-tabs default-value="basic1">
    <t-tab-panel value="basic" label="教材简介">
      <textbookIntroduction :book-id="bookId" />
    </t-tab-panel>
    <t-tab-panel value="basic1" label="章节目录">
      <chapter
        :book-id="bookId"
        :cur-user-permissions="curUserPermissions"
        :publish-status="bookInfo.publishStatus"
        :step-id="bookInfo.stepId"
        :is-free="checkIsZero"
        :master-flag="bookInfo.masterFlag"
        :book-organize="bookInfo.bookOrganize"
        @refresh="refresh"
        :auditState="bookInfo.auditState"
      />
    </t-tab-panel>
    <t-tab-panel value="basic2" label="团队管理">
      <TeamManagement
        :book-id="bookId"
        :cur-user-permissions="curUserPermissions"
        :publish-status="bookInfo.publishStatus"
        :step-id="bookInfo.stepId"
        :auditState="bookInfo.auditState"
      ></TeamManagement>
    </t-tab-panel>
    <t-tab-panel value="basic3" label="数据总览">
      <dataSummary
        :book-id="bookId"
        :book-name="bookInfo.bookName"
        :cur-user-permissions="curUserPermissions"
      />
    </t-tab-panel>
    <t-tab-panel value="basic4" label="应用数据">
      <applicationData
        :book-id="bookId"
        :book-name="bookInfo.bookName"
        :cur-user-permissions="curUserPermissions"
      />
    </t-tab-panel>
    <t-tab-panel value="basic5" label="读者反馈">
      <readerFeedback
        :book-id="bookId"
        :cur-user-permissions="curUserPermissions"
      />
    </t-tab-panel>
    <t-tab-panel value="basic6" label="发版记录">
      <releaseHistory
        :book-id="bookId"
        :book-organize="bookInfo.bookOrganize"
        :cur-user-permissions="curUserPermissions"
      />
    </t-tab-panel>
    <t-tab-panel value="basic7" label="资源包">
      <resourcePackage
        :book-id="bookId"
        :cur-user-permissions="curUserPermissions"
        :book-organize="bookInfo.bookOrganize"
        :publish-status="bookInfo.publishStatus"
      />
    </t-tab-panel>
    <!-- <t-tab-panel value="basic8" label="读者互动">
      <readerInteraction :book-id="bookId" />
    </t-tab-panel> -->
  </t-tabs>
  <t-dialog
    v-model:visible="upload.open"
    header="教材导入"
    width="400px"
    destroy-on-close
    :confirm-btn="null"
    @cancel="upload.open = false"
  >
    <t-upload
      ref="uploadRef"
      :limit="1"
      accept=".docx,.pdf"
      :headers="upload.headers"
      :action="upload.url + '?bookId=' + bookId"
      :disabled="upload.isUploading"
      :on-progress="handleFileUploadProgress"
      :on-success="handleFileSuccess"
      :auto-upload="false"
      draggable
    />
    仅允许导入docx格式文件。
    <div class="worn-text">
      文件内，第一行必须是一级标题，全文导入后根据一级标题生成对应章节目录列表。
    </div>
  </t-dialog>
  <feedbackModal ref="feedbackModalRef" :info="info" />
</template>
<script setup name="detail">
import {
  BookIcon,
  ChevronDownDoubleIcon,
  ChevronLeftIcon,
  ChevronUpDoubleIcon,
  Download1Icon,
  SendIcon,
  Upload1Icon,
} from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { useRoute, useRouter } from 'vue-router'

import {
  bookFinalizedSubmit,
  exportBookList,
  getBook,
  updateCover,
} from '@/api/book/book.js'
import { getPermissionsInfo } from '@/api/book/bookGroup'
import { checkHasFeedback } from '@/api/message/feedback.js'
import { getToken } from '@/request/token.js'
import { OssService } from '@/utils/aliOss'
import { bookNatureTypeList, getOptionDesc } from '@/utils/optionUtil.js'

import applicationData from '../components/applicationData.vue'
import chapter from '../components/chapter.vue'
import dataSummary from '../components/dataSummary.vue'
import feedbackModal from '../components/feedbackModal.vue'
import readerFeedback from '../components/readerFeedback.vue'
import releaseHistory from '../components/releaseHistory.vue'
import resourcePackage from '../components/resourcePackage.vue'
import TeamManagement from '../components/teamManagement.vue'
import textbookIntroduction from '../components/textbookIntroduction.vue'

const { proxy } = getCurrentInstance()

const route = useRoute()
const router = useRouter()
const isShow = ref(true)
const bookInfo = ref({})
const finalizedDialogVisible = ref(false)
const curUserPermissions = ref({
  permissions: [],
  isEditor: false,
  isAuthor: false,
  bookId: null,
})
const bookId = ref(null)
const info = ref('')
const acceptList = ['jpg', 'jpeg', 'png']
const upload = reactive({
  open: false,
  isUploading: false,
  headers: {
    Authorization: `Bearer ${getToken()}`,
    Language: sessionStorage.getItem('Language'),
  },
  url: `${import.meta.env.VITE_APP_BASE_API}/book/book/importBook`,
})
const checkIsZero = computed(() => {
  return Number(bookInfo.value.priceSale || 0) == 0
})

const showToggle = () => {
  isShow.value = !isShow.value
}

onMounted(() => {
  checkHasFeedbackList()
  getBookDetail()
  getPermissions()
  document.documentElement.removeAttribute('theme-mode')
  const boxCss = document.querySelectorAll('.box')

  if (boxCss) {
    boxCss[0].style.cssText = ''
  }
})

// 检测是否有未读反馈
const checkHasFeedbackList = () => {
  checkHasFeedback().then((res) => {
    if (res.data) {
      info.value = res.data
      proxy.$refs['feedbackModalRef'].show()
    }
  })
}

function getBookDetail() {
  bookId.value = route.query.bookId
  getBook(bookId.value).then((response) => {
    bookInfo.value = response.data
    if (bookInfo.value.masterBookName) {
      bookInfo.value.deputyBookName = bookInfo.value.masterBookName
    }
  })
}
function refresh() {
  getBookDetail()
}

//返回上一页
const handleBack = () => {
  router.push('/pages/list')
}

// 获取权限
function getPermissions() {
  getPermissionsInfo(bookId.value).then((res) => {
    curUserPermissions.value = res.data || {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    }
  })
}
function handlePreview() {
  router.push({
    path: '/bookPreview',
    query: {
      bookId: bookId.value,
      bookPreviewType: 1,
      formType: 1,
    },
  })
  // const { chapterId } = item
  // const { bookId } = item
  // window.open(
  //   `${`${import.meta.env.VITE_READER_PREVIEW_URL}?k=${bookId.value}`}`,
  // )
}

// 文件上传中处理
function handleFileUploadProgress(event, file, fileList) {
  upload.isUploading = true
}

// 文件上传成功处理
function handleFileSuccess(response, file, fileList) {
  upload.open = false
  upload.isUploading = false
  const confirmDia = DialogPlugin.confirm({
    header: '导入Word',
    body: '导入操作已成功完成，请您移步至任务中心查看详细的导入进度信息。在此期间，为了确保数据处理的完整性和准确性，请勿进行保存操作。',
    theme: 'warning',
    cancelBtn: '留在本页',
    confirmBtn: '前往任务中心',
    onClose: () => {
      confirmDia.destroy()
    },
    onConfirm() {
      router.push('/pages/taskCenter')
      confirmDia.destroy()
    },
  })
}

// 导入章节
function importBook() {
  upload.open = true
}

// 导出教材
function exportBook() {
  exportBookList({
    bookIdList: [bookId.value],
  }).then((res) => {
    MessagePlugin.success('已发起导出任务，请到任务中心查看')
  })
}

// 更新教材封面
function handleUpdateCover() {
  if (
    bookInfo.value.publishStatus == 1 &&
    bookInfo.value.stepId == 1 &&
    (curUserPermissions.value.isEditor ||
      curUserPermissions.value.permissions.includes(1) ||
      curUserPermissions.value.permissions.includes(2))
  ) {
    const InputFile = document.createElement('input')
    InputFile.type = 'file'
    InputFile.click()
    InputFile.addEventListener('change', async (e) => {
      const { files } = e.target
      const fileArr = []
      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        if (fileExtension && !acceptList.includes(fileExtension)) {
          MessagePlugin.error(`不允许上传${fileExtension}类型的文件`)
          break
        }
        if (file.size / 1024 / 1024 > 20) {
          MessagePlugin.error(`请上传不大于20MB的文件`)
          break
        }
        MessagePlugin.loading('正在上传文件，请稍候...')
        const res = await OssService(file)
        fileArr.push({
          fileName: file.name,
          fileSize: file.size,
          fileUrl: res.url,
        })
        MessagePlugin.closeAll()
      }
      fileArr.forEach((e) => {
        updateCover({
          bookId: bookId.value,
          cover: e.fileUrl,
        }).then((res) => {
          getBookDetail()
        })
      })
      InputFile.remove && InputFile.remove()
    })
  }
}

// 定稿提交
function finalizedSubmit() {
  bookFinalizedSubmit({
    bookId: bookId.value,
  }).then((res) => {
    finalizedDialogVisible.value = false
    MessagePlugin.success('定稿提交成功')
    refresh()
  })
}

// 推送教材
function goToPushBook() {
  if (bookInfo.value.publishStatus == 1) {
    MessagePlugin.error('当前教材尚未审核完成，无法进行推送')
    return
  }
  if (bookInfo.value.masterFlag == 3) {
    MessagePlugin.error('副教材不能定稿提交')
    return
  }
  router.push({
    path: '/pages/pushTextBooks',
    query: {
      bookId: bookId.value,
      bookOrganize: bookInfo.value.bookOrganize,
      bookName: bookInfo.value.bookName,
    },
  })
}
</script>
<style lang="less" scoped>
:deep(.t-upload__single-name) {
  width: 50%;
}
.worn-text {
  margin-top: 10px;
  margin-right: 7px;
  padding: 10px;
  border-radius: 5px;
  color: #ff6262;
  background-color: #ffeded;
  border: 2px solid #ffc8c8;
}
.back-icon {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.book-info-container {
  display: grid;
  grid-template-columns: 196px 1fr;
  margin-bottom: 20px;
  padding: 30px;

  .book-info-left {
    width: 165px;
    height: 230px;
    margin-right: 31px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .book-info-right {
    .book-info-title {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .book-info-title-left {
        display: flex;
        align-items: flex-start;

        h2 {
          font-size: 20px;
          font-weight: 400;
          margin: 0;
        }

        .book-info-tags {
          display: flex;
          margin-left: 15px;

          div {
            span {
              margin-right: 10px;
            }
          }
        }
      }

      .book-info-title-right {
        .book-info-title-btn {
          display: flex;
          justify-content: flex-end;
        }
      }
    }

    .book-info-price {
      display: flex;
      align-items: center;
      margin-top: 11px;

      .book-info-price-discount {
        font-size: 20px;
        color: #dc0808;
        line-height: 28px;

        &::before {
          content: '￥';
          font-size: 14px;
          color: #dc0808;
        }
      }

      .book-info-price-original {
        font-size: 14px;
        color: #999;
        line-height: 28px;
        text-decoration-line: line-through;
        margin-left: 10px;

        &::before {
          content: '￥';
          font-size: 14px;
          color: #999;
          text-decoration-line: none;
        }
      }
    }

    .book-info-description {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 30px;

      .book-info-description-left {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 30px;

        .book-info-description-item {
          line-height: 30px;

          .book-info-description-item-panel {
            .book-info-description-item-label {
              color: #999;
              margin-right: 14px;
              display: inline-block;
              min-width: 56px;
              font-size: 14px;
            }

            .book-info-description-item-value {
              display: inline-block;
              color: #333;
              font-size: 14px;
            }
          }
        }
      }

      .book-info-description-right {
        line-height: 28px;

        .book-info-description-item-panel {
          display: grid;
          grid-template-columns: 56px 1fr;

          .book-info-description-item-label {
            color: #999;
            margin-right: 14px;
            display: inline-block;
            min-width: 56px;
            font-size: 14px;
          }

          .book-info-description-item-value {
            display: inline-block;
            color: #333;
            font-size: 14px;
            margin-left: 14px;
          }
        }
      }
    }

    .book-tools {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      margin-top: 37px;

      .book-tool-item {
        display: flex;
        align-items: center;

        .book-tool-item-label {
          font-size: 14px;
          font-weight: bold;
        }

        .book-tool-item-value {
          margin-left: 25px;
        }
      }

      .book-tool-btngroup {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        div {
          margin: 0 5px;
        }

        .el-icon {
          .confirmIcom {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: url('http://dutp-test.oss-cn-beijing.aliyuncs.com/confirm.svg')
              no-repeat;
            background-size: contain;

            :hover {
              color: #fff;
            }
          }
        }
      }
    }
  }
}
</style>
