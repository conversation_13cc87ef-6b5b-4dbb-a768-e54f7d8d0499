<template>
  <div>
    <div class="book-approve-header">
      <div class="book-approve-header-left">
        <t-link theme="primary" @click="handleBack">返回</t-link>
      </div>
      <div class="book-approve-header-node">
        <span>当前节点:</span
        ><span style="margin-left: 10px">{{ stepName }}</span>
      </div>
    </div>
    <div class="form-title">教材基本信息</div>
    <t-button
      v-if="false"
      theme="primary"
      @click="handlePreview"
      style="margin-left: 20px; float: right"
    >
      教材预览
    </t-button>
    <t-form :model="form" labt-width="120px">
      <t-row class="mb8" :gutter="16">
        <t-col :span="3">
          <t-form-item label="教材名称">
            <span>{{ form.bookName }}</span>
          </t-form-item>
        </t-col>
      </t-row>
      <t-row class="mb8" :gutter="16">
        <t-col :span="3">
          <t-form-item label="教材编号">
            <span>{{ form.bookNo }}</span>
          </t-form-item>
        </t-col>
        <t-col :span="3">
          <t-form-item label="ISSN" v-if="form.bookOrganize == 1 && form.issn && form.issn.trim() !== '' && (!form.isbn || form.isbn.trim() === '')">
            <span>{{ form.issn }}</span>
          </t-form-item>
          <t-form-item label="ISBN" v-else-if="form.bookOrganize == 1">
            <span>{{ form.isbn }}</span>
          </t-form-item>
        </t-col>
        <t-col :span="3" v-if="form.bookOrganize == 1">
          <t-form-item label="录排编号">
            <span>{{ form.recordNo }}</span>
          </t-form-item>
        </t-col>
      </t-row>
      <t-row class="mb8" :gutter="16">
        <t-col :span="3">
          <t-form-item label="教材类型">
            <span>{{ getOptionDesc(bookTypeList, form.bookOrganize) }}</span>
          </t-form-item>
        </t-col>
        <t-col :span="3">
          <t-form-item label="主/副教材">
            <span>{{
              getOptionDesc(bookNatureTypeList, form.masterFlag)
            }}</span>
          </t-form-item>
        </t-col>
        <t-col :span="3">
          <t-form-item label="关联教材">
            <span>{{ form.deputyBookName }}</span>
          </t-form-item>
        </t-col>
      </t-row>
      <t-row class="mb8" :gutter="16">
        <t-col :span="3">
          <t-form-item label="书稿联系人">
            <span>{{ form.contact }}</span>
          </t-form-item>
        </t-col>
        <t-col :span="8">
          <t-form-item label="主编">
            <!--            <span v-for="item in form.editor">{{ item }}</span>-->
            <div class="editor-list">
              <span v-for="item in form.editor" :key="item">{{ item }}</span>
            </div>
          </t-form-item>
        </t-col>
      </t-row>

      <t-row class="mb8" :gutter="16">
        <t-col :span="3"> </t-col>
      </t-row>
    </t-form>
    <div class="form-title">教材发版流程</div>
    <div v-for="(versionItem, versionIndex) in versionList" :key="versionIndex">
      <div>
        <span style="font-size: 16px">版本号:{{ versionItem.versionNo }}</span>
        <span style="font-size: 14px; margin-left: 50px"
          >创建时间:{{ versionItem.createTime }}</span
        >
      </div>
      <t-timeline mode="same">
        <t-timeline-item
          v-for="(stepItem, stepIndex) in filteredProcessList(processList)"
          :key="stepIndex"
          :dot-color="`#1684fc`"
        >
          <div class="timeLine-title">{{ stepItem.rootStepName }}</div>
          <div
            v-for="(
              processItem, processIndex
            ) in stepItem.dtbBookPublishProcessList"
          >
            <div
              v-if="processItem.versionId == versionItem.versionId"
              :key="processIndex"
              class="process-detail-row"
            >
              <t-button
                v-if="
                  processItem.processId &&
                  processItem.versionId == versionItem.versionId &&
                  currentUserId == processItem.handleUserId &&
                  processItem.additionFlag == 1
                "
                style="position: absolute; top: 5px; right: 5px; width: 80px"
                theme="primary"
                @click="
                  openEdition(
                    processItem.processId,
                    stepItem.rootStepId,
                    stepItem.rootStepName,
                    versionItem.versionId,
                  )
                "
              >
                编辑
              </t-button>
              <div
                v-if="
                  processItem.processId &&
                  processItem.versionId == versionItem.versionId
                "
                class="process-item-row"
              >
                <div>
                  <span>审核人:</span
                  ><span>{{ processItem.handleUserName }}</span>
                </div>
                <div>
                  <span>部门:</span><span>{{ processItem.deptName }}</span>
                </div>
                <div>
                  <span>审批状态:</span
                  ><span>{{ statusList[processItem.state].label }}</span>
                </div>
                <div>
                  <span>审批时间:</span
                  ><span>{{ processItem.processDate }}</span>
                </div>
              </div>
              <div
                v-if="
                  processItem.processId &&
                  processItem.versionId == versionItem.versionId
                "
                class="process-item-row"
              >
                <div>
                  <span>审批意见:</span><span>{{ processItem.reason }}</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 节点补录按钮（每个步骤只显示一次） -->
          <div
            v-if="
              showAddRecord == 1 &&
              stepId == 16 &&
              state == 2 &&
              !hasMatchingVersion(stepItem, versionItem.versionId)
            "
            class="process-detail-row"
          >
            <div style="text-align: center"></div>
          </div>
          <div
            v-else-if="!hasMatchingVersion(stepItem, versionItem.versionId)"
            style="text-align: center"
            class="process-detail-row"
          >
            <span class="not-data">暂无数据</span>
          </div>
        </t-timeline-item>
      </t-timeline>
    </div>

    <t-dialog
      v-model:visible="additionVisible"
      header="节点补录"
      width="700px"
      :on-confirm="handleAddition"
    >
      <div>
        <t-form :model="form" labt-width="120px">
          <t-row class="mb8">
            <t-col :span="11">
              <t-form-item label="补录环节">
                {{ additionStepName }}
              </t-form-item>
            </t-col>
          </t-row>
          <t-row class="mb8">
            <t-col :span="5">
              <t-form-item label="审批部门" prop="processDeptId" required>
                <t-select v-model="form.processDeptId" :onChange="deptChange">
                  <t-option
                    v-for="item in deptList"
                    :key="item.deptId"
                    :label="item.deptName"
                    :value="item.deptId"
                  />
                </t-select>
              </t-form-item>
            </t-col>
            <t-col :span="5" :offset="1">
              <t-form-item label="审批人" prop="processDate" required>
                <t-select v-model="form.userId">
                  <t-option
                    v-for="item in deptUserList"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.userId"
                  />
                </t-select>
              </t-form-item>
            </t-col>
          </t-row>
          <t-row class="mb8">
            <t-col :span="6">
              <t-form-item label="审批结果"> 通过 </t-form-item>
            </t-col>
          </t-row>
          <t-row class="mb8">
            <t-col :span="6">
              <t-form-item label="审批时间" prop="processDate" required>
                <t-date-picker
                  style="width: 100%"
                  :enable-time-picker="true"
                  v-model="form.processDate"
                  type="date"
                  clearable
                  value-format="YYYY-MM-DD"
                  placeholder="请选择审批时间"
                  :presets="presets"
                >
                </t-date-picker>
              </t-form-item>
            </t-col>
          </t-row>
          <t-row class="mb8">
            <t-col :span="11">
              <t-form-item label="审批意见" prop="reason" required>
                <t-textarea
                  placeholder="请输入内容"
                  :maxlength="5000"
                  show-word-limit
                  :autosize="{ minRows: 4, maxRows: 6 }"
                  v-model="form.reason"
                >
                </t-textarea>
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { onBeforeMount, onMounted, ref } from 'vue'
import { bookVersionListNotPage } from '@/api/book/bookVersion.js'
import { listStepNotPage, getStepById } from '@/api/book/bookPublishStep.js'
import { processDataList } from '@/api/book/process.js'
import { getBook } from '@/api/book/book.js'
import { getBookGroup, groupUserList } from '@/api/book/bookGroup.js'
import {
  listConfig,
  getConfig,
  delConfig,
  addConfig,
  updateConfig,
} from '@/api/book/config'
import { listDept } from '@/api/system/detp.js'
import { listUserByDeptId } from '@/api/system/user.js'
import {
  addProcessAddition,
  getProcessAddition,
  editProcessAddition,
} from '@/api/book/process.js'
import { getInfo } from '@/api/login'
import dayjs from 'dayjs'
import {
  bookNatureTypeList,
  bookTypeList,
  getOptionDesc,
  statusList,
} from '@/utils/quetions-utils'
import { SettingIcon } from 'tdesign-icons-vue-next'
import { getNowTime } from '@/utils/dateTimeUtil.js'

const route = useRoute()
const router = useRouter()
const bookId = ref()
const stepId = ref()
const stepName = ref()
const processId = ref()
const bookOrganize = ref()
const versionList = ref([])
const stepList = ref([])
const processList = ref([])
const userList = ref([])
const showAddRecord = ref(2)
const deptList = ref([])
const deptUserList = ref([])
const additionVisible = ref(false)
const additionStepId = ref()
const additionStepName = ref()
const additionVersionId = ref()
const currentUserId = ref()
const state = ref()
const funType = ref()

const data = reactive({
  form: {
    processDeptId: '',
    userId: '',
    editor: [],
  },
})
const { form } = toRefs(data)
const presets = ref({
  此刻: dayjs().format('YYYY-MM-DD HH:mm:ss'),
})

// 返回
function handleBack() {
  if (funType.value == 1) {
    router.push({
      path: '/pages/detail',
      query: { bookId: bookId.value },
    })
  } else if (funType.value == 2 || funType.value == 3) {
    router.push({ path: '/pages/backup',query: { bookOrganize: bookOrganize.value }, })
  }
}

const current = ref(0)

// 查询教材版本信息
function getBookVersionList() {
  // 获取教材版本信息
  const param = {
    bookId: bookId.value,
  }
  bookVersionListNotPage(param).then((res) => {
    versionList.value = res.data
  })
}

// 判断当前步骤是否存在匹配版本的流程
const hasMatchingVersion = (stepItem: any, versionId: string) => {
  return stepItem.dtbBookPublishProcessList.some(
    (item: any) => item.versionId === versionId,
  )
}

function filteredProcessList(items) {
  return items.slice(1, -1) // 移除第一个元素
}

// 查询书籍的审批详情列表 以版本和步骤id分组
function getProcessDetaList() {
  const param = {
    bookId: bookId.value,
    processId: processId.value,
    bookOrganize: bookOrganize.value,
  }
  processDataList(param).then((res) => {
    processList.value = res.data
    console.log(processList.value)
  })
}

// 获取教材信息
async function getBookInfo(bookId) {
  let response = await getBook(bookId)
  form.value = response.data
  if (form.value.masterBookName) {
    form.value.deputyBookName = form.value.masterBookName
  }
}

// 获取用户列表
async function getUserList() {
  const response = await groupUserList(bookId.value)
  userList.value = response.data
}

// 获取团队信息
async function getBookUserInfo() {
  const res = await getBookGroup(bookId.value)
  const editor = []
  userList.value.map((item) => {
    if (item.userId == res.data.contact) {
      form.value.contact = item.nickName
    }
    res.data.editor.map((editorItem) => {
      if (editorItem == item.userId) {
        editor.push(item.nickName)
      }
    })
  })
  form.value.editor = editor
}

function openAddition(stepId, stepName, versionId) {
  form.value.processId = ''
  form.value.processDeptId = ''
  form.value.userId = ''
  form.value.processDate = getNowTime()
  form.value.reason = ''
  additionStepId.value = stepId
  additionStepName.value = stepName
  additionVersionId.value = versionId
  additionVisible.value = true
}

// 节点补录详情
function openEdition(processId, stepId, stepName, versionId) {
  form.value.processId = ''
  additionStepId.value = stepId
  additionStepName.value = stepName
  additionVersionId.value = versionId
  additionVisible.value = true
  const param = {
    processId: processId,
  }
  getProcessAddition(param).then((res) => {
    form.value.processId = processId
    form.value.processDeptId = res.data.processDeptId
    if (res.data.processDeptId) {
      listUserByDeptId(form.value.processDeptId).then((response) => {
        deptUserList.value = response.data
        form.value.userId = res.data.userId
      })
    }
    form.value.processDate = res.data.processDate
    form.value.reason = res.data.reason
    additionVisible.value = true
  })
}

// 获取当前节点
function getStepInfo() {
  getStepById(stepId.value).then((res) => {
    stepName.value = res.data.stepName
  })
}

// 查询平台的补录设置
function getAddition() {
  listConfig().then((response) => {
    showAddRecord.value = response.rows[0].showAddRecord
  })
}

// 查询部门列表
function getListDept() {
  listDept().then((response) => {
    deptList.value = response.data
  })
}

// 根据部门查询人员
function deptChange(deptId) {
  form.value.userId = ''
  listUserByDeptId(deptId).then((response) => {
    deptUserList.value = response.data
  })
}

// 节点补录提交
function handleAddition() {
  form.value.bookId = bookId.value
  form.value.stepId = additionStepId.value
  form.value.state = 2 // 补录环节只能通过
  form.value.versionId = additionVersionId.value
  form.value.additionFlag = '1' // 是补录
  form.value.dealUserId = form.value.userId

  /** 提交按钮 */
  if (form.value.processId) {
    editProcessAddition(form.value).then((response) => {
      MessagePlugin.success('修改成功')
      getStepInfo()
      initData()
      getProcessDetaList()
      getListDept()
      additionVisible.value = false
    })
  } else {
    addProcessAddition(form.value).then((response) => {
      MessagePlugin.success('补录成功')
      getStepInfo()
      initData()
      getProcessDetaList()
      getListDept()
      additionVisible.value = false
    })
  }
}

function getUser() {
  getInfo().then((res) => {
    currentUserId.value = res.user.userId
  })
}

// 初始化数据
async function initData() {
  getBookVersionList()
  await getBookInfo(bookId.value)
  await getUserList()
  await getBookUserInfo()
  getAddition()
}

function handlePreview() {
  router.push({
    path: '/bookPreview',
    query: {
      bookId: bookId.value,
      bookPreviewType: 1,
      formType: 1,
      funType: route.query.funType,
      bookId: bookId.value,
      processId: processId.value,
      stepId: stepId.value,
      state: state.value,
    },
  })
}

onBeforeMount(() => {
  bookId.value = route.query.bookId
  processId.value = route.query.processId
  bookOrganize.value = route.query.bookOrganize
  stepId.value = route.query.stepId
  state.value = route.query.state
  funType.value = route.query.funType || 1
})

onMounted(() => {
  getUser()
  getListDept()
  getStepInfo()
  initData()
  getProcessDetaList()
})
</script>

<style scoped lang="less">
.mb8 {
  margin-bottom: 8px;
}

.mt30 {
  margin-top: 30px;
}

.form-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #666;
  margin-top: 20px;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background-color: #409eff;
    border-radius: 8px;
    margin-right: 8px;
  }
}

.book-approve-header {
  width: 100%;
  height: 30px;
  display: flex;

  .book-approve-header-left {
    width: 60px;
    height: 30px;
  }

  .book-approve-header-node {
    width: 200px;
    height: 30px;
  }
}

.timeLine-title {
  font-size: 16px;
  font-weight: bold;
  color: #1684fc;
}

.process-detail-row {
  width: 70%;
  min-height: 90px;
  background-color: #f6f6f6;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  margin-top: 10px;
  position: relative;

  .process-item-row {
    width: 100%;
    min-height: 20px;
    display: flex;
    justify-content: space-between;

    div {
      &:nth-child(1) {
        flex: 1;
      }

      &:nth-child(2) {
        flex: 2;
      }

      &:nth-child(3) {
        flex: 2;
      }

      &:nth-child(4) {
        flex: 2;
      }
    }
  }
}

.not-data {
  font-weight: bold;
  color: #cccccc;
}
.editor-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 24px;
}
.editor-list span {
  width: 180px;
  white-space: nowrap;
}
</style>