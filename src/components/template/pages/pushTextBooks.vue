<template>
  <div class="app-container">
    <div class="back">
      <div class="back-btn" @click="$router.go(-1)">
        <ChevronLeftIcon /><span>返回</span>
      </div>
      <div class="back-title">{{ bookName }}</div>
    </div>

    <div class="content">
      <t-tabs v-model="activeName" class="demo-tabs">
        <t-tab-panel v-if="activeName == 'first'" label="账号" value="first">
          <div class="account">
            <p>注：1、只有已发版的公开教材可以推送到账号内</p>
            <p style="padding-left: 30px">
              2、账号指的是前台读者、学生、教师账号，允许重复推送
            </p>
            <p style="padding-left: 30px">
              3、推送后会以消息的形式推送到指定账号中
            </p>
            <p style="padding-left: 30px">
              4、只有主教材和常规教材可以进行推送
            </p>
          </div>
          <div class="formContent">
            <t-form
              ref="queryRef"
              :data="queryParams"
              labt-width="80px"
              layout="inline"
              @reset="resetQuery"
            >
              <t-form-item label="账号" name="userName">
                <t-input
                  v-model="queryParams.userName"
                  placeholder="请输入账号"
                  clearable
                  maxlength="30"
                  style="width: 220px"
                />
              </t-form-item>
              <t-form-item label="姓名" name="nickName">
                <t-input
                  v-model="queryParams.nickName"
                  placeholder="请输入姓名"
                  clearable
                  maxlength="30"
                  style="width: 220px"
                />
              </t-form-item>
              <t-form-item label="角色" name="userType">
                <t-select
                  v-model="queryParams.userType"
                  placeholder="请选择角色"
                  clearable
                  style="width: 220px"
                >
                  <t-option
                    v-for="item in userTypeOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </t-select>
              </t-form-item>
              <t-form-item label="学校" name="schoolId">
                <t-select
                  v-model="queryParams.schoolId"
                  placeholder="请选择学校"
                  clearable
                  maxlength="30"
                  style="width: 220px"
                >
                  <t-option
                    v-for="item in schoolList"
                    :key="item.schoolId"
                    :label="item.schoolName"
                    :value="item.schoolId"
                  />
                </t-select>
              </t-form-item>
              <t-form-item label="状态" name="status">
                <t-select
                  v-model="queryParams.status"
                  placeholder="请选择"
                  clearable
                  style="width: 220px"
                >
                  <t-option
                    v-for="item in bookPushTextBooksStatus"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </t-select>
              </t-form-item>
              <t-form-item>
                <t-button
                  theme="default"
                  style="margin-right: 10px"
                  type="reset"
                >
                  <template #icon>
                    <RefreshIcon />
                  </template>
                  重置</t-button
                >
                <t-button theme="primary" @click="handleQuery">
                  <template #icon>
                    <SearchIcon />
                  </template>
                  查询</t-button
                >
              </t-form-item>
            </t-form>
          </div>
          <div class="tableContent">
            <div class="tableBtn">
              <t-button theme="primary" @click="handleOpenPushBookModal">
                <template #icon>
                  <RocketIcon />
                </template>
                推送</t-button
              >
            </div>

            <t-table
              row-key="createTime"
              :data="dataList"
              :columns="columns"
              :loading="loading"
              :pagination="pagination"
              cell-empty-content="-"
            >
              <template #roleName="{ row }">
                {{ getOptionDesc(userTypeOption, row.userType) }}
              </template>
              <template #pushStatus="{ row }"> 成功 </template>
            </t-table>
          </div>
        </t-tab-panel>
        <t-tab-panel v-if="activeName == 'second'" label="教务" value="second">
          <div class="account">
            <p>注：1、只有已完成的校本教材可以推送到学校教务内</p>
            <p style="padding-left: 30px">
              2、只能推送给当前教材关联的学校教务
            </p>
            <p style="padding-left: 30px">
              3、当指定的学校教务推送有效期截止后在可再次推送
            </p>
          </div>
          <div class="formContent">
            <t-form
              ref="queryRefSecond"
              :data="queryParams"
              labt-width="80px"
              layout="inline"
              @reset="handleReset"
            >
              <t-form-item label="学校" name="schoolId">
                <t-select
                  v-model="queryParams.schoolId"
                  placeholder="请选择学校"
                  clearable
                  maxlength="30"
                  style="width: 220px"
                >
                  <t-option
                    v-for="item in schoolList"
                    :key="item.schoolId"
                    :label="item.schoolName"
                    :value="item.schoolId"
                  />
                </t-select>
              </t-form-item>
              <t-form-item label="状态" name="status">
                <t-select
                  v-model="queryParams.status"
                  placeholder="请选择"
                  clearable
                  style="width: 220px"
                >
                  <t-option
                    v-for="item in bookPushTextBooksStatus"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </t-select>
              </t-form-item>
              <t-form-item>
                <t-button
                  type="reset"
                  theme="default"
                  style="margin-right: 10px"
                >
                  <template #icon>
                    <RefreshIcon />
                  </template>
                  重置</t-button
                >
                <t-button theme="primary" @click="handleQuery">
                  <template #icon>
                    <SearchIcon />
                  </template>
                  查询</t-button
                >
              </t-form-item>
            </t-form>
          </div>
          <div class="tableContent">
            <div class="tableBtn">
              <t-button theme="primary" @click="handleOpenPushBookModal">
                <template #icon>
                  <RocketIcon />
                </template>
                推送</t-button
              >
            </div>
            <t-table
              row-key="createTime"
              :data="dataList"
              :columns="pushColumns"
              :loading="loading"
              :pagination="pagination"
              cell-empty-content="-"
            >
              <template #roleName="{ row }">
                {{ getOptionDesc(userTypeOption, row.userType) }}
              </template>
              <template #pushStatus="{ row }"> 成功 </template>
            </t-table>
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
    <modal
      v-model:visible="pushBookDialogVisible"
      header="推送"
      width="30%"
      :footer="false"
    >
      <t-form
        ref="pushBookRef"
        :data="form"
        labt-width="80px"
        :rules="rules"
        @submit="submitPushBookForm"
      >
        <t-form-item
          v-if="activeName == 'first'"
          label="账号"
          name="userPhoneStrList"
          required
        >
          <t-textarea
            v-model="form.userPhoneStrList"
            placeholder="请输入手机号，多个手机号用','隔开，最多可输入100个手机号"
            :maxlength="1250"
            show-word-limit
            :autosize="{ minRows: 4, maxRows: 6 }"
          >
          </t-textarea>
        </t-form-item>
        <t-form-item
          v-if="activeName == 'second'"
          label="学校"
          name="schoolIdList"
        >
          <t-select
            v-model="form.schoolIdList"
            placeholder="请选择学校"
            multiple
            filterable
          >
            <t-option
              v-for="item in schoolList"
              :key="item.schoolId"
              :label="item.schoolName"
              :value="item.schoolId"
            />
          </t-select>
        </t-form-item>
        <t-form-item
          v-if="activeName == 'second'"
          label="有效期"
          name="dayLimit"
        >
          <t-input-number
            v-model="form.dayLimit"
            :step="1"
            step-strictly
            :min="1"
            :max="999999999"
          />
          <span style="margin-left: 10px">天</span>
        </t-form-item>
        <div class="push-footer">
          <t-button
            style="margin-right: 10px"
            theme="default"
            @click="pushBookDialogVisible = false"
            >取消</t-button
          >
          <t-button theme="primary" :loading="loadingBtn" type="submit">
            确定
          </t-button>
        </div>
      </t-form>
    </modal>
  </div>
</template>

<script setup>
import {
  ChevronLeftIcon,
  RefreshIcon,
  RocketIcon,
  SearchIcon,
} from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { useRoute } from 'vue-router'

import { listNoPageOfBook } from '@/api/basic/school.js'
import { addSchoolBook, listSchoolBook } from '@/api/book/schoolBookPush'
import { pushBook, pushBookList } from '@/api/message/message.js'
import {
  bookPushTextBooksStatus,
  getOptionDesc,
  userTypeOption,
} from '@/utils/optionUtil'

const { proxy } = getCurrentInstance()
const route = useRoute()
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  bookId: null,
  status: null,
})

const rules = {
  userPhoneStrList: [{ required: true, message: '请输入账号', type: 'error' }],
  schoolIdList: [
    {
      required: true,
      message: '请选择学校',
      trigger: 'change',
      type: 'error',
    },
  ],
  dayLimit: [{ required: true, message: '请输入有效期', type: 'error' }],
}
const activeName = ref('first')
const total = ref(0)
const dataList = ref([])
const loading = ref(false)
const bookId = ref(null)
const pushBookDialogVisible = ref(false)
const loadingBtn = ref(false)
const form = ref({
  bookId: bookId.value,
})
const pagination = {
  defaultCurrent: 1,
  defaultPageSize: 10,
  total: total.value,
}
const schoolList = ref([])
const queryRefSecond = ref(null)
const bookName = ref(null)
const columns = ref([
  { colKey: 'userName', title: '账号' },
  { colKey: 'nickName', title: '姓名' },
  { colKey: 'roleName', title: '角色' },
  { colKey: 'schoolName', title: '学校' },
  { colKey: 'createTime', title: '推送时间' },
  { colKey: 'pushStatus', title: '推送状态' },
])
const pushColumns = ref([
  { colKey: 'schoolCode', title: '学校编码' },
  { colKey: 'schoolName', title: '学校' },
  { colKey: 'dayLimit', title: '有效期（天）' },
  { colKey: 'expireDate', title: '有效期截止时间' },
  { colKey: 'createTime', title: '推送时间' },
  { colKey: 'pushStatus', title: '推送状态' },
])
//#region  生命周期相关

onMounted(() => {
  bookId.value = route.query.bookId
  queryParams.value.bookId = route.query.bookId
  bookName.value = route.query.bookName
  activeName.value = route.query.bookOrganize == 1 ? 'first' : 'second'
  getSchoolList()
  getList()
})
//#endregion

// 获取学校
function getSchoolList() {
  listNoPageOfBook({
    bookId: bookId.value,
  }).then((res) => {
    schoolList.value = res.data
  })
}

//#region 获取数据相关

// 查询教材资源列表
function getList() {
  if (queryParams.value.status == 2) {
    dataList.value = []
    return
  }
  loading.value = true
  if (activeName.value == 'first') {
    pushBookList(queryParams.value).then((response) => {
      dataList.value = response.rows
      pagination.total = response.total
      loading.value = false
    })
  } else {
    listSchoolBook(queryParams.value).then((response) => {
      dataList.value = response.rows
      pagination.total = response.total
      loading.value = false
    })
  }
}
//#endregion

//#region 操作相关

// 打开推送窗口
function handleOpenPushBookModal() {
  form.value = {
    bookId: bookId.value,
    userPhoneStrList: '',
    schoolIdList: [],
    dayLimit: '',
  }
  pushBookDialogVisible.value = true
}
const pushBookRef = ref(null)
// 推送教材
function submitPushBookForm({ validateResult, firstError }) {
  if (validateResult === true) {
    form.value.bookId = bookId.value
    if (activeName.value == 'first') {
      const phoneList = form.value.userPhoneStrList
        .replaceAll('，', ',')
        .split(',')
        .filter(Boolean)
      if (phoneList.length == 0) {
        MessagePlugin.error('请输入账号')
        return
      }
      if (phoneList.length > 100) {
        MessagePlugin.error('最多只能输入100个账号')
        return
      }
      form.value.userPhoneStrList = phoneList.join(',')
      loadingBtn.value = true
      pushBook(form.value).then((response) => {
        if (response.data === '推送成功') {
          MessagePlugin.success(response.data)
          pushBookDialogVisible.value = false
        } else {
          MessagePlugin.error(response.data)
        }
        loadingBtn.value = false
        getList()
      })
    } else {
      loadingBtn.value = true
      addSchoolBook(form.value).then((response) => {
        if (response.data === '推送成功') {
          MessagePlugin.success(response.data)

          pushBookDialogVisible.value = false
        } else {
          MessagePlugin.error(response.data)
        }
        loadingBtn.value = false
        getList()
      })
    }
  } else {

    MessagePlugin.warning(firstError)
  }

  // proxy.$refs['pushBookRef'].validate((valid) => {
  //   if (valid) {

  //   }
  // })
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

// 重置按钮操作
function resetQuery() {
  MessagePlugin.success('重置成功')
  //   proxy.resetForm('queryRef')
  //   handleQuery()
}

//教务重置按钮操作
function handleReset() {
  MessagePlugin.success('重置成功')
}

//#endregion
</script>

<style lang="less" scoped>
.app-container {
  padding: 20px;
  background-color: #fff;

  .back {
    display: flex;
    width: 100%;
    padding-bottom: 20px;
    border-bottom: 1px solid #f1f1f1;

    .back-title {
      margin-left: 60px;
      color: #666;
      font-size: 14px;
    }

    .back-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      color: #999;
      font-size: 14px;

      span {
        margin-left: 5px;
      }
    }
  }

  .formContent {
    margin-top: 20px;
  }

  .content {
    margin: 40px;

    .t-tabs__item {
      font-size: 20px;
    }

    .account {
      padding: 20px;
      background-color: #daeaf7;
      width: 100%;
      color: #666;
    }
  }

  .tableContent {
    margin: 10px 40px 0;
    .tableBtn {
      margin-bottom: 10px;
    }
  }
  .t-dialog__body {
    :global(.push-footer) {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
  }
}
</style>
