<template>
  <div>
    <div class="search-form">
      <t-form ref="form" :data="formData" label-width="100px" layout="inline" @reset="resetQuery">
        <t-form-item label="任务类型" name="taskType">
          <t-select v-model="formData.taskType" clearable style="width: 200px">
            <t-option
              :value="item.value"
              :label="item.label"
              v-for="item in taskTypeList"
              :key="item.value"
              >{{ item.label }}</t-option
            >
          </t-select>
        </t-form-item>
        <t-form-item label="状态" name="taskState">
          <t-select v-model="formData.taskState" clearable style="width: 200px">
            <t-option
              :value="item.value"
              :label="item.label"
              v-for="item in taskStatusList"
              :key="item.value"
              >{{ item.label }}</t-option
            >
          </t-select>
        </t-form-item>
        <t-form-item label="教材名称" name="bookName">
          <t-input
            v-model="formData.bookName"
            placeholder="请输入教材名称"
            clearable
            style="width: 200px"
          ></t-input>
        </t-form-item>
        <t-button theme="primary" @click="search" style="margin-right: 20px"
          >查询</t-button
        >
        <t-button theme="default" type="reset">重置</t-button>
        <t-button theme="default" style="margin-left: 20px;" @click="handleDownload" >下载</t-button>
      </t-form>
    </div>
    <div>
      <t-table
        hover
        row-key="taskId"
        :columns="columns"
        :data="dataList"
        :pagination="formData"
        v-model:selectedRowKeys="selectedRowKeys"
        :reserveSelectedRowOnPaginate=false
        @page-change="onPageChange"
      >
      </t-table>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import {
  getOptionDesc,
  taskTypeList,
  taskStatusList,
  mainSubTextbookList,
} from '@/utils/quetions-utils'
import { getToken } from '@/request/token.js'
import { listTask, downloadTask, editCaptionStyle } from '@/api/book/task.js'
import { blobValidate } from '@/utils/dutp.js'
import { saveAs } from 'file-saver'
const formData = ref({
  current: 1,
  pageSize: 10,
  total: 10,
})
const selectType = ref('1')
const columns = [
  {
    colKey: 'row-select',
    type: 'multiple',
    // 这种方式禁用行选中，行文本不会变灰
    checkProps: ({ row }) => ({ disabled: (row.taskType == '5' || row.taskType == '10') && row.taskState == '2' ? false : true }),
    width: 50,
  },
  {
    colKey: 'bookNo',
    title: '教材编号',
    ellipsis: true,
    cell: (h, { row }) => {
      if (row.bookNo == null) {
        return h('div', '-')
      } else {
        return h('div', row.bookNo)
      }
    },
  },
  {
    colKey: 'bookName',
    title: '教材名称',
    ellipsis: true,
    cell: (h, { row }) => {
      if (row.bookNo == null) {
        return h('div', '-')
      } else {
        return row.bookName
      }
    },
  },
  { colKey: 'userName', title: '创建人', ellipsis: true },
  { colKey: 'userPhone', title: '创建人电话', ellipsis: true },
  {
    colKey: 'taskType',
    title: '任务类型',
    ellipsis: true,
    cell: (h, { row }) => {
      return h('div',  getOptionDesc(taskTypeList, row.taskType))
    },
  },
  { colKey: 'remark', title: '备注', ellipsis: true },
  { colKey: 'createTime', title: '创建时间', ellipsis: true },
  { colKey: 'endTime', title: '结束时间', ellipsis: true },
  {
    colKey: 'taskState',
    title: '状态',
    ellipsis: true,
    cell: (h, { row, rowIndex }) => {
      let taskStateName = ''
      if (row.taskState == 0) {
        taskStateName = '未开始'
      } else if (row.taskState == 1) {
        taskStateName = '进行中'
      } else if (row.taskState == 2) {
        taskStateName = '成功'
      } else if (row.taskState == 3) {
        taskStateName = '失败'
      }
      return taskStateName
    },
  },
  {
    title: '操作',
    colKey: 'link',
    fixed: 'right',
    foot: '-',
    width: 240,
    cell: (h, { row }) => {
      const items = [
        {
          label: '下载',
          color: '#0966B4',
          onClick: (row) => {
            download(row.taskId)
          },
        },
        {
          label: '立即更新',
          color: '#0966B4',
          onClick: (row) => {
            edit(row)
          },
        },
      ]

      return h(
        'div',
        items.map((item) => {
          if (
            (row.taskType == '5' || row.taskType == '10') &&
            item.label == '下载' &&
            row.taskState == '2'
          ) {
            if (row.hasRoleType == 1) {
              return h(
                'div',
                {
                  onclick: () => item.onClick(row),
                  style: `display:flex;align-items:center;justify-content:start;cursor:pointer;color:${item.color}`,
                },
                item.label,
              )
            }
            else if (row.hasRoleType == 0 && isWithin30Days(row.createTime)) {
              return h(
                'div',
                {
                  onclick: () => item.onClick(row),
                  style: `display:flex;align-items:center;justify-content:start;cursor:pointer;color:${item.color}`,
                },
                item.label,
              )
            }
          } else if (
            row.taskState == '0' &&
            row.taskType == '6' &&
            item.label == '立即更新'
          ) {
            return h(
              'div',
              {
                onclick: () => item.onClick(row),
                style: `display:flex;align-items:center;justify-content:start;cursor:pointer;color:${item.color}`,
              },
              item.label,
            )
          } else {
            return h('div')
          }
        }),
      )
    },
  },
]
const dataList = ref([])
const selectedRowKeys = ref([])
const activeRow = ref(false)
// 获取教材数据
const getList = () => {
  listTask(formData.value).then((res) => {
    dataList.value = res.rows
    formData.value.total = res.total
  })
}

const handleDownload = async () => {
  if (selectedRowKeys.value.length == 0) {
    MessagePlugin.warning('请选择要下载的数据')
    return
  }
  let loading = LoadingPlugin({
    loading: true,
    text: '正在下载数据，请稍候',
  })
  try {
    for (let i = 0; i < selectedRowKeys.value.length; i++) {
      const taskId = selectedRowKeys.value[i];
      const res = await downloadTask(taskId)
      let filename = res?.headers['content-disposition']?.split('filename=')[1]
      if (filename) {
        filename = decodeURIComponent(filename)
      } else {
        filename = new Date().getTime() + '.zip'
      }
      let data = res.data
      const isBlob = blobValidate(data)
      if (isBlob) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        MessagePlugin.error('下载失败')
      }
    }
  } catch (r) {
    console.error(r)
    MessagePlugin.error('下载文件出现错误，请联系管理员！')
  } finally {
    loading.hide()
  }
}

function isWithin30Days(createTime) {
  const now = new Date()
  const diffTime = Math.abs(now - new Date(createTime))
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 30
}


//#region 操作相关
const onPageChange = (pageInfo) => {
  formData.value.pageSize = pageInfo.pageSize
  formData.value.pageNum = pageInfo.current
  formData.value.current = pageInfo.current
  getList()
}
function download(taskId) {
  let loading = LoadingPlugin({
    loading: true,
    text: '正在下载数据，请稍候',
  })
  downloadTask(taskId)
    .then(async (res) => {
      let filename = res?.headers['content-disposition']?.split('filename=')[1]
      if (filename) {
        filename = decodeURIComponent(filename)
      } else {
        filename = new Date().getTime() + '.zip'
      }
      let data = res.data
      const isBlob = blobValidate(data)
      if (isBlob) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        MessagePlugin.error('下载失败')
      }
      loading.hide()
    })
    .catch((r) => {
      console.error(r)
      MessagePlugin.error('下载文件出现错误，请联系管理员！')
      loading.hide()
    })
}
function edit(row) {
  let loading = LoadingPlugin({
    loading: true,
    text: '正在更新教材，请稍候',
  })
  let data = {
    taskId: row.taskId,
    dataId: row.dataId,
  }
  editCaptionStyle(data)
    .then(async (res) => {
      MessagePlugin.success('更新成功')
      loading.hide()
      getList()
    })
    .catch((r) => {
      console.error(r)
      MessagePlugin.error('更新题注出现错误，请联系管理员！')
      loading.hide()
    })
}
const search = () => {
  formData.value.pageNum = 1
  formData.value.current = 1
  getList()
}
function resetQuery(){
  search();
}
getList()
</script>

<style lang="less" scoped>
.t-radio-group.t-radio-group--filled {
  border-color: var(--td-bg-color-component);
  padding: 6px;
  border-radius: 8px;
  background-color: #e8f4fe;
  position: relative;
}

.t-radio-group.t-radio-group--primary-filled .t-radio-button.t-is-checked {
  color: #0966b4;
  background-color: #fff;
}

.search-form {
  margin: 20px 0;
}
</style>
