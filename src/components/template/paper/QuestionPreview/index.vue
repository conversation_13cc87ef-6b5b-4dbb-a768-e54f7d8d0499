<template>
    <div class="question-content">
        <!-- 根据题型动态引入对应组件 -->
        <component
            :config="{hasAnswer: showAnswer,contentLabel:true}"
            :is="getQuestionComponent"
            :data="question"
            v-bind="questionTypeSpecificProps"
            :options="question.options || []"
            :showAnswer="showAnswer"
        >
            <template #selection>
                <span class="toggle-answer" v-if="showAnswerButton">
                    <t-link theme="primary" @click="toggleAnswer">
                        {{ showAnswer ? '折叠' : '查看答案解析' }}
                    </t-link>
                </span>
            </template>
        </component>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, watchEffect } from 'vue'
import { Link as TLink } from 'tdesign-vue-next'
import OptionSelectQuestion from '@/components/template/questions/onview/optionSelectQuestion.vue'
import TrueOrFalseQuestion from '@/components/template/questions/onview/trueOrFalseQuestion.vue'
import FillinBlanksQuestion from '@/components/template/questions/onview/fillinBlanksQuestion.vue'
import SortingQuestion from '@/components/template/questions/onview/sortingQuestion.vue'
import MatchingQuestion from '@/components/template/questions/onview/matchingQuestion.vue'
import AnswerInShortQuestion from '@/components/template/questions/onview/answerInShortQuestion.vue'
import ProgrammingQuestion from '@/components/template/questions/onview/programmingQuestion.vue'

// 添加组件名称定义
defineOptions({
    name: 'QuestionPreview'
})

const props = defineProps({
    question: {
        type: Object,
        required: true
    },
    showAnswerButton: {
        type: Boolean,
        default: true
    }
})

// 控制答案显示的状态
const showAnswer = ref(false)

// 切换答案显示状态的方法
const toggleAnswer = () => {
    showAnswer.value = !showAnswer.value
}

// 添加一个响应式变量来存储格式化后的题干内容
const formattedContent = ref('')

// 监听showAnswer的变化，重新格式化填空题内容
watch(showAnswer, () => {
    if (props.question.questionType === 3) {
        // 强制重新渲染填空题内容
        formattedContent.value = formatBlankQuestion(props.question.questionContent, props.question.questionType)
    }
})

// 处理填空题格式
const formatBlankQuestion = (content, questionType) => {
    if (questionType !== 3) return content;
    return content.replace(/###(.*?)###/g, (match, answer) => {
        // 当showAnswer为true时显示答案，否则只显示空白
        return `<span class="blank-line"><span class="blank-answer" ${!showAnswer.value ? 'style="display: none;"' : ''}>${answer}</span></span>`;
    });
}

// 处理代码内容
const codeContent = ref({})

// 使用 watchEffect 来处理 codeContent
watchEffect(() => {
    if (props.question.codeContent) {
        try {
            codeContent.value = JSON.parse(props.question.codeContent)
        } catch (e) {
            console.error('解析代码内容失败:', e)
            codeContent.value = { code: '', language: 'javascript' }
        }
    }
})

// 添加一个计算属性来获取乱序的选项
const shuffledOptions = computed(() => {
    if (!props.question.options || props.question.questionType !== 4) {
        return [];
    }
    
    // 复制选项数组，避免修改原数组
    const options = [...props.question.options];
    
    // Fisher-Yates 洗牌算法打乱数组
    for (let i = options.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [options[i], options[j]] = [options[j], options[i]];
    }
    
    return options;
});

// 根据题型返回对应的组件
const getQuestionComponent = computed(() => {
    const typeMap = {
        1: OptionSelectQuestion, // 单选
        2: OptionSelectQuestion, // 多选
        3: FillinBlanksQuestion, // 填空
        4: SortingQuestion, // 排序
        5: MatchingQuestion, // 连线
        6: AnswerInShortQuestion, // 简答
        7: TrueOrFalseQuestion, // 判断
        8: ProgrammingQuestion, // 编程题
    }
    return typeMap[props.question.questionType] || OptionSelectQuestion
})

// 添加一个新的计算属性来处理特定题型的props
const questionTypeSpecificProps = computed(() => {
    // 基础属性，适用于所有题型
    const baseProps = {
        codeContent: codeContent.value,
        formattedContent: formattedContent.value,
        shuffledOptions: shuffledOptions.value
    }
    
    if (props.question.questionType === 5) { // 连线题
        const options = props.question.options || []
        
        const leftOption = options
            .filter(opt => opt.optionPosition == 1)
            .map((opt, index) => ({
                ...opt,
                optionType: 'left',
                optionContent: opt.optionContent,
                index
            }))
        
        const rightOption = options
            .filter(opt => opt.optionPosition == 2)
            .map((opt, index) => ({
                ...opt,
                optionType: 'right',
                optionContent: opt.optionContent,
                index
            }))

        let matchingResult = []
        try {
            matchingResult = props.question.rightAnswer ? JSON.parse(props.question.rightAnswer) : []
        } catch (error) {
            console.error('解析 rightAnswer 失败:', error)
        }

        return {
            ...baseProps,
            leftOption,
            rightOption,
            matchingResult
        }
    }
    
    return baseProps
})

// 当题目变化时重新生成乱序选项
watch(() => props.question, () => {
    // 题目变化时，重新计算 shuffledOptions
    // 由于 shuffledOptions 是计算属性，会自动重新计算
}, { deep: true });
</script>

<style lang="css" scoped>
.question-content {
    max-width: 100%;
    overflow-x: hidden;
    position: relative;
    padding-bottom: 50px;
}

.toggle-answer {
    text-align: right;
    float: right;
    margin: 15px 0;
}

:deep(.blank-line) {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;
    margin: 0 5px;
    
    .blank-answer {
        color: #409EFF;
        font-size: 14px;
        margin-bottom: 2px;
    }
    
    &::after {
        content: '';
        width: 100%;
        height: 1px;
        background-color: #000;
        display: block;
    }
}

:deep(.sort-answer-item) {
    margin-bottom: 8px;
    padding: 5px 10px;
    background-color: #f0f9eb;
    border-radius: 4px;
    display: flex;
    align-items: center;
    
    .sort-number {
        font-weight: bold;
        margin-right: 10px;
        min-width: 20px;
    }
}

:deep(.language-tag) {
    margin-bottom: 8px;
    color: #000000;
    font-size: 14px;
    font-style: italic;
}


:deep(pre) {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border-radius: 4px;
    padding: 16px;
    margin: 12px 0;
    overflow-x: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
    line-height: 1.5;
    border: 1px solid #333;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>