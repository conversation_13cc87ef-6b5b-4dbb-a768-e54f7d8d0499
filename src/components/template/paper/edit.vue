<template>
  <div class="paper-edit">
    <t-card>
      <t-button theme="default" @click="goBack" style="float: left;">
          <template #icon><t-icon name="chevron-left" /></template>
          返回
        </t-button>
      <!-- 添加返回按钮 -->
      <div class="header-container">
        <step-header :active="currentStep" :paper-type="paperType"></step-header>
      </div>

      <!-- 使用新的PaperEditor组件 -->
      <paper-editor
        ref="paperEditor"
        :type="type"
        :paperId="paperId" 
        :paperType="paperType"
        :isEmbedded="false"
        @step-change="handleStepChange"
        @save="handleSave"
      />
    </t-card>
  </div>
</template>

<script>
import StepHeader from './step/stepHeader.vue'
import PaperEditor from './step/paperEditor.vue'
import { MessagePlugin } from 'tdesign-vue-next'

export default {
  name: 'PaperEdit',
  components: {
    StepHeader,
    PaperEditor
  },
  data() {
    return {
      currentStep: 1,
      type: 'add',
      paperId: null,
      paperType: ''
    }
  },
  created() {
    this.initializeData();
  },
  // 添加 beforeRouteEnter 导航守卫
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.initializeData();
    });
  },
  methods: {
    // 初始化数据方法
    initializeData() {
      const { type, paperId, paperType } = this.$route.query;
      this.type = type;
      this.paperId = paperId;
      this.paperType = paperType;

      // 根据类型设置当前步骤
      this.currentStep = (this.type === 'edit' && this.paperId) ? 2 : 1;
    },
    goBack() {
      this.$router.push('/resourceLibrary/paper')
    },
    // 处理步骤变化
    handleStepChange(step) {
      this.currentStep = step;
    },
    // 处理保存成功
    handleSave() {
      this.$router.push('/resourceLibrary/paper');
    },
    // 显示错误信息
    showError(message) {
      MessagePlugin.error(message)
    }
  }
}
</script>

<style scoped>
.paper-edit {
  padding: 20px;

  .header-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    min-width: 800px;
    height: 40px;

    .el-button {
      position: absolute;
      left: 0;
    }
  }
}
</style>
