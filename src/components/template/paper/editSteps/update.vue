<template>
  <div>
    <paper-editor
      :type="'edit'"
      :paper-id="editStepData.paperId"
      :paper-type="editStepData.paperType"
      :is-embedded="true"
      @save="handleSave"
    />
  </div>
</template>

<script setup>
import PaperEditor from '../step/paperEditor.vue'
import { reactive, onMounted } from 'vue'

const emit = defineEmits(['confirm'])
const props = defineProps({
  paperId: {
    type: String,
    default: '',
  },
  paperType: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: '',
  },
})

const editStepData = reactive({
  type: props.type,
  paperId: props.paperId,
  paperType: props.paperType,
})

onMounted(() => {
  initializeData()
})

// 初始化数据的方法
function initializeData() {

  editStepData.paperId = props.paperId
  editStepData.paperType = props.paperType
}


// 处理保存事件
function handleSave(data) {
  emit('confirm', data)
}
</script>
