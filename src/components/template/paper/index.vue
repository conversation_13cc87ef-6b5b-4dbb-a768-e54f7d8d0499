<template>
  <div class="app-container">
    <Cards>
      <!-- Tab切换 -->
      <t-tabs v-model="activeTab" @change="handleTabClick">
        <t-tab-panel value="1" label="试卷" />
        <t-tab-panel value="2" label="作业" />
      </t-tabs>

      <div class="tool">
        <!-- 搜索表单 -->
        <t-form
          :data="queryParams"
          ref="queryRef"
          layout="inline"
          v-show="showSearch"
        >
          <t-form-item
            name="paperTitle"
            :label="activeTab === '2' ? '作业名称:' : '试卷名称:'"
          >
            <t-input
              v-model="queryParams.paperTitle"
              :placeholder="
                activeTab === '2' ? '请输入作业名称' : '请输入试卷名称'
              "
              clearable
              @keyup="handleQuery"
            />
          </t-form-item>
          <t-form-item style="margin-left: -100px">
            <t-button theme="primary" @click="handleQuery">
              <template #icon><SearchIcon /></template>
              搜索
            </t-button>
            <t-button @click="resetQuery" style="margin-left: 5px">
              <template #icon><RefreshIcon /></template>
              重置
            </t-button>
          </t-form-item>
        </t-form>

        <!-- 操作按钮 -->
        <div class="operation-area">
          <t-button
            theme="primary"
            @click="handleAdd"

          >
            <template #icon><FileAddIcon /></template>
            {{ activeTab === '2' ? '创建作业' : '创建试卷' }}
          </t-button>
          <t-button
            theme="primary"
            @click="handleAddQuestion"
          >
            <template #icon><FileAddIcon /></template>
            添加题目
          </t-button>
          <t-button
            theme="warning"
            @click="goToRecycle"
          >
            <template #icon><DeleteIcon /></template>
            回收站
          </t-button>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          />
        </div>
      </div>

      <!-- 试卷列表 -->
      <div class="paper-list">
        <t-list v-if="paperList.length > 0">
          <t-list-item v-for="(item, index) in paperList" :key="index">
            <t-card :hover="true" class="paper-item">
              <div class="paper-content">
                <div class="paper-icon">
                  <FileIcon size="40px" />
                </div>

                <div class="paper-info">
                  <h3 class="paper-title">{{ item.paperTitle }}</h3>
                  <div class="paper-stats">
                    <span>试题数量: {{ item.questionQuantity }}</span>
                    <t-divider layout="vertical" />
                    <template v-if="item.paperType == '1'">
                      <span>总分: {{ item.totalScore }}</span>
                      <t-divider layout="vertical" />
                    </template>
                    <span>创建时间: {{ item.createTime }}</span>
                  </div>
                </div>

                <div class="paper-actions">
                  <t-space>
                    <t-button
                      theme="primary"
                      variant="text"
                      @click="handleEdit(item)"
                    >
                      {{ activeTab === '2' ? '编辑作业' : '编辑试卷' }}
                    </t-button>
                    <t-button
                      theme="primary"
                      variant="text"
                      @click="handleCopy(item)"
                      >复制</t-button
                    >
                    <t-button
                      theme="primary"
                      variant="text"
                      @click="handlePreview(item)"
                      >预览</t-button
                    >
                    <t-button
                      theme="danger"
                      variant="text"
                      @click="handleDelete(item)"
                    >
                      删除
                    </t-button>
                  </t-space>
                </div>
              </div>
            </t-card>
          </t-list-item>
        </t-list>
        <t-empty v-else  style="margin-top: 200px"/>
     
      </div>

      <!-- 分页 -->
      <t-pagination
        v-if="total > 0"
        :total="total"
        v-model:current="queryParams.pageNum"
        v-model:pageSize="queryParams.pageSize"
        @change="getList"
      />
    </Cards>

    <!-- 添加/修改对话框 -->
    <t-dialog :visible="open" :header="title" :width="500" @close="cancel">
      <t-form ref="paperRef" :data="form" :rules="rules" :label-width="80">
        <t-form-item label="试卷标题" prop="paperTitle">
          <t-input v-model="form.paperTitle" placeholder="请输入试卷标题" />
        </t-form-item>
        <t-form-item label="小题数" prop="questionQuantity">
          <t-input v-model="form.questionQuantity" placeholder="请输入小题数" />
        </t-form-item>
        <t-form-item label="总分" prop="totalScore">
          <t-input v-model="form.totalScore" placeholder="请输入总分" />
        </t-form-item>
        <t-form-item label="删除标志" prop="delFlag">
          <t-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </t-form-item>
        <t-divider content-position="center">试卷题型集合信息</t-divider>
        <t-row :gutter="10" class="mb8">
          <t-col :span="1.5">
            <t-button
              type="primary"
              icon="Plus"
              @click="handleAddDtbTestPaperQuestionCollection"
              >添加</t-button
            >
          </t-col>
          <t-col :span="1.5">
            <t-button
              type="danger"
              icon="Delete"
              @click="handleDeleteDtbTestPaperQuestionCollection"
              >删除</t-button
            >
          </t-col>
        </t-row>
        <t-table
          :data="dtbTestPaperQuestionCollectionList"
          :columns="dtbTestPaperQuestionCollectionColumns"
          :row-class-name="rowDtbTestPaperQuestionCollectionIndex"
          @selection-change="
            handleDtbTestPaperQuestionCollectionSelectionChange
          "
          ref="dtbTestPaperQuestionCollection"
        />
      </t-form>
      <template #footer>
        <t-space>
          <t-button theme="primary" @click="submitForm">确 定</t-button>
          <t-button theme="default" @click="cancel">取 消</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 预览对话框 -->
    <template-paper-preview
      v-model="previewDialogVisible"
      :paper-id="currentPaperId"
      :paper-type="activeTab"
    />

    <!-- 删除确认对话框 -->
    <DeleteModal
      v-model:visible="deleteModalVisible"
      :item="currentDeleteItem"
      @confirm="handleDeleteConfirm"
    />
  </div>
</template>

<script setup name="Paper">
import {
  listPaper,
  getPaper,
  addPaper,
  updatePaper,
  getTestPaperQuestions,
  moveToRecycleBin,
  copyPaper,
  checkBeforeEdit,
} from '@/api/book/paper'
import TemplatePaperPreview from './preview/index.vue'
import DeleteModal from '@/components/DeleteModal/index.vue'
import { useRouter } from 'vue-router'
import {
  Table as TTable,
  MessagePlugin,
  DialogPlugin,
  Input as TInput,
  Select as TSelect,
  Option as TOption,
} from 'tdesign-vue-next'
import {
  FileAddIcon,
  DeleteIcon,
  FileIcon,
  SearchIcon,
  RefreshIcon,
} from 'tdesign-icons-vue-next'
import { questionTypeMap } from '@/utils/quetions-utils'

const { proxy } = getCurrentInstance()

const router = useRouter()
const route = router.currentRoute.value
const activeTab = ref('1')

const paperList = ref([])
const dtbTestPaperQuestionCollectionList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const checkedDtbTestPaperQuestionCollection = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')

const formState = reactive({
  form: {
    paperId: null,
    paperTitle: null,
    questionQuantity: null,
    totalScore: null,
    paperType: '',
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    delFlag: null,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    paperTitle: null,
    questionQuantity: null,
    totalScore: null,
    paperType: '1', // 默认查询试卷类型
  },
  rules: {
    paperType: [{ required: true, message: '类型不能为空', trigger: 'change' }],
  },
})

const { queryParams, form, rules } = toRefs(formState)

onMounted(() => {
  activeTab.value = route.query.activeTab?.toString() || '1'
  handleTabClick(activeTab.value)
})
/** 查询试卷列表 */
function getList() {
  loading.value = true
  listPaper(queryParams.value).then((response) => {
    paperList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    paperId: null,
    paperTitle: null,
    questionQuantity: null,
    totalScore: null,
    paperType: activeTab.value, // 根据当前tab设置类型
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    delFlag: null,
  }
  dtbTestPaperQuestionCollectionList.value = []
  proxy.$refs.paperRef.reset()
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.$refs.queryRef.reset()
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.paperId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  proxy.$router.push({
    path: '/resourceLibrary/paper/edit',
    query: {
      type: 'add',
      paperType: activeTab.value,
    },
  })
}

/** 添加试题按钮操作 */
function handleAddQuestion() {
  const currentOrigin = window.location.origin
  const targetUrl = `${currentOrigin}/#/resourceLibrary/userQuestion`
  window.open(targetUrl, '_blank')
}

/** 修改按钮操作 */
function handleEdit(row) {
  // 先调用检测接口
  checkBeforeEdit(row.paperId)
    .then((response) => {
      if (response.code === 200 && response.data === true) {
        // 可以编辑，跳转到编辑页面
        proxy.$router.push({
          path: '/resourceLibrary/paper/edit',
          query: {
            type: 'edit',
            paperId: row.paperId,
            paperType: activeTab.value,
          },
        })
      } else {
        // 不能编辑，显示提示信息
        MessagePlugin.error('教材已发布，当前试卷不可编辑')
      }
    })
    .catch((error) => {
      console.error('检测编辑失败：', error)
      MessagePlugin.error('检测编辑失败')
    })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['paperRef'].validate((valid) => {
    if (valid) {
      form.value.dtbTestPaperQuestionCollectionList =
        dtbTestPaperQuestionCollectionList.value
      if (form.value.paperId != null) {
        updatePaper(form.value).then((response) => {
          MessagePlugin.success('修改成功')
          open.value = false
          getList()
        })
      } else {
        addPaper(form.value).then((response) => {
          MessagePlugin.success('新增成功')
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 改为移入回收站 */
function handleDelete(row) {
  currentDeleteItem.value = row
  deleteModalVisible.value = true
}

/** 确认删除操作 */
function handleDeleteConfirm(callback) {
  const _paperIds = currentDeleteItem.value?.paperId || ids.value
  moveToRecycleBin(_paperIds)
    .then(() => {
      getList()
      MessagePlugin.success('已移入回收站')
      deleteModalVisible.value = false  // 关闭弹窗
      currentDeleteItem.value = null    // 清空当前删除项
      callback(true)
    })
    .catch((error) => {
      console.error(error)
      callback(false)
    })
}

/** 试卷题型集合序号 */
function rowDtbTestPaperQuestionCollectionIndex({ row, rowIndex }) {
  row.index = rowIndex + 1
}

/** 试卷题型集合添加按钮操作 */
function handleAddDtbTestPaperQuestionCollection() {
  let obj = {}
  obj.sort = ''
  obj.questionType = ''
  dtbTestPaperQuestionCollectionList.value.push(obj)
}

/** 试卷题型集合删除按钮操作 */
function handleDeleteDtbTestPaperQuestionCollection() {
  if (checkedDtbTestPaperQuestionCollection.value.length == 0) {
    MessagePlugin.error('请先选择要删除的试卷题型集合数据')
  } else {
    const dtbTestPaperQuestionCollections =
      dtbTestPaperQuestionCollectionList.value
    const checkedDtbTestPaperQuestionCollections =
      checkedDtbTestPaperQuestionCollection.value
    dtbTestPaperQuestionCollectionList.value =
      dtbTestPaperQuestionCollections.filter(function (item) {
        return checkedDtbTestPaperQuestionCollections.indexOf(item.index) == -1
      })
  }
}

/** 复选框选中数据 */
function handleDtbTestPaperQuestionCollectionSelectionChange(selection) {
  checkedDtbTestPaperQuestionCollection.value = selection.map(
    (item) => item.index,
  )
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'book/paper/export',
    {
      ...queryParams.value,
    },
    `paper_${new Date().getTime()}.xlsx`,
  )
}

// 修改 handleTabClick 方法
function handleTabClick(value) {
  // 更新 query 参数
  router.replace({
    query: { ...route.query, activeTab: value },
  })
  // 更新 paperType 并获取列表
  queryParams.value.paperType = value
  queryParams.value.pageNum = 1 // 切换类型时重置页码
  getList()
}

function goToRecycle() {
  proxy.$router.push({
    path: '/resourceLibrary/paper/recycle',
    query: { activeTab: activeTab.value },
  })
}

// 预览相关的响应式变量
const previewDialogVisible = ref(false)
const currentPaperId = ref(null)
const deleteModalVisible = ref(false)
const currentDeleteItem = ref(null)

// 修改 handlePreview 方法
const handlePreview = (row) => {
  currentPaperId.value = row.paperId
  previewDialogVisible.value = true
}

// 获取题型名称的方法
const getQuestionTypeName = (type) => {
  return questionTypeMap[type] || '未知题型'
}

onActivated(() => {
  getList()
})

/** 复制按钮操作 */
const handleCopy = async (row) => {
  // 使用DialogPlugin.confirm()创建对话框实例
  const confirmDialog = DialogPlugin.confirm({
    header: '确认',
    body: `是否确认复制该${activeTab.value === '2' ? '作业' : '试卷'}?`,
    confirmBtn: '确定',
    cancelBtn: '取消',
    onClose: () => {
      confirmDialog.hide() // 使用实例的hide方法关闭
    },
    onConfirm: () => {
      loading.value = true // 开始加载
      copyPaper(row.paperId)
        .then(() => {
          MessagePlugin.success('复制成功')
          getList() // 刷新列表
          confirmDialog.hide() // 使用实例的hide方法关闭
        })
        .catch((error) => {
          console.error('复制失败:', error)
          MessagePlugin.error('复制失败')
        })
        .finally(() => {
          loading.value = false // 无论成功或失败都结束加载
        })
    },
  })
}

// 修改 dtbTestPaperQuestionCollectionColumns 定义
const dtbTestPaperQuestionCollectionColumns = ref([
  { colKey: 'selection', type: 'multiple', width: 50, align: 'center' },
  { colKey: 'index', title: '序号', width: 50, align: 'center' },
  {
    colKey: 'sort',
    title: '排序',
    width: 150,
    cell: (h, { row }) =>
      h(TInput, {
        value: row.sort,
        'onUpdate:value': (value) => (row.sort = value),
        placeholder: '请输入排序',
      }),
  },
  {
    colKey: 'questionType',
    title: '小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程',
    width: 150,
    cell: (h, { row }) =>
      h(
        TSelect,
        {
          value: row.questionType,
          'onUpdate:value': (value) => (row.questionType = value),
          placeholder:
            '请选择小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程',
        },
        {
          default: () =>
            h(TOption, {
              label: '请选择字典生成',
              value: '',
            }),
        },
      ),
  },
])

// 修改消息提示的实现
function showMessage(message, type = 'success') {
  MessagePlugin[type](message)
}

// 修改确认对话框的实现
function showConfirm(message) {
  return DialogPlugin.confirm({
    header: '提示',
    body: message,
    confirmBtn: '确定',
    cancelBtn: '取消',
  })
}

getList()
</script>
<style scoped>
.tool {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .operation-area {
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
  }
}

.paper-list {
  padding: 20px;

  .paper-item {
    margin-bottom: 16px;
    background-color: #edf4fd;
    width: 100%;

    .paper-content {
      display: flex;
      align-items: center;
      padding: 24px 30px;
      min-height: 60px;
    }

    .paper-icon {
      margin-right: 24px;
    }

    .paper-info {
      flex: 1;

      .paper-title {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }

      .paper-stats {
        color: #666;
        font-size: 14px;
        display: flex;
        align-items: center;

        .t-divider--vertical {
          margin: 0 16px;
          height: 14px;
        }
      }
    }

    .paper-actions {
      display: flex;
      gap: 12px;
      margin-left: 24px;
    }
  }
}

.pagination {
  float: right;
  margin: 20px 0;
}

.preview-container {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;

  .paper-info {
    text-align: center;
    margin-bottom: 20px;

    .paper-title {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 15px;
    }

    .paper-meta {
      color: #606266;
      font-size: 14px;

      .t-divider--vertical {
        margin: 0 15px;
      }
    }
  }

  .questions-list {
    .question-collection {
      margin-bottom: 24px;

      .collection-header {
        margin-bottom: 16px;

        h3 {
          color: #303133;
          font-size: 18px;
          font-weight: 500;
        }
      }
    }

    .question-item {
      margin-bottom: 20px;

      .question-header {
        margin-bottom: 15px;

        .question-index {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-right: 10px;
        }

        .question-score {
          color: #409eff;
          margin-left: 8px;
          font-size: 14px;
        }
      }
    }
  }
}

.preview-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #909399;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

:deep(.t-card) {
  margin-bottom: 16px;
  width: 100%;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

:deep(.t-space) {
  width: 100%;
}

:deep(.t-form--inline) {
  .t-form__item {
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }

  .t-button + .t-button {
    margin-left: 12px;
  }
}

.question-collection {
  margin-bottom: 24px;

  .collection-header {
    margin-bottom: 16px;

    h3 {
      color: #303133;
      font-size: 18px;
      font-weight: 500;
    }
  }
}

.question-score {
  color: #409eff;
  margin-left: 8px;
  font-size: 14px;
}
</style>
