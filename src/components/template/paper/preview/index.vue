<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :title="title"
    width="900px"
    :close-on-overlay-click="false"
    attach="body"
    :confirm-btn="null"
    :cancel-btn="null"
  >
    <div class="preview-container">
      <div class="paper-info">
        <h2
          class="paper-title"
          :style="`color:${options.theme !== 'light' ? '#fff' : '#666'}`"
        >
          {{ previewData.paperTitle }}
        </h2>
        <div class="paper-meta">
          <span v-if="paperType != '2'"
            >总分: {{ previewData.totalScore }}分</span
          >
          <span>题目数量: {{ previewData.questionQuantity }}道</span>
        </div>
      </div>
      <t-divider />
      <!-- 题目列表 -->
      <div class="questions-list">
        <!-- 按题型分组显示 -->
        <div
          v-for="(collection, collectionIndex) in previewData.groupedQuestions"
          :key="collectionIndex"
          class="question-collection"
        >
          <div class="collection-header">
            <h3>
              {{ getQuestionTypeName(collection.questionType) }}
              <template v-if="paperType != '2'">
                （共{{ collection.questions.length }}题，共{{
                  collection.totalScore
                }}分）
              </template>
              <template v-else>
                （共{{ collection.questions.length }}题）
              </template>
            </h3>
          </div>
          <div
            v-for="(question, index) in collection.questions"
            :key="question.questionId"
            class="question-item"
          >
            <div class="question-header">
              <span class="question-index">第{{ index + 1 }}题</span>
              <span v-if="paperType != '2'" class="question-score"
                >({{ question.score || 0 }}分)</span
              >
            </div>
            <template-paper-question-preview :question="question" />
            <t-divider v-if="index !== collection.questions.length - 1" />
          </div>
          <t-divider
            v-if="
              collectionIndex !==
              Object.keys(previewData.groupedQuestions).length - 1
            "
          />
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script setup>
import { MessagePlugin } from 'tdesign-vue-next'
import { computed, ref, watch } from 'vue'

import { getPaper, getTestPaperQuestions } from '@/api/book/paper'
const { options } = useStore()
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  paperId: {
    type: [String, Number],
    default: null,
  },
  paperType: {
    type: String,
    default: '1',
  },
})

const emit = defineEmits(['update:modelValue', 'load-error'])

const dialogVisible = ref(false)
const previewData = ref({
  paperTitle: '',
  totalScore: 0,
  questionQuantity: 0,
  groupedQuestions: [],
})

// 计算标题
const title = computed(() => {
  return props.paperType === '2' ? '作业预览' : '试卷预览'
})

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
    if (val && props.paperId) {
      loadPreviewData()
    }
  },
)

// 监听 dialogVisible 变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:modelValue', val)
  },
)

// 获取题型名称的方法
const getQuestionTypeName = (type) => {
  const questionTypes = {
    1: '单选题',
    2: '多选题',
    3: '填空题',
    4: '排序题',
    5: '连线题',
    6: '简答题',
    7: '判断题',
    8: '编程题',
  }
  return questionTypes[type] || '未知题型'
}

// 加载预览数据
const loadPreviewData = async () => {
  try {
    const [paperResponse, questionsResponse] = await Promise.all([
      getPaper(props.paperId),
      getTestPaperQuestions(props.paperId),
    ])

    const paperData = paperResponse.data
    if (!paperData) {
      let message = '该试卷不存在，请重新引入试卷'
      if (props.paperType === '2') {
        message = '该作业不存在，请重新引入试卷'
      }
      MessagePlugin.error(message)
      emit('load-error', message)
      return
    }

    const questionsData = questionsResponse.data || []

    const groupedQuestions = {}

    questionsData.forEach((collection) => {
      const { questionType } = collection
      if (!groupedQuestions[questionType]) {
        groupedQuestions[questionType] = {
          questionType,
          questions: [],
          totalScore: 0,
          sort: collection.sort || 0, // 保存题型集合的sort值
        }
      }

      collection.questionList.forEach((item) => {
        const question = item.questionContent
        // 优先使用题目本身的分数，如果没有则使用题型集合的分数
        const score = Number(item.questionScore || 0)
        groupedQuestions[questionType].questions.push({
          ...question,
          sort: item.sort || collection.sort,
          score,
          options: question.options || [],
        })
        groupedQuestions[questionType].totalScore += score
      })
    })

    // 将对象转换为数组，然后按题型集合的sort属性排序
    const sortedGroupedQuestions = Object.values(groupedQuestions).sort(
      (a, b) => (a.sort || 0) - (b.sort || 0),
    )

    // 更新预览数据
    previewData.value = {
      paperTitle: paperData.paperTitle,
      totalScore: paperData.totalScore,
      questionQuantity: paperData.questionQuantity,
      groupedQuestions: sortedGroupedQuestions,
    }
  } catch (error) {
    console.error('获取试卷预览数据失败:', error)
    const message = '获取预览数据失败'
    MessagePlugin.error(message)
    emit('load-error', message)
  }
}
</script>

<style lang="less" scoped>
.preview-container {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;

  .paper-info {
    text-align: center;
    margin-bottom: 20px;

    .paper-title {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 15px;
    }

    .paper-meta {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: nowrap;
      gap: 8px;

      span {
        font-size: 15px;
        color: #1890ff;
        font-weight: 500;
        white-space: nowrap;
      }

      .t-divider--vertical {
        margin: 0 4px;
        height: 16px;
      }
    }
  }

  .questions-list {
    .question-collection {
      margin-bottom: 24px;

      .collection-header {
        margin-bottom: 16px;

        h3 {
          color: #303133;
          font-size: 18px;
          font-weight: 500;
        }
      }
    }

    .question-item {
      margin-bottom: 20px;

      .question-header {
        margin-bottom: 15px;

        .question-index {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-right: 10px;
        }

        .question-score {
          color: #409eff;
          margin-left: 8px;
          font-size: 14px;
        }
      }
    }
  }
}

.preview-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #909399;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

:deep(.t-card) {
  margin-bottom: 16px;
  width: 100%;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

:deep(.t-space) {
  width: 100%;
}

:deep(.t-form--inline) {
  .t-form__item {
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }

  .t-button + .t-button {
    margin-left: 12px;
  }
}

.question-collection {
  margin-bottom: 24px;

  .collection-header {
    margin-bottom: 16px;

    h3 {
      color: #303133;
      font-size: 18px;
      font-weight: 500;
    }
  }
}

.question-score {
  color: #409eff;
  margin-left: 8px;
  font-size: 14px;
}

// 为富文本中的代码块添加黑框样式
:deep(pre) {
  background-color: #1e1e1e;
  color: #d4d4d4;
  border-radius: 4px;
  padding: 16px;
  margin: 12px 0;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.preview-container {
  .paper-info {
    .paper-meta {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: nowrap;
      gap: 8px;

      span {
        font-size: 15px;
        color: #1890ff;
        font-weight: 500;
        white-space: nowrap;
      }

      .t-divider--vertical {
        margin: 0 4px;
        height: 16px;
      }
    }
  }

  .questions-list {
    .question-collection {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;

      .collection-header {
        padding: 16px 0;

        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          text-align: left;
          margin: 0;
        }
      }

      .question-item {
        background-color: #fafafa;
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 16px;

        .question-header {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          border-bottom: 1px solid #e8e8e8;
          padding-bottom: 12px;

          .question-index {
            font-size: 16px;
            color: #1890ff;
            margin-right: 12px;
          }

          .question-score {
            color: #ff4d4f;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 添加 dialog 相关样式
:deep(.t-dialog) {
  margin-top: 5vh !important;
  height: 90vh;
  display: flex;
  flex-direction: column;

  .t-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }
}

.preview-container {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  // 会造成下方增加滚动轴
  // width: 100%;
  display: flex;
  flex-direction: column;
}
</style>
