<template>
  <div class="dialog-content">
    <t-form ref="dataRef" :data="quizData" layout="inline" :label-width="120">
      <t-form-item
        :label="
          paperType === '1'
            ? t('insert.testPaper.labelName')
            : t('insert.schoolAssignment.labelName')
        "
      >
        <t-input
          v-model="quizData.paperTitle"
          style="width: 160px"
          size="medium"
          clearable
          :placeholder="
            paperType === '1'
              ? t('insert.testPaper.placeholderName')
              : t('insert.schoolAssignment.placeholderName')
          "
        ></t-input>
      </t-form-item>
      <t-space>
        <t-button theme="primary" type="submit" @click="handleQuery">
          <template #icon><t-icon name="search" /></template
          >{{ t('insert.testPaper.search') }}
        </t-button>
        <t-button theme="primary" variant="outline" @click="handleAdd">
          <template #icon><t-icon name="add" /></template
          >{{
            paperType === '1'
              ? t('insert.testPaper.addTestPaper')
              : t('insert.schoolAssignment.addAssignment')
          }}
        </t-button>
      </t-space>
    </t-form>
    <div class="content-container">
      <div class="quiz-list-container">
        <template v-if="quizData.rows.length <= 0">
          <t-space
            direction="vertical"
            :align="'center'"
            style="margin-top: 80px"
          >
            <t-empty />
            <t-button
              theme="primary"
              type="submit"
              @click="navigateToPapersAndAssignment"
            >
              <template #icon><t-icon name="add" /></template
              >{{
                props.paperType === '1'
                  ? t('insert.testPaper.addTestPaper')
                  : t('insert.schoolAssignment.addAssignment')
              }}
            </t-button>
          </t-space>
        </template>
        <div
          v-for="item in quizData.rows"
          :key="item.id"
          class="list-item quiz-questions-list"
        >
          <div class="list-item-checkbox">
            <t-checkbox @change="selectedQuizChange($event, item)"></t-checkbox>
          </div>
          <div class="list-item-img">
            <img :src="paperIcon" alt="" />
          </div>
          <div class="list-item-info">
            <div class="list-item-info-title">{{ item.paperTitle }}</div>
            <div class="paper-stats">
              <span>试题数量: {{ item.questionQuantity }}</span>
              <t-divider layout="vertical" />
              <template v-if="item.paperType == '1'">
                <span>总分: {{ item.totalScore }}</span>
                <t-divider layout="vertical" />
              </template>
            </div>
          </div>
          <div class="list-item-info-time">
            <div class="list-item-info-time-text">
              <Calendar1Icon />{{ item.createTime }}
            </div>
            <div class="list-item-info-btn">
              <t-button size="small" theme="success" @click="viewQuiz(item)">
                <template #icon>
                  <BrowseIcon />
                </template>
                {{ t('insert.testPaper.view') }}</t-button
              >
            </div>
          </div>
        </div>
      </div>
      <div class="pagination-container" v-show="quizData.rows.length > 0">
        <t-pagination
          v-model:current="quizData.pageNum"
          v-model:page-size="quizData.pageSize"
          :total="quizData.total"
          @change="queryQuizByPagination"
          style="min-width: 450px;"
        />
      </div>
    </div>
  </div>

  <template-paper-preview
    v-model="previewQuizVisibility"
    :paper-id="onPreviewQuiz?.paperId"
    :paper-type="onPreviewQuiz?.paperType"
  ></template-paper-preview>
</template>
<script setup>
import { BrowseIcon, Calendar1Icon } from 'tdesign-icons-vue-next'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { listPaper } from '@/api/book/paper'
import paperIcon from '@/assets/images/paper.svg'
const router = useRouter()
const route = useRoute()

const emit = defineEmits(['update'])
const props = defineProps({
  paperType: {
    type: String,
    default: '1',
  },
})

let selectedQuiz = []
let previewQuizVisibility = $ref(false)
const quizData = $ref({
  rows: [],
  total: 0,
  pageNum: 1,
  pageSize: 10,
  paperType: props.paperType,
  paperTitle: '',
})
let onPreviewQuiz = $ref(null)
// const dataRef = ref(null)
const loading = ref(true)
// 1编辑器后台 2管理后台
const formType = ref(1)

// 方法部分
function handleAdd() {
  // 使用当前页面的完整URL作为base，确保认证状态正确
  const currentOrigin = window.location.origin
  const targetUrl = `${currentOrigin}/#/resourceLibrary/paper?activeTab=${props.paperType}`
  window.open(targetUrl, '_blank')
}
function navigateToPapersAndAssignment() {
  if (formType.value == 1) {
    router.push({
      path: '/resourceLibrary/paper',
      query: {
        activeTab: props.paperType,
      },
    })
  } else {
    window.location.href = `${import.meta.env.VITE_APP_MANAGE_URL}resource/paper?activeTab=${
      props.paperType
    }`
  }
}
function selectedQuizChange(selected, quiz) {
  if (selected) {
    selectedQuiz.push(quiz)
  } else {
    selectedQuiz = selectedQuiz.filter((item) => item.paperId !== quiz.paperId)
  }

  emit('update', selectedQuiz)
}
function handleQuery() {
  quizData.pageNum = 1
  loading.value = true
  quizData.rows = []
  listPaper(quizData).then(handlePaginationResponse)
}
function queryQuizByPagination() {
  loading.value = true
  quizData.rows = []
  listPaper(quizData).then(handlePaginationResponse)
}
function viewQuiz(quizItem) {
  console.log('quizItem', quizItem)
  onPreviewQuiz = {
    paperType: quizItem.paperType,
    paperId: quizItem.paperId,
  }
  previewQuizVisibility = true
}
function handlePaginationResponse(quizResp) {
  quizData.rows = quizResp.rows || []
  quizData.total = quizResp.total || 0
  loading.value = false
}
onMounted(() => {
  quizData.rows = []
  selectedQuiz = []
  formType.value = route.query.formType || 1
  listPaper(quizData).then(handlePaginationResponse)
})
</script>
<style lang="less" scoped>
.dialog-content {
  padding: 20px;
  max-height: 60vh;
  display: flex;
  flex-direction: column;

  .t-form {
    flex-shrink: 0; // 搜索表单不参与滚动
    margin-bottom: 16px; // 适中的底部间距
  }

  // 内容容器
  .content-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    
    // 题目列表区域（可滚动）
    .quiz-list-container {
      flex: 1;
      overflow-y: auto;
      min-height: 0;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      
      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
        &:hover {
          background: #a8a8a8;
        }
      }
    }
    
    // 分页容器固定在底部
    .pagination-container {
      flex-shrink: 0;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #eaeef3;
      display: flex;
      justify-content: center;
    }
  }

  .quiz-questions-list {
    display: flex;
    padding: 20px 10px;
    background-color: #fff;
    margin: 20px 0;
    border: 1px solid #eaeef3;
    cursor: pointer;
    transition: all 0.3s;
    border-radius: 5px;
    color: #666;
    &:hover {
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .list-item-checkbox {
      width: 30px;
    }
    .list-item-img {
      width: 50px;
      height: 50px;
      background-color: #eaeef3;
      border-radius: 5px;
      img {
        width: 100%;
      }
    }

    .list-item-info {
      flex: 1;
      margin-left: 20px;

      justify-content: space-between;
      .list-item-info-title {
        font-size: 16px;
        font-weight: bold;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 270px;
        .paper-stats {
          color: #666;
          font-size: 14px;
          display: flex;
          align-items: center;

          .t-divider--vertical {
            margin: 0 16px;
            height: 14px;
          }
        }
      }
      .list-item-info-desc {
        display: flex;
        margin-top: 10px;
        div {
          margin-right: 20px;
        }
      }
    }
    .list-item-info-time {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-top: 10px;
      color: #999;
      .list-item-info-time-text {
        display: flex;
        align-items: center;
      }
      .list-item-info-btn {
        margin-top: 10px;
        text-align: right;
      }
    }
  }
}
</style>
