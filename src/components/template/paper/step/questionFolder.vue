<template>
  <div class="question-folder">
    <!-- 题目分类目录 -->
    <t-tree
      :data="folderData"
      :keys="defaultProps"
      @click="handleNodeClick"
      hover
    >
      <template #label="{ node }">
        <div class="custom-tree-node">
          <span class="folder-icon">
            <t-icon :name="node.data.icon || 'folder'" />
          </span>
          <span class="folder-label">{{ node.label }}</span>
          <span class="folder-count" v-if="node.data.questionCount">({{ node.data.questionCount }})</span>
        </div>
      </template>
    </t-tree>
  </div>
</template>

<script>
import { getPersonalQuestionLibrary } from '@/api/book/bookQuestionFolder'
// 引入 TDesign 图标
import { FolderIcon } from 'tdesign-icons-vue-next'

export default {
  name: 'QuestionFolder',
  components: {
    FolderIcon,
  },
  data() {
    return {
      folderData: [],
      defaultProps: {
        children: 'children',
        label: 'folderName'
      }
    }
  },
  methods: {
    // 获取题库数据
    async getFolders() {
      try {
        const response = await getPersonalQuestionLibrary()
        // 处理新的数据格式
        const processedData = response.data.map(item => {
          // 移除了对 folders 数组的检查，允许空文件夹
          const folders = item.folders || []
          // 构建树形结构
          const folderMap = new Map()
          folders.forEach(folder => {
            folder.children = []
            folderMap.set(folder.folderId, folder)
          })
          
          const rootFolders = []
          folders.forEach(folder => {
            if (folder.parentId === '0') {
              rootFolders.push(folder)
            } else {
              const parentFolder = folderMap.get(folder.parentId)
              if (parentFolder) {
                parentFolder.children.push(folder)
              }
            }
          })
          
          return {
            folderName: item.folderName || item.bookName,
            folderId: item.folderId,
            parentId: '0',
            bookId: item.bookId,
            bookName: item.bookName,
            children: rootFolders,
            type: item.folderType,
            icon: 'el-icon-collection'
          }
        })
        
        return processedData
      } catch (error) {
        console.error('获取题库数据失败:', error)
        return []
      }
    },

    // 初始化数据
    async initFolderData() {
      const folders = await this.getFolders()
      this.folderData = folders

      // 自动选中第一个文件夹
      this.$nextTick(() => {
        if (this.folderData && this.folderData.length > 0) {
          // 选中第一个文件夹
          this.handleNodeClick(this.folderData[0])
        }
      })
    },

    handleNodeClick(data) {
      const resourceData = data.node?.data ?? {}
      this.$emit('folder-selected', {
        folderId: resourceData.folderId,
        folderName: resourceData.folderName,
        bookId: resourceData.bookId,
        parentId: resourceData.parentId,
        questionCount: resourceData.questionCount,
        userId: resourceData.userId,
        userFolderId: resourceData.userFolderId,
        type: resourceData.type || resourceData.folderType
      })
    }
  },
  created() {
    this.initFolderData()
  }
}
</script>

<style scoped>
.question-folder {
  padding: 10px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.folder-icon {
  margin-right: 8px;
  color: var(--td-brand-color);
}

.folder-label {
  flex: 1;
}

.folder-count {
  color: var(--td-text-color-secondary);
  margin-left: 4px;
}
</style>
