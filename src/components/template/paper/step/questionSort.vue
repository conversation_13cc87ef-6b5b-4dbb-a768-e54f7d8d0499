<template>
  <div class="score-settings">
    <div class="settings-header">
      <span class="title">题目排序</span>
      <div class="settings-note">
        注: 拖拽题目或题型可调整顺序
      </div>
    </div>

    <div class="collections-container">
      <draggable
        v-model="localCollections"
        :animation="150"
        :group="{ name: 'collections' }"
        item-key="questionType"
        class="collections-container"
        @end="handleCollectionDragEnd"
      >
        <div 
          v-for="item in localCollections" 
          :key="item.questionType"
          class="collection-section"
        >
          <div class="collection-header">
            <t-icon name="move" class="collection-drag-handle"/>
            <h3>{{getTypeTitle(item.questionType)}}</h3>
          </div>

          <draggable
            v-model="item.questions"
            :animation="150"
            :group="{ 
              name: `questions-${item.questionType}`,
              pull: false,
              put: false
            }"
            item-key="questionId"
            class="drag-container"
            @end="handleQuestionDragEnd"
          >
            <div 
              v-for="(question, index) in item.questions" 
              :key="question.questionId"
              class="drag-item"
            >
              <t-icon name="move" class="drag-handle"/>
       
            </div>
          </draggable>
        </div>
      </draggable>
    </div>
  </div>
</template>

<script>
import { VueDraggable } from "vue-draggable-plus";
import { Icon as TIcon } from 'tdesign-vue-next'
import { questionTypeMap } from '@/utils/quetions-utils';

export default {
  name: 'QuestionSort',
  components: {
    draggable: VueDraggable,
    TIcon
  },
  props: {
    collections: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  emits: ['update:collections'],
  data() {
    return {
      localCollections: []
    }
  },
  created() {
    // 添加调试日志

  },
  watch: {
    collections: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.localCollections = JSON.parse(JSON.stringify(newVal || []))
        // 添加调试日志
       
      }
    }
  },
  methods: {
    getTypeTitle(type) {
      return questionTypeMap[type] || '未知题型';
    },
    handleCollectionDragEnd(evt) {


        // 更新每个collection的sort属性，使其与当前顺序一致
        this.localCollections.forEach((collection, index) => {
        collection.sort = index + 1
      })

      this.$emit('update:collections', this.localCollections)
    },
    handleQuestionDragEnd(evt) {

      this.$emit('update:collections', this.localCollections)
    }
  }
}
</script>

<style  scoped>
.score-settings {
  padding: 20px;
  background: var(--td-bg-color-container);
  
  .settings-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--td-border-level-2-color);
    
    .title {
      font-size: 16px;
      font-weight: bold;
      color: var(--td-text-color-primary);
      display: block;
      margin-bottom: 8px;
    }
    
    .settings-note {
      color: red;
      font-size: 14px;
    }
  }

  .collections-container {
    min-height: 42px;
  }
  
  .collection-section {
    margin-bottom: 20px;
    background: var(--td-bg-color-page);
    border-radius: var(--td-radius-medium);
    padding: 10px;
    
    .collection-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 10px;
      
      .collection-drag-handle {
        cursor: move;
        color: var(--td-text-color-placeholder);
        
        &:hover {
          color: var(--td-brand-color);
        }
      }
      
      h3 {
        margin: 0;
        font-size: 16px;
        color: var(--td-text-color-primary);
      }
    }
  }

  .drag-container {
    min-height: 42px;
  }
  
  .drag-item {
    background: var(--td-bg-color-container);
    padding: 10px 15px;
    margin: 8px 0;
    border-radius: var(--td-radius-medium);
    border: 1px solid var(--td-border-level-2-color);
    display: flex;
    align-items: center;
    gap: 12px;
    
    &:hover {
      background: var(--td-bg-color-page);
      
      .drag-handle {
        color: var(--td-brand-color);
      }
    }
    
    .drag-handle {
      cursor: move;
      color: var(--td-text-color-placeholder);
      font-size: 18px;
    }
    
    .question-index {
      color: #606266;
      min-width: 60px;
    }
    
    .question-content {
      flex: 1;
      color: var(--td-text-color-primary);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>