<template>
  <div class="score-settings">
    <div class="settings-header">
      <span class="title">分值设置</span>
      <div class="settings-note">
      </div>
    </div>
    
    <!-- 根据collections顺序展示题目 -->
    <div v-for="collection in collections" :key="collection.questionType" class="question-section">
      <h3>{{ getQuestionTypeTitle(collection.questionType) }}</h3>
      <t-table
        row-key="index"
        :data="getQuestionsForType(collection)"
        :columns="columns"

      >
      </t-table>
    </div>
  </div>
</template>

<script lang="ts">
import { Table as TTable, InputNumber as TInputNumber } from 'tdesign-vue-next'
import { questionTypeMap } from '@/utils/quetions-utils'

interface Question {
  questionId: string | number
  questionType: number
  questionScore: number
  serialNumber?: string | number
}

interface Collection {
  questionType: number
  questions: Question[]
}

export default defineComponent({
  name: 'ScoreSettings',
  components: {
    TTable,
    TInputNumber
  },
  props: {
    questions: {
      required: true,
      default: () => []
    },
    collections: {
      required: true,
      default: () => []
    }
  },
  emits: ['score-change'],
  setup(props, { emit }) {
    const getQuestionsForType = (collection: Collection) => {
      if (!collection?.questions?.length) return []
      
      return collection.questions.map((q, index) => ({
        ...q,
        questionScore: q.questionScore ?? 1,
        questionId: q.questionId,
        questionType: q.questionType,
        index: index + 1
      }))
    }

    const handleScoreChange = (question: Question, value: number) => {
      if (!question?.questionId) return
      
      emit('score-change', {
        questionId: question.questionId,
        questionScore: value
      })
    }

    const columns = ref([
      { 
        colKey: 'index', 
        title: '题号', 
        width: 80, 
        align: 'center',
        cell: (h,{ row }) => row?.index || '未设置'
      },
      { 
        colKey: 'questionScore', 
        title: '分值(分)', 
        width: 120,
        align: 'center',
        cell: (h,{ row }) => {
  
          if (!row) return null
          
          return h(TInputNumber, {
            modelValue: row.questionScore,
            min: 1,
            max: 150,
            size: 'small',
            theme: 'normal',
            align: 'center',
            style: { width: '100px' },
            'onUpdate:modelValue': (value: number) => handleScoreChange(row, value),
            onBlur: (e: Event) => {
              if ( row.questionScore > 150) {
                row.questionScore = '150'
                handleScoreChange(row, 150)
              }

              if ( row.questionScore < 1) {
                row.questionScore = '1'
                handleScoreChange(row, 1)
              }
            }
          })
        }
      }
    ])

    const getQuestionTypeTitle = (type: number) => {
      const typeMap = questionTypeMap;
      return typeMap[type] || '未知题型'
    }

    return {
      columns,
      getQuestionTypeTitle,
      getQuestionsForType
    }
  }
})
</script>

<style scoped>
.score-settings {
  padding: 20px;
  
  .settings-header {
    margin-bottom: 15px;
    
    .title {
      font-size: 16px;
      font-weight: bold;
    }
  }
  
  .settings-note {
    color: #f56c6c;
    font-size: 14px;
  }
  
  .question-section {
    margin-bottom: 20px;
    
    h3 {
      margin-bottom: 10px;
      font-size: 14px;
      color: #666;
    }
  }
}
</style>