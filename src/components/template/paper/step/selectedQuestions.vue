<template>
    <div class="selected-questions">
      <div class="header">
        <div class="title">
          <t-icon name="shop" />
          试题篮
        </div>
      </div>
      <div class="basket-content">
        <template v-for="(group, groupIndex) in groupedQuestions" :key="groupIndex">
          <div class="question-group">
            <div class="group-title">{{ getQuestionTypeName(group.type) }}</div>
            <div v-for="(item, index) in group.questions" :key="item.questionId" class="question-item">
              <div class="question-header">
 
                <span class="question-type">
                  <template v-if="item.questionType === 2">
                    (多选题)
                  </template>
                  <template v-else>
                    ({{ getQuestionTypeName(item.questionType) }})
                  </template>
                </span>
              </div>
              <QuestionPreview 
                :question="item" 
                :showAnswerButton="false"
              />
              <t-button variant="text" class="remove-btn" @click="removeQuestion(item.originalIndex)">移除</t-button>
              <t-divider v-if="index !== group.questions.length - 1" />
            </div>
          </div>
        </template>
      </div>
      <div class="basket-footer">
        <div class="total">
          已选试题（共 {{ selectedCount }} 题）
          <t-button 
            variant="text" 
            size="small" 
            @click="clearAll"
            :disabled="selectedCount === 0"
          >
            清空试题
          </t-button>
        </div>
        <t-button theme="primary" @click="startGenerate" :disabled="!selectedCount">
          {{ paperType === '2' ? '作业编辑' : '开始组卷' }}
        </t-button>
      </div>
    </div>
  </template>
  
  <script>
  import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
  import { ShopIcon } from 'tdesign-icons-vue-next'
import QuestionPreview from '../QuestionPreview/index.vue'
import { questionTypeMap } from '@/utils/quetions-utils'
  
  export default {
    name: 'SelectedQuestions',
    components: {
      QuestionPreview,
      't-icon': ShopIcon
    },
    props: {
      selectedQuestions: {
        type: Array,
        default: () => []
      },
      paperType: {
        type: String,
        default: '1'  // 默认为试卷类型
      }
    },
    computed: {
      selectedCount() {
        return this.selectedQuestions.length
      },
      groupedQuestions() {
        // 按题型分组，但保持原有顺序
        const groups = {}
        const typeOrder = [] // 记录题型出现的顺序
        
        this.selectedQuestions.forEach((question, index) => {
          question.originalIndex = index // 保存原始索引用于移除
          const type = question.questionType
          
          if (!groups[type]) {
            groups[type] = []
            typeOrder.push(type) // 记录题型第一次出现的顺序
          }
          groups[type].push(question)
        })
        
        // 按题型第一次出现的顺序重组并计算全局索引
        let globalIndex = 1
        return typeOrder.map(type => {
          const questions = groups[type].map(q => ({
            ...q,
            globalIndex: globalIndex++
          }))
          return {
            type,
            questions
          }
        })
      }
    },
    methods: {
      getQuestionTypeName(type) {
        return questionTypeMap[type] || '未知题型'
      },
      removeQuestion(index) {
        this.$emit('remove-question', index)
      },
      startGenerate() {
        this.$emit('start-generate')
      },
      handlePreviewError(error) {
        console.error('QuestionPreview error:', error)
        MessagePlugin.error('题目预览加载失败：' + error.message)
      },
      clearAll() {
       const dialog = DialogPlugin.confirm({
          header: '提示',
          body: '确认清空所有已选试题吗？',
          theme: 'warning',
          onConfirm: () => {
            this.$emit('clear-all')
            MessagePlugin.success('已清空所有试题')
            dialog.hide()
          }
        })
      }
    }
  }
  </script>
  
  <style  scoped>
  .selected-questions {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
  
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .title {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  
    .basket-footer {
      border-top: 1px solid #eee;
      padding-top: 10px;
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  
    .basket-content {
      max-height: 500px;
      overflow-y: auto;
      
      .question-group {
        margin-bottom: 20px;
        
        .group-title {
          font-weight: bold;
          margin-bottom: 10px;
          color: var(--td-brand-color);
          border-bottom: 1px solid #eee;
          padding-bottom: 5px;
        }
      }
      
      .question-item {
        position: relative;
        padding: 15px;
        border-radius: 4px;
        background-color: #fff;
        margin-bottom: 10px;
  
        .question-header {
          margin-bottom: 10px;
          color: #606266;
          
          .question-index {
            font-weight: bold;
            margin-right: 8px;
          }
          
          .question-type {
            color: #909399;
          }
        }
  
        .remove-btn {
          position: absolute;
          right: 0;
          color: var(--td-error-color);
          top: 10px;
        }
      }
    }
  }
  
  .debug-info {
    margin: 10px 0;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 12px;
    
    pre {
      margin: 0;
      white-space: pre-wrap;
    }
  }
  </style>