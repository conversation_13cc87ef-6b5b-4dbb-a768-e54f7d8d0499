<template>
    <div class="step-header">
      <t-steps 
        :value="active" 
        @change="$emit('update:active', $event)"
        style="min-width: 400px"
      >
        <t-step-item title="选择题目" />
        <t-step-item :title="paperType === 'homework' ? '作业编辑' : '开始组卷'" />
      </t-steps>
    </div>
  </template>
  
  <script setup lang="ts">

  // 声明props和emits
  const props = defineProps({
    active: {
      type: Number,
      required: true,
      default: 1
    },
    paperType: {
      type: String,
      default: 'paper'
    }
  })

  defineEmits(['update:active'])
  </script>
  
  <style  scoped>
  .step-header {
    display: flex;
    justify-content: center;
  }
  </style>