<template>
  <section class="questionType">
    <div class="question-content">
      <div class="data-item">
        <div class="data-item-em">
          <div v-html="currentData.questionContent"></div>
        </div>
        <div class="data-item-em">
          <!-- 单选框 -->
          <t-radio-group @change="cacheAnswer" v-model="selectedOptionId" style="gap: 10px;width: 100%">
            <t-radio
              v-for="(option, index) in currentData.moocPsychologyHealthScaleQuestionOption"
              :key="option.optionId"
              :label="option.optionId"
              :value="option.optionId"
              :class="getBackgroundColor(index)" class="custom-radio"
            >
<!--              <div style="color: black">{{ getOptionLabel(index) }}. {{ option.optionContent }}</div>-->
              <div class="radio-content">
                <img
                  v-if="selectedOptionId === option.optionId"
                  src="@/assets/editorImg/psy-checked.png"
                  alt="图标"
                  class="radio-icon"
                />
                <div v-else class="radio-icon"></div>
                <div style="color: black">
                  {{ getOptionLabel(index) }}. {{ option.optionContent }}
                </div>
              </div>
            </t-radio>
          </t-radio-group>
        </div>
      </div>
      <div class="data-item-hid"></div>
    </div>

    <!-- 控制按钮：上一题与下一题 -->
    <div class="footer" style="margin-top: 30px;margin-right: 10px">
      <div class="button-container">
        <!--  顺序作答  -->
        <t-button :disabled="currentIndex === 0" v-if="!jump" class="custom-btn shadow-on-hover" round @click="prevQuestion">上一题</t-button>
        <t-button :disabled="currentIndex === count - 1" v-if="!jump" class="custom-btn shadow-on-hover" @click="nextQuestion">下一题</t-button>
        <!--  跳转作答  -->
        <t-button :disabled="jump && isEnd" v-if="jump" class="custom-btn shadow-on-hover" round @click="prevJumpQuestion ">上一题</t-button>
        <t-button :disabled="jump && isEnd" v-if="jump" class="custom-btn shadow-on-hover" @click="umpToQuestion">下一题</t-button>
        <t-button v-if="jump && isEnd" class="custom-btn shadow-on-hover" @click="commitAll">提交</t-button>
        <t-button v-if="!jump && currentIndex == count - 1" class="custom-btn shadow-on-hover" @click="commitAll">提交</t-button>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, onMounted } from "vue";
import { MessagePlugin } from 'tdesign-vue-next'

const emit = defineEmits(["submitTool"]);
const props = defineProps({
  data: Array,
  footer: {
    type: Boolean,
    default: true,
  },
  questionSort: {
    type: Number,
    default: null,
  },
  count: {
    type: Number,
    default: null,
  },
  scaleId: {
    type: String,
    default: null,
  },
  scaleType: {
    type: String,
    default: null,
  },
});

const currentIndex = ref(0);
const currentData = computed(() => props.data[currentIndex.value]);
const selectedOptionId = ref(null);
const allAnswerList = ref([]);
const isEnd = ref(false);
const jump = ref(false);
const jumpToIndex = ref(1);
const jumpToQuestion = ref(null);
const previousIndexes = ref([]); // 用于保存跳转作答的上一题索引
const hoverIndex = ref(null);// 用于跟踪鼠标悬停的选项

const getBackgroundColor = (index) => {
  const colors = ['bg-color-1', 'bg-color-2'];
  const baseColor = colors[index % colors.length];
  // 如果当前选中或鼠标悬停，返回红色背景
  if (selectedOptionId.value === currentData.value.moocPsychologyHealthScaleQuestionOption[index].optionId || hoverIndex.value === index) {
    return 'bg-color-red';
  }
  return baseColor; // 默认背景色
};

// 缓存选项
const cacheAnswer = () => {
  if (jump.value) {
    // 获取要跳转的题号
    jumpToQuestion.value = currentData.value.moocPsychologyHealthScaleQuestionOption.find(option => option.optionId === selectedOptionId.value).jumpQuestionId
    jumpToIndex.value = props.data.findIndex(item => item.questionId === jumpToQuestion.value);
    if (jumpToQuestion.value==-1) {
      isEnd.value = true
    } else {
      isEnd.value = false
    }
  }
  if (selectedOptionId.value) {
    // 获取选中的答案
    const selectedAnswer = currentData.value.moocPsychologyHealthScaleQuestionOption.find(
      option => option.optionId === selectedOptionId.value
    );

    if (selectedAnswer) {
      // 查找已选的答案项，确保更新
      const existingAnswer = allAnswerList.value.find(item => item.questionId === currentData.value.questionId);

      if (existingAnswer) {
        // 如果已有答案，更新选项 ID 和分数
        existingAnswer.optionId = selectedOptionId.value;
        existingAnswer.score = selectedAnswer.score;
      } else {
        // 如果没有答案，则添加新的答案项
        allAnswerList.value.push({
          questionId: currentData.value.questionId,
          optionId: selectedOptionId.value,
          score: selectedAnswer.score, // 新添加的分数
        });
      }
    }
  }
};

//是否为跳转作答
const isJump = () => {
  if(props.scaleType === 2){
    jump.value = true
  } else {
    jump.value = false
  }
}

// 生成字母标签
const getOptionLabel = (index) => {
  let label = '';
  let i = index;
  while (i >= 0) {
    label = String.fromCharCode((i % 26) + 65) + label;
    i = Math.floor(i / 26) - 1;
  }
  return label;
};

// 上一题
const prevQuestion = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--;
    updateSelectedOption();
    emit("submitTool", currentIndex.value);
    if (props.questionSort === 2) {
      emit("getFacet", currentData.value.facetName);
    }
  }
};

// 下一题
const nextQuestion = () => {
  if (currentIndex.value < props.count - 1) {
    currentIndex.value++;
    updateSelectedOption();
    emit("submitTool", currentIndex.value);
    if (props.questionSort === 2) {
      emit("getFacet", currentData.value.facetName);
    }
  }
};

// 跳转作答下一题
const umpToQuestion = () => {
  if (!selectedOptionId.value) {
    MessagePlugin.error('请选择选项！');
    return;
  }
  // 保存当前题号用于返回
  previousIndexes.value.push(currentIndex.value);
  // 获取跳转题号
  const selectedOption = currentData.value.moocPsychologyHealthScaleQuestionOption.find(
    option => option.optionId === selectedOptionId.value
  );
  if (selectedOption.jumpQuestionId === -1) {
    isEnd.value = true;
    return;
  }
  // 查找跳转题目索引
  const jumpIndex = props.data.findIndex(
    item => item.questionId === selectedOption.jumpQuestionId
  );
  if (jumpIndex !== -1) {
    currentIndex.value = jumpIndex;
    updateSelectedOption();
    emit("submitTool", currentIndex.value);
    if (props.questionSort === 2) {
      emit("getFacet", currentData.value.facetName);
    }
  }
};




// 根据当前题目索引更新选中的数据
const updateSelectedOption = () => {
  const selected = allAnswerList.value.find(item => item.questionId === currentData.value.questionId);
  if (selected) {
    selectedOptionId.value = selected.optionId;
    if (jump) {
      let jump = selected.jumpQuestionId;
      if (jump.value==-1) {
        isEnd.value = true
      } else {
        isEnd.value = false
      }
    }
  } else {
    selectedOptionId.value = null;
  }
};

// 跳转作答上一题
const prevJumpQuestion = () => {
  if (previousIndexes.value.length > 0) {
    currentIndex.value = previousIndexes.value.pop();
    updateSelectedOption();
    emit("submitTool", currentIndex.value);
    if (props.questionSort === 2) {
      emit("getFacet", currentData.value.facetName);
    }
  }
}

// 提交所有答案
const commitAll = () => {
  emit("commitAll", allAnswerList.value);
  const totalScore = allAnswerList.value.reduce((total, answer) => total + answer.score, 0);
};

// 每次组件加载时尝试反显上次选中的选项
onMounted(() => {
  isJump()
  updateSelectedOption();
});
</script>

<style scoped>
.questionType{
  padding-bottom: 10px;
  overflow-y:auto;
}
.question-content {
  max-width: 100%;
  overflow-x: hidden;
  position: relative;
  padding-bottom: 50px;
}
.footer {
  text-align: center;
}
.data-item {
  position: relative;
  margin-left: 5%;
  margin-right: 5%;
  box-sizing: border-box;
  min-height: 100px;
  padding: 60px 25px 25px 45px;
  margin-top: 20px;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid;
  color:black;
  &:last-child {
    margin-bottom: 0;
  }
}

.data-item-hid {
  position: absolute; /* 绝对定位 */
  min-height: 120px;
  top: 55px; /* 向下偏移 5px */
  left: 80px; /* 向右偏移 5px */
  right: 50px; /* 使其宽度与父元素相同 */
  bottom: 40px; /* 使其高度与父元素相同 */
  background-color: #ADBCFF; /* 背景色为紫色 */
  border-radius: 12px; /* 与 .data-item 相同的圆角 */
  z-index: -1; /* 使其位于 .data-item 的后面 */
  border: 2px solid black;
}

.data-item-em {
  font-weight: bold;
  font-size: 22px;
  font-style: normal;
  text-transform: none;
  line-height: 1.5;
  margin-bottom: 20px;
  :deep(img) {
    max-width: 100%;
  }
  :deep(code) {
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
  }
}

.custom-btn {
  width: 200px;
  height: 50px;
  background: linear-gradient(to bottom, #F1F6FF, #4F9CF4, #3676ED, #C2E1FF); /* 渐变背景 */
  border: none; /* 去掉默认边框 */
  color: white; /* 字体颜色 */
  padding: 10px 20px; /* 内边距 */
  font-size: 20px; /* 字体大小 */
  border-radius: 30px; /* 圆角 */
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2); /* 阴影效果 */
  transition: background 0.3s, transform 0.2s; /* 背景和变换过渡效果 */
}

/* 悬停效果 */
.custom-btn:hover {
  background: linear-gradient(to bottom, #A0BFFF, #0056b3); /* 悬停时的渐变背景 */
  transform: translateY(-2px); /* 悬停时轻微上移 */
}

/* 点击效果 */
.custom-btn:active {
  transform: translateY(2px); /* 点击时下移 */
}

.shadow-on-hover {
  box-shadow: 0 4px 6px rgba(0,0,0,.1), 0 2px 4px rgba(0,0,0,.5);
  transition: all .3s ease;
}

.shadow-on-hover:hover {
  background-color: #2ED2A9;
  color: white;
  box-shadow: 0 8px 12px rgba(0,0,0,.2), 0 4px 8px rgba(0,0,0,.12);
}

.bg-color-1 {
  background-color: #A283FF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 10px;
  width: 100%;
}

.bg-color-2 {
  background-color: #67B8FF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 10px;
  width: 100%;
}

.bg-color-1:hover {
  background-color: #C0ABFF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 10px;
  width: 100%;
  transform: translateY(-2px);
}

.bg-color-1:checked {
  background-color: #C0ABFF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 10px;
  width: 100%;
}

.bg-color-2:hover {
  background-color: #C0ABFF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 10px;
  width: 100%;
  transform: translateY(-2px);
}

.bg-color-red{
  background-color: #C0ABFF;
  border-radius: 8px;
  border: 2px solid black;
  padding: 10px;
  width: 100%;
}

.button-container {
  display: flex; /* 使用flexbox布局 */
  justify-content: flex-end; /* 靠右对齐 */
  margin-right: 50px;
  gap: 40px; /* 按钮间距为20px */
}

.custom-radio {
  position: relative; /* 确保容器中内容的定位正确 */
}

:deep(.umo-radio__input){
  display: none !important;
}

.radio-icon{
  width: 20px;
  height: 20px;
  margin-right: 8px;
  margin-left: 0px;
}

.radio-content {
  display: flex; /* 水平布局 */
  align-items: center; /* 垂直居中对齐 */
}

</style>
