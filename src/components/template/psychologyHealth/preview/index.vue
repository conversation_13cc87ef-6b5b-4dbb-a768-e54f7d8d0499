<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :title="title"
    width="900px"
    :close-on-overlay-click="false"
    attach="body"
    :confirm-btn="null"
    :cancel-btn="null"
  >
    <div class="preview-container">
      <div class="paper-info">
        <h2
          class="paper-title"
          :style="`color:${options.theme !== 'light' ? '#fff' : '#666'}`"
        >
          {{ scaleDetail.scaleName }}
        </h2>
          <h3 style="text-align: left">{{ scaleDetail.scanQuestion }}</h3>
          <h3 style="text-align: left;margin-top: 20px">{{ scaleDetail.evaluationMethod }}</h3>
          <h3 style="text-align: left;margin-top: 20px">{{ scaleDetail.evaluateReference }}</h3>
        <div class="paper-meta">
          <span>题目数量: {{ questionCount }}道</span>
        </div>
      </div>
      <t-divider />
      <!-- 题目列表 -->
      <div class="questions-list">
        <div
          v-for="(collection, collectionIndex) in questionList"
          :key="collectionIndex"
          class="question-collection"
        >
          <div class="collection-header" v-if="scaleDetail.questionSort == 2">
            <h3>
              {{ displayText(collection.facetName,30) }}
              <template>
                （共{{ collection.moocPsychologyHealthScaleQuestion.length }}题）
              </template>
            </h3>
          </div>
          <div
            v-if="scaleDetail.questionSort == 2"
            v-for="(question, index) in collection.moocPsychologyHealthScaleQuestion"
            :key="question.scaleId"
            class="question-item"
          >
            <div class="question-header">
              <span class="question-index">第{{ index + 1 }}题</span>
            </div>
            <template-psychology-health-question-preview
              :question="question"
              :index="index"
              :collection="collection"
            />
          </div>
          <div
            v-else
            class="question-item"
          >
            <div class="question-header">
              <span class="question-index">第{{ collectionIndex + 1 }}题</span>
            </div>
            <template-psychology-health-question-preview
              :question="collection"
              :index="collectionIndex"
              :collection="collection"
            />
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<script setup>
import { MessagePlugin } from 'tdesign-vue-next'
import { computed, ref, watch } from 'vue'

import { getPaper, getTestPaperQuestions } from '@/api/book/paper'
import { getPhychology } from '@/api/resource/psychologyHealth.js'
const { options } = useStore()
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  paperId: {
    type: [String, Number],
    default: null,
  },
  scaleId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue', 'load-error'])

const dialogVisible = ref(false)
const previewData = ref({})
const scaleDetail = ref({})
const questionCount = ref(0)
const questionList = ref([])

// 计算标题
const title = computed(() => {
  return '心理健康量表预览'
})

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val
    if (val && props.paperId) {
      loadPreviewData()
    }
  },
)

// 监听 dialogVisible 变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:modelValue', val)
  },
)

const displayText = (str,length) => {
  return str.length > length ? str.substring(0, length) + '...' : str;
}

// 加载预览数据
const loadPreviewData = async () => {
  try {
    const response = await getPhychology(props.paperId)
    console.log(response)
    scaleDetail.value = response.data.scale
    questionList.value = response.data.questionList
    if (scaleDetail.value.questionSort == 1) {
      questionCount.value = questionList.value.length
      console.log("单维度" + questionCount.value)
    } else {
      questionCount.value  = questionList.value.reduce((sum, item) => sum + (item.moocPsychologyHealthScaleQuestion?.length || 0), 0);
      console.log(questionCount.value)
    }
    console.log(questionList.value)
    console.log(scaleDetail.value)

  } catch (error) {
    console.error('获取试卷预览数据失败:', error)
    const message = '获取预览数据失败'
    MessagePlugin.error(message)
    emit('load-error', message)
  }
}
</script>

<style lang="less" scoped>
.preview-container {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;

  .paper-info {
    text-align: center;
    margin-bottom: 20px;

    .paper-title {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 15px;
    }

    .paper-meta {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: nowrap;
      gap: 8px;

      span {
        font-size: 15px;
        color: #1890ff;
        font-weight: 500;
        white-space: nowrap;
      }

      .t-divider--vertical {
        margin: 0 4px;
        height: 16px;
      }
    }
  }

  .questions-list {
    .question-collection {
      margin-bottom: 24px;

      .collection-header {
        margin-bottom: 16px;

        h3 {
          color: #303133;
          font-size: 18px;
          font-weight: 500;
        }
      }
    }

    .question-item {
      margin-bottom: 20px;

      .question-header {
        margin-bottom: 15px;

        .question-index {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          margin-right: 10px;
        }

        .question-score {
          color: #409eff;
          margin-left: 8px;
          font-size: 14px;
        }
      }
    }
  }
}

.preview-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #909399;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

:deep(.t-card) {
  margin-bottom: 16px;
  width: 100%;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

:deep(.t-space) {
  width: 100%;
}

:deep(.t-form--inline) {
  .t-form__item {
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }

  .t-button + .t-button {
    margin-left: 12px;
  }
}

.question-collection {
  margin-bottom: 24px;

  .collection-header {
    margin-bottom: 16px;

    h3 {
      color: #303133;
      font-size: 18px;
      font-weight: 500;
    }
  }
}

.question-score {
  color: #409eff;
  margin-left: 8px;
  font-size: 14px;
}

// 为富文本中的代码块添加黑框样式
:deep(pre) {
  background-color: #1e1e1e;
  color: #d4d4d4;
  border-radius: 4px;
  padding: 16px;
  margin: 12px 0;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.preview-container {
  .paper-info {
    .paper-meta {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: nowrap;
      gap: 8px;

      span {
        font-size: 15px;
        color: #1890ff;
        font-weight: 500;
        white-space: nowrap;
      }

      .t-divider--vertical {
        margin: 0 4px;
        height: 16px;
      }
    }
  }

  .questions-list {
    .question-collection {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;

      .collection-header {
        padding: 16px 0;

        h3 {
          color: #333;
          font-size: 18px;
          font-weight: 600;
          text-align: left;
          margin: 0;
        }
      }

      .question-item {
        background-color: #fafafa;
        border-radius: 6px;
        padding: 16px;
        margin-bottom: 16px;

        .question-header {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          border-bottom: 1px solid #e8e8e8;
          padding-bottom: 12px;

          .question-index {
            font-size: 16px;
            color: #1890ff;
            margin-right: 12px;
          }

          .question-score {
            color: #ff4d4f;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 添加 dialog 相关样式
:deep(.t-dialog) {
  margin-top: 5vh !important;
  height: 90vh;
  display: flex;
  flex-direction: column;

  .t-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }
}

.preview-container {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  // 会造成下方增加滚动轴
  // width: 100%;
  display: flex;
  flex-direction: column;
}
</style>
