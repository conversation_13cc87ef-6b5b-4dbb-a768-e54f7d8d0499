<template>
  <div class="question-content">
    <div class="data-item">
      <div class="data-item-em">
        <label><b>题干：</b></label>
        <div v-html="question.questionContent"></div>
      </div>
      <ul v-if="question.moocPsychologyHealthScaleQuestionOption?.length > 0" class="data-item-option count">
        <li v-for="(optItem, index) in question.moocPsychologyHealthScaleQuestionOption" :key="index"
          class="option-item">
          <div class="option-item-content" v-html="optItem.optionContent"></div> {{ "(" + (optItem.score ? optItem.score
            : 0)
            + "分)" }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
// 添加组件名称定义
defineOptions({
  name: 'QuestionPreview'
})

const props = defineProps({
  question: {
    type: Object,
    required: true
  },
})

// 添加一个计算属性来获取乱序的选项
const shuffledOptions = computed(() => {
  if (!props.question.options || props.question.questionType !== 4) {
    return [];
  }

  // 复制选项数组，避免修改原数组
  const options = [...props.question.options];

  // Fisher-Yates 洗牌算法打乱数组
  for (let i = options.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [options[i], options[j]] = [options[j], options[i]];
  }

  return options;
});

// 当题目变化时重新生成乱序选项
watch(() => props.question, () => {
  // 题目变化时，重新计算 shuffledOptions
  // 由于 shuffledOptions 是计算属性，会自动重新计算
}, { deep: true });
</script>

<style lang="css" scoped>
.question-content {
  max-width: 100%;
  overflow-x: hidden;
  position: relative;
  padding-bottom: 50px;
}

.toggle-answer {
  text-align: right;
  float: right;
  margin: 15px 0;
}

:deep(.blank-line) {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
  margin: 0 5px;

  .blank-answer {
    color: #409EFF;
    font-size: 14px;
    margin-bottom: 2px;
  }

  &::after {
    content: '';
    width: 100%;
    height: 1px;
    background-color: #000;
    display: block;
  }
}

:deep(.sort-answer-item) {
  margin-bottom: 8px;
  padding: 5px 10px;
  background-color: #f0f9eb;
  border-radius: 4px;
  display: flex;
  align-items: center;

  .sort-number {
    font-weight: bold;
    margin-right: 10px;
    min-width: 20px;
  }
}

:deep(.language-tag) {
  margin-bottom: 8px;
  color: #000000;
  font-size: 14px;
  font-style: italic;
}


:deep(pre) {
  background-color: #1e1e1e;
  color: #d4d4d4;
  border-radius: 4px;
  padding: 16px;
  margin: 12px 0;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  border: 1px solid #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}


.data-item {
  width: 100%;
  box-sizing: border-box;
  min-height: 100px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 22px 20px 22px 20px;
  color: #333;

  &:last-child {
    margin-bottom: 0;
  }

  ul {
    list-style: none;
  }

  .data-item-top {

    width: 100%;
  }

  .data-item-em {

    margin-bottom: 20px;

    :deep(img) {
      max-width: 100%;
    }

    :deep(code) {
      white-space: pre-wrap;
      /* 保留空格和换行符，并自动换行 */
      word-wrap: break-word;
      /* 防止长单词溢出容器 */
      word-break: break-all;
      /* 允许在任意字符处断行（可选） */
    }
  }

  .data-item-option {
    width: 100%;

    &.true-or-false {
      display: flex;
    }

    &.sorting {
      display: block;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .option-item {
      text-align: left;
      margin-bottom: 14px;

      &:last-child {
        margin-bottom: 0;
      }

      &.is-right-answer {
        &::after {
          content: '';
          display: inline-block;
          background: url(@/assets/icons/checkedWhiteNike.svg) no-repeat;
          background-size: contain;
          height: 14px;
          width: 14px;
        }

        .right-answer-icon {
          display: inline-block;
        }
      }

      .right-answer-icon {
        display: none;
      }
    }

    &.count {
      .option-item {
        counter-increment: my-counter;

        /* 计数器递增 */
        &::before {
          content: counter(my-counter, upper-alpha) ". ";
          /* 使用小写字母作为前缀 */
        }

        .option-item-content {
          display: inline-block;

          :deep(img) {
            max-width: 30%;
          }
        }
      }
    }
  }

  .data-item-analysis {
    display: flex;
    margin-top: 10px;

    label {
      line-height: 1.4;
      min-width: 30px;
    }

    p {
      flex: 1;
    }
  }
}
</style>
