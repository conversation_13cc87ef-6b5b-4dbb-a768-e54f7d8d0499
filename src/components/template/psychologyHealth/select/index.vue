<template>
  <div class="dialog-content">
    <t-form ref="dataRef" :data="quizData" layout="inline" :label-width="120">
      <t-form-item
        :label="t('insert.psychologyHealth.labelName')"
      >
        <t-input
          v-model="quizData.scaleName"
          style="width: 160px"
          size="medium"
          clearable
          :placeholder="t('insert.psychologyHealth.placeholderName')"
        ></t-input>
      </t-form-item>
      <t-space>
        <t-button theme="primary" type="submit" @click="handleQuery">
          <template #icon><t-icon name="search" /></template
          >{{ t('insert.psychologyHealth.search') }}
        </t-button>
        <t-button theme="primary" type="submit" @click="handleAdd">
          <template #icon><t-icon name="add" /></template
          >{{ t('insert.psychologyHealth.add') }}
        </t-button>
      </t-space>
    </t-form>
    <div
      style="
        display: flex;
        justify-content: center;
        min-height: 200px;
        flex-direction: column;
      "
    >
      <template v-if="quizData.rows.length <= 0">
        <t-space
          direction="vertical"
          :align="'center'"
          style="margin-top: 80px"
        >
          <t-empty />
          <t-button
            theme="primary"
            type="submit"
            @click="navigateToPapersAndAssignment"
          >
            <template #icon><t-icon name="add" /></template
            >{{ t('insert.psychologyHealth.addPsychologyHealth') }}
          </t-button>
        </t-space>
      </template>
      <div
        v-for="item in quizData.rows"
        :key="item.id"
        class="list-item quiz-questions-list"
      >
        <div class="list-item-checkbox">
          <t-checkbox @change="selectedQuizChange($event, item)"></t-checkbox>
        </div>
        <div class="list-item-img">
          <img :src="paperIcon" alt="" />
        </div>
        <div class="list-item-info">
          <div class="list-item-info-title">{{ item.scaleName }}</div>
          <div class="paper-stats">
            <span>试题数量: {{ item.questionCount }}</span>
            <t-divider layout="vertical" />
          </div>
        </div>
        <div class="list-item-info-time">
          <div class="list-item-info-time-text">
            <Calendar1Icon />{{ item.createTime }}
          </div>
          <div class="list-item-info-btn">
            <t-button size="small" style="margin-right: 10px" theme="success" @click="handleQueryPsy(item)">
              <template #icon>
                <Edit2Icon />
              </template>
              {{ t('insert.psychologyHealth.edit') }}</t-button
            >
            <t-button size="small" theme="success" @click="viewQuiz(item)">
              <template #icon>
                <BrowseIcon />
              </template>
              {{ t('insert.psychologyHealth.view') }}</t-button
            >
          </div>
        </div>
      </div>
      <div class="page-item">
        <t-pagination
          v-show="quizData.rows.length > 0"
          v-model:current="quizData.pageNum"
          v-model:page-size="quizData.pageSize"
          :total="quizData.total"
          @change="queryQuizByPagination"
        />
      </div>
    </div>
  </div>

  <template-psychologyHealth-preview
    v-model="previewQuizVisibility"
    :paper-id="onPreviewQuiz?.scaleId"
  ></template-psychologyHealth-preview>
</template>
<script setup>
import { BrowseIcon, Calendar1Icon, Edit2Icon } from 'tdesign-icons-vue-next'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import paperIcon from '@/assets/images/paper.svg'
import { listPsychologyForEditor } from '@/api/resource/psychologyHealth.js'
const router = useRouter()
const route = useRoute()

const emit = defineEmits(['update'])

let selectedQuiz = []
let previewQuizVisibility = $ref(false)
const quizData = $ref({
  rows: [],
  total: 0,
  pageNum: 1,
  pageSize: 10,
})
let onPreviewQuiz = $ref(null)
// const dataRef = ref(null)
const loading = ref(true)
// 1编辑器后台 2管理后台
const formType = ref(1)

// 方法部分
function navigateToPapersAndAssignment() {
  router.push({
    path: '/resourceLibrary/myPsychologyHealth',
  })
}
function selectedQuizChange(selected, quiz) {
  if (selected) {
    selectedQuiz.push(quiz)
  } else {
    selectedQuiz = selectedQuiz.filter((item) => item.scaleId !== quiz.scaleId)
  }

  emit('update', selectedQuiz)
}
function handleQuery() {
  quizData.pageNum = 1
  loading.value = true
  quizData.rows = []
  listPsychologyForEditor(quizData).then(handlePaginationResponse)
}

function handleAdd() {
  window.open(
    `/resourceLibrary/psychologyHealth/psychologyHealth#/resourceLibrary/myPsychologyHealth`,
  )
}
function queryQuizByPagination() {
  loading.value = true
  quizData.rows = []
  listPsychologyForEditor(quizData).then(handlePaginationResponse)
}
function viewQuiz(quizItem) {
  onPreviewQuiz = {
    scaleId: quizItem.scaleId,
  }
  previewQuizVisibility = true
}

function handleQueryPsy(quizItem) {
  // window.open(
  //   `/resourceLibrary/psychologyHealth/psychologyHealth#/resourceLibrary/myPsychologyHealth?scaleId=${quizItem.scaleId}&scaleName=${quizItem.scaleName}`,
  // )
  const url = new URL('/resourceLibrary/psychologyHealth/psychologyHealth', window.location.origin);
  url.hash = `/resourceLibrary/myPsychologyHealth?scaleId=${quizItem.scaleId}&scaleName=${encodeURIComponent(quizItem.scaleName)}`;
  window.open(url.toString());
}

function handlePaginationResponse(quizResp) {
  console.log('quizResp', quizResp)
  quizData.rows = quizResp.rows || []
  quizData.total = quizResp.total || 0
  loading.value = false
}
onMounted(() => {
  quizData.rows = []
  selectedQuiz = []
  formType.value = route.query.formType || 1
  listPsychologyForEditor(quizData).then(handlePaginationResponse)
})
</script>
<style lang="less" scoped>
.dialog-content {
  padding: 20px;

  .quiz-questions-list {
    display: flex;
    padding: 20px 10px;
    background-color: #fff;
    margin: 20px 0;
    border: 1px solid #eaeef3;
    cursor: pointer;
    transition: all 0.3s;
    border-radius: 5px;
    color: #666;
    &:hover {
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    .list-item-checkbox {
      width: 30px;
    }
    .list-item-img {
      width: 50px;
      height: 50px;
      background-color: #eaeef3;
      border-radius: 5px;
      img {
        width: 100%;
      }
    }

    .list-item-info {
      flex: 1;
      margin-left: 20px;

      justify-content: space-between;
      .list-item-info-title {
        font-size: 16px;
        font-weight: bold;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 270px;
        .paper-stats {
          color: #666;
          font-size: 14px;
          display: flex;
          align-items: center;

          .t-divider--vertical {
            margin: 0 16px;
            height: 14px;
          }
        }
      }
      .list-item-info-desc {
        display: flex;
        margin-top: 10px;
        div {
          margin-right: 20px;
        }
      }
    }
    .list-item-info-time {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-top: 10px;
      color: #999;
      .list-item-info-time-text {
        display: flex;
        align-items: center;
      }
      .list-item-info-btn {
        margin-top: 10px;
        text-align: right;
      }
    }
  }
}
</style>
