<!-- 模块说明 -->
<template>
  <div class="matching">
    <div style="padding: 0 0 20px 0">
      <t-alert
        theme="warning"
        message="点击左右侧+号可连线配对，选中连线可取消配对"
        style="width: 400px"
      />
    </div>

    <div class="matching-main-content">
      <div class="matching-answer-left">
        <div
          v-for="(item, index) in leftOptionData"
          :key="item.optionId"
          class="matching-answer-left-item"
          :style="
            selectKey === item.optionId
              ? 'box-shadow: 0 0 12px rgba(0, 0, 0, 0.4)'
              : ''
          "
        >
          <nav class="matching-main-header">
            <span>第{{ 1 + index }}项</span>
            <t-button line size="small" @click="deleteItem(index, 'left')">{{
              t('insert.questions.delBtnText')
            }}</t-button>
          </nav>
          <wangEditor
            :text-content="item.optionContent"
            :need-t="false"
            :toolbar-config="editorToolbarConfig"
            @update-text-content="updateTextContentHandler($event, item)"
          >
            <t-icon
              :id="'l' + item.optionId"
              class="matching-answer-left-addIcon"
              name="add-circle"
              @click="startConnection(item, index)"
            ></t-icon>
          </wangEditor>
        </div>
        <section style="text-align: center">
          <t-button @click="addItem('left')">添加选项</t-button>
        </section>
      </div>
      <div class="matching-answer-strings-canvas">
        <svg
          id="connectionPaintCanvas"
          width="100%"
          height="100%"
          xmlns="http://www.w3.org/2000/svg"
        ></svg>
      </div>
      <div class="matching-answer-right">
        <div
          v-for="(item, index) in rightOptionData"
          :key="item.optionId"
          class="matching-answer-right-item"
        >
          <nav class="matching-main-header">
            <span>第{{ 1 + index }}项</span>
            <t-button line size="small" @click="deleteItem(index, 'right')">{{
              t('insert.questions.delBtnText')
            }}</t-button>
          </nav>
          <wangEditor
            :need-t="false"
            :text-content="item.optionContent"
            :toolbar-config="editorToolbarConfig"
            @update-text-content="updateTextContentHandler($event, item)"
          >
            <t-icon
              :id="'r' + item.optionId"
              class="matching-answer-right-addIcon"
              name="add-circle"
              @click="completeConnection(item, index)"
            ></t-icon>
          </wangEditor>
        </div>
        <section style="text-align: center">
          <t-button @click="addItem('right')">添加选项</t-button>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup>
import { debounce } from 'lodash'
import { onMounted, ref } from 'vue'

import WangEditor from '@/components/wangEditor/index.vue'
import { WangEditorToolbarConfigItemType } from '@/components/wangEditor/toolbarConfigItems.ts'
import { deepCopyObject, uuid } from '@/utils/quetions-utils'

import { MatchingQuestionItemPainter } from './matchingQuestionPainter'

const emits = defineEmits(['onSave', 'optionContentUpdate'])
const props = defineProps({
  editorToolbarConfig: {
    type: Object,
    default: null,
  },
  leftOption: {
    type: Array,
    default: () => [],
  },
  rightOption: {
    type: Array,
    default: () => [],
  },
  matchingResult: {
    type: Array /* [{source: '', target: ''}] */,
    default: () => [],
  },
})

// 业务数据
let leftOptionData = $ref([])
let rightOptionData = $ref([])
let matchingResultData = $ref([])
const selectKey = ref(null) //鼠标移入的删除的key
let svgPainter

const EDITOR_EXCLUDE_KEYS = [
  WangEditorToolbarConfigItemType.HEADER,
  WangEditorToolbarConfigItemType.UNDERLINE,
  WangEditorToolbarConfigItemType.SPLIT_LINE,
  WangEditorToolbarConfigItemType.EMOTION,
  WangEditorToolbarConfigItemType.COLOR,
  WangEditorToolbarConfigItemType.BG_COLOR,
  WangEditorToolbarConfigItemType.BLOCKQUOTE,
  WangEditorToolbarConfigItemType.BULLETED_LIST,
  WangEditorToolbarConfigItemType.NUMBERED_LIST,
  WangEditorToolbarConfigItemType.JUSTIFY_CENTER,
  WangEditorToolbarConfigItemType.JUSTIFY_LEFT,
  WangEditorToolbarConfigItemType.JUSTIFY_RIGHT,
  WangEditorToolbarConfigItemType.INSERT_LINK,
  WangEditorToolbarConfigItemType.TODO,
  WangEditorToolbarConfigItemType.GROUP_INDENT,
  WangEditorToolbarConfigItemType.GROUP_JUSTIFY,
  WangEditorToolbarConfigItemType.LINE_HEIGHT,
]

// 通知父组件连线题有更新
const matchingQuestionContentUpdate = debounce(() => {
  emits('optionContentUpdate', {
    leftOption: leftOptionData,
    rightOption: rightOptionData,
    matchingResult: matchingResultData,
  })
}, 500)
const updateAllLinesPosition = debounce(() => {
  svgPainter?.updateAllLinesPosition()
}, 300)
function updateTextContentHandler(content, optionItem) {
  optionItem.optionContent = content
  matchingQuestionContentUpdate()
  // 如果富文本输入框高度发生变化，需要调整连线位置
  updateAllLinesPosition()
}
// 连接题目
function startConnection(item, index) {
  const startDom = document.querySelector(`#l${item.optionId}`)
  svgPainter.appendLeftAnchor(startDom, index)
}
function completeConnection(item, index) {
  const endDom = document.querySelector(`#r${item.optionId}`)
  svgPainter.appendRightAnchor(endDom, index)
  matchingResultData = svgPainter.getMatchingResult()
  matchingQuestionContentUpdate()
}

//添加一个答案选项
function addItem(type) {
  if (type === 'left') {
    leftOptionData.push({
      optionId: uuid(),
      optionContent: '',
      optionPosition: 1,
    })
  } else {
    rightOptionData.push({
      optionId: uuid(),
      optionContent: '',
      optionPosition: 2,
    })
  }
}
function deleteItem(index, type) {
  let findedIndex = -1
  if (
    (type === 'left' && leftOptionData.length <= 1) ||
    (type === 'right' && rightOptionData.length <= 1)
  ) {
    MessagePlugin.error('左右侧选项不能少于1个')
    return
  }

  if (type === 'left') {
    leftOptionData.splice(index, 1)
    findedIndex = matchingResultData.findIndex((mrItem) => {
      return mrItem.leftOptIndex === index
    })
  } else {
    rightOptionData.splice(index, 1)
    findedIndex = matchingResultData.findIndex((mrItem) => {
      return mrItem.rightOptIndex === index
    })
  }
  if (findedIndex !== -1) {
    // 删除连线信息
    const deletedItem = matchingResultData.splice(findedIndex, 1)
    // 删除线条
    if (deletedItem.length === 1) {
      svgPainter.removeLine(deletedItem[0].lineId)
    }
    // 如果删除左侧右侧选项，需要调整连线位置
    updateAllLinesPosition()
  }
  matchingQuestionContentUpdate()
}

function initOptions() {
  leftOptionData = deepCopyObject(props.leftOption ?? [])
  rightOptionData = deepCopyObject(props.rightOption ?? [])
  matchingResultData = deepCopyObject(props.matchingResult ?? [])
}
function drawLines() {
  if (props.matchingResult.length <= 0) {
    return
  }
  matchingResultData.forEach((connectionData) => {
    startConnection(
      leftOptionData[connectionData.source],
      connectionData.source,
    )
    completeConnection(
      rightOptionData[connectionData.target],
      connectionData.target,
    )
  })
}

// hook
onMounted(() => {
  // 初始化左侧和右侧所有options
  initOptions()
  nextTick(() => {
    svgPainter = new MatchingQuestionItemPainter(
      document.querySelector('#connectionPaintCanvas'),
    )
    svgPainter.subscribeLineUpdate(({ leftOptIndex, rightOptIndex }) => {
      const findedIndex = matchingResultData.findIndex(
        (opt) =>
          opt.leftOptIndex === leftOptIndex &&
          opt.rightOptIndex === rightOptIndex,
      )
      if (findedIndex !== -1) {
        matchingResultData.splice(findedIndex, 1)
      }
      matchingQuestionContentUpdate()
    })
    drawLines()
  })
})
onUnmounted(() => {
  svgPainter = null
})
watch(
  () => [props.leftOption, props.rightOption, props.matchingResult],
  () => {
    initOptions()
    nextTick(() => {
      drawLines()
    })
  },
  { deep: true },
)
</script>
<style lang="less" scoped>
.matching {
  width: 100%;
  .matching-answer-strings-canvas {
    width: 200px;
  }
  .matching-main-header {
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 10px;
    background-color: #efefef;
  }
  .matching-main-content {
    width: 100%;
    box-sizing: border-box;
    height: 100%;
    display: flex;
    .matching-answer-left {
      width: calc(50% - 100px);
      max-width: calc(50% - 100px);
      position: relative;
      .matching-answer-left-item {
        margin-bottom: 20px;
        position: relative;
        .matching-answer-left-addIcon {
          position: absolute;
          right: -15px;
          top: 50%;
          transform: translateY(-10%);
          z-index: 1;
          color: #666464;
          cursor: pointer;
          width: 30px;
          height: 30px;
          &:hover {
            color: #000;
          }
        }
      }
    }

    .matching-answer-right {
      width: calc(50% - 100px);
      max-width: calc(50% - 100px);
      position: relative;
      .matching-answer-right-item {
        margin-bottom: 20px;
        position: relative;
        .matching-answer-right-addIcon {
          position: absolute;
          left: -15px;
          top: 50%;
          transform: translateY(-10%);
          z-index: 1;
          color: #666464;
          cursor: pointer;
          width: 30px;
          height: 30px;
          &:hover {
            color: #000;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.matching-answer-strings-canvas {
  .line:hover {
    cursor: pointer;
    stroke: #f00; /* 高亮颜色 */
    stroke-width: 3; /* 加粗 */
  }
  .delete-icon {
    display: none;
    cursor: pointer; /* 鼠标指针变为手型 */
  }

  /* 点击线条时显示删除图标 */
  .line-group.active .delete-icon {
    display: block;
  }
}
</style>
