const defaultOpts = {
  line: {
    removeIcon: true,
    clickEvent: true
  }
}
export class MatchingQuestionItemPainter {
  private opts!: {[key: string]: any}
  private strokedLineOpts = {
    stroke: 'stroke',
    strokeWidth: 2,
    strokeColor: '#000',
    lineStyle: 'stroke'
  }
  private svgTarget!: SVGElement

  private currentSelectedLeftAnchor!: HTMLElement | undefined
  
  private currentSelectedLeftAnchorIndex!: number

  private lines = new Map()

  private resizeObserver: ResizeObserver;
  
  private linesUpdater: any = null;

  constructor(svgTarget: SVGElement, opts: {[key: string]: any}) {
    this.opts = Object.assign(defaultOpts, opts)
    this.svgTarget = svgTarget
    svgTarget.addEventListener('click', () => {
      this.hideDeleteIcons()
    });
    // 初始化 ResizeObserver
    this.resizeObserver = new ResizeObserver(() => {
      this.updateAllLinesPosition(); // 视口大小变化时更新线条
    });
    // 监控 svgTarget 或其父元素的大小变化
    this.resizeObserver.observe(svgTarget);
  }

  appendLeftAnchor(newAnchor: HTMLElement, optionIndex?: number) {
    // 左侧节点应该只能连接上右侧节点
    this.currentSelectedLeftAnchor = newAnchor
    this.currentSelectedLeftAnchorIndex = optionIndex ?? 0
  }

  appendRightAnchor(newAnchor: HTMLElement, optionIndex?: number) {
    // 右侧节点应该只能被左侧连接
    const startPosition = this.getAnchorCenter(this.currentSelectedLeftAnchor!)
    const endPosition = this.getAnchorCenter(newAnchor)
    this.createOperationalLine({
      startPosition,
      endPosition,
      selectedLeftOptAnchor: this.currentSelectedLeftAnchor!,
      selectedRightOptAnchor: newAnchor,
      selectedLeftOptAnchorIndex: this.currentSelectedLeftAnchorIndex,
      selectedRightOptAnchorIndex: optionIndex ?? 0
    })
    this.currentSelectedLeftAnchor = undefined
  }

  updateAllLinesPosition() {
    // 更新所有与该锚点相关的线条
    this.lines.forEach((lineInfo, lineId) => {
      this.updateLine(lineId);
    });
  }

  private hideDeleteIcons() {
    this.lines.forEach((lineInfo) => {
      lineInfo.group.classList.remove('active'); // 隐藏其他线条的删除图标
    });
  }

  // 更新线条位置
  private updateLine(lineId: string) {
    const { line, deleteIcon, startAnchor, endAnchor } = this.lines.get(lineId);
    const start = this.getAnchorCenter(startAnchor);
    const end = this.getAnchorCenter(endAnchor);

    // 更新线条位置
    line.setAttribute('x1', start.x);
    line.setAttribute('y1', start.y);
    line.setAttribute('x2', end.x);
    line.setAttribute('y2', end.y);

    if (deleteIcon) {
      // 更新删除图标位置
      deleteIcon.setAttribute('x', (start.x + end.x) / 2 - 10); // 居中
      deleteIcon.setAttribute('y', (start.y + end.y) / 2 - 10); // 居中
    }

    this.hideDeleteIcons()
  }

  // private observeAnchor(anchor: HTMLElement) {
  //   const observer = new ResizeObserver(() => {
  //     // 更新所有与该锚点相关的线条
  //     this.lines.forEach((lineInfo, lineId) => {
  //       if (lineInfo.startAnchor === anchor || lineInfo.endAnchor === anchor) {
  //         this.updateLine(lineId);
  //       }
  //     });
  //   });
  //   observer.observe(anchor);
  // }

  createLine(group:any, params: {
    startPosition: {x:any, y: any},
    endPosition: {x: any, y: any},
    selectedLeftOptAnchor: HTMLElement,
    selectedRightOptAnchor: HTMLElement,
    selectedLeftOptAnchorIndex: number,
    selectedRightOptAnchorIndex: number
  }) {
    const start = params.startPosition
    const end = params.endPosition
    const startAnchor = params.selectedLeftOptAnchor
    const endAnchor = params.selectedRightOptAnchor
    const selectedLeftOptAnchorIndex = params.selectedLeftOptAnchorIndex
    const selectedRightOptAnchorIndex = params.selectedRightOptAnchorIndex

    // 存储线条信息
    const lineId = `line-${startAnchor.id}-${endAnchor.id}`;

    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    line.setAttribute('x1', start.x);
    line.setAttribute('y1', start.y);
    line.setAttribute('x2', end.x);
    line.setAttribute('y2', end.y);
    line.setAttribute('stroke', this.strokedLineOpts.strokeColor);
    line.setAttribute('stroke-width', `${this.strokedLineOpts.strokeWidth}`);
    line.classList.add('line')

    if (this.opts.line.clickEvent) {
      // 点击线条时显示删除图标
      line.addEventListener('click', (event) => {
        event.stopPropagation(); // 阻止事件冒泡
        this.hideDeleteIcons()
        group.classList.add('active'); // 显示当前线条的删除图标
      });
    }
  
    // 创建删除图标
    let deleteIcon: SVGElement | undefined = undefined
    if (this.opts.line.removeIcon) {
      deleteIcon = document.createElementNS('http://www.w3.org/2000/svg', 'image');
      deleteIcon.setAttribute('width', '30');
      deleteIcon.setAttribute('height', '30');
      deleteIcon.setAttribute('x', `${(start.x + end.x) / 2 - 10}`); // 居中
      deleteIcon.setAttribute('y', `${(start.y + end.y) / 2 - 10}`); // 居中
      deleteIcon.classList.add('delete-icon');
      deleteIcon.setAttribute('href', 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjZmYwMDAwIj4KICA8cGF0aCBkPSJNMTkgNi40MUwxNy41OSA1IDEyIDEwLjU5IDYuNDEgNSA1IDYuNDEgMTAuNTkgMTIgNSAxNy41OSA2LjQxIDE5IDEyIDEzLjQxIDE3LjU5IDE5IDE5IDE3LjU5IDEzLjQxIDEyIDE5IDYuNDF6Ii8+Cjwvc3ZnPg==')
      // 点击删除图标时删除线条
      deleteIcon.addEventListener('click', (event) => {
        event.stopPropagation(); // 阻止事件冒泡
        this.svgTarget.removeChild(group); // 删除线条
        this.lines.delete(lineId); // 从 Map 中移除线条信息
        this.linesUpdater && typeof(this.linesUpdater) === 'function'? this.linesUpdater({leftOptIndex: selectedLeftOptAnchorIndex, rightOptIndex: selectedRightOptAnchorIndex}) : ''
      });
      group.appendChild(deleteIcon);
    }

    group.appendChild(line);

    this.lines.set(lineId, { group, line, deleteIcon, startAnchor, endAnchor, leftOptIndex: selectedLeftOptAnchorIndex, rightOptIndex: selectedRightOptAnchorIndex });

    return { line, deleteIcon }
  }

  removeLine(toBeDeletedLineId) {
    this.lines.forEach((lineInfo, lineId) => {
      if (toBeDeletedLineId === lineId) {
        this.svgTarget.removeChild(lineInfo.group); // 删除线条
        this.lines.delete(lineId); // 从 Map 中移除线条信息
      }
    })
  }

  createOperationalLine(params: {
      startPosition: {x:any, y: any},
      endPosition: {x: any, y: any},
      selectedLeftOptAnchor: HTMLElement,
      selectedRightOptAnchor: HTMLElement,
      selectedLeftOptAnchorIndex: number,
      selectedRightOptAnchorIndex: number
  }) {
    // 创建组元素，包裹线条和删除图标
    const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    group.classList.add('line-group');
    
    this.createLine(group, params)

    this.svgTarget.appendChild(group);
  }

  getMatchingResult() {
    const result: Array<{[key: string]: any}> = []
    this.lines.forEach((lineInfo, lineId) => {
      result.push({
        leftOptIndex: lineInfo.leftOptIndex,
        rightOptIndex: lineInfo.rightOptIndex,
        lineId
      })
    })
    return result
  }

  getAnchorCenter(anchor: HTMLElement) {
    const rect = anchor.getBoundingClientRect();
    return {
      x: rect.left + rect.width / 2 - this.svgTarget.getBoundingClientRect().left,
      y: rect.top + rect.height / 2 - this.svgTarget.getBoundingClientRect().top,
    };
  }

  subscribeLineUpdate(callback) {
    this.linesUpdater = callback
  }
}
