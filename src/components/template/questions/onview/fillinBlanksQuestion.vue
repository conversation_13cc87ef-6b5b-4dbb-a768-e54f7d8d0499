<template>
  <div class="data-item">
    <div class="data-item-top" v-if="localConfig?.questionTypeShow==1"><slot name="selection" /><b>填空题</b></div>
    <div class="data-item-em">
      <div v-if="data.questionRemark"><div v-html="data.questionRemark"></div></div>
      <label v-if="props.config?.contentLabel"><b>题干：</b></label><div v-html="rightAnswerBlanksContent"></div>
    </div>
    <div class="data-item-analysis" v-if="localConfig.hasAnswer">
      <label><b>解析：</b></label>
      <div v-html="data.analysis" style="margin-left: 10px;"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  config: {
    type: Object,
    // 引入showAnswer配置是因为的该组件也用在编辑器中，试题正确答案以及解析不需要打印出来。
    default: { contentLabel: false, hasAnswer: true }
  }
})
const defaultCfg = { contentLabel: false, hasAnswer: true, questionTypeShow: true }
let localConfig = $ref({
  ...defaultCfg,
  ...props.config
})
const rightAnswerBlanksContent = computed(() => {
  const input = props.data.questionContent
  if (localConfig.hasAnswer) {
    return input.replace(/###([^#]+)###/g, "___$1___")
  } else {
    return input.replace(/###([^#]+)###/g, "________")
  }
})
watch(
  () => props.config,
  (nCfg) => {
    if (nCfg) {
      localConfig = {
        ...defaultCfg,
        ...nCfg
      }
    }
  },
  {
    deep: true
  }
)
</script>
<style lang="less" scoped>
@import url('./question-data-item-style.less');
</style>
