<template>
  <div class="data-item">
    <div class="data-item-top" v-if="localConfig?.questionTypeShow==1"><slot name="selection" /><b>连线题</b></div>
    <div class="data-item-em">
      <div v-if="data.questionRemark"><div v-html="data.questionRemark"></div></div>
      <label v-if="props.config?.contentLabel"><b>题干：</b></label>
      <div v-html="data.questionContent"></div>
    </div>

    <!-- 连线题 -->
    <div :id="connectionPaintCanvas" class="option-con matching-question">
      <div class="matching-question-left">
        <div
          v-for="(optionItem, optionIndex) in leftOption"
          :key="optionIndex"
          class="matching-question-opt"
        >
          <div
            class="option-item-content"
            v-html="optionItem.optionContent"
          ></div>
        </div>
      </div>
      <div class="matching-question-paint">
        <svg
          v-show="localConfig.hasAnswer"
          width="100%"
          height="100%"
          xmlns="http://www.w3.org/2000/svg"
        ></svg>
      </div>
      <div class="matching-question-right">
        <div
          v-for="(optionItem, optionIndex) in rightOption"
          :key="optionIndex"
          class="matching-question-opt"
        >
          <div v-html="optionItem.optionContent"></div>
        </div>
      </div>
    </div>
    <div v-if="localConfig.hasAnswer" class="data-item-analysis">
      <label><b>解析：</b></label>
      <div v-html="data.analysis" style="margin-left: 10px;"></div>
    </div>
  </div>
</template>

<script setup>
import { throttle } from 'lodash'
import { nextTick, onMounted } from 'vue'

import { uuid } from '@/utils/quetions-utils'

// 业务数据
const connectionPaintCanvas = `c${uuid()}`

// 宏定义
const props = defineProps({
  leftOption: {
    type: Array,
    required: true,
  },
  rightOption: {
    type: Array,
    required: true,
  },
  matchingResult: {
    type: Array,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    // 引入showAnswer配置是因为的该组件也用在编辑器中，试题正确答案以及解析不需要打印出来。
    default: { contentLabel: false, hasAnswer: true },
  },
})
const defaultCfg = { contentLabel: false, hasAnswer: true, questionTypeShow: true }
let localConfig = $ref({
  ...defaultCfg,
  ...props.config,
})

// 方法
function initCanvas() {
  // 画布大小
  const canvasEle = document.querySelector(`#${connectionPaintCanvas} svg`)
  if (!canvasEle) {
    return
  }
  const computedStyle = window.getComputedStyle(canvasEle)
  const width = parseInt(computedStyle.width)
  const height = parseInt(computedStyle.height)
  return {
    height,
    width,
    x: canvasEle.getBoundingClientRect().left,
    y: canvasEle.getBoundingClientRect().top,
  }
}

function getAnchorCenter(index, totalItems, height, width, isLeft = true) {
  // 计算每个选项的高度间隔
  const itemHeight = height / totalItems
  // 计算当前选项的中心 Y 坐标
  const y = itemHeight * index + 6
  // 左侧选项的 X 坐标为 0，右侧选项的 X 坐标为 SVG 的宽度
  const x = isLeft ? 0 : width
  return { x, y }
}

function startDrawing(linePositions) {
  const svg = document.querySelector(`#${connectionPaintCanvas} svg`)
  // 清空之前的线条
  svg.innerHTML = ''

  linePositions.forEach(({ start, end }) => {
    // 创建线条元素
    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line')
    line.setAttribute('x1', start.x)
    line.setAttribute('y1', start.y)
    line.setAttribute('x2', end.x)
    line.setAttribute('y2', end.y)
    line.setAttribute('stroke', '#000') // 线条颜色
    line.setAttribute('stroke-width', '2') // 线条宽度
    svg.appendChild(line)
  })
}

const initPositions = throttle(() => {
  const canvasSize = initCanvas()
  if (!canvasSize) {
    return
  }
  const { height, width } = canvasSize
  const linePositions = []

  // 遍历匹配结果，计算每个匹配项的起点和终点坐标
  props.matchingResult.forEach(({ source, target }) => {
    const start = getAnchorCenter(
      source,
      props.leftOption.length,
      height,
      width,
      true,
    )
    const end = getAnchorCenter(
      target,
      props.rightOption.length,
      height,
      width,
      false,
    )
    linePositions.push({ start, end })
  })

  // 开始绘制线条
  startDrawing(linePositions)
}, 500)

// 监听
onMounted(() => {
  nextTick(() => {
    if (!document.querySelector(`#${connectionPaintCanvas} svg`)) {
      return
    }
    if (
      props.leftOption &&
      props.rightOption &&
      props.leftOption.length &&
      props.rightOption.length &&
      localConfig.hasAnswer
    ) {
      initPositions()
    }
    // 监听 connectionPaintCanvas 的大小变化
    const canvasElement = document.querySelector(`#${connectionPaintCanvas}`)
    if (canvasElement) {
      const resizeObserver = new ResizeObserver(() => {
        // 当大小变化时，重新计算并绘制线条
        initPositions()
      })
      resizeObserver.observe(canvasElement)
    }
  })
})
watch(
  () => props.config,
  (nCfg) => {
    if (nCfg) {
      localConfig = {
        ...defaultCfg,
        ...nCfg,
      }
    }
  },
  {
    deep: true,
  },
)
</script>

<style lang="less" scoped>
@import url('./question-data-item-style.less');
@paddingLeft: 2px;

.matching-question {
  display: flex;
  justify-content: space-around;

  .matching-question-left {
    flex: 1;
    .matching-question-opt {
      padding-right: @paddingLeft;
    }
  }
  .matching-question-right {
    flex: 1;
    .matching-question-opt {
      padding-left: @paddingLeft;
    }
  }
  .matching-question-left,
  .matching-question-right {
    display: flex;
    flex-direction: column;
    min-width: 50px;
    .matching-question-opt {
      flex-grow: 1;
      min-height: 20px;
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  .matching-question-paint {
    flex: 1;
    min-width: 50px;
  }
}

/* 限制图片宽度，添加到scoped样式中 */
:deep(.matching-question-opt img) {
  max-width: 85%;
  height: auto;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

:deep(.option-item-content img) {
  max-width: 85%;
  height: auto;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

.umo-node-focused {
  .matching-question-opt {
    background-color: #fff;
  }
}
/* 全局样式增强确保图片宽度限制 */
.matching-question img,
.matching-question-opt img,
.option-item-content img {
  max-width: 85% !important;
  height: auto !important;
  object-fit: contain !important;
  display: block !important;
  margin: 0 auto !important;
}
</style>
