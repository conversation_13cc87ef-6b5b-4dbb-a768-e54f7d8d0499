<template>
    <div class="option-content-resolver">
        <div class="option-content-media-template" v-if="allMediaElements.length > 0"
            @click="showScaledMediaDialog = true">
            <t-image v-if="allMediaElements[0].type === 'image'" class="content-image" fit="contain"
                :src="allMediaElements[0].src"></t-image>
            <t-image v-if="allMediaElements[0].type === 'video'" class="content-image" fit="contain"
                :src="sortVedio"></t-image>
            <t-image v-if="allMediaElements[0].type === 'audio'" class="content-image" fit="contain"
                :src="audiobg"></t-image>
        </div>
        <div class="option-content-template" v-html="optionContent" @click="showOptionContentDetailDialog = true"></div>
        <t-dialog v-model:visible="showScaledMediaDialog" header="资源详情" :confirmBtn="null" attach="body"
            :close-on-overlay-click="true" width="600">

            <t-swiper v-if="elementsOfImgs.length > 0"
                :navigation="{ type: 'fraction', paginationPosition: 'bottom-right' }" :autoplay="false">
                <t-swiper-item v-for="(item, index) in elementsOfImgs">
                    <t-image class="content-image" fit="contain" :src="item.src"></t-image>
                </t-swiper-item>
            </t-swiper>
            <div v-for="(item, index) in allMediaElements" :key="index">
                <div v-if="item.type === 'video'" v-html="item.originHTML"></div>
                <div v-if="item.type === 'audio'" v-html="item.originHTML"></div>
            </div>
        </t-dialog>


        <!-- <t-dialog v-model:visible="showOptionContentDetailDialog" title="选项详情" confirmBtn="关闭"
            :close-on-overlay-click="true" attach="body">
            <div>123</div>
            <div v-html="optionContent" class="option-content-detail"></div>
        </t-dialog> -->
    </div>
    <t-dialog v-model:visible="showOptionContentDetailDialog" :header="t('pageOptions.title')" confirmBtn="关闭"
        :close-on-overlay-click="true" attach="body" :prevent-scroll-through="false" placement="center">
        <div v-html="optionContent" class="option-content-detail"></div>
    </t-dialog>


</template>
<script setup>
import { ref, defineProps, onMounted } from "vue";
import audiobg from '@/assets/images/audiobg.png'
import sortVedio from "@/assets/images/sortVedio.svg";
import modal from "@/components/modal.vue";
// "optionContent": "<p>美国<img src=\"https://dutp-test.oss-cn-beijing.aliyuncs.com/1757382428311.png\" alt=\"\" data-href=\"\" style=\"\"/></p>",
const props = defineProps({
    optionContent: {
        required: true,
        type: String,
        default: ''
    }
});
const emits = defineEmits(['close'])
const allMediaElements = ref([]);
const elementsOfImgs = ref([]);
const showScaledMediaDialog = ref(false);
const showOptionContentDetailDialog = ref(false);

/**
 * @description  使用正则表达式提取媒体元素, 提取出来的媒体元素顺序并非其在HTML中的顺序
 */
function extractMediaElementsWithRegex(optionContent) {
    console.log(optionContent)
    // 只有当optionContent不为空时才处理
    if (!optionContent) {
        return { images: [], videos: [], audios: [] };
    }

    // 提取图片元素的正则表达式
    const imgRegex = /<img\s+([^>]*)src\s*=\s*["']([^"']*)["']\s*([^>]*)>/gi;
    const images = [];
    let imgMatch;
    while ((imgMatch = imgRegex.exec(optionContent)) !== null) {
        const fullMatch = imgMatch[0];
        const src = imgMatch[2];

        // 提取其他属性
        // const altMatch = /alt\s*=\s*["']([^"']*)/i.exec(fullMatch);
        const styleMatch = /style\s*=\s*["']([^"']*)/i.exec(fullMatch);
        // const dataHrefMatch = /data-href\s*=\s*["']([^"']*)/i.exec(fullMatch);

        // 提取宽度和高度属性
        const widthMatch = /width\s*=\s*["']([^"']*)/i.exec(fullMatch);
        const heightMatch = /height\s*=\s*["']([^"']*)/i.exec(fullMatch);

        images.push({
            type: 'image',
            src,
            width: widthMatch ? widthMatch[1] : '',
            height: heightMatch ? heightMatch[1] : '',
            style: styleMatch ? styleMatch[1] : '',
            originHTML: fullMatch,
            // 'data-href': dataHrefMatch ? dataHrefMatch[1] : ''
        });
    }

    // 提取视频元素的正则表达式
    const videoRegex = /<video\s+([^>]*)>(.*?)<\/video>/gi;
    const videos = [];
    let videoMatch;
    while ((videoMatch = videoRegex.exec(optionContent)) !== null) {
        const fullMatch = videoMatch[0];
        const attributes = videoMatch[1];

        // 提取src属性
        const srcMatch = /src\s*=\s*["']([^"']*)/i.exec(attributes);
        const src = srcMatch ? srcMatch[1] : '';

        // 检查是否有controls和autoplay属性
        const hasControls = /controls/i.test(attributes);
        const hasAutoplay = /autoplay/i.test(attributes);

        videos.push({
            type: 'video',
            src,
            originHTML: fullMatch,
            controls: hasControls,
            autoplay: hasAutoplay
        });
    }

    // 提取音频元素的正则表达式
    const audioRegex = /<audio\s+([^>]*)>(.*?)<\/audio>/gi;
    const audios = [];
    let audioMatch;
    while ((audioMatch = audioRegex.exec(optionContent)) !== null) {
        const fullMatch = audioMatch[0];
        const attributes = audioMatch[1];

        // 提取src属性
        const srcMatch = /src\s*=\s*["']([^"']*)/i.exec(attributes);
        const src = srcMatch ? srcMatch[1] : '';

        // 检查是否有controls和autoplay属性
        const hasControls = /controls/i.test(attributes);
        const hasAutoplay = /autoplay/i.test(attributes);

        audios.push({
            type: 'audio',
            src,
            originHTML: fullMatch,
            controls: hasControls,
            autoplay: hasAutoplay
        });
    }

    return { images, videos, audios };
};

onMounted(() => {
    const mediaElements = extractMediaElementsWithRegex(props.optionContent);
    console.log('mediaElements', mediaElements.images);
    elementsOfImgs.value = [...mediaElements.images]
    allMediaElements.value = [...mediaElements.images, ...mediaElements.videos, ...mediaElements.audios];
});

onUnmounted(() => {
    allMediaElements.value = [];
    elementsOfImgs.value = [];
})


watch(() => props.optionContent, (newVal) => {
    const mediaElements = extractMediaElementsWithRegex(newVal);
    console.log('mediaElements', mediaElements.images);
    elementsOfImgs.value = [...mediaElements.images]
    allMediaElements.value = [...mediaElements.images, ...mediaElements.videos, ...mediaElements.audios];
}, {
    deep: true
})
</script>
<style lang="less" scoped>
.option-content-resolver {
    display: flex;

    .content-image {
        width: 72px;
        height: 100%;
    }

    .option-content-template {
        padding: 0 0 0 5px;

        :deep(video) {
            display: none;
        }

        :deep(audio) {
            display: none;
        }

        :deep(img, video, audio) {
            display: none;
        }
    }
}

.option-content-detail {
    text-align: initial;

    :deep(img) {
        max-width: 50px;
    }
}
</style>