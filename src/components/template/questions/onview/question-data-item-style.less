.data-item {
  width: 100%;
  box-sizing: border-box;
  min-height: 100px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 22px 20px 22px 20px;
  color:#333;
  &:last-child {
    margin-bottom: 0;
  }
  ul {
    list-style: none;
  }
  .data-item-top {
    margin-bottom: 20px;
    width: 100%;
  }
  .data-item-em {
    
   
    :deep(img) {
      max-width: 100%;
    }

    :deep(code) {
      white-space: pre-wrap; /* 保留空格和换行符，并自动换行 */
      word-wrap: break-word; /* 防止长单词溢出容器 */
      word-break: break-all; /* 允许在任意字符处断行（可选） */
    }
  }
  .data-item-option {
    width: 100%;
    &.true-or-false {
      display: flex;
    }
    &.sorting {
      display: block;
    }
    &:last-child {
      margin-bottom: 0;
    }
    .option-item {
      text-align: left;
      margin-bottom: 14px;
      &:last-child {
        margin-bottom: 0;
      }
      &.is-right-answer {
        &::after {
          content: '';
          display: inline-block;
          background: url(@/assets/icons/checkedWhiteNike.svg) no-repeat;
          background-size: contain;
          height: 14px;
          width: 14px;
        }
        .right-answer-icon {
          display: inline-block;
        }
      }
      .right-answer-icon {
        display: none;
      }
    }
    &.count {
      .option-item {
        counter-increment: my-counter; /* 计数器递增 */ 
        .option-item-content {
          display: inline-block;
          max-width: 580px;
          :deep(img) {
            max-width: 30%;
          }
          ::v-deep(p){
            display: block;
          }
        }
      }
    }
  }
  .data-item-analysis {
    display: flex;
    margin-top: 10px;
    label {
      line-height: 1.4;
      min-width: 30px;
    }
    p {
      flex: 1;
    }
  }
}