<template>
  <div class="app-container">
    <t-row :gutter="20">
      <!-- 左侧目录 -->
      <t-col :span="3">
        <t-card class="catalog-card">
          <Catalog
            :catalogs="catalogList"
            @click-catalog="handleCatalogClick"
            @add-catalog="submitCatalogForm"
            @delete-catalog="handleDeleteCatalog"
            @update-catalog="submitCatalogForm"
            @move-catalog="submitCatalogForm"
            @refresh-catalogs="getCatalogList"
            @search="getCatalogList"
          />
        </t-card>
      </t-col>

      <!-- 右侧内容区 -->
      <t-col :span="9">
        <!-- 搜索和工具栏卡片 -->
        <t-card>
          <div class="tool">
            <t-form
              v-show="showSearch"
              ref="queryRef"
              :model="queryParams"
              layout="inline"
              label-width="68px"
              style="margin-bottom: 20px"
            >
              <t-form-item label="类型" prop="questionType">
                <t-select
                  v-model="queryParams.questionType"
                  placeholder="请选择小题类型"
                  clearable
                  style="width: 150px"
                >
                  <t-option
                    v-for="item in questionTypes"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </t-select>
              </t-form-item>
              <t-form-item label="题目" prop="questionContent">
                <t-input
                  v-model="queryParams.questionContent"
                  placeholder="请输入题目关键词"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </t-form-item>
              <t-form-item>
                <t-button theme="primary" @click="handleQuery">
                  <template #icon>
                    <SearchIcon />
                  </template>
                  搜索
                </t-button>
                <t-button
                  theme="default"
                  style="margin-left: 8px"
                  @click="resetQuery"
                >
                  <template #icon>
                    <RefreshIcon />
                  </template>
                  重置
                </t-button>
              </t-form-item>
            </t-form>

            <t-row :gutter="10" class="mb8" style="margin-left: 10px">
              <t-col :span="1.5">
                <t-button
                  v-hasPermi="['book:userQuestion:remove']"
                  theme="danger"
                  variant="outline"
                  :disabled="multiple"
                  @click="handleDelete"
                >
                  <template #icon>
                    <DeleteIcon />
                  </template>
                  删除
                </t-button>
              </t-col>
              <t-col :span="1.5">
                <t-button
                  theme="default"
                  variant="outline"
                  @click="goToRecycleBin"
                >
                  <template #icon>
                    <DeleteIcon />
                  </template>
                  回收站
                </t-button>
              </t-col>
              <t-col :span="1.5">
                <t-button theme="default" variant="outline" @click="goBack">
                  <template #icon>
                    <ChevronLeftIcon />
                  </template>
                  返回
                </t-button>
              </t-col>
            </t-row>
          </div>
        </t-card>

        <!-- 题目列表卡片 -->
        <t-card class="top20">
          <div v-loading="loading" class="question-list">
            <div
              v-for="item in bookQuestionList"
              :key="item.bookQuestionId"
              class="question-card"
            >
              <div class="question-header">
                <div class="question-info">
                  <t-checkbox
                    v-model="item.selected"
                    class="question-checkbox"
                    @change="handleSelectionChange"
                  ></t-checkbox>
                  <t-tag theme="success" size="small">
                    {{ getQuestionTypeName(item.questionType) }}
                  </t-tag>
                </div>
                <div class="operation">
                  <t-dropdown
                    :options="operationOptions"
                    @click="(data) => handleOperationClick(data, item)"
                  >
                    <t-button variant="text" class="more-btn">
                      <t-icon name="more" />
                    </t-button>
                  </t-dropdown>
                </div>
              </div>

              <!-- 题目内容区 -->
              <div class="question-content">
                <!-- 题干 -->
                <div class="content-item">
                  <div class="label">题干：</div>
                  <div
                    class="content"
                    v-html="
                      formatBlankQuestion(
                        item.questionContent,
                        item.questionType,
                      )
                    "
                  ></div>
                </div>

                <!-- 选项区域 -->
                <div
                  v-if="
                    shouldShowOptions(item.questionType) &&
                    item.questionType !== 5 &&
                    item.options &&
                    item.options.length
                  "
                  class="options-area"
                >
                  <div
                    v-for="(option, index) in item.options.filter(
                      (opt) => opt.optionContent,
                    )"
                    :key="index"
                    class="option-item"
                  >
                    <div class="option-content">
                      <span class="option-label">
                        {{
                          item.questionType === 4
                            ? `${index + 1}. `
                            : `${String.fromCharCode(65 + index)}. `
                        }}
                      </span>
                      <span v-html="option.optionContent"></span>
                      <t-icon
                        v-if="option.rightFlag"
                        name="check-circle-filled"
                        class="correct-icon"
                      />
                    </div>
                  </div>
                </div>

                <!-- 连线题专用组件 -->
                <div v-if="item.questionType === 5" class="matching-question">
                  <MatchingQuestion
                    :options="item.options"
                    :right-answer="item.rightAnswer"
                    :show-answer="true"
                  />
                </div>

                <!-- 参考答案区域 -->
                <div
                  v-if="item.rightAnswer && item.questionType === 6"
                  class="content-item"
                >
                  <div class="label">参考答案：</div>
                  <div class="content" v-html="item.rightAnswer"></div>
                </div>

                <!-- 解析区域 -->
                <div v-if="item.analysis" class="content-item">
                  <div class="label">解析：</div>
                  <div class="content" v-html="item.analysis"></div>
                </div>

                <!-- 编程题相关模板 -->
                <template v-if="item.questionType === 8">
                  <div class="content-item">
                    <div class="label">代码：</div>
                    <code-editor
                      v-model="item.code"
                      :language="item.language || 'javascript'"
                      :read-only="true"
                    />
                  </div>
                </template>
              </div>
            </div>
          </div>

          <t-pagination
            v-show="total > 0"
            v-model:current="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :total="total"
            class="pagination"
            @change="handlePageChange"
          />
        </t-card>
      </t-col>
    </t-row>
  </div>

  <!-- 添加移动题目对话框 -->
  <t-dialog
    v-model:visible="moveQuestionDialogVisible"
    header="移动到"
    width="400px"
    :footer="false"
  >
    <t-form
      ref="moveQuestionFormRef"
      :data="moveQuestionForm"
      label-width="80px"
      @submit="submitMoveQuestion"
    >
      <t-tree-select
        v-model="moveQuestionForm.folderId"
        :data="folderTreeOptions"
        :keys="{ value: 'folderId', label: 'folderName' }"
        clearable
        placeholder="请选择目标目录"
      />
      <div class="dialog-footer">
        <t-button theme="primary" type="submit">确 定</t-button>
        <t-button
          theme="default"
          style="margin-left: 20px"
          @click="moveQuestionDialogVisible = false"
          >取 消</t-button
        >
      </div>
    </t-form>
  </t-dialog>

  <!-- 查看题目对话框 -->
  <t-dialog
    v-model:visible="viewOpen"
    :header="title"
    width="800px"
    :footer="false"
  >
    <!-- 题目详情内容 -->
    <div class="question-detail">
      <div class="content-item">
        <div class="label">题型：</div>
        <div class="content">{{ getQuestionTypeName(form.questionType) }}</div>
      </div>

      <div class="content-item">
        <div class="label">题干：</div>
        <div class="content" v-html="form.questionContent"></div>
      </div>

      <!-- 选项区域 -->
      <div
        v-if="
          shouldShowOptions(form.questionType) &&
          form.questionType !== 5 &&
          form.options &&
          form.options.length
        "
        class="options-area"
      >
        <div
          v-for="(option, index) in form.options.filter(
            (opt) => opt.optionContent,
          )"
          :key="index"
          class="option-item"
        >
          <div class="option-content">
            <span class="option-label">
              {{
                form.questionType === 4
                  ? `${index + 1}. `
                  : `${String.fromCharCode(65 + index)}. `
              }}
            </span>
            <span v-html="option.optionContent"></span>
            <t-icon
              v-if="option.rightFlag"
              name="check-circle-filled"
              class="correct-icon"
            />
          </div>
        </div>
      </div>

      <!-- 连线题专用组件 -->
      <div v-if="form.questionType === 5" class="matching-question">
        <MatchingQuestion
          :options="form.options"
          :right-answer="form.rightAnswer"
          :show-answer="true"
        />
      </div>

      <!-- 参考答案区域 -->
      <div
        v-if="form.rightAnswer && form.questionType === 6"
        class="content-item"
      >
        <div class="label">参考答案：</div>
        <div class="content" v-html="form.rightAnswer"></div>
      </div>

      <!-- 解析区域 -->
      <div v-if="form.analysis" class="content-item">
        <div class="label">解析：</div>
        <div class="content" v-html="form.analysis"></div>
      </div>

      <!-- 编程题相关模板 -->
      <template v-if="form.questionType === 8">
        <div class="content-item">
          <div class="label">代码：</div>
          <code-editor
            v-model="form.code"
            :language="form.language || 'javascript'"
            :read-only="true"
          />
        </div>
      </template>
    </div>

    <div class="dialog-footer">
      <t-button @click="viewOpen = false">关 闭</t-button>
    </div>
  </t-dialog>
</template>

<script setup name="BookQuestion">
import {
  ChevronLeftIcon,
  DeleteIcon,
  RefreshIcon,
  SearchIcon,
} from 'tdesign-icons-vue-next'
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next'
import { useRouter } from 'vue-router'
import * as XLSX from 'xlsx'

import {
  addQuestion,
  getQuestion,
  listBookQuestionWithOptions,
  moveToRecycleBin,
  updateQuestion,
} from '@/api/book/bookQuestion.js'
import {
  addFolder,
  delFolder,
  listFolder,
  updateFolder,
} from '@/api/book/bookQuestionFolder.js'
// 更新组件导入路径
import Catalog from '@/components/template/resourceLibrary/components/catalog/index.vue'
import CodeEditor from '@/components/template/resourceLibrary/components/CodeEditor/index.vue'
import MatchingQuestion from '@/components/template/resourceLibrary/components/MatchingQuestion.vue'

const { proxy } = getCurrentInstance()

const bookQuestionList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')

const importDialogVisible = ref(false)
const importList = ref([])
const importForm = ref({
  file: null,
  fileList: [],
  importType: 'normal', // 默认为普通题型
})

// 添加 bookId 响应式变量
const bookId = ref(null)

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    folderId: null,
    questionType: null,
    questionContent: null,
    rightAnswer: null,
    analysis: null,
    chapterId: null,
    bookId: computed(() => bookId.value), // 使用计算属性
    disorder: null,
    sort: null,
  },
  rules: {
    questionType: [
      {
        required: true,
        message: '小题类型1单选2多选3判断4简答...不能为空',
        trigger: 'change',
      },
    ],
    disorder: [
      {
        required: true,
        message: '是否乱序1不可乱序2可乱序不能为空',
        trigger: 'blur',
      },
    ],
  },
})

const { queryParams, form, rules } = toRefs(data)

// 在导入语句后添加题型常量
const questionTypes = [
  { value: 1, label: '单选题' },
  { value: 2, label: '多选题' },
  { value: 3, label: '填空题' },
  { value: 4, label: '排序题' },
  { value: 5, label: '连线题' },
  { value: 6, label: '简答题' },
  { value: 7, label: '判断题' },
  { value: 8, label: '编程题' },
]

// 添加 router
const router = useRouter()

/** 查询数字教材习题列表 */
function getList() {
  loading.value = true
  listBookQuestionWithOptions(queryParams.value).then((response) => {
    bookQuestionList.value = response.rows

    // 处理编程题的代码内容
    bookQuestionList.value = bookQuestionList.value.map((item) => {
      if (item.questionType === 8) {
        try {
          item.code = getCodeContent(item.codeContent) || ''
          item.language =
            getProgrammingLanguage(item.codeContent) || 'javascript'
        } catch (e) {
          console.error('解析编程题内容失败:', e)
          item.code = ''
          item.language = 'javascript'
        }
      }
      return item
    })

    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    bookQuestionId: null,
    questionType: 1,
    questionContent: '',
    rightAnswer: '',
    analysis: '',
    chapterId: null,
    bookId: null,
    disorder: 1,
    sort: 0,
    folderId: queryParams.value.folderId || null,
    options: [],
    language: 'javascript',
  }

  // 根据题型设置不同的默认选项
  if (form.value.questionType === 7) {
    // 判断题
    form.value.options = [
      {
        optionContent: '正确',
        rightFlag: false,
        optionType: null,
        optionIndex: 0,
      },
      {
        optionContent: '错误',
        rightFlag: false,
        optionType: null,
        optionIndex: 1,
      },
    ]
  } else {
    // 其他题型
    form.value.options = [
      { optionContent: '', rightFlag: false, optionType: null, optionIndex: 0 },
      { optionContent: '', rightFlag: false, optionType: null, optionIndex: 1 },
      { optionContent: '', rightFlag: false, optionType: null, optionIndex: 2 },
      { optionContent: '', rightFlag: false, optionType: null, optionIndex: 3 },
    ]
  }

  // 重置表单校验状态
  nextTick(() => {
    if (proxy.$refs['bookQuestionRef']) {
      proxy.$refs['bookQuestionRef'].resetFields()
    }
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.bookQuestionId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  form.value.folderId = queryParams.value.folderId // 设置当前选中的目录ID
  open.value = true
  title.value = '添加数字教材习题'
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _bookQuestionId = row.bookQuestionId || ids.value
  getQuestion(_bookQuestionId).then((response) => {
    // 解码富文本内容
    const data = response.data.userQuestion
    data.questionContent = decodeURIComponent(data.questionContent || '')
    data.analysis = decodeURIComponent(data.analysis || '')
    data.rightAnswer = decodeURIComponent(data.rightAnswer || '')

    // 确保保留必要的属性
    data.bookId = bookId.value
    data.bookQuestionId = data.bookQuestionId || row.bookQuestionId
    data.userQuestionId = data.userQuestionId || row.userQuestionId
    data.questionType = data.questionType || row.questionType
    data.folderId = data.folderId || queryParams.value.folderId

    // 处理选项数据
    if (data.options) {
      data.options = data.options.map((option) => ({
        ...option,
        optionContent: decodeURIComponent(option.optionContent || ''),
        rightFlag: option.rightFlag === 1,
      }))

      // 处理连线题的选项
      if (data.questionType === 5) {
        // 初始化 matchingData
        matchingData.value = {
          leftOptions: [],
          rightOptions: [],
          matchingPairs: [],
        }

        // 分离左右选项
        const leftOptions = data.options
          .filter((opt) => opt.optionPosition === '1')
          .sort((a, b) => a.sort - b.sort)
          .map((opt, index) => ({
            id: index,
            content: decodeURIComponent(opt.optionContent || ''),
          }))

        const rightOptions = data.options
          .filter((opt) => opt.optionPosition === '2')
          .sort((a, b) => a.sort - b.sort)
          .map((opt, index) => ({
            id: index,
            content: decodeURIComponent(opt.optionContent || ''),
          }))

        // 解析匹配对
        let pairs = []
        try {
          if (data.rightAnswer) {
            const answers =
              typeof data.rightAnswer === 'string'
                ? JSON.parse(data.rightAnswer)
                : data.rightAnswer

            pairs = answers.map((pair) => ({
              leftId: parseInt(pair.source),
              rightId: parseInt(pair.target),
            }))
          }
        } catch (e) {
          console.error('解析连线答案失败:', e)
          pairs = []
        }

        // 更新 matchingData
        matchingData.value = {
          leftOptions,
          rightOptions,
          matchingPairs: pairs,
        }
      }
    }

    form.value = data

    // 监听题型和目录变化
    watch(
      () => form.value.questionType,
      (newType) => {
        if (form.value.bookQuestionId) {
          // 如果是编辑状态，更新题型
          form.value.questionType = newType
        }
      },
    )

    watch(
      () => form.value.folderId,
      (newFolderId) => {
        if (form.value.bookQuestionId) {
          // 如果是编辑状态，更新目录ID
          form.value.folderId = newFolderId
        }
      },
    )

    open.value = true
    title.value = '修改数字教材习题'
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['bookQuestionRef'].validate((valid) => {
    if (valid) {
      // 构建基础数据结构
      const formData = {
        bookQuestionId: form.value.bookQuestionId,
        questionType: form.value.questionType,
        chapterId: form.value.chapterId,
        bookId: bookId.value,
        folderId: queryParams.value.folderId || form.value.folderId || 0,
        userQuestionId: String(form.value.userQuestionId),
        userQuestion: {
          questionId: String(form.value.userQuestionId), // 确保是字符串类型
          questionType: form.value.questionType,
          questionContent: encodeURIComponent(form.value.questionContent || ''),
          rightAnswer: encodeURIComponent(form.value.rightAnswer || ''),
          analysis: encodeURIComponent(form.value.analysis || ''),
          userId: form.value.userId,
          disorder: form.value.disorder || 1,
          folderId: queryParams.value.folderId || form.value.folderId || 0,
        },
      }

      // 处理编程题的代码内容
      if (form.value.questionType === 8) {
        formData.userQuestion.codeContent = JSON.stringify({
          code: form.value.code || '',
          language: form.value.language || 'javascript',
        })
      }

      // 处理选项
      if (form.value.options) {
        if (form.value.questionType === 5) {
          // 连线题特殊处理
          formData.userQuestion.options = []

          // 处理左侧选项
          matchingData.value.leftOptions.forEach((left, index) => {
            formData.userQuestion.options.push({
              optionContent: encodeURIComponent(left.content || ''),
              optionPosition: '1',
              sort: index,
            })
          })

          // 处理右侧选项
          matchingData.value.rightOptions.forEach((right, index) => {
            formData.userQuestion.options.push({
              optionContent: encodeURIComponent(right.content || ''),
              optionPosition: '2',
              sort: index,
            })
          })

          // 处理连线对
          formData.userQuestion.rightAnswer = JSON.stringify(
            matchingData.value.matchingPairs.map((pair) => ({
              source: pair.left.id,
              target: pair.right.id,
            })),
          )
        } else {
          // 其他题型的选项处理
          formData.userQuestion.options = form.value.options.map((option) => ({
            ...option,
            optionContent: encodeURIComponent(option.optionContent || ''),
            rightFlag: option.rightFlag ? 1 : 0,
          }))
        }
      }

      if (formData.bookQuestionId != null) {
        updateQuestion(formData).then((response) => {
          MessagePlugin.success('修改成功')
          open.value = false
          getList()
        })
      } else {
        addQuestion(formData).then((response) => {
          MessagePlugin.success('新增成功')
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _bookQuestionIds = row.bookQuestionId || ids.value

  const dialogInstance = DialogPlugin.confirm({
    header: '确认操作',
    body: '是否确认将所选题目移入回收站？',
    onConfirm: () => {
      return moveToRecycleBin(_bookQuestionIds)
        .then(() => {
          getList()
          MessagePlugin.success('已移入回收站')
          // 显式关闭对话框
          dialogInstance.destroy()
        })
        .catch((error) => {
          MessagePlugin.error(`操作失败：${error.message}`)
          // 出错时也关闭对话框
          dialogInstance.destroy()
        })
    },
  })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'book/bookQuestion/export',
    {
      ...queryParams.value,
    },
    `bookQuestion_${new Date().getTime()}.xlsx`,
  )
}

// 添加目录相关数据和方法
const catalogList = ref([])

// 获取目录列表
async function getCatalogList(searchForm) {
  try {
    const queryForm = {
      ...searchForm,
      bookId: bookId.value,
    }

    const response = await listFolder(queryForm)
    const folders = response.rows || []

    // 只在以下条件下创建默认目录：
    // 1. 没有搜索条件
    // 2. 当前没有任何目录
    // 3. bookId 存在
    if (folders.length === 0 && !searchForm?.folderName && bookId.value) {
      // 检查是否已经有默认目录的请求正在进行
      if (!getCatalogList.isCreatingDefault) {
        getCatalogList.isCreatingDefault = true

        const defaultCatalog = {
          folderName: '默认目录',
          parentId: '0',
          orderNum: 1,
          bookId: bookId.value,
        }

        try {
          await addFolder(defaultCatalog)
          // 重新获取目录列表
          const newResponse = await listFolder(queryForm)
          const newFolders = newResponse.rows || []

          // 更新状态
          catalogList.value = buildTreeData(newFolders)
          folderTreeData.value = newFolders
          debugger
          // 设置默认选中第一个目录
          if (newFolders.length > 0) {
            const firstFolder = newFolders[0]
            queryParams.value.folderId = firstFolder.folderId

            handleCatalogClick(firstFolder)
          }
        } finally {
          getCatalogList.isCreatingDefault = false
        }
      }
    } else {
      // 更新目录列表状态
      catalogList.value = buildTreeData(folders)
      folderTreeData.value = folders

      // 如果没有选中的目录且有目录数据，默认选中第一个

      if (!queryParams.value.folderId && folders.length > 0) {
        const firstFolder = folders[0]
        firstFolder.data = firstFolder
        queryParams.value.folderId = firstFolder.folderId
        handleCatalogClick(firstFolder)
      }
    }

    // 构建移动目录的树形选项
    buildFolderTree()
  } catch (error) {
    console.error('获取目录数据失败:', error)
    proxy.$modal.msgError('获取目录数据失败')
  }
}

// 优化构建树形数据的函数
function buildTreeData(folders) {
  const map = new Map()
  const tree = []

  // 第一遍：创建所有节点的映射
  folders.forEach((folder) => {
    map.set(folder.folderId, {
      ...folder,
      children: [],
    })
  })

  // 第二遍：构建树形结构
  folders.forEach((folder) => {
    const node = map.get(folder.folderId)
    const parentId = folder.parentId?.toString() || '0'

    if (parentId === '0') {
      // 根节点直接加入树中
      tree.push(node)
    } else {
      // 找到父节点并添加到其子节点中
      const parent = map.get(parentId)
      if (parent) {
        parent.children.push(node)
      } else {
        // 如果找不到父节点，作为根节点处理
        tree.push(node)
      }
    }
  })

  // 按 orderNum 排序
  const sortNodes = (nodes) => {
    nodes.sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0))
    nodes.forEach((node) => {
      if (node.children && node.children.length > 0) {
        sortNodes(node.children)
      }
    })
  }

  sortNodes(tree)
  return tree
}

// 优化移动目录树的构建
function buildFolderTree() {
  if (!folderTreeData.value) return

  // 构建树形结构
  const tree = buildTreeData(folderTreeData.value)

  // 更新树形选项
  folderTreeOptions.value = [
    {
      folderId: '0',
      folderName: '根目录',
      children: tree,
    },
  ]
}

// 获取所有子目录ID
function getChildFolderIds(parentId) {
  const childIds = []
  const findChildren = (pid) => {
    folderTreeData.value.forEach((folder) => {
      if (folder.parentId === pid) {
        childIds.push(folder.folderId)
        findChildren(folder.folderId)
      }
    })
  }
  findChildren(parentId)
  return childIds
}

// 提交移动操作
async function submitMove() {
  if (moveForm.value.parentId) {
    try {
      const targetFolderId =
        moveForm.value.parentId[moveForm.value.parentId.length - 1]

      await updateFolder({
        folderId: moveForm.value.currentId,
        parentId: targetFolderId,
      })

      MessagePlugin.success('移动成功')
      moveCatalogDialogVisible.value = false
      getCatalogList()
    } catch (error) {
      MessagePlugin.error(`移动失败：${error.message}`)
    }
  } else {
    MessagePlugin.warning('请选择目标目录')
  }
}

const matchingData = ref({
  leftOptions: [],
  rightOptions: [],
  matchingPairs: [],
})

const handleMatchingUpdate = (newValue) => {
  // 更新表单中的连线题数据
  form.value.options = []
  form.value.rightAnswer = []

  // 处理左侧选项
  newValue.leftOptions.forEach((left, index) => {
    form.value.options.push({
      optionContent: left.content,
      optionPosition: '1',
      sort: index,
    })
  })

  // 处理右侧选项
  newValue.rightOptions.forEach((right, index) => {
    form.value.options.push({
      optionContent: right.content,
      optionPosition: '2',
      sort: index,
    })
  })

  // 处理连线对
  form.value.rightAnswer = JSON.stringify(
    newValue.matchingPairs.map((pair) => ({
      source: pair.left.id,
      target: pair.right.id,
    })),
  )
}

// 处理搜索
function handleSearch(form) {
  // 将搜索表单中的值更新到查询参数中
  queryParams.value = {
    ...queryParams.value,
    ...form,
  }
  // 重置页码到第一页
  queryParams.value.pageNum = 1
  // 执行查询
  getList()
}

// 在 script setup 中添加获取题型名称的方法
const getQuestionTypeName = (type) => {
  const questionType = questionTypes.find((item) => item.value === type)
  return questionType ? questionType.label : '未知题型'
}

// 在 script setup 中添加判断正确选项的方法
const isCorrectOption = (rightAnswer, option) => {
  // 根据题目类型和答案格式进行判断
  if (typeof rightAnswer === 'string') {
    // 如果答案是字符串形式，可能是以逗号分隔的多个答案
    const correctAnswers = rightAnswer.split(',')
    return correctAnswers.includes(option.value)
  }
  return false
}

// 添加判断是否显示选项的方法
const shouldShowOptions = (questionType) => {
  // 填空题(type=3)不显示选项
  return questionType !== 3
}

// 添加处理填空题格式的方法
const formatBlankQuestion = (content, questionType) => {
  if (questionType !== 3) return content

  // 将 ### answer ### 格式转换为带下划线和答案的HTML
  return content.replace(/###(.*?)###/g, (match, answer) => {
    return `<span class="blank-line"><span class="blank-answer">${answer}</span></span>`
  })
}

// 添加选项
function addOption() {
  if (!form.value.options) {
    form.value.options = []
  }
  form.value.options.push({
    optionContent: '',
    rightFlag: false,
  })
}

// 移除选项
function removeOption(index) {
  form.value.options.splice(index, 1)
}

// 移动选项位置
function moveOption(index, direction) {
  const { options } = form.value
  if (direction === 'up' && index > 0) {
    ;[options[index], options[index - 1]] = [options[index - 1], options[index]]
  } else if (direction === 'down' && index < options.length - 1) {
    ;[options[index], options[index + 1]] = [options[index + 1], options[index]]
  }
}

// 检查是否有其他正确选项（用于单选题）
function hasOtherCorrectOption(currentIndex) {
  return form.value.options.some(
    (option, index) => index !== currentIndex && option.rightFlag,
  )
}

// 添加连线题选项
function addMatchingOption(side) {
  const target = side === 'left' ? 'leftOptions' : 'rightOptions'
  if (!form.value[target]) {
    form.value[target] = []
  }
  form.value[target].push({ content: '' })
}

// 移除连线题选项
function removeMatchingOption(side, index) {
  const target = side === 'left' ? 'leftOptions' : 'rightOptions'
  form.value[target].splice(index, 1)
}

// 添加 watch 来监听题型变化
watch(
  () => form.value.questionType,
  (newType) => {
    if (newType === 7) {
      // 判断题
      form.value.options = [
        {
          optionContent: '正确',
          rightFlag: false,
          optionType: null,
          optionIndex: 0,
        },
        {
          optionContent: '错误',
          rightFlag: false,
          optionType: null,
          optionIndex: 1,
        },
      ]
    } else if (
      [1, 2].includes(newType) &&
      (!form.value.options || form.value.options.length < 2)
    ) {
      // 单选或多选题，如果选项少于2个，则重置为默认4个选项
      form.value.options = [
        {
          optionContent: '',
          rightFlag: false,
          optionType: null,
          optionIndex: 0,
        },
        {
          optionContent: '',
          rightFlag: false,
          optionType: null,
          optionIndex: 1,
        },
        {
          optionContent: '',
          rightFlag: false,
          optionType: null,
          optionIndex: 2,
        },
        {
          optionContent: '',
          rightFlag: false,
          optionType: null,
          optionIndex: 3,
        },
      ]
    }
  },
)

// 目录相关的响应式变量
const catalogOpen = ref(false)
const moveCatalogDialogVisible = ref(false)
const catalogForm = ref({
  folderId: null,
  folderName: null,
  parentId: 0,
})
const moveForm = ref({
  currentId: null,
  parentId: null,
})
const folderTreeOptions = ref([])
const folderTreeData = ref([])

const catalogRules = {
  folderName: [
    { required: true, message: '目录名称不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
  ],
}

// 取消目录对话框
function cancelCatalog() {
  catalogOpen.value = false
  resetCatalogForm()
}

// 重置目录表单
function resetCatalogForm() {
  catalogForm.value = {
    folderId: null,
    folderName: null,
    parentId: 0,
  }
  proxy.resetForm('catalogRef')
}

// 在组件挂载时获取目录列表
onMounted(() => {
  bookId.value = proxy.$route.query.bookId
  getCatalogList()
})

// 监听路由变化，更新 bookId
watch(
  () => proxy.$route.query.bookId,
  (newBookId) => {
    if (newBookId !== bookId.value) {
      bookId.value = newBookId
      getCatalogList()
      getList()
    }
  },
)

// 添加处理目录点击的函数
function handleCatalogClick(catalog) {
  catalog = catalog.data

  if (!catalog) return

  // 更新当前选中的目录ID
  queryParams.value.folderId = catalog.folderId || catalog.catalogId

  // 重置分页到第一页
  queryParams.value.pageNum = 1

  // 获取该目录下的题目列表
  getList()
}

// 添加处理目录相关的其他函数
function handleAddCatalog(row) {
  resetCatalogForm()
  catalogForm.value.parentId = row.folderId || 0
  catalogOpen.value = true
  title.value = '添加目录'
}

function handleUpdateCatalog(data) {
  resetCatalogForm()
  catalogForm.value = { ...data }
  catalogOpen.value = true
  title.value = '修改目录'
}

function handleDeleteCatalog(data) {
  delFolder(data.folderId).then(() => {
    getCatalogList()
    MessagePlugin.success('删除成功')
  })
}

function handleMoveCatalog(data) {
  moveForm.value.currentId = data.folderId
  moveForm.value.parentId = null
  moveCatalogDialogVisible.value = true
  buildFolderTree()
}

// 提交目录表单
function submitCatalogForm(formData) {
  const catalogData = {
    ...formData,
    bookId: bookId.value,
  }

  debugger

  if (catalogData.folderId != null) {
    updateFolder(catalogData).then((response) => {
      MessagePlugin.success('修改成功')
      getCatalogList()
    })
  } else {
    addFolder(catalogData).then((response) => {
      debugger
      MessagePlugin.success('新增成功')
      getCatalogList()
    })
  }
}

// 添加解析编程题内容的辅助函数
const getCodeContent = (questionContent) => {
  try {
    const codeData = JSON.parse(decodeURIComponent(questionContent || '{}'))
    return codeData.code || ''
  } catch (e) {
    console.error('解析代码内容失败:', e)
    return ''
  }
}

const getProgrammingLanguage = (questionContent) => {
  try {
    const codeData = JSON.parse(decodeURIComponent(questionContent || '{}'))
    return codeData.language || 'python'
  } catch (e) {
    console.error('解析编程语言失败:', e)
    return 'python'
  }
}

function handleImport() {
  importDialogVisible.value = true
  importList.value = []
  importForm.value = {
    file: null,
    fileList: [],
    importType: 'normal',
  }
}

const handleImportChange = (file) => {
  // 检查文件类型
  const isExcel =
    file.raw.type ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.raw.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    MessagePlugin.error('只能上传Excel文件！')
    return
  }
  // 检查文件大小
  const isLt10M = file.raw.size / 1024 / 1024 < 10
  if (!isLt10M) {
    MessagePlugin.error('文件大小不能超过 10MB!')
    return
  }

  file.uploadProgress = 0
  importList.value.push(file)
}

const removeImportFile = (index) => {
  importList.value.splice(index, 1)
}

const submitImportForm = async () => {
  if (importList.value.length === 0) {
    MessagePlugin.warning('请选择要导入的文件')
    return
  }

  try {
    const allQuestionData = [] // 收集所有题目数据

    for (const file of importList.value) {
      const formData = new FormData()
      formData.append('file', file.raw)
      formData.append('importType', importForm.value.importType)
      formData.append('folderId', queryParams.value.folderId)

      // 读取Excel文件内容
      const workbook = await readExcelFile(file.raw)
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const rows = XLSX.utils.sheet_to_json(worksheet)

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i]

        // 根据导入类型处理不同的模板
        if (importForm.value.importType === 'matching') {
          // 连线题模板处理
          if (!row['题干'] || !row['选项']) {
            console.warn('跳过无效行:', row)
            continue
          }

          const questionData = {
            bookId: bookId.value,
            questionType: 5, // 连线题类型
            questionContent: encodeURIComponent(row['题干'] || ''),
            analysis: encodeURIComponent(row['解析'] || ''),
            folderId: queryParams.value.folderId,
            options: [],
          }

          const leftOptions = []
          const rightOptions = []

          // 处理左侧选项
          for (let j = 'A'.charCodeAt(0); j <= 'E'.charCodeAt(0); j++) {
            const letter = String.fromCharCode(j)
            const content = row[`左侧项-${letter}`]
            if (content) {
              leftOptions.push({
                optionContent: encodeURIComponent(content),
                optionPosition: '1',
                sort: j - 'A'.charCodeAt(0),
              })
            }
          }

          // 处理右侧选项
          for (let j = 1; j <= 5; j++) {
            const content = row[`右侧项-${j}`]
            if (content) {
              rightOptions.push({
                optionContent: encodeURIComponent(content),
                optionPosition: '2',
                sort: j - 1,
              })
            }
          }

          // 合并所有选项
          questionData.options = [...leftOptions, ...rightOptions]

          // 处理连线答案
          const connections = (row['选项'] || '').split(',').map((pair) => {
            const [left, right] = pair.split('-')
            return {
              source: left.charCodeAt(0) - 'A'.charCodeAt(0),
              target: parseInt(right) - 1,
            }
          })

          questionData.rightAnswer = JSON.stringify(connections)
          allQuestionData.push(questionData)
        } else {
          // 普通题型模板处理
          if (!row['题型'] || !row['题干']) {
            console.warn('跳过无效行:', row)
            continue
          }

          const questionData = {
            bookId: bookId.value,
            questionType: getQuestionTypeValue(row['题型']),
            questionContent: encodeURIComponent(row['题干'] || ''),
            rightAnswer: encodeURIComponent(row['正确答案'] || ''),
            analysis: encodeURIComponent(row['答案解析'] || ''),
            folderId: queryParams.value.folderId,
            options: [],
          }

          // 处理普通题型的选项
          if ([1, 2, 4, 7].includes(questionData.questionType)) {
            // 保持原有的选项处理逻辑
            if (questionData.questionType === 7) {
              // 判断题
              questionData.options = [
                {
                  optionContent: encodeURIComponent('正确'),
                  rightFlag: row['正确答案'] === 'A' ? 1 : 0,
                  optionIndex: 0,
                },
                {
                  optionContent: encodeURIComponent('错误'),
                  rightFlag: row['正确答案'] === 'B' ? 1 : 0,
                  optionIndex: 1,
                },
              ]
            } else if (questionData.questionType === 4) {
              // 排序题
              const sortedOptions = []
              for (let j = 1; j <= 5; j++) {
                const optionContent = row[`选项${j}`]
                if (optionContent) {
                  sortedOptions.push({
                    optionContent: encodeURIComponent(optionContent),
                    rightFlag: 0,
                    optionIndex: j - 1,
                    sort: j - 1,
                  })
                }
              }
              questionData.options = sortedOptions
            } else {
              // 单选题和多选题
              for (let j = 1; j <= 5; j++) {
                const optionContent = row[`选项${j}`]
                if (optionContent) {
                  const rightAnswer = row['正确答案'] || ''
                  const rightFlag = rightAnswer.includes(
                    String.fromCharCode(64 + j),
                  )

                  questionData.options.push({
                    optionContent: encodeURIComponent(optionContent),
                    rightFlag: rightFlag ? 1 : 0,
                    optionIndex: j - 1,
                  })
                }
              }
            }
            allQuestionData.push(questionData)
          }

          file.uploadProgress = Math.floor(((i + 1) / rows.length) * 100)
        }
      }

      // 一次性提交所有题目数据
      await importQuestions(allQuestionData).then((response) => {
        MessagePlugin.success(response.msg)
        importDialogVisible.value = false
        importList.value = []
        getList()
      })
    }
  } catch (error) {
    console.error('导入失败:', error)
    MessagePlugin.error(
      `导入失败：${error.message || '请检查文件格式是否正确'}`,
    )
  }
}

const isAllUploaded = (list) => {
  return (
    list.length > 0 &&
    list.every((file) => !file.uploadProgress || file.uploadProgress === 100)
  )
}

// 添加下载模板方法
function downloadTemplate(type) {
  const templateName =
    type === 'matching' ? '连线题目导入模板.xlsx' : '普通题目导入模板.xlsx'
  window.location.href = `/${templateName}`
}

// 添加读取Excel文件的辅助函数
const readExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        resolve(workbook)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = (error) => reject(error)
    reader.readAsArrayBuffer(file)
  })
}

// 添加分页处理函数
const handlePageChange = (page) => {
  queryParams.value.pageNum = page.current
  getList()
}

// 修改题型转换函数，利用已有的 questionTypes 数组
const getQuestionTypeValue = (typeText) => {
  const questionType = questionTypes.find((type) => type.label === typeText)
  return questionType ? questionType.value : 1 // 默认返回单选题类型
}

// 添加 viewOpen ref
const viewOpen = ref(false)

// 添加查看按钮处理函数
function handleView(row) {
  reset()
  const { bookQuestionId } = row
  getQuestion(bookQuestionId).then((response) => {
    // 解码富文本内容
    const data = response.data.userQuestion
    data.questionContent = decodeURIComponent(data.questionContent || '')
    data.analysis = decodeURIComponent(data.analysis || '')
    data.rightAnswer = decodeURIComponent(data.rightAnswer || '')

    // 处理选项数据
    if (data.options) {
      data.options = data.options.map((option) => ({
        ...option,
        optionContent: decodeURIComponent(option.optionContent || ''),
        rightFlag: option.rightFlag === 1,
      }))
    }

    // 处理编程题的代码内容
    if (data.questionType === 8) {
      try {
        const codeContent = JSON.parse(data.codeContent || '{}')
        data.code = codeContent.code || ''
        data.language = codeContent.language || 'javascript'
      } catch (e) {
        console.error('解析编程题内容失败:', e)
        data.code = ''
        data.language = 'javascript'
      }
    }

    form.value = data
    viewOpen.value = true
    title.value = '查看题目'
  })
}

// 添加跳转方法
function goToRecycleBin() {
  proxy.$router.push({
    path: '/resource/bookQuestionRecycle',
    query: { bookId: bookId.value },
  })
}

// 定义操作选项
const operationOptions = [
  { content: '查看', value: 'view' },
  { content: '移动', value: 'move' },
  { content: '删除', value: 'delete', theme: 'danger' },
]

// 处理操作点击
function handleOperationClick(operation, item) {
  switch (operation.value) {
    case 'view':
      handleView(item)
      break
    case 'edit':
      handleUpdate(item)
      break
    case 'move':
      handleMoveQuestion(item)
      break
    case 'delete':
      handleDelete(item)
      break
  }
}

// 添加移动题目相关的响应式变量
const moveQuestionDialogVisible = ref(false)
const moveQuestionForm = ref({
  bookQuestionId: null,
  folderId: null,
})

// 添加处理移动题目的函数
function handleMoveQuestion(row) {
  moveQuestionForm.value = {
    questionId: row.bookQuestionId,
    folderId: null,
  }
  moveQuestionDialogVisible.value = true
  buildFolderTree() // 确保树形数据是最新的
}

// 提交移动题目操作
async function submitMoveQuestion() {
  if (moveQuestionForm.value.folderId) {
    try {
      const targetFolderId = moveQuestionForm.value.folderId

      // 调用更新接口，只传递必要的字段
      await updateQuestion({
        bookQuestionId: moveQuestionForm.value.questionId,
        folderId: targetFolderId,
      })
      MessagePlugin.success('移动成功')
      moveQuestionDialogVisible.value = false
      getList() // 刷新题目列表
      getCatalogList() // 添加这一行以刷新目录列表
    } catch (error) {
      MessagePlugin.error(`移动失败：${error.message}`)
    }
  } else {
    MessagePlugin.warning('请选择目标目录')
  }
}

/** 返回到教材资源页面 */
function goBack() {
  router.back()
}
</script>
<style>
.tool {
  padding: 20px 0 0;
}

.blank-line {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
  margin: 0 5px;

  .blank-answer {
    color: #409eff;
    font-size: 14px;
    margin-bottom: 2px;
  }

  &::after {
    content: '';
    width: 100%;
    height: 1px;
    background-color: #000;
    display: block;
  }
}
</style>

<style lang="css" scoped>
.app-container {
  padding: 20px;
  background-color: #ffffff;
  min-height: calc(100vh - 60px);
}

/* 替换原有的 Cards 相关样式 */
:deep(.t-card) {
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: #ffffff;
}

:deep(.t-card__body) {
  padding: 0;
}

.top20 {
  margin-top: 20px;
}

/* 左侧目录样式 */
:deep(.t-card) .catalog-container {
  height: calc(100vh - 120px);
  overflow-y: auto;
}

/* 右侧内容区样式 */
.question-list {
  display: grid;
  grid-gap: 20px;
  padding: 10px;
  max-width: 100%;
  overflow-x: hidden;
}

.question-card {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 15px;
  background: #fff;
  width: 100%;
  box-sizing: border-box;
  overflow-wrap: break-word;
  word-wrap: break-word;
  min-width: 0;
  max-width: 100%;
  overflow-x: hidden;
}

.question-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
}

.question-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.question-content {
  max-width: 100%;
  overflow-x: hidden;
}

.content-item {
  margin-bottom: 15px;
}

.content-item .label {
  font-weight: bold;
  margin-bottom: 8px;
  color: #606266;
}

.content-item .content {
  color: #303133;
  line-height: 1.6;
  word-break: break-word;
  overflow-wrap: break-word;
}

.options-area {
  margin: 10px 0;
}

.option-item {
  margin-bottom: 8px;
  padding: 5px 10px;
}

.option-item:hover {
  background-color: #f5f7fa;
}

.option-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;

  .correct-icon {
    margin-left: 8px;
    color: var(--td-success-color);
  }
}

.option-label {
  flex-shrink: 0;
  font-weight: bold;
}

.correct-icon {
  color: var(--td-success-color);
  margin-left: auto;
}

/* 编程题代码编辑器样式 */
:deep(.code-editor-container) {
  margin: 10px 0;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

/* 连线题样式 */
.matching-question {
  margin: 15px 0;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

/* 更多按钮样式 */
.operation .more-btn {
  padding: 4px 8px;
}

.operation .more-btn:hover {
  background-color: var(--td-bg-color-container-hover);
  border-radius: var(--td-radius-small);
}

.operation .more-btn .t-icon {
  font-size: 16px;
  color: var(--td-text-color-secondary);
}

/* 复选框样式 */
:deep(.t-checkbox) {
  margin: 0;
}

/* 标签样式 */
:deep(.t-tag) {
  margin: 0;
}

.mb8 {
  margin-bottom: 8px;
}
/* 分页样式 */
.pagination {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  background: #fff;
  border-top: 1px solid var(--td-component-border);
}

.catalog-card :deep(.t-card__body) {
  padding: 16px;
}
</style>
