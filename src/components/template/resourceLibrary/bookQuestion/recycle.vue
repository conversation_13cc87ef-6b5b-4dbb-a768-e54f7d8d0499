<template>
    <div class="app-container">
        <t-card>
            <div class="tool">
                <t-form
                    v-show="showSearch"
                    ref="queryRef"
                    :data="queryParams"
                    layout="inline"
                    label-width="68px"
                    style="margin-bottom: 20px"
                >
                    <t-form-item label="题型" name="questionType">
                        <t-select
                            v-model="queryParams.questionType"
                            placeholder="请选择题型"
                            clearable
                            style="width: 200px"
                        >
                            <t-option label="全部" value="" />
                            <t-option
                                v-for="item in questionTypes"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </t-select>
                    </t-form-item>
                    <t-form-item>
                        <t-button theme="primary" @click="handleQuery">
                            <template #icon><t-icon name="search" /></template>
                            搜索
                        </t-button>
                        <t-button @click="resetQuery">
                            <template #icon><t-icon name="refresh" /></template>
                            重置
                        </t-button>
                    </t-form-item>
                </t-form>

                <t-row :gutter="10" class="mb8">
                    <t-col :span="1.5">
                        <t-button
                            theme="primary"
                            variant="outline"
                            :disabled="multiple"
                            @click="handleRestore"
                        >
                            <template #icon><t-icon name="refresh" /></template>
                            还原
                        </t-button>
                    </t-col>
                    <t-col :span="1.5">
                        <t-button
                            theme="danger"
                            variant="outline"
                            :disabled="multiple"
                            @click="handleDelete"
                        >
                            <template #icon><t-icon name="delete" /></template>
                            彻底删除
                        </t-button>
                    </t-col>
                    <t-col :span="1.5">
                        <t-button theme="primary" variant="outline" @click="goBack">
                            <template #icon><t-icon name="arrow-left" /></template>
                            返回
                        </t-button>
                    </t-col>
                </t-row>
            </div>
        </t-card>

        <t-card class="top20">
            <div class="recycle-list">
                <t-table
                    :data="recycleList"
                    :loading="loading"
                    :columns="columns"
                    :select-on-row-click="false"
                    @select-change="handleSelectionChange"
                />

                <t-pagination
                    v-if="total > 0"
                    :total="total"
                    v-model:current="queryParams.pageNum"
                    v-model:pageSize="queryParams.pageSize"
                    @change="getList"
                />
            </div>
        </t-card>

        <restore-dialog
            v-model:visible="restoreDialogVisible"
            :question-ids="selectedQuestionIds"
            @close="cancelRestore"
            @submit="confirmRestore"
        />
    </div>
</template>

<script setup name="QuestionRecycleBin">
import { ref, reactive, toRefs, getCurrentInstance, computed, onMounted, watch } from 'vue'
import { recycleBinList, restoreFromRecycleBin, delQuestion } from "@/api/book/bookQuestion";
import { DialogPlugin } from 'tdesign-vue-next'
import RestoreDialog from '@/components/template/resourceLibrary/components/restore.vue'

const { proxy } = getCurrentInstance();

// 题型定义
const questionTypes = [
    { value: 1, label: '单选题' },
    { value: 2, label: '多选题' },
    { value: 3, label: '填空题' },
    { value: 4, label: '排序题' },
    { value: 5, label: '连线题' },
    { value: 6, label: '简答题' },
    { value: 7, label: '判断题' },
    { value: 8, label: '编程题' }
];

const loading = ref(false);
const showSearch = ref(true);
const multiple = ref(true);
const total = ref(0);
const recycleList = ref([]);
const ids = ref([]);
const bookId = ref(null);
const restoreDialogVisible = ref(false)
const selectedQuestionIds = ref([])

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        questionType: undefined,
        bookId: computed(() => bookId.value)
    }
});

const { queryParams } = toRefs(data);

const columns = [
    { colKey: 'row-select', type: 'multiple', width: 55 },
    { 
        colKey: 'index', 
        title: '序号', 
        width: 55,
        cell: (h, { rowIndex }) => {
            return (queryParams.value.pageNum - 1) * queryParams.value.pageSize + rowIndex + 1
        }
    },
    { 
        colKey: 'questionType', 
        title: '题目类型',
        cell: (h, { row }) => getQuestionTypeName(row.questionType)
    },
    { 
        colKey: 'questionContent', 
        title: '题目内容',
        cell: (h, { row }) => {
            return h('div', {
                innerHTML: formatContent(decodeURIComponent(row.questionContent)),
                class: 'question-content'
            })
        }
    },
    { 
        colKey: 'folderName', 
        title: '所属目录',
        cell: (h, { row }) => row.folderName || '默认目录'
    },
    { 
        colKey: 'updateTime', 
        title: '删除时间',
        width: 180,
        cell: (h, { row }) => parseTime(row.updateTime)
    },
    { 
        colKey: 'operate', 
        title: '操作', 
        width: 180,
        cell: (h, { row }) => [
            h(
                'button',
                {
                    class: 't-button t-button--theme-primary t-button--variant-text',
                    onClick: () => handleRestore(row),
                    style: 'margin-right: 8px'
                },
                [
                    h('t-icon', { name: 'refresh' }),
                    '还原'
                ]
            ),
            h(
                'button',
                {
                    class: 't-button t-button--theme-danger t-button--variant-text',
                    onClick: () => handleDelete(row)
                },
                [
                    h('t-icon', { name: 'delete' }),
                    '彻底删除'
                ]
            )
        ]
    }
];

/** 查询回收站列表 */
function getList() {
    loading.value = true;
    recycleBinList(queryParams.value).then(response => {
        recycleList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    }).catch(error => {
        console.error("获取回收站列表失败:", error);
        proxy.$modal.msgError("获取列表失败");
        loading.value = false;
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 还原按钮操作 */
function handleRestore(row) {
    selectedQuestionIds.value = row.bookQuestionId ? [row.bookQuestionId] : ids.value;
    restoreDialogVisible.value = true;
}

function cancelRestore() {
    restoreDialogVisible.value = false;
    selectedQuestionIds.value = [];
}

async function confirmRestore(questionIds) {
    try {
        await restoreFromRecycleBin(questionIds);
        getList();
        DialogPlugin.success("还原成功");
        restoreDialogVisible.value = false;
    } catch (error) {
        console.error("还原失败:", error);
        DialogPlugin.error("还原失败");
    }
}

/** 彻底删除按钮操作 */
function handleDelete(row) {
    const questionIds = row.bookQuestionId ? [row.bookQuestionId] : ids.value;
    const dialog = DialogPlugin.confirm({
        content: '是否确认彻底删除选中的题目？删除后将无法恢复！',
        onConfirm: async () => {
            try {
                await delQuestion(questionIds);
                getList();
                DialogPlugin.success("删除成功");
            } catch (error) {
                console.error("删除失败:", error);
                DialogPlugin.error("删除失败");
            } finally {
                dialog.hide();
            }
        },
        onClose: () => {
            dialog.hide();
        }
    });
}

/** 格式化时间 */
function parseTime(time) {
  if (!time) return ''
  const date = new Date(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.bookQuestionId);
    multiple.value = !selection.length;
}

/** 获取题型名称 */
function getQuestionTypeName(type) {
    const questionType = questionTypes.find(item => item.value === type);
    return questionType ? questionType.label : '未知题型';
}

/** 返回方法 */
function goBack() {
    proxy.$router.push({
        path: '/resource/bookQuestion',
        query: { bookId: bookId.value }
    });
}

/** 格式化内容 */
function formatContent(content) {
    if (!content) return '';
    const maxLength = 50;
    const text = content.replace(/<[^>]+>/g, ''); // 移除HTML标签
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// 在组件挂载时获取 bookId 并加载列表
onMounted(() => {
    bookId.value = proxy.$route.query.bookId;
    getList();
});

// 监听路由变化
watch(
    () => proxy.$route.query.bookId,
    (newBookId) => {
        if (newBookId !== bookId.value) {
            bookId.value = newBookId;
            getList();
        }
    }
);
</script>

<style  scoped>
.tool {
    padding: 20px 0 0;
}

.mb8 {
    display: flex;
    gap: 8px;
}

.top20 {
    margin-top: 20px;
}

.recycle-list {
    padding: 20px;
}

.question-content {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
}
</style> 