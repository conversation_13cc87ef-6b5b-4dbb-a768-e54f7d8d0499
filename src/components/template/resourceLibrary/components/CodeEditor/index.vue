<template>
  <div class="code-editor-container" :class="theme">
    <div v-if="!readOnly" class="code-toolbar">
      <span>代码编辑器</span>
    </div>
    <div ref="editorContainer" class="prism-code-editor"></div>
    <div v-if="readOnly" class="editor-mask"></div>
  </div>
</template>

<script setup>
// 引入需要支持的语言
import 'prism-code-editor/prism/languages/bash'
import 'prism-code-editor/prism/languages/css'
import 'prism-code-editor/prism/languages/css-extras'
import 'prism-code-editor/prism/languages/ini'
import 'prism-code-editor/prism/languages/kotlin'
import 'prism-code-editor/prism/languages/xml'
import 'prism-code-editor/prism/languages/markup'
import 'prism-code-editor/prism/languages/r'
import 'prism-code-editor/prism/languages/basic'
import 'prism-code-editor/prism/languages/vbnet'
import 'prism-code-editor/prism/languages/c'
import 'prism-code-editor/prism/languages/opencl'
import 'prism-code-editor/prism/languages/diff'
import 'prism-code-editor/prism/languages/java'
import 'prism-code-editor/prism/languages/sass'
import 'prism-code-editor/prism/languages/erlang'
import 'prism-code-editor/prism/languages/matlab'
import 'prism-code-editor/prism/languages/markdown'
import 'prism-code-editor/prism/languages/less'
import 'prism-code-editor/prism/languages/objectivec'
import 'prism-code-editor/prism/languages/ruby'
import 'prism-code-editor/prism/languages/sql'
import 'prism-code-editor/prism/languages/wasm'
import 'prism-code-editor/prism/languages/cpp'
import 'prism-code-editor/prism/languages/go'
import 'prism-code-editor/prism/languages/javascript'
import 'prism-code-editor/prism/languages/js-templates'
import 'prism-code-editor/prism/languages/jsx'
import 'prism-code-editor/prism/languages/lua'
import 'prism-code-editor/prism/languages/perl'
import 'prism-code-editor/prism/languages/python'
import 'prism-code-editor/prism/languages/rust'
import 'prism-code-editor/prism/languages/swift'
import 'prism-code-editor/prism/languages/clike'
import 'prism-code-editor/prism/languages/php'
import 'prism-code-editor/prism/languages/csharp'
import 'prism-code-editor/prism/languages/graphql'
import 'prism-code-editor/prism/languages/json'
import 'prism-code-editor/prism/languages/makefile'
import 'prism-code-editor/prism/languages/scss'
import 'prism-code-editor/prism/languages/typescript'
import 'prism-code-editor/prism/languages/tsx'
import 'prism-code-editor/prism/languages/yaml'
import 'prism-code-editor/prism/languages/regex'
import 'prism-code-editor/layout.css'
// import 'prism-code-editor/themes/github-dark.css'
import 'prism-code-editor/themes/github-light.css'
import 'prism-code-editor/code-folding.css'

import { createEditor } from 'prism-code-editor'
import { defaultCommands, editHistory } from 'prism-code-editor/commands'
import { cursorPosition } from 'prism-code-editor/cursor'
import { indentGuides } from 'prism-code-editor/guides'
import { highlightBracketPairs } from 'prism-code-editor/highlight-brackets'
import { matchBrackets } from 'prism-code-editor/match-brackets'
import { matchTags } from 'prism-code-editor/match-tags'
import { onBeforeUnmount, onMounted, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  language: {
    type: String,
    default: 'css',
  },
  theme: {
    type: String,
    default: 'github-dark',
  },
  readOnly: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const editorContainer = ref(null)
let editor = null

function recreateEditor() {
  editor = createEditor(
    editorContainer.value,
    {
      readOnly: props.readOnly,
      language: props.language || 'java',
      prismLanguage: props.language,
      tabSize: 2,
      lineNumbers: true,
      wordWrap: true,
      value: props.modelValue,
      theme: props.theme === 'dark' ? 'github-dark' : 'github-light',
      languages: {
        bash: 'Bash',
        css: 'CSS',
        ini: 'INI',
        kotlin: 'Kotlin',
        xml: 'XML',
        markup: 'HTML',
        r: 'R',
        basic: 'BASIC',
        vbnet: 'VB.NET',
        c: 'C',
        opencl: 'OpenCL',
        diff: 'Diff',
        java: 'Java',
        less: 'Less',
        objectivec: 'Objective-C',
        ruby: 'Ruby',
        sql: 'SQL',
        wasm: 'WebAssembly',
        cpp: 'C++',
        go: 'Go',
        javascript: 'JavaScript',
        jsx: 'JSX',
        lua: 'Lua',
        perl: 'Perl',
        PHP: 'PHP',
        python: 'Python',
        rust: 'Rust',
        swift: 'Swift',
        clike: 'C-like',
        csharp: 'C#',
        graphql: 'GraphQL',
        json: 'JSON',
        makefile: 'Makefile',
        scss: 'SCSS',
        typescript: 'TypeScript',
        tsx: 'TSX',
        yaml: 'YAML',
        regex: 'RegExp',
      },
      onUpdate(value) {
        emit('update:modelValue', value)
      },
    },
    indentGuides(),
    matchBrackets(),
    ...(props.readOnly ? [] : []),
  )

  editor.addExtensions(
    matchBrackets(),
    matchTags(),
    indentGuides(),
    highlightBracketPairs(),
    cursorPosition(),
    ...(!props.readOnly ? [defaultCommands(), editHistory()] : []),
  )
}

onMounted(() => {
  recreateEditor()

  // 添加事件监听器来阻止输入
  if (props.readOnly) {
    const editorElement = editorContainer.value.querySelector('textarea')
    if (editorElement) {
      editorElement.addEventListener('compositionstart', (e) =>
        e.preventDefault(),
      )
      editorElement.addEventListener('compositionupdate', (e) =>
        e.preventDefault(),
      )
      editorElement.addEventListener('compositionend', (e) =>
        e.preventDefault(),
      )
      editorElement.addEventListener('input', (e) => e.preventDefault())
      editorElement.addEventListener('keydown', (e) => {
        // 允许导航键和功能键
        if (
          !e.ctrlKey &&
          !e.metaKey &&
          !e.altKey &&
          ![
            'ArrowUp',
            'ArrowDown',
            'ArrowLeft',
            'ArrowRight',
            'Home',
            'End',
            'PageUp',
            'PageDown',
          ].includes(e.key)
        ) {
          e.preventDefault()
        }
      })
    }
  }
})

onBeforeUnmount(() => {
  editor?.remove()
})

watch(
  () => props.language,
  (nValue, oValue) => {
    if (nValue === oValue) {
      return
    }
    editor?.remove()
    recreateEditor()
  },
)
</script>

<style scoped>
.code-editor-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  position: relative;

  .code-toolbar {
    position: absolute;
    right: 5px;
    top: 5px;
    font-size: 12px;
    padding: 3px 6px;
    cursor: pointer;
    background-color: #fff;
    border: solid 1px #ddd;
    border-radius: 4px;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.2s;
  }

  &:hover .code-toolbar {
    opacity: 1;
  }

  &.dark .code-toolbar {
    background-color: #161b22;
    border-color: #30363d;
    color: #c9d1d9;
  }

  .prism-code-editor {
    min-height: 150px;
    height: fit-content;
    width: 100%;
    min-width: 500px;
    font-size: 14px;
    font-family: monospace;
  }

  .editor-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 16px;
    bottom: 16px;
    background: transparent;
    z-index: 20;
    cursor: not-allowed;
    pointer-events: none;
  }
}

.light {
  background: #fff;
  color: #24292e;
}

.dark {
  background: #0d1117;
  color: #c9d1d9;
}
</style>
