<template>
    <div class="matching-container">
      <!-- 左侧选项 -->
      <div class="matching-left">
        <div v-for="(leftOption, index) in leftOptions" 
             :key="'left-' + index"
             class="draggable-item"
             :id="'left-' + leftOption.optionId"
             @click="selectOption('left', leftOption)"
             :class="{'selected': leftOption.selected}">
          <div v-html="leftOption.optionContent"/>
        </div>
      </div>
  
      <!-- 右侧选项 -->
      <div class="matching-right">
        <div v-for="(rightOption, index) in rightOptions" 
             :key="'right-' + index"
             class="drop-target"
             :id="'right-' + rightOption.optionId"
             @click="selectOption('right', rightOption)"
             :class="{'selected': rightOption.selected}">
          <div v-html="rightOption.optionContent"/>
        </div>
      </div>
  
      <!-- 连线 SVG -->
      <svg class="lines-svg">
        <line v-for="(line, index) in lines" 
              :key="index"
              :x1="line.x1" 
              :y1="line.y1" 
              :x2="line.x2" 
              :y2="line.y2"
              :stroke="line.color" 
              stroke-width="2"/>
      </svg>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, watch, nextTick } from 'vue';
  
  const props = defineProps({
    options: {
      type: Array,
      required: true
    },
    rightAnswer: {
      type: String,
      required: true
    },
    showAnswer: {
      type: Boolean,
      default: false
    }
  });
  
  const leftOptions = ref([]);
  const rightOptions = ref([]);
  const lines = ref([]);
  const selectedLeft = ref(null);
  const selectedRight = ref(null);
  
  // 初始化选项
  onMounted(async () => {
    // 分离左右选项
    const leftOpts = props.options.filter(opt => opt.optionPosition === 1);
    const rightOpts = props.options.filter(opt => opt.optionPosition === 2);
    
    leftOptions.value = leftOpts.map(opt => ({
      ...opt,
      selected: false
    }));
    
    rightOptions.value = rightOpts.map(opt => ({
      ...opt,
      selected: false
    }));

    // 等待DOM更新完成
    await nextTick();
    
    if (props.showAnswer) {
      showAllAnswers();
    }
    
    const resizeObserver = new ResizeObserver(() => {
      updateLines();
    });
    
    const container = document.querySelector('.matching-container');
    if (container) {
      resizeObserver.observe(container);
    }
  });
  
  // 选择选项
  const selectOption = (side, option) => {
    if (side === 'left') {
      // 取消其他左侧选中
      leftOptions.value.forEach(opt => opt.selected = false);
      option.selected = true;
      selectedLeft.value = option;
    } else {
      // 取消其他右侧选中
      rightOptions.value.forEach(opt => opt.selected = false);
      option.selected = true;
      selectedRight.value = option;
    }
  
    // 如果两边都选中了，画线
    if (selectedLeft.value && selectedRight.value) {
      drawLine();
    }
  };
  
  // 画线
  const drawLine = () => {
    const leftElement = document.getElementById('left-' + selectedLeft.value.optionId);
    const rightElement = document.getElementById('right-' + selectedRight.value.optionId);
  
    if (leftElement && rightElement) {
      const container = leftElement.closest('.matching-container');
      const containerRect = container.getBoundingClientRect();
      const leftRect = leftElement.getBoundingClientRect();
      const rightRect = rightElement.getBoundingClientRect();
  
      // 计算相对位置
      const x1 = leftRect.right - containerRect.left;
      const y1 = leftRect.top - containerRect.top + leftRect.height / 2;
      const x2 = rightRect.left - containerRect.left;
      const y2 = rightRect.top - containerRect.top + rightRect.height / 2;
  

    
      // 检查是否正确匹配
      const answers = JSON.parse(props.rightAnswer);
      const isCorrect = answers.some(answer => 
        answer.source === selectedLeft.value.optionPosition && 
        answer.target === selectedRight.value.optionPosition
      );
  
      // 添加连线
      lines.value.push({
        x1, y1, x2, y2,
        color: isCorrect ? '#67C23A' : '#F56C6C',
        leftId: selectedLeft.value.optionId,
        rightId: selectedRight.value.optionId
      });
  
      // 重置选中状态
      selectedLeft.value.selected = false;
      selectedRight.value.selected = false;
      selectedLeft.value = null;
      selectedRight.value = null;
    }
  };
  
  // 添加显示答案的方法
  const showAllAnswers = async () => {
    // 确保DOM更新完成
    await nextTick();
    
    const answers = JSON.parse(props.rightAnswer);
    for (const answer of answers) {
      const leftOption = leftOptions.value[answer.source];
      const rightOption = rightOptions.value[answer.target];
      
      if (leftOption && rightOption) {
        await nextTick(); // 等待每次循环的DOM更新
        const leftElement = document.getElementById('left-' + leftOption.optionId);
        const rightElement = document.getElementById('right-' + rightOption.optionId);
        
        if (leftElement && rightElement) {
          const container = leftElement.closest('.matching-container');
          const containerRect = container.getBoundingClientRect();
          const leftRect = leftElement.getBoundingClientRect();
          const rightRect = rightElement.getBoundingClientRect();
          
          lines.value.push({
            x1: leftRect.right - containerRect.left,
            y1: leftRect.top - containerRect.top + leftRect.height / 2,
            x2: rightRect.left - containerRect.left,
            y2: rightRect.top - containerRect.top + rightRect.height / 2,
            color: '#67C23A',
            leftId: leftOption.optionId,
            rightId: rightOption.optionId
          });
        }
      }
    }
  };
  
  // 监听 showAnswer 属性变化
  watch(() => props.showAnswer, (newVal) => {
    if (newVal) {
      lines.value = []; // 清除现有的线
      showAllAnswers();
    }
  });
  
  // 添加 updateLines 方法
  const updateLines = () => {
    nextTick(() => {
      const newLines = [];
      lines.value.forEach(line => {
        const leftElement = document.getElementById('left-' + line.leftId);
        const rightElement = document.getElementById('right-' + line.rightId);
        
        if (leftElement && rightElement) {
          const container = leftElement.closest('.matching-container');
          const containerRect = container.getBoundingClientRect();
          const leftRect = leftElement.getBoundingClientRect();
          const rightRect = rightElement.getBoundingClientRect();
          
          newLines.push({
            ...line,
            x1: leftRect.right - containerRect.left,
            y1: leftRect.top - containerRect.top + leftRect.height / 2,
            x2: rightRect.left - containerRect.left,
            y2: rightRect.top - containerRect.top + rightRect.height / 2
          });
        }
      });
      lines.value = newLines;
    });
  };
  </script>
  
  <style scoped>
  .matching-container {
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 20px;
    min-height: 200px;
  }
  
  .matching-left, .matching-right {
    width: 45%;
    position: relative;
    z-index: 1;
  }
  
  .draggable-item, .drop-target {
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    background: #FFF;
    cursor: pointer;
    transition: all 0.3s;
  }
  
  .draggable-item:hover, .drop-target:hover {
    border-color: #409EFF;
  }
  
  .selected {
    background: #ECF5FF;
    border-color: #409EFF;
  }
  
  .lines-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
  }
  </style>