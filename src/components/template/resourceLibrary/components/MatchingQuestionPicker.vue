<template>
    <div class="matching-picker">
      <div class="matching-columns">
        <!-- 左侧选项编辑区 -->
        <div class="column left-column">
          <div class="column-header">
            <h4>左侧选项</h4>
            <t-button theme="primary" variant="text" @click="addOption('left')" :disabled="disabled">
              <template #icon><t-icon name="add" /></template>
              添加选项
            </t-button>
          </div>
          <div class="options-list">
            <div v-for="(option, index) in leftOptions" 
                 :key="'left-' + index"
                 :data-id="'left-' + option.id"
                 class="option-item">
              <div class="option-content">
                <WangEditor
                  :text-content="option.content"
                  :height-props="100"
                  :need-t="false"
                  @update-text-content="(content) => handleEditorChange(content, 'left', index)"
                />
              </div>
              <div class="option-actions">
                <t-button theme="danger" variant="text" @click="removeOption('left', index)" :disabled="disabled">
                  <template #icon><t-icon name="delete" /></template>
                </t-button>
              </div>
              <div class="connection-point right">
                <t-button theme="primary" shape="circle" size="small" @click="handleSelect('left', index)" :disabled="disabled">
                  <template #icon><t-icon name="link" /></template>
                </t-button>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 右侧选项编辑区 -->
        <div class="column right-column">
          <div class="column-header">
            <h4>右侧选项</h4>
            <t-button theme="primary" variant="text" @click="addOption('right')" :disabled="disabled">
              <template #icon><t-icon name="add" /></template>
              添加选项
            </t-button>
          </div>
          <div class="options-list">
            <div v-for="(option, index) in rightOptions" 
                 :key="'right-' + index"
                 :data-id="'right-' + option.id"
                 class="option-item">
              <div class="connection-point left">
                <t-button theme="primary" shape="circle" size="small" @click="handleSelect('right', index)" :disabled="disabled">
                  <template #icon><t-icon name="link" /></template>
                </t-button>
              </div>
              <div class="option-content">
                <WangEditor
                  :need-t="false"
                  :text-content="option.content"
                  :height-props="100"
                  @update-text-content="(content) => handleEditorChange(content, 'right', index)"
                />
              </div>
              <div class="option-actions">
                <t-button theme="danger" variant="text" @click="removeOption('right', index)" :disabled="disabled">
                  <template #icon><t-icon name="delete" /></template>
                </t-button>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <!-- 连线展示区域 -->
      <svg class="matching-lines" ref="svgContainer">
        <g v-for="(line, index) in matchingLines" 
            :key="index"
            @click="removeLine(index)"
            class="line-group"
            :class="{ 'readonly': disabled }">
          <line 
            :x1="line.x1" 
            :y1="line.y1" 
            :x2="line.x2" 
            :y2="line.y2"
            stroke="var(--td-brand-color)" 
            stroke-width="2"/>
          <line 
            :x1="line.x1" 
            :y1="line.y1" 
            :x2="line.x2" 
            :y2="line.y2"
            stroke="transparent" 
            stroke-width="10"
            class="line-hitbox"/>
        </g>
      </svg>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted, watch, nextTick, onUnmounted } from 'vue';
  import WangEditor from '@/components/wangEditor/index.vue';
  
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({
        leftOptions: [],
        rightOptions: [],
        matchingPairs: []
      })
    },
    disabled: {
      type: Boolean,
      default: false
    }
  });
  
  const emit = defineEmits(['update:modelValue']);
  
  // 初始化数据
  const leftOptions = ref(props.modelValue.leftOptions.length ? 
    props.modelValue.leftOptions : 
    [{ content: '', selected: false, id: 0 }]
  );
  
  const rightOptions = ref(props.modelValue.rightOptions.length ? 
    props.modelValue.rightOptions : 
    [{ content: '', selected: false, id: 0 }]
  );
  
  const matchingLines = ref([]);
  const selectedLeft = ref(null);
  const selectedRight = ref(null);
  
  // 添加本地响应式数据
  const matchingPairs = ref(props.modelValue.matchingPairs || []);
  
  // 添加选项
  const addOption = (side) => {
    const options = side === 'left' ? leftOptions : rightOptions;
    const newId = Math.max(...options.value.map(opt => opt.id), -1) + 1;
    options.value.push({
      content: '',
      selected: false,
      id: newId
    });
    updateModelValue();
  };
  
  // 删除选项
  const removeOption = (side, index) => {
    const options = side === 'left' ? leftOptions : rightOptions;
    const removedId = options.value[index].id;
    options.value.splice(index, 1);
    
    // 删除相关的连线
    matchingPairs.value = matchingPairs.value.filter(pair => {
      if (side === 'left') {
        return pair.left.id !== removedId;
      } else {
        return pair.right.id !== removedId;
      }
    });
    
    updateModelValue();
    updateLines();
  };
  
  // 处理选项选中
  const handleSelect = (side, index) => {
    const option = side === 'left' ? leftOptions.value[index] : rightOptions.value[index];
    
    if (side === 'left') {
      // 取消其他左侧选中
      leftOptions.value.forEach((opt, i) => {
        if (i !== index) opt.selected = false;
      });
      option.selected = !option.selected;
      selectedLeft.value = option.selected ? option : null;
    } else {
      // 取消其他右侧选中
      rightOptions.value.forEach((opt, i) => {
        if (i !== index) opt.selected = false;
      });
      option.selected = !option.selected;
      selectedRight.value = option.selected ? option : null;
    }
  
    // 如果两边都选中了，创建连线
    if (selectedLeft.value && selectedRight.value) {
      createMatchingPair();
    }
  };
  
  // 创建匹配对
  const createMatchingPair = () => {
    // 确保两边都有选中的选项
    if (!selectedLeft.value || !selectedRight.value) {
      return;
    }

    // 检查是否已存在相同的配对
    const existingPair = matchingPairs.value.find(
      pair => pair.left.id === selectedLeft.value.id && 
             pair.right.id === selectedRight.value.id
    );

    if (!existingPair) {
      const newPair = {
        left: {
          id: selectedLeft.value.id,
          content: selectedLeft.value.content
        },
        right: {
          id: selectedRight.value.id,
          content: selectedRight.value.content
        }
      };
      
      matchingPairs.value.push(newPair);
      updateModelValue();
      updateLines();
    }

    // 重置选中状态
    if (selectedLeft.value) {
      selectedLeft.value.selected = false;
    }
    if (selectedRight.value) {
      selectedRight.value.selected = false;
    }
    selectedLeft.value = null;
    selectedRight.value = null;
  };
  

  // 更新连线
  const updateLines = () => {
    // 使用 setTimeout 确保在下一个渲染周期执行
    setTimeout(() => {

      
      if (!matchingPairs.value.length) {
        matchingLines.value = [];
        return;
      }

      // 确保 DOM 已经完全渲染
      const container = document.querySelector('.matching-picker');
      if (!container) {

        return;
      }
      
      const containerRect = container.getBoundingClientRect();
      
      // 使用强制的方式获取所有必要元素
      matchingLines.value = matchingPairs.value.map(pair => {
        try {
          const leftEl = document.querySelector(`[data-id="left-${pair.left.id}"]`);
          const rightEl = document.querySelector(`[data-id="right-${pair.right.id}"]`);
          
          if (!leftEl || !rightEl) {
            console.warn(`找不到连线元素: left-${pair.left.id} 或 right-${pair.right.id}`);
            return null;
          }
          
          const leftRect = leftEl.getBoundingClientRect();
          const rightRect = rightEl.getBoundingClientRect();
          
          return {
            x1: leftRect.right - containerRect.left,
            y1: leftRect.top - containerRect.top + leftRect.height / 2,
            x2: rightRect.left - containerRect.left,
            y2: rightRect.top - containerRect.top + rightRect.height / 2
          };
        } catch (err) {
          console.error('计算连线位置时出错:', err);
          return null;
        }
      }).filter(line => line !== null);
    }, 100); // 添加一个短暂的延迟
  };
  
  // 更新父组件的值
  const updateModelValue = () => {
    emit('update:modelValue', {
      leftOptions: leftOptions.value,
      rightOptions: rightOptions.value,
      matchingPairs: matchingPairs.value
    });
  };
  
  // 监听选项变化，更新连线
  watch([leftOptions, rightOptions, matchingPairs], () => {
    updateLines();
  }, { deep: true }); // 添加深度监听
  
  // 添加对 modelValue 的监听，确保当父组件数据更新时能刷新连线
  watch(() => props.modelValue, (newVal) => {
    if (newVal) {
      leftOptions.value = newVal.leftOptions && newVal.leftOptions.length ? 
        newVal.leftOptions : 
        [{ content: '', selected: false, id: 0 }];
      
      rightOptions.value = newVal.rightOptions && newVal.rightOptions.length ? 
        newVal.rightOptions : 
        [{ content: '', selected: false, id: 0 }];
      
      matchingPairs.value = newVal.matchingPairs || [];
      
      // 使用延时确保所有DOM元素都已完全渲染
      setTimeout(() => {
        updateLines();
      }, 300);
    }
  }, { deep: true, immediate: true }); // 添加即时执行选项
  
  // 组件挂载后初始化连线
  onMounted(() => {
    // 创建一个重复执行updateLines的函数来确保连线最终会渲染
    const ensureLines = () => {
      updateLines();
      
      // 500ms后再次检查并更新连线
      setTimeout(() => {
        if (matchingPairs.value.length > 0 && matchingLines.value.length === 0) {

          updateLines();
        }
      }, 500);
    };

    // 设置ResizeObserver
    const resizeObserver = new ResizeObserver(() => {
      updateLines();
    });
    
    const container = document.querySelector('.matching-picker');
    if (container) {
      resizeObserver.observe(container);
    }
    
    // 初始连线渲染尝试多次
    ensureLines();
    
    // 添加滚动事件监听
    window.addEventListener('scroll', updateLines, { passive: true });
    
    // 在组件卸载时清理
    onUnmounted(() => {
      window.removeEventListener('scroll', updateLines);
      if (container) {
        resizeObserver.unobserve(container);
      }
    });
  });
  
  // 处理编辑器内容更新
  const handleEditorChange = (content, side, index) => {
    const options = side === 'left' ? leftOptions : rightOptions;
    options.value[index].content = content;
    updateModelValue();
  };
  
  // 添加删除连线的方法
  const removeLine = (index) => {
    if (props.disabled) return; // 禁用状态下不允许删除连线
    
    // 删除匹配对
    matchingPairs.value.splice(index, 1);
    updateModelValue();
    updateLines();
  };
  </script>
  
  <style  scoped>
  .matching-picker {
    position: relative;
    padding: 20px;
    
    .matching-columns {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      
      .column {
        &.left-column,
        &.right-column {
          width: 40%;
        }

        &.middle-column {
          width: 20%;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          padding-top: 50px;
        }
        
        .column-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          
          h4 {
            margin: 0;
            color: var(--td-text-color-primary);
          }
        }
        
        .options-list {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }
      }
    }
    
    .option-item {
      position: relative;
      border: 1px solid var(--td-component-border);
      border-radius: var(--td-radius-default);
      padding: 10px;
      background: var(--td-bg-color-container);
      
      .option-content {
        margin-bottom: 10px;
      }
      
      .option-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 8px;
      }

      .connection-point {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);

        &.right {
          right: -16px;
        }

        &.left {
          left: -16px;
        }
      }
    }
    
    .matching-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      
      .line-group {
        cursor: pointer;
        pointer-events: all;
        
        &:hover line:first-child {
          stroke: var(--td-error-color);
          stroke-dasharray: 5;
        }
        
        &.readonly {
          pointer-events: none;
          cursor: default;
        }
        
        .line-hitbox {
          pointer-events: all;
        }
      }
    }
  }
  </style>