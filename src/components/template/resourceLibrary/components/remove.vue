<template>
  <t-dialog
    :visible="visible"
    header="确认删除"
    @update:visible="emit('update:visible', $event)"
    @close="handleClose"
  >
    <p>确定要删除选中的{{ getItemTypeText() }}吗？</p>
    <template #footer>
      <t-button theme="default" @click="handleClose">取消</t-button>
      <t-button theme="danger" @click="handleConfirm">确定</t-button>
    </template>
  </t-dialog>
</template>

<script setup>
const props = defineProps({
  visible: Boolean,
  form: {
    type: Object,
    default: () => ({
      questionIds: [], // 题目ID数组
      resourceIds: [], // 资源ID数组
      type: '', // 删除类型：'question' 或 'resource'
    }),
  },
})

const emit = defineEmits(['close', 'submit', 'update:visible'])

function getItemTypeText() {
  const { questionIds, resourceIds } = props.form
  const questionCount = questionIds?.length || 0
  const resourceCount = resourceIds?.length || 0

  if (questionCount > 0 && resourceCount > 0) {
    return `${questionCount}个题目和${resourceCount}个资源`
  } else if (questionCount > 0) {
    return `${questionCount}个题目`
  } else if (resourceCount > 0) {
    return `${resourceCount}个资源`
  } else {
    return '选中项'
  }
}

function handleClose() {
  emit('close')
}

function handleConfirm() {
  emit('submit', props.form)
}
</script>
