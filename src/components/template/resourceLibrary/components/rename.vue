<template>
  <t-dialog
    :visible="visible"
    :header="header"
    @update:visible="emit('update:visible', $event)"
    @close="handleClose"
  >
    <t-form :data="form">
      <t-form-item label="名称">
        <t-input v-model="form.name" :maxlength="props.max" />
      </t-form-item>
    </t-form>
    <template #footer>
      <t-button @click="handleClose">取消</t-button>
      <t-button theme="primary" @click="handleConfirm">确定</t-button>
    </template>
  </t-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  visible: Boolean,
  item: Object,
  header: {
    type: String,
    default: '重命名'
  },
  max: {
    type: Number,
    default: 50
  }
})


const emit = defineEmits(['close', 'confirm', 'update:visible'])

const form = ref(props.item);


function handleClose() {
  emit('close')
}

function handleConfirm() {
  emit('confirm', form.value)
}
</script>

<style lang="less" scoped>
.rename-content {
    padding: 30px;
    display: flex;
    align-items: center;
   .label{
    margin-right:5px;
   }
}
</style>