<template>
  <t-dialog
    :visible="visible"
    header="确认还原"
    @update:visible="emit('update:visible', $event)"
    @close="handleClose"
  >
    <p>确定要还原选中的{{ getItemCount() }}个题目吗？</p>
    <template #footer>
      <t-button theme="default" @click="handleClose">取消</t-button>
      <t-button theme="primary" @click="handleConfirm">确定</t-button>
    </template>
  </t-dialog>
</template>

<script setup>
const props = defineProps({
  visible: Boolean,
  questionIds: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['close', 'submit', 'update:visible'])

function getItemCount() {
  return props.questionIds.length
}

function handleClose() {
  emit('close')
}

function handleConfirm() {
  emit('submit', props.questionIds)
}
</script>
