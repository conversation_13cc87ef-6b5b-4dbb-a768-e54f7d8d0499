t-button
<template>
  <!-- 添加音频对话框 -->
  <t-dialog v-model:visible="innerVisible" @close="handleClose" :header="t('insert.library.addAudio')" width="500px">
    <t-form ref="audioFormRef" :data="audioForm" label-width="100px">
      <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleAudioChange" :files="audioList"
        :show-upload-progress="false" :before-upload="() => false">
        <t-button theme="primary">{{
    t('insert.library.selectAudio')
  }}</t-button>
        <template #tips>
          <div class="upload-tips">{{ t('insert.library.audioTips') }}</div>
        </template>
      </t-upload>

      <!-- 音频列表展示 -->
      <div class="audio-list" style="max-height: 200px">
        <t-card v-for="(audio, index) in audioList" :key="index" class="audio-item" hover-shadow>
          <div class="audio-item-content">
            <div class="audio-info">
              <t-icon name="sound" />
              <span class="audio-name">{{ audio.name }}</span>
            </div>
            <div class="audio-controls">
              <audio :src="audio.url" controls preload="metadata" class="audio-player"></audio>
              <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeAudio(index)">
                {{ t('insert.library.remove') }}
              </t-button>
            </div>
            <!-- 上传进度条 -->
            <t-progress v-if="audio.uploadProgress > 0 && audio.uploadProgress < 100" :percentage="audio.uploadProgress"
              status="success" />
          </div>
        </t-card>
      </div>
    </t-form>
    <template #footer>
      <t-space>
        <t-button theme="primary" :disabled="!isAllUploaded(audioList)" @click="submitAudioForm">
          {{ t('insert.library.confirm') }}
        </t-button>
        <t-button @click="handleClose">{{
    t('insert.library.cancel')
  }}</t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup>
import { OssService } from '@/utils/aliOss'
import { MessagePlugin } from 'tdesign-vue-next'
import { addUserResource } from '@/api/resource/userResource'

const props = defineProps({
  modelValue: Boolean,
  parentId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const audioList = ref([])
const innerVisible = ref(false)
const audioForm = ref({
  fileName: '',
  fileUrl: '',
  type: '2',
})

watch(
  () => props.modelValue,
  (nv) => {
    innerVisible.value = nv
  },
)

// 关闭弹窗
function handleClose() {
  emit('update:modelValue', false)
}

// 刷新数据
function handleRefresh() {
  emit('refresh')
}

// 修改音频处理函数
async function handleAudioChange(files) {
  for (const file of files) {
    try {
      const isAudio = file.type.startsWith('audio/')
      const isValidSize = file.size <= 50 * 1024 * 1024 // 50MB

      if (!isAudio) {
        MessagePlugin.error(`${file.name} 不是音频文件`)
        continue
      }

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过50MB`)
        continue
      }

      const localUrl = URL.createObjectURL(file)
      const audioFile = {
        name: file.name,
        url: localUrl,
        file,
        uploadProgress: 0,
      }

      audioList.value.push(audioFile)

      const ossUrl = await OssService(file, {
        progressCallback: (progress) => {
          const index = audioList.value.findIndex(
            (item) => item.name === file.name,
          )
          if (index !== -1) {
            audioList.value[index].uploadProgress = Math.floor(progress * 100)
          }
        },
        folderId: props.parentId
      })

      const index = audioList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        audioList.value[index].ossUrl = ossUrl.url
        audioList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      const index = audioList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        audioList.value.splice(index, 1)
      }
    }
  }
}

// 保存资源库
async function submitAudioForm() {
  try {
    // 遍历音频列表中的每个文件
    for (const audio of audioList.value) {
      const audioData = {
        fileName: audio.name,
        fileUrl: audio.ossUrl, // 使用OSS URL
        fileType: '2',
        folderId: props.parentId,
      }

      // 为每个音频调用添加资源API
      // await addUserResource(audioData)
    }

    MessagePlugin.success('添加成功')

    // 清理本地预览URL
    audioList.value.forEach((audio) => {
      if (audio.url) {
        URL.revokeObjectURL(audio.url)
      }
    })

    // 清空音频列表
    audioList.value = []
    // 关闭对话框
    handleClose()
    // 刷新数据
    handleRefresh()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}

// 检测是否允许保存
function isAllUploaded(fileList) {
  if (!fileList || fileList.length === 0) {
    return false
  }
  return fileList.every((file) => {
    // 检查是否有ossUrl（上传完成）且进度为100
    return file.ossUrl && (!file.uploadProgress || file.uploadProgress === 100)
  })
}

// 音频相关
function removeAudio(index) {
  // 释放本地预览URL
  if (audioList.value[index].url) {
    URL.revokeObjectURL(audioList.value[index].url)
  }
  audioList.value.splice(index, 1)
}
</script>
