t-button
<template>
  <!-- 添加课件对话框 -->
  <t-dialog :visible="innerVisible" @close="handleClose" :header="t('insert.library.addCourseware')" width="500px">
    <t-form ref="coursewareFormRef" :data="coursewareForm" :rules="coursewareRules" label-width="100px">
      <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleCoursewareChange"
        :files="coursewareList" :show-upload-progress="false" :before-upload="() => false">
        <t-button theme="primary">{{
    t('insert.library.selectCourseware')
  }}</t-button>
        <template #tips>
          <div class="upload-tips">
            {{ t('insert.library.coursewareTips') }}
          </div>
        </template>
      </t-upload>

      <!-- 课件列表展示 -->
      <div class="preview-list" style="max-height: 200px">
        <t-card v-for="(courseware, index) in coursewareList" :key="index" class="courseware-item" hover-shadow>
          <div class="courseware-item-content">
            <div class="courseware-info">
              <t-icon name="file" />
              <span class="courseware-name">{{ courseware.name }}</span>
            </div>
            <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeCourseware(index)">
              {{ t('insert.library.remove') }}
            </t-button>
            <!-- 上传进度条 -->
            <t-progress v-if="courseware.uploadProgress > 0 && courseware.uploadProgress < 100
    " :percentage="courseware.uploadProgress" status="success" />
          </div>
        </t-card>
      </div>
    </t-form>
    <template #footer>
      <t-space>
        <t-button theme="primary" :disabled="!isAllUploaded(coursewareList)" @click="submitCoursewareForm">
          {{ t('insert.library.confirm') }}
        </t-button>
        <t-button @click="handleClose">{{
    t('insert.library.cancel')
  }}</t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup>
import { OssService } from '@/utils/aliOss'
import { MessagePlugin } from 'tdesign-vue-next'
import { addUserResource } from '@/api/resource/userResource'

const props = defineProps({
  modelValue: Boolean,
  parentId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const coursewareList = ref([])
const innerVisible = ref(false)
const coursewareForm = ref({
  fileName: '',
  fileUrl: '',
  type: '8',
})

const coursewareRules = {
  fileUrl: [{ required: true, message: '请上传课件', trigger: 'change' }],
}

watch(
  () => props.modelValue,
  (nv) => {
    innerVisible.value = nv
  },
)

// 关闭弹窗
function handleClose() {
  emit('update:modelValue', false)
}

// 刷新数据
function handleRefresh() {
  emit('refresh')
}

// 修改课件处理函数
async function handleCoursewareChange(files) {
  for (const file of files) {
    try {
      const isValidSize = file.size <= 100 * 1024 * 1024 // 100MB

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过100MB`)
        continue
      }

      const coursewareFile = {
        name: file.name,
        file,
        uploadProgress: 0,
      }

      coursewareList.value.push(coursewareFile)

      const ossUrl = await OssService(file, {
        progressCallback:
          (progress) => {
            const index = coursewareList.value.findIndex(
              (item) => item.name === file.name,
            )
            if (index !== -1) {
              coursewareList.value[index].uploadProgress = Math.floor(
                progress * 100,
              )
            }
          }, folderId: props.parentId
      })

      const index = coursewareList.value.findIndex(
        (item) => item.name === file.name,
      )
      if (index !== -1) {
        coursewareList.value[index].ossUrl = ossUrl.url
        coursewareList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      const index = coursewareList.value.findIndex(
        (item) => item.name === file.name,
      )
      if (index !== -1) {
        coursewareList.value.splice(index, 1)
      }
    }
  }
}

// 提交课件表单
async function submitCoursewareForm() {
  try {
    // 遍历课件列表中的每个文件
    for (const courseware of coursewareList.value) {
      const coursewareData = {
        fileName: courseware.name,
        fileUrl: courseware.ossUrl,
        fileType: '8', // 课件类型
        folderId: props.parentId,
      }

      // 为每个课件调用添加资源API
      // await addUserResource(coursewareData)
    }

    MessagePlugin.success('添加成功')
    // 清空课件列表
    coursewareList.value = []
    // 关闭对话框
    handleClose()
    // 刷新数据
    handleRefresh()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}

// 检测是否允许保存
function isAllUploaded(fileList) {
  if (!fileList || fileList.length === 0) {
    return false
  }
  return fileList.every((file) => {
    // 检查是否有ossUrl（上传完成）且进度为100
    return file.ossUrl && (!file.uploadProgress || file.uploadProgress === 100)
  })
}

// 删除视频
function removeCourseware(index) {
  // 释放本地预览URL
  if (coursewareList.value[index].url) {
    URL.revokeObjectURL(coursewareList.value[index].url)
  }
  coursewareList.value.splice(index, 1)
}
</script>
