<template>
  <!-- 添加图片对话框 -->
  <t-dialog header="添加图片" :visible="innerVisible" width="1200px" @close="handleClose">
    <t-form ref="imageFormRef" :data="imageForm" label-width="100px">
      <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleImageChange" :files="imageList"
        :show-upload-progress="false" :before-upload="() => false" accept="image/*">
        <t-button theme="primary">选择图片</t-button>
        <template #tips>
          <div class="upload-tips">支持 jpg/png/gif 等图片格式</div>
        </template>
      </t-upload>

      <!-- 图片列表预览 -->
      <div class="preview-list">
        <t-card v-for="(image, index) in imageList" :key="index" class="image-item" hover-shadow>
          <div class="image-item-content">
            <div class="image-info">
              <img t- :src="image.url" class="preview-image" alt="预览图" />
              <div class="image-name">{{ image.name }}</div>
            </div>
            <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeImage(index)">
              删除
            </t-button>
          </div>
          <template v-if="image.uploadProgress > 0 && image.uploadProgress < 100">
            <t-progress theme="info" :percentage="Number(image.uploadProgress)" :label="false" />
          </template>
        </t-card>
      </div>
    </t-form>
    <template #footer>
      <t-space>
        <t-button theme="primary" :disabled="!isAllUploaded(imageList)" @click="submitImageForm">
          确定
        </t-button>
        <t-button @click="handleClose">取消</t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup>
import { MessagePlugin } from 'tdesign-vue-next'
import  { checkResourceIsExists} from '@/api/resource/userResource'
import { defaultOptPreChekck } from '@/utils/file'
import { OssService } from '@/utils/aliOss'

const props = defineProps({
  modelValue: Boolean,
  parentId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const imageList = ref([])
const innerVisible = ref(false)
const imageForm = ref({
  fileName: '',
  fileUrl: '',
  type: '1',
})

watch(
  () => props.modelValue,
  (nv) => {
    innerVisible.value = nv
  },
)

// 关闭弹窗
function handleClose() {
  emit('update:modelValue', false)
}

// 刷新数据
function handleRefresh() {
  emit('refresh')
}

// 图片相关处理函数
async function handleImageChange(files) {
  // files是一个数组,包含新添加的文件
  for (const file of files) {
    try {
      // 验证文件类型和大小
      const isImage = file.type.startsWith('image/')
      const isValidSize = file.size <= 5 * 1024 * 1024 // 5MB

      if (!isImage) {
        MessagePlugin.error(`${file.name} 不是图片文件`)
        continue
      }

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过5MB`)
        continue
      }

      // 创建本地预览URL
      const localUrl = URL.createObjectURL(file)

      // 创建新的图片文件对象
      const imageFile = {
        name: file.name,
        url: localUrl,
        file,
        uploadProgress: 0,
      }

      // 添加到图片列表
      imageList.value.push(imageFile)

      const res = await defaultOptPreChekck(file, {
       confirmBtn: '确认上传', cancelBtn: '取消上传'
      })
      // 确认返回的是false, 取消返回的是true，兼容修改后的逻辑
      if (res.allow) {
         // 从列表中移除失败的文件
      const index = imageList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        imageList.value.splice(index, 1)
      }
      continue
      }
      // 上传到OSS并获取URL
      const ossUrl = await OssService(file, {
        progressCallback:
          (progress) => {
            // 更新特定文件的上传进度
            const index = imageList.value.findIndex(
              (item) => item.name === file.name,
            )
            if (index !== -1) {
              imageList.value[index].uploadProgress = Math.floor(progress * 100)
            }
          }, 
          folderId: props.parentId,
          hash: res.hash,
      })

      // 更新图片文件的URL为OSS URL
      const index = imageList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        imageList.value[index].ossUrl = ossUrl.url
        imageList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      // 从列表中移除失败的文件
      const index = imageList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        imageList.value.splice(index, 1)
      }
    }
  }
}

// 保存资源库
async function submitImageForm() {
  try {
    // 遍历图片列表中的每个文件
    for (const image of imageList.value) {
      const imageData = {
        fileName: image.name,
        fileUrl: image.ossUrl,
        fileType: '1', // 图片类型
        folderId: props.parentId,
      }

      // 为每个图片调用添加资源API
      // await addUserResource(imageData)
    }

    MessagePlugin.success('添加成功')
    // 清空图片列表
    imageList.value = []
    // 关闭对话框
    handleClose()
    // 重新获取列表
    handleRefresh()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}

// 检测是否允许保存
function isAllUploaded(fileList) {
  if (!fileList || fileList.length === 0) {
    return false
  }
  return fileList.every((file) => {
    // 检查是否有ossUrl（上传完成）且进度为100
    return file.ossUrl && (!file.uploadProgress || file.uploadProgress === 100)
  })
}

// 删除图片
function removeImage(index) {
  // 释放本地预览URL
  if (imageList.value[index].url) {
    URL.revokeObjectURL(imageList.value[index].url)
  }
  imageList.value.splice(index, 1)
}
</script>
<style lang="less" scoped>
.image-name {
  text-align: center;
}
</style>
