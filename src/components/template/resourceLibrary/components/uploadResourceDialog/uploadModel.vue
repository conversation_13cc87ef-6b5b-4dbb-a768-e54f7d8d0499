<template>
  <!-- 添加虚拟仿真/AR/VR/3D模型对话框 -->
  <t-dialog :visible="innerVisible" @close="handleClose" :header="headerTitle(fileType)" width="500px">
    <t-form ref="modelFormRef" :data="modelForm" label-width="100px">
      <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleModelChange" :files="modelList"
        :show-upload-progress="false" :before-upload="() => false">
        <t-button theme="primary">{{ t('insert.library.upload') }}</t-button>
        <template #tips>
          <div class="upload-tips">{{ t('insert.library.modelTips') }}</div>
        </template>
      </t-upload>

      <!-- 模型列表展示 -->
      <div class="preview-list" style="max-height: 200px">
        <t-card v-for="(model, index) in modelList" :key="index" class="model-item" hover-shadow>
          <div class="model-item-content">
            <div class="model-info">
              <t-icon name="cube" />
              <span class="model-name">{{ model.name }}</span>
            </div>
            <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeModel(index)">
              {{ t('insert.library.remove') }}
            </t-button>
            <!-- 上传进度条 -->
            <t-progress v-if="model.uploadProgress > 0 && model.uploadProgress < 100" :percentage="model.uploadProgress"
              status="success" />
          </div>
        </t-card>
      </div>
    </t-form>
    <template #footer>
      <t-space>
        <t-button theme="primary" :disabled="!isAllUploaded(modelList)" @click="submitModelForm">
          {{ t('insert.library.confirm') }}
        </t-button>
        <t-button @click="handleClose">{{
    t('insert.library.cancel')
  }}</t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup>
import { OssService } from '@/utils/aliOss'
import { MessagePlugin } from 'tdesign-vue-next'
import { addUserResource } from '@/api/resource/userResource'

const props = defineProps({
  modelValue: Boolean,
  fileType: {
    type: String,
    default: '4',
  },
  parentId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const modelList = ref([])
const innerVisible = ref(false)
const modelForm = ref({
  fileName: '',
  fileUrl: '',
  type: '4',
})
watch(
  () => props.modelValue,
  (nv) => {
    innerVisible.value = nv
    if (nv) {
      modelForm.value.type = props.fileType
    }
  },
)

// 关闭弹窗
function handleClose() {
  emit('update:modelValue', false)
}

// 刷新数据
function handleRefresh() {
  emit('refresh')
}

// 标题初始化
function headerTitle(type) {
  switch (type) {
    case '4':
      return t('insert.library.simulation')
    case '5':
      return t('insert.library.avandvr')
    case '6':
      return t('insert.library.threeD')
  }
}

// 修改模型文件处理函数
async function handleModelChange(files) {
  for (const file of files) {
    try {
      const isValidSize = file.size <= 1024 * 1024 * 1024 // 1GB

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过1GB`)
        continue
      }

      const modelFile = {
        name: file.name,
        file,
        uploadProgress: 0,
      }

      modelList.value.push(modelFile)

      const ossUrl = await OssService(file, {
        progressCallback: (progress) => {
          const index = modelList.value.findIndex(
            (item) => item.name === file.name,
          )
          if (index !== -1) {
            modelList.value[index].uploadProgress = Math.floor(progress * 100)
          }
        }, folderId: props.parentId
      })

      const index = modelList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        modelList.value[index].ossUrl = ossUrl.url
        modelList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      const index = modelList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        modelList.value.splice(index, 1)
      }
    }
  }
}

async function submitModelForm() {
  try {
    // 遍历模型列表中的每个文件
    for (const model of modelList.value) {
      const modelData = {
        fileName: model.name,
        fileUrl: model.ossUrl, // 使用OSS URL
        fileType: modelForm.value.type,
        folderId: props.parentId,
      }

      // 为每个模型调用添加资源API
      // await addUserResource(modelData)
    }

    MessagePlugin.success('添加成功')
    // 清空模型列表
    modelList.value = []
    // 关闭对话框
    handleClose()
    // 刷新数据
    handleRefresh()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}

// 检测是否允许保存
function isAllUploaded(fileList) {
  if (!fileList || fileList.length === 0) {
    return false
  }
  return fileList.every((file) => {
    // 检查是否有ossUrl（上传完成）且进度为100
    return file.ossUrl && (!file.uploadProgress || file.uploadProgress === 100)
  })
}

// 删除
function removeModel(index) {
  // 释放本地预览URL
  if (modelList.value[index].url) {
    URL.revokeObjectURL(modelList.value[index].url)
  }
  modelList.value.splice(index, 1)
}
</script>
