t-button
<template>
  <!-- 添加视频对话框 -->
  <t-dialog :visible="innerVisible" :header="t('insert.library.addVideo')" width="500px" @close="handleClose">
    <t-form ref="videoFormRef" :data="videoForm" label-width="100px">
      <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleVideoChange" :files="videoList"
        :show-upload-progress="false" :before-upload="() => false">
        <t-button theme="primary">{{
    t('insert.library.selectVideo')
  }}</t-button>
        <template #tips>
          <div class="upload-tips">{{ t('insert.library.videoTips') }}</div>
        </template>
      </t-upload>

      <!-- 视频列表展示 -->
      <div class="video-list" style="max-height: 200px">
        <t-card v-for="(video, index) in videoList" :key="index" class="video-item" hover-shadow>
          <div class="video-item-content">
            <div class="video-info">
              <t-icon name="video" />
              <span class="video-name">{{ video.name }}</span>
            </div>
            <div class="video-controls">
              <video :src="video.url" controls preload="metadata" class="video-player"></video>
              <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeVideo(index)">
                {{ t('insert.library.remove') }}
              </t-button>
            </div>
            <!-- 上传进度条 -->
            <t-progress v-if="video.uploadProgress > 0 && video.uploadProgress < 100" :percentage="video.uploadProgress"
              status="success" />
          </div>
        </t-card>
      </div>
    </t-form>
    <template #footer>
      <t-space>
        <t-button theme="primary" :disabled="!isAllUploaded(videoList)" @click="submitVideoForm">
          {{ t('insert.library.confirm') }}
        </t-button>
        <t-button @click="handleClose">{{
    t('insert.library.cancel')
  }}</t-button>
      </t-space>
    </template>
  </t-dialog>
</template>

<script setup>
import { OssService } from '@/utils/aliOss'
import { MessagePlugin } from 'tdesign-vue-next'
import { addUserResource } from '@/api/resource/userResource'

const props = defineProps({
  modelValue: Boolean,
  parentId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const videoList = ref([])
const innerVisible = ref(false)
const videoForm = ref({
  fileName: '',
  fileUrl: '',
  type: '3',
})

watch(
  () => props.modelValue,
  (nv) => {
    innerVisible.value = nv
  },
)

// 关闭弹窗
function handleClose() {
  emit('update:modelValue', false)
}

// 刷新数据
function handleRefresh() {
  emit('refresh')
}

// 修改视频处理函数
async function handleVideoChange(files) {
  // files现在是一个数组
  for (const file of files) {
    try {
      // 验证文件类型和大小
      const isVideo = file.type.startsWith('video/')
      const isValidSize = file.size <= 500 * 1024 * 1024 // 500MB

      if (!isVideo) {
        MessagePlugin.error(`${file.name} 不是视频文件`)
        continue
      }

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过500MB`)
        continue
      }

      // 创建本地预览URL
      const localUrl = URL.createObjectURL(file)

      // 创建新的视频文件对象
      const videoFile = {
        name: file.name,
        url: localUrl,
        file,
        uploadProgress: 0,
      }

      // 添加到视频列表
      videoList.value.push(videoFile)

      // 上传到OSS并获取URL
      const ossUrl = await OssService(file, {
        progressCallback: (progress) => {
          const index = videoList.value.findIndex(
            (item) => item.name === file.name,
          )
          if (index !== -1) {
            videoList.value[index].uploadProgress = Math.floor(progress * 100)
          }
        }, folderId: props.parentId
      })

      // 更新视频文件的URL为OSS URL
      const index = videoList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        videoList.value[index].ossUrl = ossUrl.url
        videoList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      const index = videoList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        videoList.value.splice(index, 1)
      }
    }
  }
}

// 保存资源库
async function submitVideoForm() {
  try {
    // 遍历视频列表中的每个文件
    for (const video of videoList.value) {
      const videoData = {
        fileName: video.name,
        fileUrl: video.ossUrl, // 使用OSS URL
        fileType: '3',
        folderId: props.parentId,
      }

      // 为每个视频调用添加资源API
      // await addUserResource(videoData)
    }

    MessagePlugin.success('添加成功')

    // 清理本地预览URL
    videoList.value.forEach((video) => {
      if (video.url) {
        URL.revokeObjectURL(video.url)
      }
    })

    // 清空视频列表
    videoList.value = []
    // 关闭对话框
    handleClose()
    // 刷新数据
    handleRefresh()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}
// 检测是否允许保存
function isAllUploaded(fileList) {
  if (!fileList || fileList.length === 0) {
    return false
  }
  return fileList.every((file) => {
    // 检查是否有ossUrl（上传完成）且进度为100
    return file.ossUrl && (!file.uploadProgress || file.uploadProgress === 100)
  })
}

// 删除视频
function removeVideo(index) {
  // 释放本地预览URL
  if (videoList.value[index].url) {
    URL.revokeObjectURL(videoList.value[index].url)
  }
  videoList.value.splice(index, 1)
}
</script>
