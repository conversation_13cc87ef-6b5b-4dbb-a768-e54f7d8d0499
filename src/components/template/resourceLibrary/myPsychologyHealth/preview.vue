<template>
  <div style="width: 100%;">
    <t-form
      ref="formRef"
      :data="form"
      :labelWidth="100"
      :rules="rules"
    >

      <t-row :gutter="8">
        <t-col :span="4">
          <t-form-item
            name="scaleName"
            label="量表名称"
          >
            <t-input
              v-model="form.scaleName"
              placeholder="请输入量表名称"
              style="width: 250px;"
              :disabled="true"
            ></t-input>
          </t-form-item>
          <t-form-item
            label="量表题干"
            name="scanQuestion"
          >
            <t-textarea
              v-model="form.scanQuestion"
              placeholder="请输入量表题干"
              :disabled="true"
              :autosize="{ minRows: 3 }"
              style="width: 250px;"
            ></t-textarea>
          </t-form-item>
          <t-form-item
            label="测评方法"
            name="evaluationMethod"
          >
            <t-textarea
              v-model="form.evaluationMethod"
              placeholder="请输入测评方法"
              :disabled="true"
              :autosize="{ minRows:3 }"
              style="width: 250px;"
            ></t-textarea>
          </t-form-item>
          <t-form-item
            label="评价参考"
            name="evaluateReference"
          >
            <t-textarea
              v-model="form.evaluateReference"
              :disabled="true"
              placeholder="请输入参考评价"
              :autosize="{ minRows:5 }"
              style="width: 250px;"
            ></t-textarea>
          </t-form-item>
          <t-form-item
            label="量表类型"
            name="scaleType"
          >
            <t-radio-group v-model="form.scaleType" :disabled="true">
              <t-radio :value='1'>顺序作答</t-radio>
              <t-radio :value='2'>跳转作答</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item
            label="题目顺序"
            name="questionSort"
            v-if="form.scaleType == 1"
          >
            <t-radio-group
              v-model="form.questionSort"
              @change="changeForm"
              :disabled="true"
            >
              <t-radio :value='1'>单维度量表</t-radio>
              <t-radio :value='2'>多维度量表</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item
            label="题目排序"
            name="showSortType"
            v-if="form.scaleType == 1 && form.questionSort == 2"
            :disabled="props.disabled === 2"
          >
            <t-radio-group v-model="form.showSortType" :disabled="true">
              <t-radio :value='1'>显示维度名称</t-radio>
              <t-radio :value='2'>隐藏维度名称</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item
            label="总成绩"
            name="showSumScore"
            v-if="form.scaleType == 1 && form.questionSort == 2"
          >
            <t-radio-group v-model="form.showSumScore" :disabled="true">
              <t-radio :value=1>显示</t-radio>
              <t-radio :value=2>隐藏</t-radio>
            </t-radio-group>
          </t-form-item>
          <t-form-item
            label="状态"
            name="status"
          >
            <t-radio-group v-model="form.status" :disabled="true">
              <t-radio value=1>启用</t-radio>
              <t-radio value=2>禁用</t-radio>
            </t-radio-group>
          </t-form-item>
        </t-col>
        <t-col
          :span="8"
          class="question-container"
        >
          <t-form-item
            label="量表题目"
            v-model="form.questionId"
          >
            <div class="action-buttons">
              <t-button
                @click="preview"
                size="medium"
                theme="primary"
                shape="round"
              >
                查看
              </t-button>
            </div>
          </t-form-item>
          <div class="scrollable-questions">
            <div v-if="form.questionSort == 1 || form.scaleType == 2">
              <div class="dimension-table">
                <t-table
                  row-key="questionId"
                  :columns="questionColumns"
                  :expand-icon="false"
                  :data="form.moocPsychologyHealthScaleQuestion"
                  :expanded-row-keys="expandedRowKeys"
                  v-model:expanded-row-keys="expandedRowKeys"
                  table-layout="fixed"
                  style="margin-top: 90px;"
                  bordered
                >
                  <template #drag>
                    <drag-icon
                      class="drag-handle"
                      style="cursor: move;"
                    />
                  </template>
                  <template #questionSort="{ row }">
                    <div>
                      <t-input
                        v-model="row.questionSort"
                        placeholder="请输入阅读端序号"
                      ></t-input>

                    </div>
                  </template>
                  <template #questionContent="{ row: question }">
                    <div
                      class="question-content-wrapper"
                      @click.stop="handleWrapperClick(e, question.questionId)"
                    >
                      <t-input
                        v-model="question.questionContent" :disabled="true"
                        placeholder="请输入题目内容"
                      ></t-input>
                    </div>
                  </template>

                  <template #expandedRow="{ row: question }">
                    <t-table
                      rowKey="index"
                      :data="question.moocPsychologyHealthScaleQuestionOption"
                      :columns="optionColumns"
                      bordered
                    >
                      <template #optionContent="{ row: option }">
                        <t-input
                          v-model="option.optionContent"
                          placeholder="请输入选项内容" :disabled="true"
                        ></t-input>
                      </template>

                      <template #score="{ row: option }">
                        <t-input-number
                          v-model="option.score"
                          placeholder="请输入分值"
                          style="width: 200px;"
                          :disabled="true"
                          :step="1"
                        />
                      </template>

                      <template
                        #jumpId="{ row: option }"
                        v-if="form.scaleType === 2"
                      >
                        <div>
                          <div>
                            <t-input :value="getOptionLabel(option.jumpId)" disabled />
                          </div>
                        </div>
                      </template>

                      <template #operation="{ row: option }">
                        <t-button
                          size="medium"
                          variant="text"
                          theme="danger"
                          :disabled="true"
                          @click="removeOption(option)"
                        >
                          删除
                        </t-button>
                      </template>
                    </t-table>
                  </template>
                  <template #operation="{ row: question }">
                    <t-button
                      size="medium"
                      variant="text"
                      theme="danger"
                      :disabled="true"
                      @click="removeSingleQuestion(question)"
                    >
                      删除题目
                    </t-button>
                    <t-button
                      @click="addOption(question)"
                      variant="text"
                      theme="primary" :disabled="true"
                    >
                      添加选项
                    </t-button>
                    <t-button
                      @click="copy(question)"
                      variant="text"
                      theme="primary" :disabled="true"
                    >
                      复制
                    </t-button>
                  </template>
                </t-table>
              </div>
            </div>
            <!-- 多维度 -->
            <div v-else>
              <div
                class="dimension-container"
                v-for="(dimension, dimIndex) in form.moocPsychologyHealthScaleFacet"
                :key="dimIndex"
              >
                <!-- 维度名称 -->
                <div class="dimension-header">
                  <span>{{dimension.facetName}}</span>
                </div>
                <div class="dimension-table">
                  <t-table
                    row-key="questionId"
                    :columns="questionColumns"
                    :expand-icon="false"
                    :data="dimension.moocPsychologyHealthScaleQuestion"
                    table-layout="fixed"
                    style="margin-top: 90px;"
                    bordered
                    :expanded-row-keys="expandedRowKeys"
                    v-model:expanded-row-keys="expandedRowKeys"
                  >
                    <template #drag>
                      <drag-icon
                        class="drag-handle"
                        style="cursor: move;"
                      />
                    </template>

                    <template #questionSort="{ row }">
                      <div>
                        <t-input
                          v-model="row.questionSort"
                          placeholder="请输入题号"
                          :disabled="true"
                        ></t-input>

                      </div>
                    </template>
                    <template #questionContent="{ row: question }">
                      <div
                        class="question-content-wrapper"
                        @click.stop="handleWrapperClick(e, question.questionId)"
                      >
                        <t-input
                          v-model="question.questionContent" :disabled="true"
                          placeholder="请输入题目内容"
                        ></t-input>
                      </div>
                    </template>

                    <template #expandedRow="{ row: question }">
                      <t-table
                        rowKey="index"
                        :data="question.moocPsychologyHealthScaleQuestionOption"
                        :columns="optionColumns"
                        bordered
                      >
                        <template #optionContent="{ row: option }">
                          <t-input
                            v-model="option.optionContent"
                            :disabled="true"
                            placeholder="请输入选项内容"
                          ></t-input>
                        </template>

                        <template #score="{ row: option }">
                          <t-input-number
                            v-model="option.score"
                            placeholder="请输入分值"
                            :disabled="true"
                            style="width: 200px;"
                            :step="1"
                          />
                        </template>

                        <template
                          #jumpId="{ row: option }"
                          v-if="form.scaleType === 2"
                        >
                          <div>
                            <div>
                              <t-input :value="getOptionLabel(option.jumpId)" disabled />
                            </div>
                          </div>
                        </template>

                        <template #operation="{ row: option }">
                          <t-button
                            size="medium"
                            variant="text"
                            theme="danger"
                            @click="removeOption(option)" :disabled="true"
                          >
                            删除
                          </t-button>
                        </template>
                      </t-table>
                    </template>
                    <template #operation="{ row: question }">
                      <t-button
                        size="medium"
                        variant="text"
                        theme="danger"
                        @click="removeQuestion(question)" :disabled="true"
                      >
                        删除题目
                      </t-button>
                      <t-button
                        @click="addOption(question)" :disabled="true"
                        variant="text"
                        theme="primary"
                      >
                        添加选项
                      </t-button>
                      <t-button
                        @click="copy(question,dimIndex)" :disabled="true"
                        variant="text"
                        theme="primary"
                      >
                        复制
                      </t-button>
                    </template>
                  </t-table>
                </div>
              </div>
            </div>
          </div>
        </t-col>
      </t-row>
    </t-form>

    <!-- 预览 -->
    <t-dialog
      :visible="previewIsOpen"
      :on-close-btn-click="canale"
      :footer="false"
      :onClose="canale"
      width="40%"
    >
      <div>
        <!-- 量表名称 -->
        <div>
          <div class="titleForm">
            <span>
              <h2>{{ form.scaleName }}</h2>
            </span>
          </div>
          <div>
            <!-- 单维度 -->
            <div
              class="questions-list"
              v-if="form.scaleType == 1 && form.questionSort == 1"
            >
              <div
                class="question-item"
                v-for="(item, index) in form.moocPsychologyHealthScaleQuestion"
                :key="index"
              >
                <div class="question-text"><b>{{ index + 1 }}.{{ item.questionContent }}</b></div>
                <div class="options">
                  <div
                    v-for="(opt, optIndex) in item.moocPsychologyHealthScaleQuestionOption"
                    :key="optIndex"
                    class="option-item"
                    :class="{ 'selected': opt.optionId === item.chooseOptionId }"
                  >
                    {{ opt.optionContent }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 多维度 -->
            <div
              class="questions-list"
              v-if="form.questionSort == 2"
            >
              <template
                v-for="(facet, facetIndex) in form.moocPsychologyHealthScaleFacet"
                :key="facetIndex"
              >
                <div class="question-item">
                  <!-- 跳转作答模式隐藏维度名称 -->
                  <div
                    v-if="form.scaleType == 1"
                    class="question-text"
                    style="text-align: center; color: rgb(0, 131, 255)"
                  >
                    {{ facet.facetName }}
                  </div>

                  <template
                    v-for="(question, qIndex) in facet.moocPsychologyHealthScaleQuestion"
                    :key="question.questionId"
                  >
                    <div>
                      <!-- 动态选择序号显示方式 -->
                      <div class="question-text">
                        <template v-if="form.showSortType == 1 || form.showSortType == 2">
                          <!-- 模式1：维度内独立序号 -->
                          {{ qIndex + 1 }}.
                        </template>
                        <template v-else>
                          <!-- 模式2：全局连续序号 -->
                          {{ getGlobalIndex(facetIndex, qIndex) }}.
                        </template>
                        {{ question.questionContent }}
                      </div>

                      <!-- 选项列表保持不变 -->
                      <div class="options">
                        <div
                          v-for="(opt, optIndex) in question.moocPsychologyHealthScaleQuestionOption"
                          :key="opt.optionId"
                          class="option-item"
                          :class="{ 'selected': opt.optionId === question.chooseOptionId }"
                        >
                          {{ opt.optionContent }}
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </template>
            </div>
            <!-- 跳转作答 -->
            <div
              class="questions-list"
              v-if="form.scaleType == 2"
            >
              <div
                class="question-item"
                v-if="currentQuestionIndex < form.moocPsychologyHealthScaleQuestion.length"
              >
                <div class="question-text">{{ currentQuestionIndex + 1 }}.{{ currentQuestion.questionContent }}</div>
                <div class="options">
                  <div
                    v-for="(opt, optIndex) in currentQuestion.moocPsychologyHealthScaleQuestionOption"
                    :key="optIndex"
                    class="option-item"
                    :class="{ 'selected': opt.optionId === currentQuestion.chooseOptionId }"
                  >
                    {{ opt.optionContent }}
                  </div>
                </div>
              </div>
              <div>
                <button
                  @click="lastQuestion"
                  style="margin: 0 50px 0 0px;"
                >上一题</button>
                <button @click="nextQuestion">下一题</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>
<script setup >
import { h, ref } from 'vue'
import { MoveIcon as DragIcon } from 'tdesign-icons-vue-next'
import { reactive, watch, toRefs, computed } from 'vue'
import { addHealth, editHealth } from '@/api/resource/psychologyHealth.js'
import { getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance()
//隐藏题目顺序
const scaleTypeValue = ref('')
const previewIsOpen = ref(false)
const currentQuestionIndex = ref(0)
const formRef = ref(null)
const expandedRowKeys = ref([])
//1新增 2修改

//包括维度表单
const form = ref({
  scaleName: '',
  scanQuestion: '',
  evaluationMethod: '',
  evaluateReference: '',
  scaleType: 1,
  questionSort: 1,
  showSortType: 1,
  status: '1',
  moocPsychologyHealthScaleQuestion: [
    {
      questionId: Number(Date.now()),
      oldQuestionId: null,
      questionContent: '',
      sort: null,
      moocPsychologyHealthScaleQuestionOption: [
        {
          sort: null,
          optionId: Number(Date.now()),
          optionContent: '',
          score: null,
          jumpQuestionId: null,
          jumpId: '',
          questionId: Number(Date.now()),
        },
      ],
    },
  ],
})

const rules = {
  scaleName: [
    { required: true, message: '请输入量表名称', trigger: ['blur', 'change'] },
  ],
}

const toggleExpand = (questionId) => {
  const index = expandedRowKeys.value.indexOf(questionId)
  if (index >= 0) {
    // 如果已展开，则收起
    expandedRowKeys.value.splice(index, 1)
  } else {
    // 如果未展开，则展开
    expandedRowKeys.value = [...expandedRowKeys.value, questionId]
  }
}
/**点击题目内容则展开 */
const handleWrapperClick = ( e,questionId) => {
  if (e && typeof e.stopPropagation === 'function') {
    e.stopPropagation()
  }
  toggleExpand(questionId)
}

const getOptionLabel = (item) => {
  console.log(item)
  const selectedQuestion = getAllQuestions().find(
    question => question.questionId === item
  );
  return selectedQuestion ? selectedQuestion.questionContent : '完结';
}

// 计算当前题目
const currentQuestion = computed(() => {
  return form.value.moocPsychologyHealthScaleQuestion[
    currentQuestionIndex.value
    ]
})

// 选择选项
const selectOption = (option) => {
  currentQuestion.value.chooseOptionId = option.optionId
}

// 下一题方法
const nextQuestion = () => {
  if (
    currentQuestionIndex.value <
    form.value.moocPsychologyHealthScaleQuestion.length - 1
  ) {
    currentQuestionIndex.value++
  } else {
    console.log('已经是最后一题了')
  }
}

const getGlobalIndex = (facetIndex, qIndex) => {
  let count = 0
  // 累加前N-1个维度的题目总数
  for (let i = 0; i < facetIndex; i++) {
    count +=
      form.value.moocPsychologyHealthScaleFacet[i]
        .moocPsychologyHealthScaleQuestion.length
  }
  return count + qIndex + 1
}

const props = defineProps({
  addForm: {
    type: Object,
    required: true,
  }
})

defineExpose({ submitForm })
const emit = defineEmits(['update:addForm', 'submit-success'])
watch(
  () => form.value,
  (newValue) => {
    emit('update:addForm', newValue)
  },
  { deep: true }
)

watch(
  () => props.addForm,
  (newValue) => {
    form.value = newValue
  },
  { deep: true }
)
//题目
const columnList = [
  {
    colKey: 'drag',
    width: 20,
    cell: () =>
      h(DragIcon, {
        class: 'drag-handle',
        style: { transition: 'all 0.3s' },
      }),
    align: 'center',
    fixed: 'left',
  },
  {
    colKey: 'index',
    align: 'center',
    width: '30px',
    cell: (h, { rowIndex }) => {
      return `${rowIndex + 1}.`
    },
  },
  {
    colKey: 'questionSort',
    title: '阅读端题目序号',
    align: 'center',
    width: '80px', // 调大宽度确保输入框可见
  },
  {
    colKey: 'questionContent',
    title: '题目内容',
    align: 'center',
    width: '200px',
  },
  { colKey: 'operation', title: '操作', align: 'center', width: '200px' },
]
const columnQuestionList = [
  {
    colKey: 'drag',
    width: 20,
    cell: () =>
      h(DragIcon, {
        class: 'drag-handle',
        style: { transition: 'all 0.3s' },
      }),
    align: 'center',
    fixed: 'left',
  },
  {
    colKey: 'index',
    align: 'center',
    width: '30px',
    cell: (h, { rowIndex }) => {
      return `${rowIndex + 1}.`
    },
  },
  {
    colKey: 'questionContent',
    title: '题目内容',
    align: 'center',
    width: '200px',
  },
  { colKey: 'operation', title: '操作', align: 'center', width: '200px' },
]

//计算并返回选项
const questionColumns = computed(() => {
  if (form.value.showSortType == 1) {
    return columnQuestionList
  } else {
    return columnList
  }
})

//选项（顺序作答）
const orderList = [
  { colKey: 'optionContent', align: 'center', width: '200px' },
  { colKey: 'score', align: 'center', width: '200px' },
]

// 选项 跳转作答
const jumpList = [
  { colKey: 'optionContent', align: 'center', width: '200px' },
  { colKey: 'score', align: 'center', width: '250px' },
  { colKey: 'jumpId', align: 'center', width: '250px' },
  { colKey: 'operation', align: 'center', width: '100px' },
]

//计算并返回选项
const optionColumns = computed(() => {
  if (form.value.scaleType == 1) {
    scaleTypeValue.value = 1
    return orderList
  } else {
    scaleTypeValue.value = 2
    return jumpList
  }
})

function changeForm() {
  if (form.value.questionSort == 1) {
    form.value.moocPsychologyHealthScaleQuestion = [
      {
        questionId: Number(Date.now()),
        questionContent: '',
        sort: null,
        moocPsychologyHealthScaleQuestionOption: [
          {
            sort: null,
            optionId: Date.now(),
            optionContent: '',
            score: null,
            jumpQuestionId: null,
            jumpId: null,
          },
        ],
      },
    ]
  } else {
    form.value.moocPsychologyHealthScaleFacet = [
      {
        facetName: '',
        sort: null,
        moocPsychologyHealthScaleQuestion: [
          {
            questionId: Number(Date.now()),
            questionContent: '',
            sort: null,
            moocPsychologyHealthScaleQuestionOption: [
              {
                optionId: Date.now(),
                optionContent: '',
                score: null,
                jumpQuestionId: null,
                jumpId: null,
              },
            ],
          },
        ],
      },
    ]
  }
  console.log(form.value)
}

/**删除选项 */
/**删除选项 */
function removeOption(row) {
  //单维度
  if (form.value.questionSort == 1) {
    // 1. 查找包含该选项的题目
    const parentQuestion = form.value.moocPsychologyHealthScaleQuestion.find(
      (question) =>
        question.moocPsychologyHealthScaleQuestionOption.some(
          (opt) => opt.optionId === row.optionId
        )
    )
    if (!parentQuestion) return // 添加保护

    if (parentQuestion.moocPsychologyHealthScaleQuestionOption.length <= 1) {
      MessagePlugin.warning('必须保留至少一个选项')
      return false
    }
    // 2. 在题目中删除选项
    const optionIndex =
      parentQuestion.moocPsychologyHealthScaleQuestionOption.findIndex(
        (opt) => opt.optionId === row.optionId
      )
    if (optionIndex !== -1) {
      parentQuestion.moocPsychologyHealthScaleQuestionOption.splice(
        optionIndex,
        1
      )
    }
  } else {
    let targetQuestion = null
    // 1. 遍历所有维度查找包含该选项的题目
    for (const facet of form.value.moocPsychologyHealthScaleFacet) {
      targetQuestion = facet.moocPsychologyHealthScaleQuestion.find(
        (question) =>
          question.moocPsychologyHealthScaleQuestionOption.some(
            (opt) => opt.optionId === row.optionId
          )
      )
      if (targetQuestion) break // 找到后立即停止查找
    }

    // 如果没有找到题目，直接返回
    if (!targetQuestion) return

    // 检查选项数量
    if (targetQuestion.moocPsychologyHealthScaleQuestionOption.length <= 1) {
      MessagePlugin.warning('必须保留至少一个选项')
      return false
    }

    // 2. 如果找到题目，删除选项
    const optionIndex =
      targetQuestion.moocPsychologyHealthScaleQuestionOption.findIndex(
        (opt) => opt.optionId === row.optionId
      )

    if (optionIndex !== -1) {
      targetQuestion.moocPsychologyHealthScaleQuestionOption.splice(
        optionIndex,
        1
      )
    }
  }
}

/**删除维度 */
function removeFacet(dimIndex) {
  if (form.value.moocPsychologyHealthScaleFacet.length <= 1) {
    MessagePlugin.warning('必须保留至少一个维度')
    return false
  }
  form.value.moocPsychologyHealthScaleFacet.splice(dimIndex, 1)
}

/**单维度  => 删除题目 */
function removeSingleQuestion(row) {
  if (form.value.moocPsychologyHealthScaleQuestion.length <= 1) {
    MessagePlugin.warning('必须保留至少一个题目')
    return false
  }
  //找每个题目的索引
  const index = form.value.moocPsychologyHealthScaleQuestion.findIndex(
    (q) => q.questionId === row.questionId
  )
  if (index !== -1) {
    form.value.moocPsychologyHealthScaleQuestion.splice(index, 1)
  }
}

/**多维度  => 删除题目 */
function removeQuestion(dimIndex, row) {
  console.log(form.value.moocPsychologyHealthScaleFacet[dimIndex])
  const dimension = form.value.moocPsychologyHealthScaleFacet[dimIndex]
  if (dimension.moocPsychologyHealthScaleQuestion.length <= 1) {
    MessagePlugin.warning('必须保留至少一个题目')
    return false
  }
  const questionIndex = dimension.moocPsychologyHealthScaleQuestion.findIndex(
    (q) => q.questionId === row.questionId
  )
  if (questionIndex !== -1) {
    dimension.moocPsychologyHealthScaleQuestion.splice(questionIndex, 1)
  }
}

/**添加选项 */
function addOption(row) {
  row.moocPsychologyHealthScaleQuestionOption.push({
    optionId: Date.now(),
    optionContent: '',
    score: null,
    jumpQuestionId: null,
    jumpId: '',
    questionId: row.questionId,
  })

  //点击选项 => 选项自动展开
  if (!expandedRowKeys.value.includes(row.questionId)) {
    expandedRowKeys.value = [...expandedRowKeys.value, row.questionId]
  }
}

/**确认事件 */
function submitForm() {
  // 1. 处理排序逻辑
  if (
    form.value.questionSort === 2 &&
    form.value.moocPsychologyHealthScaleFacet
  ) {
    // 多维度排序处理
    form.value.moocPsychologyHealthScaleFacet.forEach((facet, index) => {
      facet.sort = index + 1

      facet.moocPsychologyHealthScaleQuestion?.forEach((question, qIndex) => {
        question.sort = qIndex + 1

        question.moocPsychologyHealthScaleQuestionOption?.forEach(
          (option, oIndex) => {
            option.sort = oIndex + 1
          }
        )
      })
    })
  } else {
    // 单维度排序处理
    form.value.moocPsychologyHealthScaleQuestion.forEach((question, qIndex) => {
      question.sort = qIndex + 1

      question.moocPsychologyHealthScaleQuestionOption?.forEach(
        (option, oIndex) => {
          option.sort = oIndex + 1
        }
      )
    })
  }

  // 2. 设置 oldQuestionId
  if (form.value.questionSort === 2) {
    // 多维度设置
    form.value.moocPsychologyHealthScaleFacet?.forEach((facet) => {
      facet.moocPsychologyHealthScaleQuestion?.forEach((question) => {
        question.oldQuestionId = question.questionId
      })
    })
  } else {
    // 单维度设置
    form.value.moocPsychologyHealthScaleQuestion?.forEach((question) => {
      question.oldQuestionId = question.questionId
    })
  }

  // 3. 表单验证和提交
  const isValid = formRef.value.validate()
  isValid.then((valid) => {
    if (valid === true) {
      if (form.value.scaleId != null) {
        editHealth(form.value).then(() => {
          MessagePlugin.success('修改成功')
          emit('submit-success')
        })
      } else {
        addHealth(form.value).then(() => {
          MessagePlugin.success('添加成功')
          emit('submit-success')
        })
      }
    }
  })
}

/**预览 */
function preview() {
  previewIsOpen.value = true
}

/**取消 */
function canale() {
  previewIsOpen.value = false
}

function getAllQuestions() {
  if (form.value.questionSort === 1) {
    return form.value.moocPsychologyHealthScaleQuestion
  }
}

onMounted(() => {
  console.log(props.addForm)
})

function copy(row, dimIndex) {
  // 深拷贝题目及其选项
  const deepCloneQuestion = JSON.parse(JSON.stringify(row))

  // 生成新的ID（避免ID冲突）
  deepCloneQuestion.questionId = Number(Date.now())
  deepCloneQuestion.moocPsychologyHealthScaleQuestionOption =
    deepCloneQuestion.moocPsychologyHealthScaleQuestionOption.map((opt) => ({
      ...opt,
      optionId: Number(Date.now() + Math.random().toString().slice(2, 6)), // 生成唯一ID
      questionId: deepCloneQuestion.questionId, // 关联新题目ID
    }))

  if (form.value.questionSort == 1) {
    // 单维度：添加到题目列表末尾
    form.value.moocPsychologyHealthScaleQuestion.push(deepCloneQuestion)
  } else {
    // 多维度：添加到指定维度的题目列表末尾
    form.value.moocPsychologyHealthScaleFacet[
      dimIndex
      ].moocPsychologyHealthScaleQuestion.push(deepCloneQuestion)
  }
}
</script>

<style scoped lang="less">
.action-buttons {
  display: flex;
  gap: 12px;
}

.dimension-container {
  display: flex;
  flex-direction: column;
}
.dimension-header {
  display: flex;
  flex-direction: row;
  left: -15px;
  width: 90%;
}
.dimension-table {
  margin-top: -90px;
  margin-bottom: 20px;
}
.titleForm {
  text-align: center;
}
.dimensionName {
  color: rgba(0, 131, 255);
  margin: 30px 0 15px 0;
}
.questions-list {
  margin: 20px 5px 0 5px;
  counter-reset: question-counter; // 初始化问题计数器
}

.question-item {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px dashed #eee;
  counter-increment: question-counter; // 递增问题计数器

  &:last-child {
    border-bottom: none;
  }
}

.question-text {
  margin: 20px 0 10px 0;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
}

.options {
  margin-left: 0;
  list-style: none;
  padding-left: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item {
  position: relative;
  padding: 8px 12px 8px 36px; // 左侧留出空间给字母编号
  font-size: 13px;
  line-height: 1.5;
  border-radius: 4px;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.2s;
  counter-increment: option-counter; // 保留字母计数器

  &::before {
    content: counter(option-counter, upper-alpha) '.'; // 保留大写字母编号
    position: absolute;
    left: 20px;
    font-weight: bold;
    color: #666;
  }

  &:hover {
    background-color: #e9ecef;
  }
}

.progress-info {
  margin-bottom: 20px;
  font-size: 14px;
  color: #666;

  .current {
    font-weight: bold;
    color: #333;
  }
}

.question-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 300px); /* 根据实际需要调整高度 */
}

.question-actions {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  padding: 10px 0;
  margin-bottom: 0;
}

.scrollable-questions {
  flex: 1;
  overflow-y: auto;
  margin-top: -10px;
}


/**拖拽样式 */
.drag-handle {
  color: #999;
  transition: color 0.2s;
  cursor: move;

  &:hover {
    color: var(--td-brand-color);
  }
}

.t-table__row--dragging {
  background-color: var(--td-brand-color-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.t-table__row--placeholder {
  background-color: var(--td-gray-color-2);
}

/* 新增题目内容区域的样式 */
.question-content-wrapper {
  width: 100%;
  cursor: pointer;
  padding: 10px 0;

  /* 输入框样式调整 */
  :deep(.t-input) {
    width: 100%;
    cursor: text; /* 输入框内显示文本光标 */
  }

  &:hover {
    background-color: rgba(0, 82, 217, 0.05);
  }
}
</style>
