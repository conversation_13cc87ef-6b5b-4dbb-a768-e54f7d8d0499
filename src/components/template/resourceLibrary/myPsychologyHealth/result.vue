<template>
  <div>
    <div style="justify-content: center;width: 100%">
      <t-card>
        <div>
          <div class="row-con">
            <label class="text">
              <t-tooltip :content="scale.scaleName" placement="top">
                <label for="checkbox" class="text-ellipsis">{{ displayText(scale.scaleName, 15) }}</label>
              </t-tooltip>
              <label for="checkbox" class="text-ellipsis" :class="scale.status == 1 ? 'text-enabled' : 'text-disabled'"
              >{{ scale.status == 1 ? '（启用）' : '（停用）' }}</label>
            </label>
          </div>
          <div class="row-detail">
            <div class="book-and-count-con">
              <t-tooltip :content="scale.bookName" placement="top">
                <label for="checkbox"
                       class="text-ellipsis">{{ displayText('引用教材：' + (scale.bookName ? scale.bookName : '--'), 40)
                  }}</label>
              </t-tooltip>
              <label for="checkbox" class="text-count">填报量表个数：{{ scale.testCount }} 份</label>
            </div>
          </div>
        </div>
      </t-card>
      <div class="row-con" style="width: 100%;margin-left: 0px">
        <t-form
          ref="formRef"
          :data="queryParams"
          label-align="left"
          layout="inline">
          <t-form-item label-width="0">
            <div style="width: 100%; display: flex; justify-content: space-between; align-items: center;">
              <t-input v-model="queryParams.realName" style="width: 650px;margin-right: 20px" placeholder="请输入昵称" align="left" />
              <t-button theme="primary" @click="handleQuery">
                <template #icon>
                  <SearchIcon />
                </template>
                搜索
              </t-button>
            </div>
          </t-form-item>
        </t-form>
      </div>
      <div class="row-con" style="width: 100%;margin-left: 0px">
        <t-table
          row-key="key"
          :data="resultList"
          :columns="columns"
          v-model:displayColumns="displayColumns"
          :total="total"
          :pagination="pagination"
          @page-change="onPageChange"
          style="width: 100%"
        >
          <template #operation="{ row }">
            <t-link theme="primary" @click="handleDetail(row)">详情</t-link>
          </template>
        </t-table>
      </div>
    </div>
    <!--  查看结果详情弹窗  -->
    <t-dialog v-model:visible="resultVisible" top="10px" placement="center" :header="header" style="height: 500px; overflow-y: auto;" attach="body" destroy-on-close width="95%" :footer="null" >
      <result-detail v-if="scale" :resultId="resultId" :scale="scale" :scaleDetail="scaleDetail"></result-detail>
    </t-dialog>
  </div>
</template>

<script setup>
import { SearchIcon } from 'tdesign-icons-vue-next'
import { listPhychologyResult } from '@/api/resource/psychologyHealth.js'
import ResultDetail from '@/components/template/resourceLibrary/myPsychologyHealth/resultDetail.vue'

const props = defineProps({
  scale: {
    type: Object,
    default: () => ({})
  }
})

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  realName: null
})

const resultId = ref(null)

// 分页配置
const pagination = ref({
  defaultPageSize: 10,
  total: 10,
  defaultCurrent: 1,
});

const total = ref(0)
const scaleDetail = ref({})
const resultVisible = ref(false)
const resultList = ref([])
const header = ref(null)
const displayColumns = ref(['bookName', 'chapterName', 'nickName', 'createTime','operation' ])
const columns = [
  {
    colKey: 'resultId',
    title: '',
    ellipsis: true,
    align: 'center'
  },
  {
    colKey: 'score',
    title: '',
    ellipsis: true,
    align: 'center'
  },
  {
    colKey: 'bookName',
    title: '教材名称',
    ellipsis: true,
    align: 'center'
  },
  {
    colKey: 'chapterName',
    title: '所属章节',
    ellipsis: true,
    align: 'center'
  },
  {
    colKey: 'nickName',
    title: '昵称',
    ellipsis: true,
    align: 'center' },
  {
    colKey: 'createTime',
    width: 180,
    title: '填报时间',
    align: 'center'
  },
  {
    colKey: 'operation',
    title: '操作',
    align: 'center'
  },
]

/** 初始化 */
onMounted(() => {
  console.log(props.scale)
  getResultList()
})

function onPageChange(pageInfo){
  queryParams.value.pageSize = pageInfo.pageSize
  queryParams.value.pageNum = pageInfo.current
  queryParams.value.current = pageInfo.current
  getResultList()
}

function displayText(str, length) {
  return str.length > length ? str.substring(0, length) + '...' : str
}

function getResultList(){
  queryParams.value.scaleId = props.scale.scaleId
  listPhychologyResult(queryParams.value).then(res => {
    resultList.value = res.rows
    pagination.value.total = res.total;
  })
}

function handleQuery(){
  getResultList()
}

/** 查看详情 */
function handleDetail(row){
  scaleDetail.value = row
  resultId.value = row.resultId
  resultVisible.value = true
  header.value = '结果详情'
}

</script>

<style scoped>
.text {
  font-weight: normal;
  font-size: 18px;
  color: #333333;
  line-height: 18px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.text-book {
  font-weight: normal;
  font-size: 16px;
  color: #585E76;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.row-con {
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
}

.row-detail {
  display: flex;
  justify-content: space-between;
}

.text-enabled {
  color: #3AB288;
}

.text-disabled {
  color: red;
}

.book-and-count-con {
  display: flex;
  align-items: center;
}

.text-count {
  margin-left: 150px;
  background-color: #F2F5FA;
  padding: 5px;
  white-space: nowrap;
}

.text-ellipsis{
  white-space: nowrap;
}
</style>
