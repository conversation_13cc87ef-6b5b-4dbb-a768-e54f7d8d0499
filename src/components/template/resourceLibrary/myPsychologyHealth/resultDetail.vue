<template>
  <div class="questionnaire-container">
    <!-- 用户信息独占一行 -->
    <div class="user-info-row">
      <div class="info-item">
        <span class="label">昵称：</span>
        <span class="value">{{ scaleDetail.nickName }}</span>
      </div>
      <div class="info-item">
        <span class="label">账号：</span>
        <span class="value">{{ scaleDetail.userName }}</span>
      </div>
      <div class="info-item">
        <span class="label">提交时间：</span>
        <span class="value">{{ scaleDetail.createTime }}</span>
      </div>
    </div>

    <!-- 下方两列布局 -->
    <div class="content-columns">
      <!-- 左侧题目区域 -->
      <div class="left-column">
        <!-- 量表标题 -->
        <div class="scale-title" v-if="scaleDetail.scaleName">
          <t-tooltip :content="scaleDetail.scaleName" placement="top">
            <label for="checkbox" class="text-ellipsis">{{ displayText(scaleDetail.scaleName, 9) }}</label>
          </t-tooltip>
          <span class="score">{{ scaleDetail.score }}分</span>
        </div>
        <!-- 量表描述 -->
        <div class="scale-description">
          <div class="description-text">{{ scaleDetail.scanQuestion }}</div>
        </div>

        <!-- 题目列表单维度 -->
        <div class="questions-list" v-if="scaleDetail.questionSort == 1">
          <div class="question-item" v-for="(item, index) in questionList" :key="index">
            <div class="question-text">{{ index + 1 }}.{{ item.questionContent }}</div>
            <div class="options">
              <div
                v-for="(opt, optIndex) in item.moocPsychologyHealthScaleQuestionOption"
                :key="optIndex"
                class="option-item"
                :class="{ 'selected': opt.optionId === item.chooseOptionId }"
              >
                {{ opt.optionContent }}
              </div>
            </div>
          </div>
        </div>
        <!-- 题目列表多维度 -->
        <div class="questions-list" v-if="scaleDetail.questionSort == 2">
          <div class="question-item" v-for="(item, index) in questionList" :key="index">
            <div class="collection-header" v-if="scaleDetail.questionSort == 2">
              <h3>
                {{ displayText(item.facetName,30) }}
                <template>
                  （共{{ item.moocPsychologyHealthScaleQuestion?.length }}题）
                </template>
              </h3>
            </div>
            <div class="question-item" v-for="(ques, quesIndex) in item.moocPsychologyHealthScaleQuestion" :key="quesIndex">
              <span>{{ quesIndex + 1 }}. {{ ques.questionContent }}</span>
              <ul v-if="ques.moocPsychologyHealthScaleQuestionOption?.length > 0" class="data-item-option count">
                <li
                  v-for="(opt, optIndex) in ques.moocPsychologyHealthScaleQuestionOption"
                  :key="optIndex"
                  class="option-item"
                  :class="{ 'selected': opt.optionId === ques.chooseOptionId }"
                >
                  {{ opt.optionContent }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧测评方法和结果区域 -->
      <div class="right-column">
        <!-- 测评方法和评价参考 -->
        <div class="evaluation-section">
          <div class="scale-description">
            <div class="description-text"><span style="font-weight: bold">测评方法：</span>{{ scaleDetail.evaluationMethod }}</div>
          </div>
          <div class="scale-description">
            <div class="description-text"><span style="font-weight: bold">评价参考：</span>{{ scaleDetail.evaluateReference }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getPhychologyResult } from '@/api/resource/psychologyHealth.js'

const visible = ref(true)
const scaleDetail = ref({})
const props = defineProps({
  scale: {
    type: Object,
    default: () => ({})
  },
  resultId: {
    type: Object,
    default: () => ({})
  }
})

const questionList = ref([])

const questionIndex = ref(0)

onMounted(() => {
  getResultDetail()
})
async function getResultDetail() {
  await getPhychologyResult(props.resultId).then(res => {
    questionList.value = res.data.questionList
    scaleDetail.value = res.data.scale
  })
}
function displayText(str, length) {
  return str.length > length ? str.substring(0, length) + '...' : str
}
</script>

<style scoped>
.questionnaire-container {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.user-info-row {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f5f5f5;
}

.info-item {
  margin-right: 30px;
  font-size: 14px;
}

.info-item .label {
  color: #666;
  font-weight: 500;
}

.content-columns {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-column {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  border-right: 1px solid #eee;
}

.right-column {
  width: 400px;
  padding: 20px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.scale-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.scale-title .score {
  margin-left: 12px;
  font-size: 18px;
  font-weight: bold;
  color: var(--td-error-color);
}

.scale-description {
  margin-left: 12px;
  font-size: 16px;
  margin-bottom: 10px;
}

.questions-list {
  margin-bottom: 20px;
}

.question-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px dashed #eee;
}

.question-item:last-child {
  border-bottom: none;
}

.question-text {
  margin-bottom: 8px;
  font-size: 15px;
  line-height: 1.5;
}

.options {
  margin-left: 20px;
}

.option-item {
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.5;
}

.option-item.selected {
  color: #07c160;
  font-weight: 500;
}

.evaluation-section {
  margin-top: 20px;
}

ul {
  list-style: none;
}

.collection-header {
  padding: 16px 0;

  h3 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    text-align: left;
    margin: 0;
  }
}

.data-item-option {
  width: 100%;
  padding-top: 10px;
  &.true-or-false {
    display: flex;
  }
  &.sorting {
    display: block;
  }
  &:last-child {
    margin-bottom: 0;
  }
  .option-item {
    text-align: left;
    margin-bottom: 14px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  &.count {
    .option-item {
      counter-increment: my-counter; /* 计数器递增 */
      &::before {
        content: counter(my-counter, upper-alpha) ". "; /* 使用小写字母作为前缀 */
      }
      .option-item-content {
        display: inline-block;
        :deep(img) {
          max-width: 30%;
        }
      }
    }
  }
}
</style>
