<template>
  <div>
    <div>
      <t-breadcrumb :max-item-width="'150'">
        <t-breadcrumb-item>资源库管理 </t-breadcrumb-item>
        <t-breadcrumb-item to="/resourceLibrary/myResource">我的资源
        </t-breadcrumb-item>
      </t-breadcrumb>
    </div>

    <div class="main">
      <div class="main-form">
        <div class="main-form-query">
          <t-form ref="formRef" :data="queryForm" label-width="80px" label-align="right" layout="inline">
            <t-form-item label="文件名称：">
              <t-input v-model="queryForm.name" placeholder="请输入文件名称" style="width: 200px" :maxcharacter="20" />
            </t-form-item>
            <t-form-item label="文件类型：">
              <t-select v-model="queryForm.fileType" placeholder="请选择文件类型" style="width: 200px">
                <t-option v-for="type in fileTypes" :key="type.value" :value="type.value" :label="type.label" clearable>
                  {{ type.label }}
                </t-option>
              </t-select>
              <t-button theme="primary" class="btn" @click="getList">
                <template #icon>
                  <SearchIcon />
                </template>
                搜索</t-button>

              <t-dropdown :options="addFileOptions" trigger="click">
                <t-button class="btn">
                  添加文件
                  <template #suffix>
                    <t-icon name="chevron-down" size="16" /></template>
                </t-button>
              </t-dropdown>

              <t-button theme="success" class="btn" @click="handleAdd">
                <template #icon>
                  <PlusIcon />
                </template>
                新建文件夹</t-button>

              <t-button theme="default" class="btn" @click="handleDelete">
                <template #icon>
                  <DeleteIcon />
                </template>
                删除</t-button>

              <t-button theme="primary" variant="outline" class="btn" @click="handleBatchMove">
                <template #icon>
                  <t-icon name="swap" />
                </template>
                移动</t-button>
            </t-form-item>
          </t-form>
        </div>
        <div>
          <t-button theme="warning" variant="outline" @click="toRecycleBin">
            <template #icon>
              <DeleteIcon />
            </template>
            个人回收站
          </t-button>
        </div>
      </div>

      <div class="navigation-bar">
        <div class="nav-buttons">
          <t-button theme="default" variant="text" :disabled="historyIndex <= 0" @click="handleBack">
            <template #icon>
              <t-icon name="chevron-left" size="26" />
            </template>
          </t-button>

          <t-button theme="default" variant="text" :disabled="historyIndex >= folderHistory.length - 1"
            @click="handleForward">
            <template #icon>
              <t-icon name="chevron-right" size="26" />
            </template>
          </t-button>
        </div>
        <t-breadcrumb>
          <t-breadcrumb-item v-for="(folder, index) in folderPath" :key="index"
            :class="{ clickable: index < folderPath.length - 1 }" @click="handleBreadcrumbClick(folder.id)">
            {{ folder.name }}
          </t-breadcrumb-item>
        </t-breadcrumb>
      </div>

      <div class="main-list">
        <div v-for="item in [...folderList, ...resourceList]" :key="item.type === 'folder'
            ? `folder_${item.userFolderId}`
            : `file_${item.resourceId}`
          " class="main-item">
          <div class="main-item-header">
            <div>
              <t-checkbox v-if="item.type == 'file' || item.defaultType !== '7' && !item.defaultType"
                :value="item.isSelected" @change="(checked) => handleSelectionChange(item, checked)" />
            </div>
            <div>
              <t-dropdown v-if="!item.defaultType || item.type == 'file'" :options="getItemOptions(item)" trigger="hover">
                <t-button theme="default" variant="outline" shape="square">
                  <t-icon name="ellipsis" size="16" />
                </t-button>
              </t-dropdown>
              <!-- 添加占位元素，当不显示dropdown时保持相同高度 -->
              <div v-else class="placeholder-dropdown"></div>
            </div>
          </div>
          <div class="main-item-img" @click="handleItemClick(item)">
            <template v-if="item.type === 'folder'">
              <img src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6%E5%A4%B9%20131x113%402x.png"
                style="width: 50px; height: 50px; object-fit: contain" />
            </template>
            <template v-else>
              <!-- 根据文件类型显示不同的预览 -->
              <img v-if="item.fileType === '1'" :src="item.fileUrl" :alt="item.fileName"
                style="width: 50px; height: 50px; object-fit: cover" />
              <img v-else-if="item.fileType === '2'"
                src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E9%9F%B3%E9%A2%91.png" alt="音频"
                style="width: 50px; height: 50px; object-fit: contain" />
              <img v-else-if="item.fileType === '3'"
                src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E8%A7%86%E9%A2%91.png" alt="视频"
                style="width: 50px; height: 50px; object-fit: contain" />
              <img v-else-if="item.fileType === '4'"
                src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="虚拟仿真"
                style="width: 50px; height: 50px; object-fit: contain" />
              <img v-else-if="item.fileType === '5'"
                src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="AR/VR"
                style="width: 50px; height: 50px; object-fit: contain" />
              <img v-else-if="item.fileType === '6'"
                src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="3D模型"
                style="width: 50px; height: 50px; object-fit: contain" />
              <img v-else-if="item.fileType === '7'"
                src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="习题"
                style="width: 50px; height: 50px; object-fit: contain" />
              <img v-else-if="item.fileType === '8'"
                src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="课件"
                style="width: 50px; height: 50px; object-fit: contain" />
              <img v-else src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="文件"
                style="width: 50px; height: 50px; object-fit: contain" />
            </template>
          </div>
          <div class="main-item-name">
            {{ item.folderName || item.fileName }}
          </div>
        </div>
      </div>

      <div class="page-item">
        <t-pagination v-model="current" v-model:page-size="pageSize" :total="total" />
      </div>
    </div>
    <rename :key="renameForm.id" :visible="renameVisible" :item="renameForm" @close="renameVisible = false"
      @confirm="submitForm" />

    <DeleteModal v-model="removeVisible" @confirm="submitRemove(removeForm)" />
    <rename :visible="open" :item="{ name: '', type: 'folder' }" header="新建文件夹" :max="20" @close="open = false"
      @confirm="submitForm" />

    <!-- 添加图片对话框 -->
    <t-dialog v-model:visible="imageDialogVisible" header="添加图片" width="500px">
      <t-form ref="imageFormRef" :data="imageForm" label-width="100px">
        <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleImageChange" :files="imageList"
          :show-upload-progress="false" :before-upload="() => false" accept="image/*">
          <t-button theme="primary">选择图片</t-button>
          <template #tips>
            <div class="upload-tips">支持 jpg/png/gif 等图片格式</div>
          </template>
        </t-upload>

        <!-- 图片列表预览 -->
        <div class="preview-list">
          <t-card v-for="(image, index) in imageList" :key="index" class="image-item" hover-shadow>
            <div class="image-item-content">
              <div class="image-info">
                <img t- :src="image.url" class="preview-image" alt="预览图" />
                <span class="image-name">{{ image.name }}</span>
              </div>
              <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeImage(index)">
                删除
              </t-button>
            </div>
            <template v-if="image.uploadProgress > 0 && image.uploadProgress < 100">
              <t-progress theme="info" :percentage="Number(image.uploadProgress)" :label="false" />
            </template>
          </t-card>
        </div>
      </t-form>
      <template #footer>
        <t-space>
          <t-button theme="primary" :disabled="!isAllUploaded(imageList)" @click="submitImageForm">
            确定
          </t-button>
          <t-button @click="imageDialogVisible = false">取消</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 添加视频对话框 -->
    <t-dialog v-model:visible="videoDialogVisible" header="添加视频" width="500px">
      <t-form ref="videoFormRef" :data="videoForm" :rules="videoRules" label-width="100px">
        <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleVideoChange" :files="videoList"
          :show-upload-progress="false" :before-upload="() => false">
          <t-button theme="primary">选择视频</t-button>
          <template #tips>
            <div class="upload-tips">只能上传mp4/avi文件，且不超过500MB</div>
          </template>
        </t-upload>

        <!-- 视频列表展示 -->
        <div class="video-list">
          <t-card v-for="(video, index) in videoList" :key="index" class="video-item" hover-shadow>
            <div class="video-item-content">
              <div class="video-info">
                <t-icon name="video" />
                <span class="video-name">{{ video.name }}</span>
              </div>
              <div class="video-controls">
                <video :src="video.url" controls preload="metadata" class="video-player"></video>
                <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeVideo(index)">
                  删除
                </t-button>
              </div>
              <!-- 上传进度条 -->
              <t-progress v-if="video.uploadProgress > 0 && video.uploadProgress < 100"
                :percentage="video.uploadProgress" status="success" />
            </div>
          </t-card>
        </div>
      </t-form>
      <template #footer>
        <t-space>
          <t-button theme="primary" :disabled="!isAllUploaded(videoList)" @click="submitVideoForm">
            确定
          </t-button>
          <t-button @click="videoDialogVisible = false">取消</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 添加音频对话框 -->
    <t-dialog v-model:visible="audioDialogVisible" header="添加音频" width="500px">
      <t-form ref="audioFormRef" :data="audioForm" :rules="audioRules" label-width="100px">
        <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleAudioChange" :files="audioList"
          :show-upload-progress="false" :before-upload="() => false">
          <t-button theme="primary">选择音频</t-button>
          <template #tips>
            <div class="upload-tips">
              只能上传mp3/wav/mpeg文件，且不超过50MB
            </div>
          </template>
        </t-upload>

        <!-- 音频列表展示 -->
        <div class="audio-list">
          <t-card v-for="(audio, index) in audioList" :key="index" class="audio-item" hover-shadow>
            <div class="audio-item-content">
              <div class="audio-info">
                <t-icon name="sound" />
                <span class="audio-name">{{ audio.name }}</span>
              </div>
              <div class="audio-controls">
                <audio :src="audio.url" controls preload="metadata" class="audio-player"></audio>
                <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeAudio(index)">
                  删除
                </t-button>
              </div>
              <!-- 上传进度条 -->
              <t-progress v-if="audio.uploadProgress > 0 && audio.uploadProgress < 100"
                :percentage="audio.uploadProgress" status="success" />
            </div>
          </t-card>
        </div>
      </t-form>
      <template #footer>
        <t-space>
          <t-button theme="primary" :disabled="!isAllUploaded(audioList)" @click="submitAudioForm">
            确定
          </t-button>
          <t-button @click="audioDialogVisible = false">取消</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 添加虚拟仿真/AR/VR/3D模型对话框 -->
    <t-dialog v-model:visible="modelDialogVisible" :header="getModelTypeTitle(modelForm.type)" width="500px">
      <t-form ref="modelFormRef" :data="modelForm" label-width="100px">
        <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleModelChange" :files="modelList"
          :show-upload-progress="false" :before-upload="() => false" :accept="modelForm.type === '4' || modelForm.type === '5'
              ? '.zip'
              : '.obj,.3ds,.stl,.ply,.gltf,.glb,.off,.3dm,.fbx,.dae,.wrl,.3mf,.ifc,.brep,.step,.iges,.fcstd,.bim,.dwg,.dxf,.dwf,.igs,.dwt,.dng,.dwfx,.cf2,.plt'
            ">
          <t-button theme="primary">选择文件</t-button>
          <template #tips>
            <div v-if="modelForm.type === '4'" class="upload-tips">
              虚拟仿真文件仅支持ZIP格式，且不超过500MB
            </div>
            <div v-if="modelForm.type === '5'" class="upload-tips">
              AR/VR文件仅支持ZIP格式，且不超过500MB
            </div>
            <div v-if="modelForm.type === '6'" class="upload-tips">
              支持3D模型文件格式，且不超过500MB
            </div>
          </template>
        </t-upload>

        <!-- 模型列表展示 -->
        <div class="preview-list">
          <t-card v-for="(model, index) in modelList" :key="index" class="model-item" hover-shadow>
            <div class="model-item-content">
              <div class="model-info">
                <t-icon name="cube" />
                <span class="model-name">{{ model.name }}</span>
              </div>
              <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeModel(index)">
                删除
              </t-button>
              <!-- 上传进度条 -->
              <t-progress v-if="model.uploadProgress > 0 && model.uploadProgress < 100"
                :percentage="model.uploadProgress" status="success" />
            </div>
          </t-card>
        </div>
      </t-form>
      <template #footer>
        <t-space>
          <t-button theme="primary" :disabled="!isAllUploaded(modelList)" @click="submitModelForm">
            确定
          </t-button>
          <t-button @click="modelDialogVisible = false">取消</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 添加课件对话框 -->
    <t-dialog v-model:visible="coursewareDialogVisible" header="添加课件" width="500px">
      <t-form ref="coursewareFormRef" :data="coursewareForm" :rules="coursewareRules" label-width="100px">
        <t-upload multiple action="#" :auto-upload="false" :on-select-change="handleCoursewareChange"
          :files="coursewareList" :show-upload-progress="false" :before-upload="() => false">
          <t-button theme="primary">选择课件</t-button>
          <template #tips>
            <div class="upload-tips">
              支持 PDF、PPT、Word 等格式文件，且不超过100MB
            </div>
          </template>
        </t-upload>

        <!-- 课件列表展示 -->
        <div class="preview-list">
          <t-card v-for="(courseware, index) in coursewareList" :key="index" class="courseware-item" hover-shadow>
            <div class="courseware-item-content">
              <div class="courseware-info">
                <t-icon name="file" />
                <span class="courseware-name">{{ courseware.name }}</span>
              </div>
              <t-button theme="danger" variant="text" class="remove-btn" @click="() => removeCourseware(index)">
                删除
              </t-button>
              <!-- 上传进度条 -->
              <t-progress v-if="
                courseware.uploadProgress > 0 &&
                courseware.uploadProgress < 100
              " :percentage="courseware.uploadProgress" status="success" />
            </div>
          </t-card>
        </div>
      </t-form>
      <template #footer>
        <t-space>
          <t-button theme="primary" :disabled="!isAllUploaded(coursewareList)" @click="submitCoursewareForm">
            确定
          </t-button>
          <t-button @click="coursewareDialogVisible = false">取消</t-button>
        </t-space>
      </template>
    </t-dialog>

    <!-- 图片预览对话框 -->
    <t-dialog v-model:visible="imagePreviewVisible" header="图片预览" width="800px" :destroy-on-close="true"
      placement="center" :footer="false">
      <div class="image-preview-container">
        <img :src="previewUrl" alt="预览图片" style="max-width: 100%; max-height: 70vh" />
      </div>
    </t-dialog>

    <!-- 音频预览对话框 -->
    <t-dialog v-model:visible="audioPreviewVisible" :header="previewFileName" width="500px" :destroy-on-close="true"
      :footer="false">
      <div class="audio-preview-container">
        <audio controls style="width: 100%">
          <source :src="previewUrl" type="audio/mpeg" />
          您的浏览器不支持音频播放
        </audio>
      </div>
    </t-dialog>

    <!-- 视频预览对话框 -->
    <t-dialog v-model:visible="videoPreviewVisible" :header="previewFileName" width="800px" :destroy-on-close="true"
      :footer="false">
      <div class="video-preview-container">
        <video controls style="width: 100%">
          <source :src="previewUrl" type="video/mp4" />
          您的浏览器不支持视频播放
        </video>
      </div>
    </t-dialog>

    <!-- 文件预览对话框 -->
    <t-dialog v-model:visible="filePreviewVisible" :header="previewFileName" width="90%" :destroy-on-close="true"
      mode="fullscreen" :footer="false">
      <div class="file-preview-container">
        <iframe :src="previewUrl" frameborder="0" style="width: 100%; height: 80vh"></iframe>
      </div>
    </t-dialog>

    <!-- 移动文件夹对话框 -->
    <t-dialog v-model:visible="moveDialogVisible" header="移动到" width="400px">
      <t-form ref="moveFormRef" :data="moveForm" :rules="moveRules" label-width="80px">
        <t-form-item label="目标文件夹" name="parentId">
          <t-tree-select v-model="moveForm.parentId" :data="folderTreeOptions"
            :keys="{ value: 'userFolderId', label: 'folderName' }" :checkable="false" :check-strictly="true"
            placeholder="请选择目标文件夹" />
        </t-form-item>
      </t-form>
      <template #footer>
        <t-space>
          <t-button theme="primary" @click="submitMove">确定</t-button>
          <t-button @click="moveDialogVisible = false">取消</t-button>
        </t-space>
      </template>
    </t-dialog>
  </div>
</template>

<script setup>
import { DeleteIcon, PlusIcon, SearchIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { getCurrentInstance, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import {
  addUserResource,
  recycleUserResource,
  updateUserResource,
} from '@/api/resource/userResource'
import {
  addUserResourceFolder,
  delUserResourceFolder,
  listUserResourceFolderAll,
  listUserResourceFolderResource,
  updateUserResourceFolder,
} from '@/api/resource/userResourceFolder'
import { OssService } from '@/utils/aliOss'

import rename from './components/rename.vue'

const { proxy } = getCurrentInstance()

// 查询表单
const queryForm = ref({
  name: '',
  type: '',
  parentId: 0,
})

const userResourceFolderList = ref([])
const userResourceList = ref([]) // 新增文件列表
const open = ref(false)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const title = ref('')
const moveDialogVisible = ref(false)
const moveFormRef = ref(null)
const moveForm = ref({
  currentId: null,
  parentId: null,
  type: '', // 新增type字段，用于区分文件('file')还是文件夹('folder')
  resourceId: null, // 新增resourceId字段，用于存储文件ID
})

const moveRules = {
  parentId: [
    { required: true, message: '请选择目标文件夹', trigger: 'change' },
  ],
}

const form = ref({
  parentId: 0, // 默认父级ID为0
})

const route = useRoute()
const router = useRouter()
const current = ref(1)
const pageSize = ref(30)
const total = ref(0)

const renameShow = ref(false)
const removeShow = ref(false)
function renameClose() {
  renameVisible.value = false
}
function removeClose() {
  removeVisible.value = false
}

// 重命名相关
const renameVisible = ref(false)
const renameForm = ref({
  id: null,
  name: '',
  type: '', // folder 或 file
  resourceId: null,
})

// 打开重命名对话框
function handleRename(item) {

  const type = item.type === 'folder' ? 'folder' : 'file'
  renameForm.value = {
    id: type == 'folder' ? item.userFolderId : item.resourceId,
    name: item.name,
    type,
    resourceId: type == 'file' ? item.resourceId : null,
  }

  renameVisible.value = true
}

// 提交表单
async function submitForm(formData) {
  try {
    if (formData.type === 'folder') {
      if (formData.id) {
        // 更新文件夹
        await updateUserResourceFolder({
          userFolderId: formData.id,
          folderName: formData.name,
          parentId: queryForm.value.parentId,
        })
      } else {
        // 新增文件夹
        await addUserResourceFolder({
          folderName: formData.name,
          parentId: queryForm.value.parentId,
        })
      }
    } else {
      // 文件只有更新操作
      await updateUserResource({
        resourceId: formData.resourceId,
        fileName: formData.name,
        folderId: queryForm.value.parentId,
      })
    }

    MessagePlugin.success(formData.id ? '修改成功' : '新增成功')
    renameVisible.value = false
    open.value = false
    getList()
  } catch (error) {
    MessagePlugin.error(
      `${formData.id ? '修改' : '新增'}失败：${error.message}`,
    )
  }
}

// 删除相关
const removeVisible = ref(false)
const removeForm = ref({
  ids: [],
  type: '', // folder 或 file
  resourceIds: [],
})

// 打开删除对话框
function handleRemove(item) {
  const type = item.type === 'folder' ? 'folder' : 'file'
  const items = type === 'folder' ? [item] : [item] // 如果是批量删除,这里可以传入数组
  removeForm.value = {
    ids: type === 'folder' ? items.map((i) => i.userFolderId) : [],
    type,
    resourceIds: type === 'file' ? items.map((i) => i.resourceId) : [],
  }
  removeVisible.value = true
}

// 提交删除
async function submitRemove(form) {


  try {
    debugger

    if (form.type) {
      if (form.type === 'folder') {
        // 删除文件夹
        for (const id of form.ids) {
          await delUserResourceFolder(id)
        }
      } else {
        // 删除文件(移入回收站)
        for (const id of form.resourceIds) {
          await recycleUserResource(id)
        }
      }
    } else {
      // 没有type，使用选中项
      if (selectedItems.value.folders.length > 0) {
        for (const id of selectedItems.value.folders) {
          await delUserResourceFolder(id)
        }
      }
      if (selectedItems.value.files.length > 0) {
        for (const id of selectedItems.value.files) {
          await recycleUserResource(id)
        }
      }

      MessagePlugin.success('删除成功')
    }
    // 刷新列表
    getList()
    removeVisible.value = false
    // 清空选中
    selectedItems.value = {
      folders: [],
      files: [],
    }
  } catch (error) {`5`
    console.error('删除失败:', error)
  }
}

// 课件上传相关变量
const coursewareDialogVisible = ref(false)
const coursewareList = ref([])
const coursewareForm = ref({
  fileName: '',
  fileUrl: '',
  type: '8',
})

const coursewareRules = {
  fileUrl: [{ required: true, message: '请上传课件', trigger: 'change' }],
}
// 文件类型定义null
const fileTypes = [
  { label: '全部', value: null },
  { label: '图片', value: '1' },
  { label: '音频', value: '2' },
  { label: '视频', value: '3' },
  { label: '虚拟仿真', value: '4' },
  { label: 'AR/VR', value: '5' },
  { label: '3D模型', value: '6' },
  { label: '课件', value: '8' },
]

function getModelTypeTitle(type) {
  // 根据文件类型返回对应的标题
  for (const item of fileTypes) {
    if (item.value === type) {
      return item.label
    }
  }
  return '未知文件类型'
}

// 导航历史
const folderHistory = ref([])
const historyIndex = ref(-1)
const folderPath = ref([{ id: 0, name: '我的资源' }])
const MAX_HISTORY = 5

// 列表数据
const folderList = ref([])
const resourceList = ref([])
const loading = ref(true)

// 选中项
const selectedItems = ref({
  folders: [],
  files: [],
})

// 文件上传相关变量
const imageDialogVisible = ref(false)
const videoDialogVisible = ref(false)
const audioDialogVisible = ref(false)
const modelDialogVisible = ref(false)
const uploadProgress = ref(0)

const imageList = ref([])
const audioList = ref([])
const videoList = ref([])
const modelList = ref([])
const fileList = ref([])

const imageForm = ref({
  fileName: '',
  fileUrl: '',
  type: '1',
})

const videoForm = ref({
  fileName: '',
  fileUrl: '',
  type: '3',
})

const audioForm = ref({
  fileName: '',
  fileUrl: '',
  type: '2',
})

const modelForm = ref({
  fileName: '',
  fileUrl: '',
  type: '4',
})

const imagePreviewVisible = ref(false)
const audioPreviewVisible = ref(false)
const videoPreviewVisible = ref(false)
const filePreviewVisible = ref(false)
const previewUrl = ref('')
const previewFileName = ref('')

// 修改添加文件下拉选项定义
const addFileOptions = fileTypes
  .filter((type) => type.value !== null) // 过滤掉 value 为 null 的"全部"选项
  .map((type) => ({
    content: type.label,
    value: type.value,
    onClick: () => handleAddFile(type.value), // 添加 onClick 处理器
  }))

/** 获取列表数据 */
async function getList() {
  loading.value = true
  try {
    const response = await listUserResourceFolderResource({
      ...queryForm.value,
      pageNum: current.value,
      pageSize: pageSize.value,
      folderName: queryForm.value.name,
      fileType: queryForm.value.fileType,
      folderId: queryForm.value.parentId,
    })

    // 分离文件夹和文件数据
    const allItems = response.rows || []
    folderList.value = allItems
      .filter((item) => item.type === 'folder')
      .map((folder) => ({
        ...folder,
        userFolderId: folder.resourceId,
        folderName: folder.name,
        type: 'folder',
        isSelected: false,
      }))

    resourceList.value = allItems
      .filter((item) => item.type !== 'folder')
      .map((file) => ({
        ...file,
        fileName: file.name,
        isSelected: false,
      }))

    // 设置总数
    total.value = response.total

    // 检查是否在根目录且没有文件夹和文件
    if (
      queryForm.value.parentId === 0 &&
      allItems.length === 0 &&
      queryForm.value.type === null &&
      queryForm.value.folderName === null
    ) {
      try {
        await addUserResourceFolder({
          folderName: '默认文件夹',
          parentId: 0,
          defaultType: '1',
        })
        await getList()
      } catch (error) {
        MessagePlugin.error(`创建默认文件夹失败：${error.message}`)
      }
    }
  } catch (error) {
    MessagePlugin.error(`获取列表失败：${error.message}`)
  } finally {
    loading.value = false
    selectedItems.value = { folders: [], files: [] }
  }
}

// 表单重置
function reset() {
  form.value = {
    userFolderId: null,
    folderName: null,
    parentId: queryForm.value.parentId,
    userId: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    delFlag: null,
    name: null,
    resourceId: null,
    folderId: null,
  }

  // 使用表单引用重置
  formRef.value?.reset()
}

/** 处理选择变更 */
function handleSelectionChange(item, checked) {
  if (item.defaultType === '1') {
    return
  }

  // 更新项目的选中状态
  item.isSelected = checked

  if (item.type === 'folder') {
    const index = selectedItems.value.folders.indexOf(item.userFolderId)
    if (checked && index === -1) {
      selectedItems.value.folders.push(item.userFolderId)
    } else if (!checked && index > -1) {
      selectedItems.value.folders.splice(index, 1)
    }
  } else {
    const index = selectedItems.value.files.indexOf(item.resourceId)
    if (checked && index === -1) {
      selectedItems.value.files.push(item.resourceId)
    } else if (!checked && index > -1) {
      selectedItems.value.files.splice(index, 1)
    }
  }
}

/** 导航相关函数 */
function handleBack() {
  if (historyIndex.value > 0) {
    historyIndex.value--
    const prevState = folderHistory.value[historyIndex.value]
    queryForm.value.parentId = prevState.parentId
    folderPath.value = [...prevState.folderPath]
    getList()
  }
}

function handleForward() {
  if (historyIndex.value < folderHistory.value.length - 1) {
    historyIndex.value++
    const nextState = folderHistory.value[historyIndex.value]
    queryForm.value.parentId = nextState.parentId
    folderPath.value = [...nextState.folderPath]
    getList()
  }
}

function addToHistory() {
  folderHistory.value = folderHistory.value.slice(0, historyIndex.value + 1)
  folderHistory.value.push({
    parentId: queryForm.value.parentId,
    folderPath: [...folderPath.value],
  })
  if (folderHistory.value.length > MAX_HISTORY) {
    folderHistory.value.shift()
  } else {
    historyIndex.value++
  }
}

function handleBreadcrumbClick(folderId) {
  const index = folderPath.value.findIndex((item) => item.id === folderId)
  if (index !== -1) {
    queryForm.value.parentId = folderId
    folderPath.value = folderPath.value.slice(0, index + 1)
    addToHistory()
    getList()
  }
}

/** 处理项目点击 */
function handleItemClick(item) {
  if (item.type === 'folder') {
    queryForm.value.parentId = item.userFolderId
    folderPath.value.push({
      id: item.userFolderId,
      name: item.folderName,
    })
    addToHistory()
    getList()
  } else {
    handleFilePreview(item)
  }
}

/** 获取项目操作菜单 */
function getItemOptions(item) {
  // 如果是默认文件夹(defaultType为1)，不允许重命名、移动和删除
  if (item.defaultType === '1') {
    return []
  }

  const options = [
    {
      content: '移动',
      value: 'move',
      onClick: () => handleMove(item),
    },
    {
      content: '重命名',
      value: 'rename',
      onClick: () => handleRename(item),
    },
    {
      content: '删除',
      value: 'delete',
      onClick: () => handleRemove(item),
    },
  ]
  return options
}

/** 处理删除操作 */
function handleDelete() {
  debugger

  // 获取选中的项目，排除defaultType为1的文件夹
  const selectedItems = [...folderList.value, ...resourceList.value].filter(
    (item) => item.isSelected && item.defaultType !== '1',
  )

  if (selectedItems.length === 0) {
    MessagePlugin.warning('请选择要删除的项目')
    return
  }

  // 显示删除确认对话框
  removeVisible.value = true
}

/** 初始化 */
onMounted(() => {
  // 初始化历史记录
  folderHistory.value.push({
    parentId: 0,
    folderPath: [...folderPath.value],
  })
  historyIndex.value = 0
  getList()
})

// 回收站跳转
function toRecycleBin() {
  router.push('/resourceLibrary/recycleBin')
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  form.value.type = 'folder'
  open.value = true
  title.value = '添加个人资源文件夹'
}

// 处理添加文件按钮
function handleAddFile(type) {
  switch (type) {
    case '1':
      imageDialogVisible.value = true
      break
    case '3':
      videoDialogVisible.value = true
      break
    case '2':
      audioDialogVisible.value = true
      break
    case '4':
    case '5':
    case '6':
      modelForm.value.type = type
      modelDialogVisible.value = true

      break
    case '8':
      coursewareDialogVisible.value = true
      break
  }
}

// 修改视频处理函数
async function handleVideoChange(files) {
  // files现在是一个数组
  for (const file of files) {
    try {
      // 验证文件类型和大小
      const isVideo = file.type.startsWith('video/')
      const isValidSize = file.size <= 500 * 1024 * 1024 // 500MB

      if (!isVideo) {
        MessagePlugin.error(`${file.name} 不是视频文件`)
        continue
      }

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过500MB`)
        continue
      }

      // 创建本地预览URL
      const localUrl = URL.createObjectURL(file)

      // 创建新的视频文件对象
      const videoFile = {
        name: file.name,
        url: localUrl,
        file,
        uploadProgress: 0,
      }

      // 添加到视频列表
      videoList.value.push(videoFile)

      // 上传到OSS并获取URL
      const ossUrl = await OssService(file, {
        progressCallback: (progress) => {
          const index = videoList.value.findIndex(
            (item) => item.name === file.name,
          )
          if (index !== -1) {
            videoList.value[index].uploadProgress = Math.floor(progress * 100)
          }
        }
      })

      // 更新视频文件的URL为OSS URL
      const index = videoList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        videoList.value[index].ossUrl = ossUrl.url
        videoList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      const index = videoList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        videoList.value.splice(index, 1)
      }
    }
  }
}

function removeVideo(index) {
  // 释放本地预览URL
  if (videoList.value[index].url) {
    URL.revokeObjectURL(videoList.value[index].url)
  }
  videoList.value.splice(index, 1)
}

// 音频相关
function removeAudio(index) {
  // 释放本地预览URL
  if (audioList.value[index].url) {
    URL.revokeObjectURL(audioList.value[index].url)
  }
  audioList.value.splice(index, 1)
}

// 修改模型文件处理函数
async function handleModelChange(files) {
  for (const file of files) {
    try {
      const isValidSize = file.size <= 1024 * 1024 * 1024 // 1GB

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过1GB`)
        continue
      }

      const modelFile = {
        name: file.name,
        file,
        uploadProgress: 0,
      }

      modelList.value.push(modelFile)

      const ossUrl = await OssService(file, {
        progressCallback: (progress) => {
          const index = modelList.value.findIndex(
            (item) => item.name === file.name,
          )
          if (index !== -1) {
            modelList.value[index].uploadProgress = Math.floor(progress * 100)
          }
        }
      })

      const index = modelList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        modelList.value[index].ossUrl = ossUrl.url
        modelList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      const index = modelList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        modelList.value.splice(index, 1)
      }
    }
  }
}

function removeModel(index) {
  modelList.value.splice(index, 1)
}

// 修改预览处理函数
function handleFilePreview(file) {
  previewUrl.value = file.fileUrl
  previewFileName.value = file.fileName

  switch (file.fileType) {
    case '1': // 图片
      if (file.fileUrl) {
        previewFile(file.fileUrl)
      } else {
        proxy.$modal.msgError('文件地址无效')
      }
      break
    case '2': // 音频
      audioPreviewVisible.value = true
      break
    case '3': // 视频
      videoPreviewVisible.value = true
      break
    case '4': // 虚拟仿真
    case '5': // AR/VR
    case '6': // 3D模型
      window.open(file.fileUrl)
      break
    case '8': // 课件
    default:
      if (file.fileUrl) {
        previewFile(file.fileUrl)
      } else {
        MessagePlugin.error('文件地址无效')
      }
      break
  }
}

function previewFile(url) {
  // 处理可能包含中文的 URL
  const previewFileUrlBase64 = btoa(unescape(encodeURIComponent(url)))
  window.open(import.meta.env.VITE_ONLINE_PREVIEW + previewFileUrlBase64)
}

// 上传文件前的校验
function beforeUpload(file, type) {
  const isValidType = {
    video: ['video/mp4', 'video/avi'].includes(file.type),
    audio: ['audio/mp3', 'audio/wav', 'audio/mpeg'].includes(file.type),
    model: true, // 模型文件类型较多,暂不限制
  }

  const maxSize = {
    video: 500 * 1024 * 1024, // 500MB
    audio: 50 * 1024 * 1024, // 50MB
    model: 1024 * 1024 * 1024, // 1GB
  }

  if (!isValidType[type]) {
    MessagePlugin.error(`请上传正确的${type}格式文件!`)
    return false
  }

  if (file.size > maxSize[type]) {
    MessagePlugin.error(`文件大小不能超过 ${maxSize[type] / (1024 * 1024)}MB!`)
    return false
  }

  return true
}

// 上传进度处理
function handleProgress(event) {
  uploadProgress.value = Math.round((event.loaded / event.total) * 100)
}

// 上传完成后重置进度
function resetProgress() {
  uploadProgress.value = 0
}

function isAllUploaded(fileList) {
  if (!fileList || fileList.length === 0) {
    return false
  }
  return fileList.every((file) => {
    // 检查是否有ossUrl（上传完成）且进度为100
    return file.ossUrl && (!file.uploadProgress || file.uploadProgress === 100)
  })
}

// 修改课件处理函数
async function handleCoursewareChange(files) {
  for (const file of files) {
    try {
      const isValidSize = file.size <= 100 * 1024 * 1024 // 100MB

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过100MB`)
        continue
      }

      const coursewareFile = {
        name: file.name,
        file,
        uploadProgress: 0,
      }

      coursewareList.value.push(coursewareFile)

      const ossUrl = await OssService(file, {
        progressCallback: (progress) => {
          const index = coursewareList.value.findIndex(
            (item) => item.name === file.name,
          )
          if (index !== -1) {
            coursewareList.value[index].uploadProgress = Math.floor(
              progress * 100,
            )
          }
        }
      })

      const index = coursewareList.value.findIndex(
        (item) => item.name === file.name,
      )
      if (index !== -1) {
        coursewareList.value[index].ossUrl = ossUrl.url
        coursewareList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      const index = coursewareList.value.findIndex(
        (item) => item.name === file.name,
      )
      if (index !== -1) {
        coursewareList.value.splice(index, 1)
      }
    }
  }
}

// 移除课件
function removeCourseware(index) {
  coursewareList.value.splice(index, 1)
}

// 提交课件表单
async function submitCoursewareForm() {
  try {
    // 遍历课件列表中的每个文件
    for (const courseware of coursewareList.value) {
      const coursewareData = {
        fileName: courseware.name,
        fileUrl: courseware.ossUrl,
        fileType: '8', // 课件类型
        folderId: queryForm.value.parentId,
      }

      // 为每个课件调用添加资源API
      await addUserResource(coursewareData)
    }

    MessagePlugin.success('添加成功')
    coursewareDialogVisible.value = false
    // 清空课件列表
    coursewareList.value = []
    // 重新获取列表
    getList()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}

// 初始化历史记录
folderHistory.value.push({
  parentId: 0,
  folderPath: [...folderPath.value],
})
historyIndex.value = 0

// 1. 创建表单引用
const formRef = ref(null)

// 图片相关处理函数ch
async function handleImageChange(files) {
  debugger
  // files是一个数组,包含新添加的文件
  for (const file of files) {
    try {
      // 验证文件类型和大小
      const isImage = file.type.startsWith('image/')
      const isValidSize = file.size <= 5 * 1024 * 1024 // 5MB

      if (!isImage) {
        MessagePlugin.error(`${file.name} 不是图片文件`)
        continue
      }

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过5MB`)
        continue
      }

      // 创建本地预览URL
      const localUrl = URL.createObjectURL(file)

      // 创建新的图片文件对象
      const imageFile = {
        name: file.name,
        url: localUrl,
        file,
        uploadProgress: 0,
      }

      // 添加到图片列表
      imageList.value.push(imageFile)

      // 上传到OSS并获取URL
      const ossUrl = await OssService(file, {
        progressCallback: (progress) => {
          // 更新特定文件的上传进度
          const index = imageList.value.findIndex(
            (item) => item.name === file.name,
          )
          if (index !== -1) {
            imageList.value[index].uploadProgress = Math.floor(progress * 100)
          }
        }
      })

      // 更新图片文件的URL为OSS URL
      const index = imageList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        imageList.value[index].ossUrl = ossUrl.url
        imageList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      // 从列表中移除失败的文件
      const index = imageList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        imageList.value.splice(index, 1)
      }
    }
  }
}

// 修改音频处理函数
async function handleAudioChange(files) {
  for (const file of files) {
    try {
      const isAudio = file.type.startsWith('audio/')
      const isValidSize = file.size <= 50 * 1024 * 1024 // 50MB

      if (!isAudio) {
        MessagePlugin.error(`${file.name} 不是音频文件`)
        continue
      }

      if (!isValidSize) {
        MessagePlugin.error(`${file.name} 大小超过50MB`)
        continue
      }

      const localUrl = URL.createObjectURL(file)
      const audioFile = {
        name: file.name,
        url: localUrl,
        file,
        uploadProgress: 0,
      }

      audioList.value.push(audioFile)

      const ossUrl = await OssService(file, {
        progressCallback: (progress) => {
          const index = audioList.value.findIndex(
            (item) => item.name === file.name,
          )
          if (index !== -1) {
            audioList.value[index].uploadProgress = Math.floor(progress * 100)
          }
        }
      })

      const index = audioList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        audioList.value[index].ossUrl = ossUrl.url
        audioList.value[index].uploadProgress = 100
      }
    } catch (error) {
      MessagePlugin.error(`${file.name} 上传失败: ${error.message}`)
      const index = audioList.value.findIndex((item) => item.name === file.name)
      if (index !== -1) {
        audioList.value.splice(index, 1)
      }
    }
  }
}

function removeImage(index) {
  // 释放本地预览URL
  if (imageList.value[index].url) {
    URL.revokeObjectURL(imageList.value[index].url)
  }
  imageList.value.splice(index, 1)
}

async function submitImageForm() {

  try {
    // 遍历图片列表中的每个文件
    for (const image of imageList.value) {
      const imageData = {
        fileName: image.name,
        fileUrl: image.ossUrl,
        fileType: '1', // 图片类型
        folderId: queryForm.value.parentId,
      }

      // 为每个图片调用添加资源API
      await addUserResource(imageData)
    }

    MessagePlugin.success('添加成功')
    imageDialogVisible.value = false
    // 清空图片列表
    imageList.value = []
    // 重新获取列表
    getList()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}

async function submitVideoForm() {
  try {
    // 遍历视频列表中的每个文件
    for (const video of videoList.value) {
      const videoData = {
        fileName: video.name,
        fileUrl: video.ossUrl, // 使用OSS URL
        fileType: '3',
        folderId: queryForm.value.parentId,
      }

      // 为每个视频调用添加资源API
      await addUserResource(videoData)
    }

    MessagePlugin.success('添加成功')
    videoDialogVisible.value = false

    // 清理本地预览URL
    videoList.value.forEach((video) => {
      if (video.url) {
        URL.revokeObjectURL(video.url)
      }
    })

    // 清空视频列表
    videoList.value = []
    // 重新获取列表
    getList()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}

async function submitAudioForm() {
  try {
    // 遍历音频列表中的每个文件
    for (const audio of audioList.value) {
      const audioData = {
        fileName: audio.name,
        fileUrl: audio.ossUrl, // 使用OSS URL
        fileType: '2',
        folderId: queryForm.value.parentId,
      }

      // 为每个音频调用添加资源API
      await addUserResource(audioData)
    }

    MessagePlugin.success('添加成功')
    audioDialogVisible.value = false

    // 清理本地预览URL
    audioList.value.forEach((audio) => {
      if (audio.url) {
        URL.revokeObjectURL(audio.url)
      }
    })

    // 清空音频列表
    audioList.value = []
    // 重新获取列表
    getList()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}

async function submitModelForm() {
  try {
    // 遍历模型列表中的每个文件
    for (const model of modelList.value) {
      const modelData = {
        fileName: model.name,
        fileUrl: model.ossUrl, // 使用OSS URL
        fileType: modelForm.value.type,
        folderId: queryForm.value.parentId,
      }

      // 为每个模型调用添加资源API
      await addUserResource(modelData)
    }

    MessagePlugin.success('添加成功')
    modelDialogVisible.value = false
    // 清空模型列表
    modelList.value = []
    // 重新获取列表
    getList()
  } catch (error) {
    MessagePlugin.error(`添加失败：${error.message}`)
  }
}

// 监听对话框的visible变化
watch(
  () => imageDialogVisible.value,
  (newVal) => {
    if (!newVal) {
      // 图片对话框关闭时清空数据
      imageList.value = []
      imageForm.value = {
        fileName: '',
        fileUrl: '',
        type: '1',
      }
    }
  },
)

watch(
  () => videoDialogVisible.value,
  (newVal) => {
    if (!newVal) {
      // 视频对话框关闭时清空数据
      videoList.value = []
      videoForm.value = {
        fileName: '',
        fileUrl: '',
        type: '3',
      }
    }
  },
)

watch(
  () => audioDialogVisible.value,
  (newVal) => {
    if (!newVal) {
      // 音频对话框关闭时清空数据
      audioList.value = []
      audioForm.value = {
        fileName: '',
        fileUrl: '',
        type: '2',
      }
    }
  },
)

watch(
  () => modelDialogVisible.value,
  (newVal) => {
    if (!newVal) {
      // 模型对话框关闭时清空数据
      modelList.value = []
      modelForm.value = {
        fileName: '',
        fileUrl: '',
        type: '4',
      }
    }
  },
)

watch(
  () => coursewareDialogVisible.value,
  (newVal) => {
    if (!newVal) {
      // 课件对话框关闭时清空数据
      coursewareList.value = []
      coursewareForm.value = {
        fileName: '',
        fileUrl: '',
        type: '8',
      }
    }
  },
)

// 监听分页变化
watch([current, pageSize], () => {
  getList()
})

// 文件夹树数据
const folderTree = ref([])
const folderTreeData = ref([]) // 添加文件夹树原始数据
const folderTreeOptions = ref([]) // 添加文件夹树选项

/**
 * 打开移动对话框
 * @param {Object} item 要移动的项目
 * @param {String} type 类型：'folder'或'file'
 */
function handleMove(item) {

  const { type } = item
  moveForm.value = {
    parentId: null,
    currentId: type === 'folder' ? item.userFolderId : null,
    resourceId: type === 'file' ? item.resourceId : null,
    type,
  }
  moveDialogVisible.value = true
  // 确保不显示当前文件夹作为目标选项
  getAllFolders(type === 'folder' ? item.userFolderId : null)
}

/**
 * 获取所有文件夹用于构建树形选择器
 * @param {String|Array|null} excludeFolderId 需要排除的文件夹ID或ID数组
 */
function getAllFolders(excludeFolderId = null) {
  // 获取所有文件夹数据
  listUserResourceFolderAll().then((res) => {
    folderTreeData.value = res.rows || []

    // 构建文件夹树
    buildFolderTree(excludeFolderId)
  })
}

/**
 * 构建文件夹树
 * @param {String|Array|null} excludeFolderId 需要排除的文件夹ID或ID数组
 */
function buildFolderTree(excludeFolderId = null) {
  // 过滤掉当前移动的文件夹及其子文件夹
  let validFolders = folderTreeData.value

  if (excludeFolderId) {
    let excludeIds = []

    // 处理单个ID或ID数组
    if (Array.isArray(excludeFolderId)) {
      // 如果是数组，找出所有需要排除的ID
      excludeFolderId.forEach(id => {
        const childIds = getChildFolderIds(validFolders, id)
        excludeIds = [...excludeIds, id, ...childIds]
      })
    } else {
      // 如果是单个ID
      const childIds = getChildFolderIds(validFolders, excludeFolderId)
      excludeIds = [excludeFolderId, ...childIds]
    }

    // 过滤掉需要排除的文件夹
    validFolders = validFolders.filter(
      (folder) => !excludeIds.includes(folder.userFolderId),
    )
  }

  // 构建树形结构
  const tree = []
  const map = {}

  validFolders.forEach((folder) => {
    map[folder.userFolderId] = { ...folder, children: [] }
  })

  validFolders.forEach((folder) => {
    const node = map[folder.userFolderId]
    if (folder.parentId == 0) {
      tree.push(node)
    } else {
      if (map[folder.parentId]) {
        map[folder.parentId].children.push(node)
      }
    }
  })

  folderTreeOptions.value = [
    {
      userFolderId: 0,
      folderName: '根目录',
      children: tree,
    },
  ]
}

/**
 * 获取指定文件夹的所有子文件夹ID
 * @param {Array} folders 所有文件夹数据
 * @param {String} parentId 父文件夹ID
 * @returns {Array} 子文件夹ID数组
 */
function getChildFolderIds(folders, parentId) {
  const childIds = []

  // 找出直接子文件夹
  const directChildren = folders.filter((folder) => folder.parentId == parentId)

  // 将直接子文件夹ID添加到结果中
  directChildren.forEach((child) => {
    childIds.push(child.userFolderId)

    // 递归查找子文件夹的子文件夹
    const grandChildIds = getChildFolderIds(folders, child.userFolderId)
    childIds.push(...grandChildIds)
  })

  return childIds
}

/**
 * 过滤文件夹树，移除指定ID的文件夹及其子文件夹
 */
function filterFolderTree(tree, excludeId) {
  return tree.filter((node) => {
    if (node.userFolderId === excludeId) {
      return false
    }
    if (node.children && node.children.length > 0) {
      node.children = filterFolderTree(node.children, excludeId)
    }
    return true
  })
}

/**
 * 提交移动操作
 */
async function submitMove() {
  // 添加表单验证
  const valid = await moveFormRef.value.validate()
  if (!valid) return

  try {
    const targetFolderId = moveForm.value.parentId

    // 检查是否是批量移动
    if (moveForm.value.type === 'batch' && moveForm.value.selectedItems) {
      // 批量移动
      for (const item of moveForm.value.selectedItems) {
        if (item.type === 'folder') {
          // 移动文件夹
          await updateUserResourceFolder({
            userFolderId: item.id,
            parentId: targetFolderId,
          })
        } else {
          // 移动文件
          await updateUserResource({
            resourceId: item.id,
            folderId: targetFolderId,
          })
        }
      }
    } else if (moveForm.value.type === 'folder') {
      // 移动单个文件夹
      await updateUserResourceFolder({
        userFolderId: moveForm.value.currentId,
        parentId: targetFolderId,
      })
    } else {
      // 移动单个文件
      await updateUserResource({
        resourceId: moveForm.value.resourceId,
        folderId: targetFolderId,
      })
    }

    MessagePlugin.success('移动成功')
    moveDialogVisible.value = false
    getList()
    getAllFolders() // 更新文件夹树

    // 清空选中项
    if (moveForm.value.type === 'batch') {
      folderList.value.forEach(item => {
        item.isSelected = false
      })
      resourceList.value.forEach(item => {
        item.isSelected = false
      })
      selectedItems.value = { folders: [], files: [] }
    }
  } catch (error) {
    MessagePlugin.error(`移动失败：${error.message}`)
  }
}

// 处理批量移动
function handleBatchMove() {
  // 获取选中的项目
  const selectedItems = [...folderList.value, ...resourceList.value].filter(
    (item) => item.isSelected && item.defaultType !== '1',
  )

  if (selectedItems.length === 0) {
    MessagePlugin.warning('请选择要移动的项目')
    return
  }

  // 初始化移动表单
  moveForm.value = {
    parentId: null,
    currentId: null,
    resourceId: null,
    type: 'batch',
    selectedItems: selectedItems.map(item => ({
      id: item.type === 'folder' ? item.userFolderId : item.resourceId,
      type: item.type
    }))
  }

  // 打开移动对话框
  moveDialogVisible.value = true

  // 获取文件夹树，排除所有选中的文件夹
  const excludeFolderIds = selectedItems
    .filter(item => item.type === 'folder')
    .map(item => item.userFolderId)

  getAllFolders(excludeFolderIds)
}
</script>

<style lang="less" scoped>
.main {
  .main-form {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;

    .main-form-query {
      .btn {
        margin-left: 20px;
      }
    }
  }

  .main-back {
    display: flex;
    align-items: center;
  }

  .main-list {
    display: flex;
    flex-wrap: wrap;
    margin: 20px 0;

    .main-item {
      padding: 20px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: #f5f5f5;
      }

      .main-item-header {
        display: flex;
        justify-content: space-between;
      }

      .main-item-name {
        width: 130px;
        white-space: nowrap;
        /* 保证文本在一行内显示 */
        overflow: hidden;
        /* 超出容器部分隐藏 */
        text-overflow: ellipsis;
        /* 使用省略号表示文本溢出 */
      }
    }
  }
}

.navigation-bar {
  display: flex;
  align-items: center;
  margin: 20px 0;

  .nav-buttons {
    margin-right: 20px;
  }

  .clickable {
    cursor: pointer;
    color: var(--td-brand-color);

    &:hover {
      color: var(--td-brand-color-hover);
    }
  }
}

.preview-list {
  margin-top: 16px;

  .image-item {
    margin-bottom: 16px;

    .image-item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px; // 添加底部间距

      .image-info {
        display: flex;
        align-items: center;

        .preview-image {
          width: 60px;
          height: 60px;
          object-fit: cover;
          margin-right: 12px;
        }

        .image-name {
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .remove-btn {
        margin-left: 12px;
      }
    }

    // 添加进度条样式
    .progress-wrapper {
      margin-top: 8px;
      padding: 0 12px;
    }
  }
}

.upload-tips {
  margin-top: 8px;
  color: var(--td-text-color-secondary);
}

.video-list {
  margin-top: 16px;

  .video-item {
    margin-bottom: 16px;

    .video-item-content {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .video-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .video-name {
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .video-controls {
        .video-player {
          width: 100%;
          max-height: 200px; // 添加最大高度限制
          object-fit: contain; // 保持视频比例
        }

        .remove-btn {
          margin-top: 8px;
        }
      }
    }
  }
}

.placeholder-dropdown {
  width: 28px;
  height: 28px;
  /* 与按钮高度保持一致 */
  display: inline-block;
}
</style>
