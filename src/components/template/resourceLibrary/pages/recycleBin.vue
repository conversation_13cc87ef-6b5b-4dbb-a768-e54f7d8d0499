<template>
    <div>
        <div>
            <t-breadcrumb :max-item-width="'150'">
                <t-breadcrumb-item>资源库管理</t-breadcrumb-item>
                <t-breadcrumb-item to="/resourceLibrary/myResource">我的资源</t-breadcrumb-item>
                <t-breadcrumb-item to="/resourceLibrary/recycleBin">回收站</t-breadcrumb-item>
            </t-breadcrumb>
        </div>

        <div class="form-main">
            <t-form :data="queryParams" :label-width="100" label-align="right" layout="inline">
                <t-form-item label="文件名称：">
                    <t-input
                        v-model="queryParams.name"
                        placeholder="请输入文件名称"
                        clearable
                        @keyup-enter="handleQuery"
                    />
                </t-form-item>
                <t-form-item label="类型：">
                    <t-select v-model="queryParams.fileType" placeholder="请选择类型" clearable style="width: 200px">
                        <t-option label="全部" value="" />

                        <t-option 
                            v-for="(type, index) in fileTypes" 
                            :key="index"
                            :label="type.label" 
                            :value="type.value"
                        />
                    </t-select>
                </t-form-item>
                <t-button theme="primary" @click="handleQuery">
                    <template #icon><SearchIcon /></template>
                    搜索
                </t-button>
                <t-button @click="resetQuery" style="margin-left:10px">
                    <template #icon><RefreshIcon /></template>
                    重置
                </t-button>
            </t-form>
            
            <div>
                <t-popconfirm content="是否确认还原选中的数据项？" @confirm="() => handleRestore()">
                    <t-button theme="primary" variant="outline" :disabled="multiple">
                        <template #icon><HistoryIcon /></template>
                        还原
                    </t-button>
                </t-popconfirm>
                <t-popconfirm content="是否确认彻底删除选中的数据项？删除后将无法恢复！" @confirm="() => handleDelete()">
                    <t-button theme="danger" variant="outline" :disabled="multiple" style="margin-left:10px">
                        <template #icon><DeleteIcon /></template>
                        彻底删除
                    </t-button>
                </t-popconfirm>
                <t-button theme="default" @click="goBack" style="margin-left:10px">
                    <template #icon><RollbackIcon /></template>
                    返回
                </t-button>
            </div>
        </div>

        <t-table
            row-key="resourceId"
            :loading="loading"
            :data="recycleList"
            :columns="columns"
            :select-on-row-click="false"
            @select-change="handleSelectionChange"
        >
            <template #operate="{ row }">
                <t-popconfirm content="是否确认还原该数据项？" @confirm="() => handleRestore(row)">
                    <t-button theme="primary" style="margin-right:10px">还原</t-button>
                </t-popconfirm>
                <t-popconfirm content="是否确认彻底删除该数据项？删除后将无法恢复！" @confirm="() => handleDelete(row)">
                    <t-button theme="danger" variant="outline">彻底删除</t-button>
                </t-popconfirm>
            </template>
        </t-table>

        <t-pagination
            v-if="total > 0"
            v-model:current="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :total="total"
            @change="getList"
        />
    </div>
</template>

<script setup name="recycleBin">
import { ref, reactive } from 'vue'
import { RollbackIcon, SearchIcon, HistoryIcon, DeleteIcon, RefreshIcon } from 'tdesign-icons-vue-next'
import { useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import { listRecycleUserResource, permanentUserResource, restoreUserResource } from '@/api/resource/userResource'

const router = useRouter()
const loading = ref(false)
const multiple = ref(true)
const total = ref(0)
const recycleList = ref([])
const ids = ref([])

// 文件类型定义
const fileTypes = [
    { label: '图片', value: '1' },
    { label: '音频', value: '2' },
    { label: '视频', value: '3' },
    { label: '虚拟仿真', value: '4' },
    { label: 'AR/VR', value: '5' },
    { label: '3D模型', value: '6' },
    { label: '课件', value: '8' }
]

const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    name: '',
    fileType: ''
})

const columns = [
    { colKey: 'row-select', type: 'multiple', width: 50 },
    { 
        colKey: 'index', 
        title: '序号', 
        width: 80,
        cell: (h, { rowIndex }) => {
            const pageNum = Number(queryParams.pageNum) || 1 
            const pageSize = Number(queryParams.pageSize) || 10
            const index = (pageNum - 1) * pageSize + rowIndex + 1
            return isNaN(index) ? '-' : index
        }
    },
    { colKey: 'fileName', title: '名称' },
    { 
        colKey: 'type', 
        title: '类型',
        cell: (h, { row }) => {
            if (!row) return '未知类型'
            return row.type === 'folder' ? '文件夹' : getFileTypeName(row.fileType)
        }
    },
    { colKey: 'folderName', title: '原位置' },
    { colKey: 'updateTime', title: '删除时间' },
    { colKey: 'operate', title: '操作', width: 200 }
]

// ... 其余方法保持不变 ...
function getList() {
    loading.value = true
    const params = {
        pageNum: queryParams.pageNum,
        pageSize: queryParams.pageSize,
        fileName: queryParams.name,
        fileType: queryParams.fileType,
    }

    listRecycleUserResource(params).then(response => {
        recycleList.value = response.rows
        total.value = response.total
    }).catch(() => {
        MessagePlugin.error('获取列表失败')
    }).finally(() => {
        loading.value = false
    })
}

function handleQuery() {
    queryParams.pageNum = 1
    getList()
}

function resetQuery() {
    queryParams.name = ''
    queryParams.fileType = ''
    handleQuery()
}

function handleRestore(row) {
    const itemIds = row?.resourceId ? [row.resourceId] : ids.value
    restoreUserResource(itemIds).then(() => {
        MessagePlugin.success('还原成功')
        getList()
    }).catch(() => {
        MessagePlugin.error('还原失败')
    })
}

function handleDelete(row) {
    const itemIds = row?.resourceId ? [row.resourceId] : ids.value
    permanentUserResource(itemIds).then(() => {
        MessagePlugin.success('删除成功')
        getList()
    }).catch(() => {
        MessagePlugin.error('删除失败')
    })
}

function handleSelectionChange(selection) {
    ids.value = selection
    multiple.value = !selection.length
}

function getFileTypeName(type) {
    const fileType = fileTypes.find(item => item.value === type)
    return fileType ? fileType.label : '未知类型'
}

function goBack() {
    router.push('/resourceLibrary/myResource')
}

getList()
</script>

<style lang="less" scoped>
.form-main {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
}
</style>