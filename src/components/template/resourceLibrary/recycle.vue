<template>
  <div class="app-container">
    <t-card>
      <div class="tool">
        <t-form
          v-show="showSearch"
          ref="queryRef"
          :data="queryParams"
          layout="inline"
          label-width="68px"
          style="margin-bottom: 20px"
        >
          <t-form-item label="题型" name="questionType">
            <t-select
              v-model="queryParams.questionType"
              placeholder="请选择题型"
              clearable
              style="width: 200px"
            >
              <t-option label="全部" value="" />
              <t-option
                v-for="item in questionTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </t-select>
          </t-form-item>
          <t-form-item>
            <t-button theme="primary" @click="handleQuery">
              <template #icon><t-icon name="search" /></template>
            搜索
            </t-button>
            <t-button
              theme="default"
              style="margin-left: 20px"
              @click="resetQuery"
            >
              <template #icon><t-icon name="refresh" /></template>
              重置
            </t-button>
          </t-form-item>
        </t-form>

        <t-row :gutter="10" class="mb8">
          <t-col :span="1.5">
            <t-button
              theme="primary"
              variant="outline"
              :disabled="multiple"
              @click="handleRestore"
            >
              <template #icon><t-icon name="refresh" /></template>
              还原
            </t-button>
          </t-col>
          <t-col :span="1.5">
            <t-button
              theme="danger"
              variant="outline"
              :disabled="multiple"
              @click="handleDelete"
            >
              <template #icon><t-icon name="delete" /></template>
              彻底删除
            </t-button>
          </t-col>
          <t-col :span="1.5">
            <t-button theme="primary" variant="outline" @click="goBack">
              <template #icon><t-icon name="arrow-left" /></template>
              返回
            </t-button>
          </t-col>
        </t-row>
      </div>
    </t-card>

    <t-card class="top20">
      <div class="recycle-list">
        <t-table
          row-key="questionId"
          :data="recycleList"
          :loading="loading"
          :columns="columns"
          :selected-row-keys="ids"
          @select-change="handleSelectionChange"
        />

        <t-pagination
          v-if="total > 0"
          v-model:current="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          @change="getList"
        />
      </div>
    </t-card>

    <restore-dialog
      v-model:visible="restoreDialogVisible"
      :question-ids="selectedQuestionIds"
      @close="cancelRestore"
      @submit="confirmRestore"
    />
  </div>
</template>

<script setup name="UserQuestionRecycleBin">
import { MessagePlugin } from 'tdesign-vue-next'
import {
  getCurrentInstance,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch,
} from 'vue'
import { Tooltip } from 'tdesign-vue-next';

import {
  delUserQuestion,
  recycleBinList,
  restoreFromRecycleBin,
} from '@/api/book/userQuestion'
import { DialogPlugin } from 'tdesign-vue-next/esm'

import RestoreDialog from './components/restore.vue'
const { proxy } = getCurrentInstance()
const loading = ref(false)
const showSearch = ref(true)
const ids = ref([])
const multiple = ref(true)
const total = ref(0)
const recycleList = ref([])
const bookId = ref(null)
const restoreDialogVisible = ref(false)
const selectedQuestionIds = ref([])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    questionType: null,
    bookId: null,
  },
})

const { queryParams } = toRefs(data)

const questionTypes = [
  { value: 1, label: '单选题' },
  { value: 2, label: '多选题' },
  { value: 3, label: '填空题' },
  { value: 4, label: '排序题' },
  { value: 5, label: '连线题' },
  { value: 6, label: '简答题' },
  { value: 7, label: '判断题' },
  { value: 8, label: '编程题' },
]

const columns = [
  { colKey: 'row-select', type: 'multiple', width: 55 },
  {
    colKey: 'index',
    title: '序号',
    width: 80,
    cell: (h, { rowIndex }) => {
      return (
        (queryParams.value.pageNum - 1) * queryParams.value.pageSize +
        rowIndex +
        1
      )
    },
  },
  {
    colKey: 'questionType',
    title: '题目类型',
    cell: (h, { row }) => getQuestionTypeName(row.questionType),
  },
  {
    colKey: 'questionContent',
    title: '题目内容',
    cell: (h, { row }) => {
      const decodedContent = safeDecode(row.questionContent);
      // 提取纯文本用于tooltip
      let plainTextContent;
      
      // 使用更可靠的方法完全去除HTML标签
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = decodedContent;
      plainTextContent = tempDiv.textContent || tempDiv.innerText || '';
      
      if (row.questionType === 3) {
        // 对于填空题，移除###标记，只保留答案文本
        plainTextContent = plainTextContent.replace(/###(.*?)###/g, '$1');
      }

      // 处理富文本内容用于v-html渲染
      let richTextContent = decodedContent;
      if (row.questionType === 3) {
        // 对填空题特殊处理，将###answer###替换为带样式的答案
        richTextContent = decodedContent.replace(/###(.*?)###/g, '<span class="blank-answer">$1</span>');
      }

      // 限制tooltip显示的文本长度
      const maxLength = 50;
      let tooltipDisplayText = plainTextContent;
      if (plainTextContent.length > maxLength) {
        tooltipDisplayText = plainTextContent.substring(0, maxLength) + '...';
      }
      
      return h(
        Tooltip, 
        {
          content: plainTextContent, // Full plain text for tooltip
          placement: 'top',
          showOn: ['hover']
        },
        [
          h('div', {
            innerHTML: richTextContent, // 使用innerHTML渲染富文本
            class: 'question-content'
          })
        ]
      );
    }
  },
  {
    colKey: 'folderName',
    title: '所属目录',
    cell: (h, { row }) => row.folderName || '默认目录',
  },
  {
    colKey: 'updateBy',
    title: '删除人',
    width: 120,
  },
  {
    colKey: 'updateTime',
    title: '删除时间',
    width: 180,
    cell: (h, { row }) => parseTime(row.updateTime),
  },
  {
    colKey: 'operate',
    title: '操作',
    width: 180,
    cell: (h, { row }) => [
      h(
        'button',
        {
          class: 't-button t-button--theme-primary t-button--variant-text',
          onClick: () => handleRestore(row),
          style: 'margin-right: 8px',
        },
        [h('t-icon', { name: 'refresh' }), '还原'],
      ),
      h(
        'button',
        {
          class: 't-button t-button--theme-danger t-button--variant-text',
          onClick: () => handleDelete(row),
        },
        [h('t-icon', { name: 'delete' }), '彻底删除'],
      ),
    ],
  },
]

/** 查询回收站列表 */
function getList() {
  loading.value = true
  recycleBinList(queryParams.value)
    .then((response) => {
      recycleList.value = response.rows
      total.value = response.total
      loading.value = false
    })
    .catch((error) => {
      console.error('获取回收站列表失败:', error)
      MessagePlugin.error('获取列表失败')
      //  proxy.$modal.msgError('获取列表失败')
      loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

/** 还原按钮操作 */
function handleRestore(row) {
  selectedQuestionIds.value = row.questionId ? [row.questionId] : ids.value
  restoreDialogVisible.value = true
}

function cancelRestore() {
  restoreDialogVisible.value = false
  selectedQuestionIds.value = []
}

async function confirmRestore(questionIds) {
  try {
    await restoreFromRecycleBin(questionIds)
    getList()
    MessagePlugin.success('还原成功')
    //proxy.$message.success('还原成功')
    restoreDialogVisible.value = false
  } catch (error) {
    console.error('还原失败:', error)
    MessagePlugin.error('还原失败')
    // proxy.$message.error('还原失败')
  }
}

/** 彻底删除按钮操作 */
function handleDelete(row) {
  const questionIds = row.questionId ? [row.questionId] : ids.value
  const confirm = DialogPlugin.confirm({
    header: '确认删除',
    body: '是否确认彻底删除选中的题目？删除后将无法恢复！',
    confirmBtn: {
      content: '确认',
      theme: 'danger',
    },
    cancelBtn: '取消',
    onConfirm: async () => {
      try {
        await delUserQuestion(questionIds)
        getList()
        MessagePlugin.success('删除成功')
        confirm.hide()
      } catch (error) {
        console.error('删除失败:', error)
        MessagePlugin.error('删除失败')
        confirm.hide()
      }
    },
  })
}

/** 多选框选中数据 */
function handleSelectionChange(key,value) {

  let selectData = value.selectedRowData ;
  ids.value = selectData.filter(item=>item!==undefined).map(item => item.questionId);
  multiple.value = !ids.value.length
}

/** 获取题型名称 */
function getQuestionTypeName(type) {
  const questionType = questionTypes.find((item) => item.value === type)
  return questionType ? questionType.label : '未知题型'
}

/** 返回方法 */
function goBack() {
  proxy.$router.push({
    path: '/resourceLibrary/userQuestion',
    query: { bookId: bookId.value },
  })
}

/** 格式化时间 */
function parseTime(time) {
  if (!time) return ''
  const date = new Date(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/** 安全解码内容 */
function safeDecode(content) {
  if (!content) return ''
  try {
    // 检查内容是否已经被编码
    const isEncoded = (str) => {
      try {
        // 尝试解码，如果解码后的结果与原字符串不同，说明是编码过的
        const decoded = decodeURIComponent(str)
        return decoded !== str
      } catch (e) {
        // 如果解码失败，说明可能包含特殊字符，此时返回 false
        return false
      }
    }

    // 如果内容已经被编码，则进行解码
    if (isEncoded(content)) {
      return decodeURIComponent(content)
    }
    
    // 如果内容未被编码或解码失败，直接返回原内容
    return content
  } catch (e) {
    console.error('解码内容失败:', e)
    return content
  }
}

onMounted(() => {
  bookId.value = proxy.$route.query.bookId
  getList()
})

watch(
  () => proxy.$route.query.bookId,
  (newBookId) => {
    if (newBookId !== bookId.value) {
      bookId.value = newBookId
      getList()
    }
  },
)
</script>

<style scoped>
.tool {
  padding: 20px 0 0;
}

.top20 {
  margin-top: 20px;
}

.recycle-list {
  padding: 20px;
}

.question-content {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

:deep(.blank-line) {
  display: inline-block;
  border-bottom: 1px solid #000;
  min-width: 50px;
  text-align: center;
}

:deep(.blank-answer) {
  color: #409EFF;
}
</style>
