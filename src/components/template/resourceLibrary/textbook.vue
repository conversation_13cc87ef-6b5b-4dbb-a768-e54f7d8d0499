<template>

    <div>
        <t-breadcrumb :max-item-width="'150'">
            <t-breadcrumb-item> 资源库 </t-breadcrumb-item>
            <t-breadcrumb-item to="/resourceLibrary/textbook">教材资源</t-breadcrumb-item>
        </t-breadcrumb>
    </div>

    <!-- 添加条件渲染 -->
    <template v-if="!selectedBook">
        <div>
            <div class="textbook-form">
                <t-form :data="queryForm" layout="inline">
                    <t-form-item label="图书名称/ID：">
                        <t-input v-model="queryForm.bookName" placeholder="请输入图书名称/编号" clearable />
                    </t-form-item>
                    <t-form-item label="ISBN/ISSN：">
                        <t-input v-model="queryForm.isbn" placeholder="请输入ISBN/ISSN" clearable />
                    </t-form-item>
                    <t-button @click="handleResourceQuery">
                        <template #icon>
                            <t-icon name="search" />
                        </template>
                        查询
                    </t-button>
                    <t-button theme="default" style="margin-left: 8px;" @click="handleReset">
                        <template #icon>
                            <t-icon name="refresh" />
                        </template>
                        重置
                    </t-button>

                    <!-- 添加排序图标下拉菜单 -->
                    <t-dropdown :options="sortOptions" @click="handleSortChange">
                        <t-button theme="default" style="margin-left: 10px;">
                            排序
                            <template #suffix> <t-icon name="chevron-down" size="16" /></template>
                        </t-button>
                    </t-dropdown>
                </t-form>

            </div>
            <div v-if="bookList && bookList.length > 0" class="book-grid">
                <div v-for="book in bookList" :key="book.bookId" class="book-item" @click="handleBookSelect(book)">
                    <t-card class="book-card" :body-style="{ cursor: 'pointer' }">
                        <div class="book-content">
                            <img :src="book.cover || 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1741232140222.png'"
                                alt="封面" class="book-cover">
                            <div class="book-info">
                                <h3 class="book-title">{{ book.bookName }}</h3>
                                <p><strong>编号：</strong> {{ book.bookNo }}</p>
                                <p><strong>{{ book.authorLabel }}:</strong> {{ book.authorValue }}</p>
                                <p :title="formatSubjectName(book, subjects)"><strong>教育学科分类：</strong> {{
                                    formatSubjectName(book,
                                        subjects) }}</p>
                                <p><strong>ISBN/ISSN：</strong> {{ book.isbn || book.issn }}</p>
                            </div>
                        </div>
                    </t-card>
                </div>
            </div>

            <div>
                <t-pagination :total="total" :page-size="queryForm.pageSize" :current="queryForm.pageNum"
                    @change="handlePageChange" />
            </div>

        </div>
    </template>

    <!-- 添加教材资源组件 -->
    <template v-else>
        <book-resource :book="selectedBook" @back="handleBack" />
    </template>
</template>
<script setup>
import { onMounted, reactive, ref } from 'vue'

import { listForResource } from '@/api/book/book' // 需要导入相关API
import { listTreeEducation } from '@/api/book/subject'
import { formatSubjectName } from "@/utils/subjectUtil";

import BookResource from './bookResource.vue'  // 导入教材资源组件

// 查询参数
const queryForm = reactive({
    bookName: '',
    isbn: '',
    pageNum: 1,
    pageSize: 10
})

// 数据列表
const bookList = ref([])
const total = ref(0)
const loading = ref(false)
const subjects = ref([])

/** 搜索按钮操作 */
function handleResourceQuery() {
    resourceQueryParams.pageNum = 1;
    getBookList();
}

// 添加重置按钮操作
function handleReset() {
    queryForm.bookName = '';
    queryForm.isbn = '';
    queryForm.pageNum = 1;
    currentSort.value = 'createTime_desc';
    getBookList();
}

// 查询参数
const resourceQueryParams = reactive({
    bookNo: null,
    pageNum: 1,
    pageSize: 10,
    bookName: null,
    author: null,
    isbn: null,
    subjectIds: [], // 存储级联选择的值
    top_subject_id: null,
    second_subject_id: null,
    third_subject_id: null,
    forth_subject_id: null
});

// 获取科目列表
const getSubjects = async () => {
    try {
        const response = await listTreeEducation()
        subjects.value = response.rows
    } catch (error) {
        console.error('获取科目列表失败:', error)
    }
}



// 添加排序选项
const sortOptions = [
    { content: '创建时间-正序', value: 'createTime_asc' },
    { content: '创建时间-倒序', value: 'createTime_desc' },
    { content: '图书名称-A-Z', value: 'bookName_asc' },
    { content: '图书名称-Z-A', value: 'bookName_desc' },
];

// 当前排序方式
const currentSort = ref('createTime_desc');

// 排序下拉菜单点击处理
const handleSortChange = ({ value }) => {
    currentSort.value = value;
    getBookList();
}

// 获取书籍列表
const getBookList = async () => {
    try {
        loading.value = true

        const response = await listForResource({
            bookName: queryForm.bookName,
            isbn: queryForm.isbn,
            pageNum: queryForm.pageNum,
            pageSize: queryForm.pageSize,
            orderType: currentSort.value
        })
        bookList.value = response.rows
        total.value = response.total
    } catch (error) {
        console.error('获取教材列表失败:', error)
    } finally {
        loading.value = false
    }
}

// 查询按钮点击事件
const handleQuery = () => {
    queryForm.pageNum = 1
    getBookList()
}

// 分页变化事件
const handlePageChange = (pageInfo) => {
    queryForm.pageNum = pageInfo.current
    queryForm.pageSize = pageInfo.pageSize
    getBookList()
}

onMounted(() => {
    getSubjects()
    getBookList()
})

// 下拉选项点击处理
const clickHandler = ({ value }) => {
    // 这里可以处理排序逻辑
    getBookList()
}



// 添加选中的教材状态
const selectedBook = ref(null)

// 处理教材选择
function handleBookSelect(book) {
    selectedBook.value = book
}

// 处理返回教材列表
function handleBack() {
    selectedBook.value = null
}
</script>

<style lang="less" scoped>
.textbook-form {
    margin: 20px 0;
}

.book-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    /* 控制项目之间的间距 */
    padding: 20px;
    /* 容器的内边距 */
}

.book-item {
    width: calc(33.333% - 20px);
    /* 假设一行3个，减去gap的影响 */
    box-sizing: border-box;

    .book-card {
        width: 100%;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
    }

    .book-content {
        display: flex;
    }

    .book-cover {
        width: 120px; // 根据admin端调整图片大小
        height: 160px; // 根据admin端调整图片大小
        object-fit: cover;
        margin-right: 15px;
        border-radius: 4px;
    }

    .book-info {
        flex: 1;

        .book-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            // 多行省略
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2; // 显示两行
            -webkit-box-orient: vertical;
        }

        p {
            font-size: 13px;
            color: #5e6066;
            margin-bottom: 12px;
            line-height: 1.5;
            // 多行省略
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1; // 显示一行
            -webkit-box-orient: vertical;

            strong {
                color: #303133;
            }
        }
    }
}

// 响应式调整，例如小屏幕下一行显示一个或两个
@media (max-width: 1200px) {
    .book-item {
        width: calc(50% - 20px); // 一行2个
    }
}

@media (max-width: 768px) {
    .book-item {
        width: 100%; // 一行1个
    }

    .book-content {
        flex-direction: column;
        align-items: center;

        .book-cover {
            width: 150px; // 在垂直布局时，图片可以稍大一些
            height: 200px;
            margin-right: 0;
            margin-bottom: 10px;
        }

        .book-info {
            text-align: center;
        }
    }
}

// 移除旧的样式
// .textbook-table{
//     display: flex;
//     flex-wrap: wrap;
//     .textbook-item{
//         margin:20px;
//         padding:20px;
//         cursor: pointer;
//         &:hover{
//             box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
//         }
//         .textbook-item-img{
//             width: 165px;
//             height: 220px;
//             img{
//                 width: 100%;
//                 height: 100%;
//             }
//         }
//         .textbook-item-info{
//             .textbook-item-info-name{
//                 font-size: 18px;
//                 font-weight: bold;
//                 margin: 10px 0;
//             }
//             .textbook-item-info-id{
//                 font-size: 14px;
//                 color: #999;
//                 margin: 10px 0;
//             }
//             .textbook-item-info-case{
//                 font-size: 14px;
//                 color: #999;
//                 margin: 10px 0;
//             }
//             .textbook-item-info-number{
//                 font-size: 14px;
//                 color: #999;
//                 margin: 10px 0;
//             }
//         }
//     }
// }</style>