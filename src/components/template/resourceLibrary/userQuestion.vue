<template>
  <div class="app-container">
    <t-row :gutter="20">
      <!-- 左侧目录 -->
      <t-col :span="3">
        <t-card>
          <Catalog
            :catalogs="catalogList"
            @click-catalog="handleCatalogClick"
            @add-catalog="submitCatalogForm"
            @delete-catalog="handleDeleteCatalog"
            @update-catalog="submitCatalogForm"
            @move-catalog="submitCatalogForm"
            @refresh-catalogs="getCatalogList"
            @search="getCatalogList"
          />
        </t-card>
      </t-col>

      <!-- 右侧内容区 -->
      <t-col :span="9">
        <!-- 搜索和工具栏卡片 -->
        <t-card>
          <div class="tool">
            <t-form
              v-show="showSearch"
              ref="queryRef"
              :model="queryParams"
              layout="inline"
              label-width="68px"
              style="margin-bottom: 20px"
            >
              <t-form-item label="类型" prop="questionType">
                <t-select
                  v-model="queryParams.questionType"
                  placeholder="请选择小题类型"
                  clearable
                  style="width: 150px"
                >
                  <t-option
                    v-for="item in questionTypes"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </t-select>
              </t-form-item>
              <t-form-item label="题目" prop="questionContent">
                <t-input
                  v-model="queryParams.questionContent"
                  placeholder="请输入题目关键词"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </t-form-item>

              <t-button theme="primary" @click="handleQuery">
                <template #icon>
                  <SearchIcon />
                </template>
                搜索
              </t-button>
              <t-button
                theme="default"
                style="margin-left: 20px"
                @click="resetQuery"
              >
                <template #icon>
                  <RefreshIcon />
                </template>
                重置</t-button
              >
            </t-form>

            <t-row :gutter="10" class="mb8">
              <t-col :span="1.5">
                <t-button
                  theme="primary"
                  variant="outline"
                  @click="handleSelectAll"
                >
                  <template #icon>
                    <CheckIcon />
                  </template>
                  全选
                </t-button>
              </t-col>
              <t-col :span="1.5">
                <t-button theme="primary" variant="outline" @click="handleAdd">
                  <template #icon>
                    <PlusIcon />
                  </template>

                  新增</t-button
                >
              </t-col>
              <t-col :span="1.5">
                <t-button
                  theme="success"
                  variant="outline"
                  @click="handleImport"
                >
                  <template #icon>
                    <UploadIcon />
                  </template>
                  导入</t-button
                >
              </t-col>
              <t-col :span="1.5">
                <t-button
                  theme="danger"
                  variant="outline"
                  :disabled="multiple"
                  @click="handleDelete"
                >
                  <template #icon>
                    <DeleteIcon />
                  </template>
                  删除</t-button
                >
              </t-col>
              <t-col :span="1.5">
                <t-button
                  theme="default"
                  variant="outline"
                  @click="goToRecycleBin"
                >
                  <template #icon>
                    <DeleteIcon />
                  </template>
                  回收站</t-button
                >
              </t-col>
              <t-col :offset="8" :span="1.8" style="justify-content: flex-end">
                <t-button
                  theme="default"
                  variant="outline"
                  shape="circle"
                  @click="goshowSearch"
                >
                  <template #icon>
                    <SearchIcon />
                  </template>
                </t-button>
                <t-button
                  theme="default"
                  variant="outline"
                  shape="circle"
                  style="margin-left: 20px"
                  @click="getList"
                >
                  <template #icon>
                    <RefreshIcon />
                  </template>
                </t-button>
              </t-col>
              <!-- <right-toolbar
                v-model:show-search="showSearch"
                @query-table="getList"
              ></right-toolbar> -->
            </t-row>
          </div>
        </t-card>

        <!-- 表格卡片 -->
        <t-card class="top20">
          <div v-loading="loading" class="question-list">
            <div
              v-for="item in bookQuestionList"
              :key="item.questionId"
              class="question-card"
            >
              <div class="question-header">
                <div class="question-info">
                  <t-checkbox
                    v-model="item.selected"
                    class="question-checkbox"
                    @change="handleSelectionChange"
                  ></t-checkbox>
                  <t-tag theme="success" size="small">
                    {{ getQuestionTypeName(item.questionType) }}
                  </t-tag>
                </div>
                <div class="operation">
                  <t-dropdown
                    :options="operationOptions"
                    @click="handleOperationClick($event, item)"
                  >
                    <t-button variant="text" class="more-btn">
                      <t-icon name="more" />
                    </t-button>
                  </t-dropdown>
                </div>
              </div>

              <!-- 题目内容区 -->
              <div class="question-content">
                <!-- 题干 -->
                <div class="content-item">

                  <div class="label" v-if="item.questionRemark"></div>
                  <div
                    class="content"
                    v-html="item.questionRemark"
                  ></div>

                  <div class="label">题干：</div>
                  <div
                    class="content"
                    v-html="
                      formatBlankQuestion(
                        item.questionContent,
                        item.questionType,
                      )
                    "
                  ></div>
                </div>

                <!-- 参考答案区域 -->
                <div
                  v-if="
                    item.rightAnswer &&
                    (item.questionType === 6 || item.questionType === 8)
                  "
                  class="content-item"
                >
                  <div class="label">参考答案：</div>
                  <div class="content" v-html="item.rightAnswer"></div>
                </div>

                <!-- 连线题专用组件 -->
                <div v-if="item.questionType === 5" class="matching-question">
                  <MatchingQuestion
                    :options="item.options"
                    :right-answer="item.rightAnswer"
                    :show-answer="true"
                  />
                </div>

                <!-- 其他题型的选项区域 -->
                <div
                  v-if="
                    shouldShowOptions(item.questionType) &&
                    item.questionType !== 5 &&
                    item.questionType !== 8 &&
                    item.options &&
                    item.options.length
                  "
                  class="options-area"
                >
                  <div
                    v-for="(option, index) in item.questionType == 4
                      ? shuffledOptions(item)
                      : item.options"
                    :key="index"
                    class="option-item"
                  >
                    <div class="option-content">
                      <span class="option-label">
                        {{ `${String.fromCharCode(65 + index)}. ` }}
                      </span>
                      <span v-html="option.optionContent"></span>
                      <t-icon
                        v-if="option.rightFlag"
                        name="check-circle-filled"
                        class="correct-icon"
                      />
                    </div>
                  </div>
                </div>

                <!-- 解析区域 -->
                <div v-if="item.analysis" class="content-item">
                  <div class="label">解析：</div>
                  <div class="content" v-html="item.analysis"></div>
                </div>

                <!-- 编程题相关模板 -->
                <template v-if="item.questionType === 8">
                  <div class="content-item">
                    <div class="label">代码：</div>
                    <code-editor
                      :key="item.code"
                      v-model="item.code"
                      :language="
                        mapLanguageToEditor(item.language) || 'javascript'
                      "
                      :read-only="true"
                    />
                  </div>
                </template>
              </div>
            </div>
          </div>

          <t-pagination
            v-show="total > 0"
            v-model="page"
            :total="total"
            @change="handlePageChange"
          />
        </t-card>

        <!-- 添加或修改数字教材习题对话框 -->
        <t-dialog
          v-model:visible="open"
          :header="title"
          width="800px"
          :footer="false"
        >
          <t-form
            ref="bookQuestionRef"
            :data="form"
            :rules="rules"
            label-width="100px"
            label-align="top"
            @submit="submitForm"
          >
            <t-form-item label="题目类型" name="questionType">
              <t-select
                v-model="form.questionType"
                placeholder="请选择题目类型"
                :disabled="!!form.questionId || viewMode"
                @change="handleQuestionTypeChange"
              >
                <t-option
                  v-for="item in questionTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </t-select>
            </t-form-item>

            
            <t-form-item label="备注" name="questionRemark">
              <t-textarea
                v-model="form.questionRemark"
                placeholder="请输入答题要求"
                :disabled="viewMode"
                :autosize="{ minRows: 2, maxRows: 5 }"
              />
            </t-form-item>
            <t-form-item label="题干" name="questionContent">
              <wang-editor
                :key="editorKey"
                v-model:text-content="form.questionContent"
                :height-props="150"
                :need-t="false"
                @onEditorMounted="(editor) => onEditorMounted(editor, 'questionContent')"
                @update-text-content="(val) => (form.questionContent = val)"
              />
              <div v-if="viewMode" class="editor-overlay"></div>
            </t-form-item>


            <t-form-item >
              <!-- 填空题特有功能 -->
              <div v-if="form.questionType === 3 && !viewMode" class="fill-blanks-actions">
                <br><div class="fill-blanks-item">
                  <t-button theme="primary" variant="outline" @click="appendBlanks">
                    <template #icon><Add-icon /></template>
                    增加填空
                  </t-button>
                </div>
                <div class="fill-blanks-item">
                  <t-checkbox
                    :checked="form.disorder === 2"
                    @change="onMultipleFillInBlanksChange"
                  >
                    允许乱序
                  </t-checkbox>
                </div>
              </div>
            </t-form-item>

            <!-- 选项编辑区域（单选、多选、判断题） -->
            <template v-if="[1, 2, 7].includes(form.questionType)">
              <div class="options-editor" :key="form.questionType">
                <div class="options-header">
                  <span class="subtitle">选项设置</span>
                  <!-- 只在单选和多选题时显示添加选项按钮，且不在查看模式下 -->
                  <t-button
                    v-if="[1, 2].includes(form.questionType) && !viewMode"
                    theme="primary"
                    variant="text"
                    @click="addOption"
                    >添加选项</t-button
                  >
                </div>
                <div
                  v-for="(option, index) in form.options"
                  :key="index"
                  class="option-item"
                >
                  <div v-if="viewMode" class="editor-overlay"></div>
                  <div class="set-option">
                    <div class="set-option-left">
                      <div class="set-option-label">
                        {{ String.fromCharCode(65 + index) }}
                      </div>
                      <!-- 移除判断题的固定文本显示，让所有题型都可以编辑选项 -->
                    </div>

                    <div class="set-option-right" style="display: flex">
                      <t-checkbox
                        v-model="option.rightFlag"
                        :disabled="
                          viewMode ||
                          (form.questionType === 1 &&
                            hasOtherCorrectOption(index))
                        "
                        >正确答案</t-checkbox
                      >
                      <t-button
                        v-if="[1, 2].includes(form.questionType) && !viewMode"
                        theme="danger"
                        variant="text"
                        @click="removeOption(index)"
                      >
                        <t-icon name="delete" />
                      </t-button>
                    </div>
                  </div>
                  <!-- 让所有题型包括判断题都可以使用富文本编辑器 -->
                  <div>
                    <wang-editor
                      :key="`option-${index}-${editorKey}`"
                      v-model:text-content="option.optionContent"
                      :height-props="80"
                      :need-t="false"
                      @update-text-content="
                        (val) => (option.optionContent = val)
                      "
                    />
                  </div>
                </div>
              </div>
            </template>

            <!-- 填空题答案 -->
            <template v-if="form.questionType === 3">
              <t-alert
                v-if="!viewMode"
                theme="info"
                message="在题干中使用 ### 答案 ### 的格式标记填空位置及答案"
                :close="false"
                class="mb-10"
              />
              <div v-else class="content-item">
                <div class="label">答案：</div>
                <div
                  class="content"
                  v-html="
                    formatBlankQuestion(form.questionContent, form.questionType)
                  "
                ></div>
              </div>
            </template>

            <!-- 排序题选项 -->
            <template v-if="form.questionType === 4">
              <div class="options-editor">
                <div class="options-header">
                  <span class="subtitle">排序项设置（请按正确顺序添加）</span>
                  <t-button
                    v-if="!viewMode"
                    theme="primary"
                    variant="text"
                    @click="addOption"
                    >添加选项</t-button
                  >
                </div>
                <t-row
                  v-for="(option, index) in form.options"
                  :key="index"
                  class="option-row"
                >
                  <t-col :span="2">{{ index + 1 }}</t-col>
                  <t-col :span="viewMode ? 22 : 18">
                    <wang-editor
                      v-model:text-content="option.optionContent"
                      :height-props="80"
                      :need-t="false"
                      @update-text-content="
                        (val) => (option.optionContent = val)
                      "
                    />
                    <div v-if="viewMode" class="editor-overlay"></div>
                  </t-col>
                  <t-col v-if="!viewMode" :span="4">
                    <t-space>
                      <t-button
                        :disabled="index === 0"
                        @click="moveOption(index, 'up')"
                      >
                        <t-icon name="chevron-up" />
                      </t-button>
                      <t-button
                        :disabled="index === form.options.length - 1"
                        @click="moveOption(index, 'down')"
                      >
                        <t-icon name="chevron-down" />
                      </t-button>
                      <t-button theme="danger" @click="removeOption(index)">
                        <t-icon name="delete" />
                      </t-button>
                    </t-space>
                  </t-col>
                </t-row>
              </div>
            </template>

            <!-- 连线题选项 -->
            <template v-if="form.questionType === 5">
              <div class="matching-editor">
                <matching-question-picker
                  :key="editorKey"
                  v-model="matchingData"
                  :disabled="viewMode"
                  @update:model-value="handleMatchingUpdate"
                />
                <div v-if="viewMode" class="editor-overlay"></div>
              </div>
            </template>

            <!-- 简答题答案 -->
            <template v-if="form.questionType === 6 || form.questionType === 8">
              <div class="label">参考答案</div>
              <!-- 参考答案编辑器 -->
              <wang-editor
                :key="editorKey"
                v-model:text-content="form.rightAnswer"
                :height-props="120"
                :need-t="false"
                @onEditorMounted="(editor) => onEditorMounted(editor, 'rightAnswer')"
                @update-text-content="(val) => (form.rightAnswer = val)"
              />
              <div v-if="viewMode" class="editor-overlay"></div>
            </template>

            <!-- 解析区域 -->
            <t-form-item label="解析" name="analysis">
              <wang-editor
                :key="editorKey"
                v-model:text-content="form.analysis"
                :height-props="120"
                :need-t="false"
                @onEditorMounted="(editor) => onEditorMounted(editor, 'analysis')"
                @update-text-content="(val) => (form.analysis = val)"
              />
              <div v-if="viewMode" class="editor-overlay"></div>
            </t-form-item>

            <!-- 编程题相关模板 -->
            <template v-if="form.questionType === 8">
              <t-form-item label="编程语言" name="language">
                <t-select
                  v-model="form.language"
                  placeholder="请选择编程语言"
                  :disabled="viewMode"
                >
                  <t-option
                    v-for="item in programmingLanguageList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </t-select>
              </t-form-item>
              <t-form-item label="代码" name="code">
                <code-editor
                  :key="form.language"
                  v-model="form.code"
                  :language="form.language || 'javascript'"
                  :read-only="viewMode"
                  theme="light"
                  style="width: 100%"
                />
              </t-form-item>
            </template>
            <div class="dialog-footer">

              <t-checkbox
                v-if="!viewMode && !form.questionId"
                v-model="continuousAdd"
                style="margin-right: 20px;"
              >
                连续添加
              </t-checkbox>

              <t-button v-if="!viewMode" theme="primary" type="submit"
                >确 定</t-button
              >
              <t-button
                theme="default"
                style="margin-left: 20px"
                @click="cancel"
                >{{ viewMode ? '关 闭' : '取 消' }}</t-button
              >
      
            </div>
          </t-form>
        </t-dialog>

        <!-- 添加或修改目录对话框 -->
        <t-dialog v-model:visible="catalogOpen" :header="title" width="400px">
          <t-form
            ref="catalogRef"
            :model="catalogForm"
            :rules="catalogRules"
            label-width="100px"
          >
            <t-form-item label="目录名称" prop="folderName">
              <t-input
                v-model="catalogForm.folderName"
                placeholder="请输入目录名称"
                maxlength="50"
                show-word-limit
              />
            </t-form-item>
          </t-form>

          <div class="dialog-footer">
            <t-button theme="primary" @click="submitCatalogForm"
              >确 定</t-button
            >
            <t-button @click="cancelCatalog">取 消</t-button>
          </div>
        </t-dialog>

        <!-- 移动目录对话框 -->
        <t-dialog
          v-model:visible="moveCatalogDialogVisible"
          header="移动到"
          width="400px"
          :footer="false"
        >
          <t-form ref="moveFormRef" :model="moveForm" label-width="80px">
            <t-tree-select
              v-model="moveForm.parentId"
              :data="folderTreeOptions"
              :keys="{ value: 'folderId', label: 'folderName' }"
              clearable
              placeholder="请选择目标目录"
            />
            <div class="dialog-footer">
              <t-button theme="primary" @click="submitMove">确 定</t-button>
              <t-button
                theme="default"
                style="margin-left: 20px"
                @click="moveCatalogDialogVisible = false"
                >取 消</t-button
              >
            </div>
          </t-form>
        </t-dialog>

        <!-- 移动题目对话框 -->
        <t-dialog
          v-model:visible="moveQuestionDialogVisible"
          header="移动到"
          width="400px"
          :footer="false"
        >
          <t-form
            ref="moveQuestionFormRef"
            :data="moveQuestionForm"
            label-width="80px"
            @submit="submitMoveQuestion"
          >
            <t-tree-select
              v-model="moveQuestionForm.folderId"
              :data="folderTreeOptions"
              :keys="{ value: 'folderId', label: 'folderName' }"
              clearable
              placeholder="请选择目标目录"
            />
            <div class="dialog-footer">
              <t-button theme="primary" type="submit">确 定</t-button>
              <t-button
                theme="default"
                style="margin-left: 20px"
                @click="moveQuestionDialogVisible = false"
                >取 消</t-button
              >
            </div>
          </t-form>
        </t-dialog>

        <t-dialog
          v-model:visible="importDialogVisible"
          header="导入题目"
          width="500px"
          :footer="false"
        >
          <t-form
            ref="importFormRef"
            :model="importForm"
            :rules="importRules"
            label-width="100px"
          >
            <div class="template-download mb-10">
              <t-button
                v-if="importForm.importType === 'normal'"
                theme="primary"
                variant="text"
                @click="downloadTemplate('normal')"
              >
                <template #icon><t-icon name="download" /></template>
                下载普通题型模板
              </t-button>
              <t-button
                v-if="importForm.importType === 'matching'"
                theme="primary"
                variant="text"
                @click="downloadTemplate('matching')"
              >
                <template #icon><t-icon name="download" /></template>
                下载连线题模板
              </t-button>
            </div>

            <t-form-item label="导入类型" prop="importType">
              <t-radio-group v-model="importForm.importType">
                <t-radio value="normal">普通题型</t-radio>
                <t-radio value="matching">连线题</t-radio>
              </t-radio-group>
            </t-form-item>

            <t-upload
              :auto-upload="true"
              :files="importList"
              accept=".xlsx,.xls"
              :request-method="handleImportChange"
            >
              <t-button theme="primary">选择文件</t-button>
              <template #tip>
                <p class="t-upload__tip">请上传Excel格式文件，且不超过10MB</p>
              </template>
            </t-upload>

            <!-- 文件列表展示 -->
            <div class="preview-list">
              <t-card
                v-for="(file, index) in importList"
                :key="index"
                class="import-item"
              >
                <div class="import-item-content">
                  <div class="import-info">
                    <t-icon name="file" />
                    <span class="import-name">{{ file.name }}</span>
                  </div>
                  <t-button
                    theme="danger"
                    variant="text"
                    @click="removeImportFile(index)"
                  >
                    删除
                  </t-button>
                  <t-progress
                    v-if="file.uploadProgress > 0 && file.uploadProgress < 100"
                    :percentage="file.uploadProgress"
                    :label="(percent) => `${percent}%`"
                    status="success"
                  />
                </div>
              </t-card>
            </div>
          </t-form>

          <div class="dialog-footer">
            <t-button theme="default" @click="importDialogVisible = false"
              >取 消</t-button
            >
            <t-button
              style="margin-left: 5px"
              theme="primary"
              :disabled="!isAllUploaded(importList)"
              @click="submitImportForm"
            >
              确 定
            </t-button>
          </div>
        </t-dialog>

        <!-- 添加删除确认对话框组件 -->
        <Remove
          v-model:visible="removeDialogVisible"
          :form="removeForm"
          @close="handleRemoveClose"
          @submit="handleRemoveConfirm"
        />
        <DeleteModal v-model="deleteModalVisible" @confirm="confirmDelete" />
      </t-col>
    </t-row>
  </div>
</template>

<script setup name="BookQuestion">
import {
  CheckIcon,
  DeleteIcon,
  PlusIcon,
  RefreshIcon,
  SearchIcon,
  UploadIcon,
  AddIcon,
} from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import * as XLSX from 'xlsx'
import { isHtmlTag } from '@/utils/matchUtil'

import {
  addUserQuestion,
  getUserQuestion,
  importQuestions,
  listUserQuestionWithOptions,
  moveToRecycleBin,
  updateUserQuestion,
  checkPaperReference
} from '@/api/book/userQuestion'
import {
  addFolder,
  delFolder,
  listFolder,
  updateFolder,
} from '@/api/book/userQuestionFolder'
import DeleteModal from '@/components/DeleteModal/index.vue'
import Catalog from '@/components/template/resourceLibrary/components/catalog/index.vue'
import CodeEditor from '@/components/template/resourceLibrary/components/CodeEditor/index.vue'
import MatchingQuestion from '@/components/template/resourceLibrary/components/MatchingQuestion.vue'
import MatchingQuestionPicker from '@/components/template/resourceLibrary/components/MatchingQuestionPicker.vue'
import Remove from '@/components/template/resourceLibrary/components/remove.vue'
import WangEditor from '@/components/wangEditor/index.vue'
import { programmingLanguageList } from '@/utils/quetions-utils.ts'

const { proxy } = getCurrentInstance()
const bookQuestionRef = ref(null)
const bookQuestionList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const page = ref({
  current: 1,
  pageSize: 10,
})

const importDialogVisible = ref(false)
const importList = ref([])
const importForm = ref({
  file: null,
  fileList: [],
  importType: 'normal', // 默认为普通题型
})

const data = reactive({
  form: {
    questionType: 1, // 改为数字类型
    questionContent: '',
    disorder: 1,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    folderId: null,
    questionType: null,
    questionContent: null,
    rightAnswer: null,
    analysis: null,
    chapterId: null,
    bookId: null,
    disorder: null,
    sort: null,
  },
  rules: {
    questionType: [
      {
        required: true,
        message: '题目类型不能为空',
        trigger: 'change',
      },
    ],
    disorder: [
      {
        required: true,
        message: '顺序不能为空',
        trigger: 'blur',
      },
    ],
  },
})

const { queryParams, form, rules } = toRefs(data)

// 在导入语句后添加题型常量
const questionTypes = [
  { value: 1, label: '单选题' },
  { value: 2, label: '多选题' },
  { value: 3, label: '填空题' },
  { value: 4, label: '排序题' },
  { value: 5, label: '连线题' },
  { value: 6, label: '简答题' },
  { value: 7, label: '判断题' },
  { value: 8, label: '编程题' },
]

// 显示form 表单对话框
function goshowSearch(data) {
  showSearch.value = !showSearch.value
}

/** 查询数字教材习题列表 */
function getList() {
  loading.value = true
  listUserQuestionWithOptions(queryParams.value).then((response) => {
    bookQuestionList.value = response.rows

    // 处理编程题的代码内容
    bookQuestionList.value = bookQuestionList.value.map((item) => {
      if (item.questionType === 8) {
        try {
          item.code = getCodeContent(item.codeContent) || ''
          item.language =
            getProgrammingLanguage(item.codeContent) || 'javascript'
        } catch (e) {
          console.error('解析编程题内容失败:', e)
          item.code = ''
          item.language = 'javascript'
        }
      }
      return item
    })

    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  viewMode.value = false
  reset()
}

// 表单重置
function reset(questionType) {
  form.value = {
    questionId: null,
    questionType: questionType || 1,
    questionContent: '',
    rightAnswer: '',
    analysis: '',
    chapterId: null,
    bookId: null,
    disorder: 1,
    sort: 0,
    folderId: queryParams.value.folderId || null,
    questionRemark: '', // 添加备注字段
    options:  [
      {
        optionContent: '',
        rightFlag: false,
        optionType: null,
        optionIndex: 0,
      },
      {
        optionContent: '',
        rightFlag: false,
        optionType: null,
        optionIndex: 1,
      },
      {
        optionContent: '',
        rightFlag: false,
        optionType: null,
        optionIndex: 2,
      },
      {
        optionContent: '',
        rightFlag: false,
        optionType: null,
        optionIndex: 3,
      },
    ],
    language: 'javascript',
  }

  matchingData.value = {
    leftOptions: [],
    rightOptions: [],
    matchingPairs: [],
  }

  // 使用新的函数初始化选项
  handleQuestionTypeChange(form.value.questionType)

  // 重置表单校验状态
  nextTick(() => {
    bookQuestionRef.value.reset()
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange() {
  ids.value = bookQuestionList.value
    .filter((item) => item.selected)
    .map((item) => item.questionId)
  multiple.value = ids.value.length === 0
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  form.value.folderId = queryParams.value.folderId // 设置当前选中的目录ID
  open.value = true
  viewMode.value = false
  title.value = '添加数字教材习题'

  // 更新编辑器key，强制重新渲染
  editorKey.value = Date.now()
}

/** 提交按钮 */
function submitForm({ validateResult }) {
  if (validateResult === true) {
    const formData = { ...form.value }
    formData.folderId = queryParams.value.folderId || formData.folderId || 0

    // 验证题干不能为空
    if (!formData.questionContent || formData.questionContent.trim() === '') {
      MessagePlugin.error('题干不能为空')
      return
    }

    // 根据题型验证必填项
    switch (formData.questionType) {
      case 1: // 单选题
      case 2: // 多选题
        if (!formData.options || formData.options.length < 2) {
          MessagePlugin.error('请至少添加两个选项')
          return
        }
        // 验证是否有正确答案
        const hasCorrectAnswer = formData.options.some(option => option.rightFlag)
        if (!hasCorrectAnswer) {
          MessagePlugin.error('请选择正确答案')
          return
        }
        break
      case 3: // 填空题
        if (!formData.questionContent.includes('###')) {
          MessagePlugin.error('请在题干中使用 ### 标记填空位置')
          return
        }
        // 新增 HTML 标签校验
        if (isHtmlTag(formData.questionContent)) {
          MessagePlugin.error('填空位置不能包含 样式，请使用纯文本')
          return
        }
        break
      case 4: // 排序题
        if (!formData.options || formData.options.length < 2) {
          MessagePlugin.error('请至少添加两个排序项')
          return
        }
        break
      case 5: // 连线题
        if (!matchingData.value.leftOptions.length || !matchingData.value.rightOptions.length) {
          MessagePlugin.error('请添加连线题的左右选项')
          return
        }
        if (!matchingData.value.matchingPairs.length) {
          MessagePlugin.error('请设置连线关系')
          return
        }
        break
      case 6: // 简答题
      case 8: // 编程题
        if (!formData.rightAnswer || formData.rightAnswer.trim() === '') {
          MessagePlugin.error('参考答案不能为空')
          return
        }
        break
      case 7: // 判断题
        if (!formData.options || formData.options.length !== 2) {
          MessagePlugin.error('判断题必须包含正确和错误两个选项')
          return
        }
        const hasJudgeAnswer = formData.options.some(option => option.rightFlag)
        if (!hasJudgeAnswer) {
          MessagePlugin.error('请选择正确答案')
          return
        }
        break
    }

    // 处理选项数据，确保保持正确的顺序
    if (formData.options) {
      formData.options = formData.options.map((option, index) => ({
        ...option,
        optionContent: safeEncode(option.optionContent || ''),
        rightFlag: option.rightFlag ? 1 : 0,
        optionIndex: index, // 添加 optionIndex 确保顺序
        sort: index, // 添加 sort 确保顺序
      }))
    }

    // 处理编程题的代码内容
    if (formData.questionType === 8) {
      formData.codeContent = safeEncode(
        JSON.stringify({
          code: formData.code || '',
          language: formData.language || 'javascript',
        }),
      )
      formData.code = ''
    }

    // URL编码富文本内容
    formData.questionContent = safeEncode(formData.questionContent || '')
    formData.analysis = safeEncode(formData.analysis || '')
    formData.rightAnswer = safeEncode(formData.rightAnswer || '')

    // 处理连线题
    if (formData.questionType === 5) {
      // 重置选项和答案
      formData.options = []

      // 处理左侧选项
      matchingData.value.leftOptions.forEach((left, index) => {
        formData.options.push({
          optionContent: safeEncode(left.content || ''),
          optionPosition: '1',
          sort: index,
        })
      })

      // 处理右侧选项
      matchingData.value.rightOptions.forEach((right, index) => {
        formData.options.push({
          optionContent: safeEncode(right.content || ''),
          optionPosition: '2',
          sort: index,
        })
      })

      // 处理连线对
      formData.rightAnswer = JSON.stringify(
        matchingData.value.matchingPairs.map((pair) => ({
          source: pair.left.id,
          target: pair.right.id,
        })),
      )
    }

    if (formData.questionId != null) {
      updateUserQuestion(formData).then((response) => {
        MessagePlugin.success('修改成功!')
        open.value = false
        getList()
      })
    } else {
      addUserQuestion(formData).then((response) => {
        MessagePlugin.success('新增成功')
        if (continuousAdd.value) {
          // 保留题目类型，重置其他数据
          const currentType = form.value.questionType
          reset(currentType)
          // 重新初始化选项
          handleQuestionTypeChange(currentType)
          // 更新所有编辑器key，强制重新渲染
          editorKey.value = Date.now()
          // 确保题目类型被正确设置
          nextTick(() => {
            form.value.questionType = currentType
            // 清空选项内容
            if (form.value.options) {
              form.value.options = form.value.options.map(option => ({
                ...option,
                optionContent: '',
                rightFlag: false
              }))
            }
          })
        } else {
          open.value = false
        }
        getList()
      })
    }
  }
}

// 添加一个处理删除流程的函数
function handleDeleteProcess(row) {
  if (row?.questionId) {
    // 单个删除
    currentDeleteRow.value = row
    deleteModalVisible.value = true
  } else {
    // 批量删除
    const selectedQuestions = bookQuestionList.value.filter(
      (item) => item.selected,
    )
    if (selectedQuestions.length === 0) {
      MessagePlugin.warning('请选择要删除的题目')
      return
    }
    currentDeleteRow.value = {
      type: 'batch',
      questions: selectedQuestions,
    }
    deleteModalVisible.value = true
  }
}

/** 删除按钮操作 */
function handleDelete(row) {

  let selectedQuestions = []
  if(row.questionId){
    selectedQuestions = [row.questionId]
  }else{
    selectedQuestions = bookQuestionList.value.filter(
      (item) => item.selected,
    ).map(item=>item.questionId)
  }


  // 检查题目是否被试卷占用
  checkPaperReference(selectedQuestions).then(response => {
    if (response.code === 200 && response.data === true) {
      // 如果被占用，显示确认弹窗
     let dialog = DialogPlugin.confirm({
        header: '警告',
        body: '该题目已被试卷占用，删除后试卷将缺少题目，确定要删除吗？',
        confirmBtn: {
          theme: 'danger',
          content: '确认删除',
        },
        cancelBtn: '取消',
        onConfirm: () => {
          // 用户确认删除，继续删除流程
          dialog.destroy()
          handleDeleteProcess(row)
        }
      })
    } else {
      // 如果没有被占用，直接进入删除流程
      handleDeleteProcess(row)
    }
  })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'book/bookQuestion/export',
    {
      ...queryParams.value,
    },
    `bookQuestion_${new Date().getTime()}.xlsx`,
  )
}

// 添加目录相关数据和方法
const catalogList = ref([])

// 获取目录列表
async function getCatalogList(searchForm) {
  try {
    // 处理搜索表单
    const queryForm = searchForm || {}
    queryForm.pageSize = 999
    const response = await listFolder(queryForm)
    const folders = response.rows || []

    // 如果没有目录且不是搜索状态，创建默认目录
    if (folders.length === 0 && !searchForm?.folderName) {
      const defaultCatalog = {
        folderName: '默认目录',
        parentId: 0,
        orderNum: 1,
      }

      await addFolder(defaultCatalog)
      // 重新获取目录列表
      const newResponse = await listFolder()
      const newFolders = newResponse.rows || []

      // 更新状态
      catalogList.value = buildTreeData(newFolders)
      folderTreeData.value = newFolders

      // 设置默认选中第一个目录
      if (newFolders.length > 0) {
        const firstFolder = newFolders[0]
        queryParams.value.folderId = firstFolder.folderId
        handleCatalogClick(firstFolder)
        getList()
      }
    } else {
      // 更新状态
      catalogList.value = buildTreeData(folders)
      folderTreeData.value = folders

      // 如果没有选中的目录，默认选中第一个
      if (!queryParams.value.folderId && folders.length > 0) {
        const firstFolder = folders[0]
        queryParams.value.folderId = firstFolder.folderId
        handleCatalogClick(firstFolder)
        getList()
      }
    }

    // 构建移动目录的树形选项
    buildFolderTree()
  } catch (error) {
    console.error('获取目录数据失败:', error)
    MessagePlugin.error('获取目录数据失败')
    //proxy.$modal.msgError('获取目录数据失败')
  }
}

// 构建树形数据的辅助函数
function buildTreeData(folders) {
  const map = {}
  const tree = []

  // 创建所有节点的映射
  folders.forEach((folder) => {
    map[folder.folderId] = {
      ...folder,
      children: [],
    }
  })

  // 构建树形结构
  folders.forEach((folder) => {
    const node = map[folder.folderId]
    if (!folder.parentId || folder.parentId === '0' || folder.parentId === 0) {
      tree.push(node)
    } else {
      const parent = map[`${folder.parentId}`] || map[folder.parentId]
      if (parent) {
        parent.children.push(node)
      }
    }
  })

  return tree
}

// 优化移动目录树的构建
function buildFolderTree() {
  if (!folderTreeData.value) return

  // 构建树形结构
  const tree = buildTreeData(folderTreeData.value)

  // 更新树形选项
  folderTreeOptions.value = [
    {
      folderId: '0',
      folderName: '根目录',
      children: tree,
    },
  ]
}

// 获取所有子目录ID
function getChildFolderIds(parentId) {
  const childIds = []
  const findChildren = (pid) => {
    folderTreeData.value.forEach((folder) => {
      if (folder.parentId === pid) {
        childIds.push(folder.folderId)
        findChildren(folder.folderId)
      }
    })
  }
  findChildren(parentId)
  return childIds
}

// 提交移动操作
async function submitMove() {


  if (moveForm.value.parentId) {
    try {
      const targetFolderId =
        moveForm.value.parentId[moveForm.value.parentId.length - 1]

      await updateFolder({
        folderId: moveForm.value.currentId,
        parentId: targetFolderId,
      })
      MessagePlugin.success('移动成功')
      //proxy.$modal.msgSuccess('移动成功')
      moveCatalogDialogVisible.value = false
      getCatalogList()
    } catch (error) {
      MessagePlugin.error(`移动失败：${error.message}`)
      // proxy.$modal.msgError(`移动失败：${error.message}`)
    }
  } else {
    MessagePlugin.warning('请选择目标目录')
    //proxy.$modal.msgWarning('请选择目标目录')
  }
}

const matchingData = ref({
  leftOptions: [],
  rightOptions: [],
  matchingPairs: [],
})

const handleMatchingUpdate = (newValue) => {
  // 更新表单中的连线题数据
  form.value.options = []
  form.value.rightAnswer = []

  // 处理左侧选项
  newValue.leftOptions.forEach((left, index) => {
    form.value.options.push({
      optionContent: left.content,
      optionPosition: '1',
      sort: index,
    })
  })

  // 处理右侧选项
  newValue.rightOptions.forEach((right, index) => {
    form.value.options.push({
      optionContent: right.content,
      optionPosition: '2',
      sort: index,
    })
  })

  // 处理连线对
  form.value.rightAnswer = JSON.stringify(
    newValue.matchingPairs.map((pair) => ({
      source: pair.left.id,
      target: pair.right.id,
    })),
  )
}

// 在 script setup 中添加获取题型名称的方法
const getQuestionTypeName = (type) => {
  const questionType = questionTypes.find((item) => item.value === type)
  return questionType ? questionType.label : '未知题型'
}

// 添加判断是否显示选项的方法
const shouldShowOptions = (questionType) => {
  // 填空题(type=3)不显示选项
  return questionType !== 3
}

// 添加处理填空题格式的方法
const formatBlankQuestion = (content, questionType) => {
  if (questionType !== 3) return content

  // 将 ### answer ### 格式转换为带下划线和答案的HTML
  return content.replace(/###(.*?)###/g, (match, answer) => {
    return `<span class="blank-line"><span class="blank-answer">${answer}</span></span>`
  })
}

// 首先在组件顶部添加一个编辑器实例的 Map
const editorInstanceMap = ref(new Map())

// 修改 onEditorMounted 函数
function onEditorMounted(editor, key) {

  // 将编辑器实例存储到 Map 中
  editorInstanceMap.value.set(key, editor)

}

// 在组件销毁时清理编辑器实例
onBeforeUnmount(() => {
  editorInstanceMap.value.clear()
})

// 添加选项
function addOption() {
  if (!form.value.options) {
    form.value.options = []
  }
  form.value.options.push({
    optionContent: '',
    rightFlag: false,
  })
}

// 移除选项
function removeOption(index) {
  form.value.options.splice(index, 1)
}

// 移动选项位置
function moveOption(index, direction) {
  const { options } = form.value
  if (direction === 'up' && index > 0) {
    ;[options[index], options[index - 1]] = [options[index - 1], options[index]]
  } else if (direction === 'down' && index < options.length - 1) {
    ;[options[index], options[index + 1]] = [options[index + 1], options[index]]
  }
}

// 检查是否有其他正确选项（用于单选题）
function hasOtherCorrectOption(currentIndex) {
  return form.value.options.some(
    (option, index) => index !== currentIndex && option.rightFlag,
  )
}

// 添加连线题选项
function addMatchingOption(side) {
  const target = side === 'left' ? 'leftOptions' : 'rightOptions'
  if (!form.value[target]) {
    form.value[target] = []
  }
  form.value[target].push({ content: '' })
}

// 移除连线题选项
function removeMatchingOption(side, index) {
  const target = side === 'left' ? 'leftOptions' : 'rightOptions'
  form.value[target].splice(index, 1)
}

// 目录相关的响应式变量
const catalogOpen = ref(false)
const moveCatalogDialogVisible = ref(false)
const catalogForm = ref({
  folderId: null,
  folderName: null,
  parentId: 0,
})
const moveForm = ref({
  currentId: null,
  parentId: null,
})
const folderTreeOptions = ref([])
const folderTreeData = ref([])

const catalogRules = {
  folderName: [
    { required: true, message: '目录名称不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
  ],
}

// 取消目录对话框
function cancelCatalog() {
  catalogOpen.value = false
  resetCatalogForm()
}

// 重置目录表单
function resetCatalogForm() {
  catalogForm.value = {
    folderId: null,
    folderName: null,
    parentId: 0,
  }
  proxy.resetForm('catalogRef')
}

// 在组件挂载时获取目录列表
onMounted(() => {
  getCatalogList()
})

// 添加处理目录点击的函数
function handleCatalogClick(catalog) {
  if (!catalog) return

  // 更新当前选中的目录ID
  if (catalog.data) {
    queryParams.value.folderId = catalog.data.folderId || catalog.data.catalogId
  } else {
    queryParams.value.folderId = catalog.folderId || catalog.catalogId
  }

  // 重置分页到第一页
  queryParams.value.pageNum = 1

  // 获取该目录下的题目列表
  getList()
}

function handleDeleteCatalog(data) {
  delFolder(data.folderId).then(() => {
    getCatalogList()
    MessagePlugin.success('删除成功')
    // proxy.$modal.msgSuccess('删除成功')
  })
}

function handleMoveCatalog(data) {
  moveForm.value.currentId = data.folderId
  moveForm.value.parentId = null
  moveCatalogDialogVisible.value = true
  buildFolderTree()

  // 禁用根目录和当前目录
  disableRootAndCurrentDirectory(data.folderId)
}

// 提交目录表单
function submitCatalogForm(formData) {
  // formData 包含了必要的字段，直接使用
  if (formData.folderId != null) {
    updateFolder(formData).then((response) => {
      MessagePlugin.success('修改成功')
      //proxy.$modal.msgSuccess('修改成功')
      getCatalogList()
    })
  } else {
    addFolder(formData).then((response) => {
      MessagePlugin.success('新增成功')
      // proxy.$modal.msgSuccess('新增成功')
      getCatalogList()
    })
  }
}

// 添加解析编程题内容的辅助函数
const getCodeContent = (questionContent) => {
  try {
    const codeData = JSON.parse(safeDecode(questionContent || '{}'))
    return codeData.code || ''
  } catch (e) {
    console.error('解析代码内容失败:', e)
    return ''
  }
}

const getProgrammingLanguage = (questionContent) => {
  try {
    const codeData = JSON.parse(safeDecode(questionContent || '{}'))
    return codeData.language || 'python'
  } catch (e) {
    console.error('解析编程语言失败:', e)
    return 'python'
  }
}

function handleImport() {
  importDialogVisible.value = true
  importList.value = []
  importForm.value = {
    file: null,
    fileList: [],
    importType: 'normal',
  }
}

const handleImportChange = (file) => {


  return new Promise((resolve, reject) => {
    // 检查文件类型
    const isExcel =
      file.raw.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.raw.type === 'application/vnd.ms-excel'
    if (!isExcel) {
      MessagePlugin.error('只能上传Excel文件！')
      //proxy.$modal.msgError('只能上传Excel文件！')
      reject(new Error('只能上传Excel文件！'))
      return
    }
    // 检查文件大小
    const isLt10M = file.raw.size / 1024 / 1024 < 10
    if (!isLt10M) {
      MessagePlugin.error('文件大小不能超过 10MB!')
      //proxy.$modal.msgError('文件大小不能超过 10MB!')
      reject(new Error('文件大小不能超过 10MB!'))
      return
    }

    file.uploadProgress = 0
    importList.value.push(file)

    // 模拟上传进度
    let progress = 0
    const progressInterval = setInterval(() => {
      progress += 10
      file.uploadProgress = Math.min(progress, 100)

      if (progress >= 100) {
        clearInterval(progressInterval)
        resolve({ status: 'success', response: { data: file } })
      }
    }, 300)
  })
}

const removeImportFile = (index) => {
  importList.value.splice(index, 1)
}

const submitImportForm = async () => {
  if (importList.value.length === 0) {
    MessagePlugin.warning('请选择要导入的文件')
    //proxy.$modal.msgWarning('请选择要导入的文件')
    return
  }

  try {
    const allQuestionData = [] // 收集所有题目数据

    for (const file of importList.value) {
      const formData = new FormData()
      formData.append('file', file.raw)
      formData.append('importType', importForm.value.importType)
      formData.append('folderId', queryParams.value.folderId)

      // 读取Excel文件内容
      const workbook = await readExcelFile(file.raw)
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const rows = XLSX.utils.sheet_to_json(worksheet)

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i]

        // 根据导入类型处理不同的模板
        if (importForm.value.importType === 'matching') {
          // 连线题模板处理
          if (!row['题干'] || !row['选项']) {
            console.warn('跳过无效行:', row)
            continue
          }

          const questionData = {
            questionType: 5, // 连线题类型
            questionContent: safeEncode(row['题干'] || ''),
            analysis: safeEncode(row['解析'] || ''),
            folderId: queryParams.value.folderId,
            options: [],
          }

          const leftOptions = []
          const rightOptions = []

          // 处理左侧选项
          for (let j = 'A'.charCodeAt(0); j <= 'E'.charCodeAt(0); j++) {
            const letter = String.fromCharCode(j)
            const content = row[`左侧项-${letter}`]
            if (content) {
              leftOptions.push({
                optionContent: safeEncode(content),
                optionPosition: '1',
                sort: j - 'A'.charCodeAt(0),
              })
            }
          }

          // 处理右侧选项
          for (let j = 1; j <= 5; j++) {
            const content = row[`右侧项-${j}`]
            if (content) {
              rightOptions.push({
                optionContent: safeEncode(content),
                optionPosition: '2',
                sort: j - 1,
              })
            }
          }

          // 合并所有选项
          questionData.options = [...leftOptions, ...rightOptions]

          // 处理连线答案
          const connections = (row['选项'] || '').split(',').map((pair) => {
            const [left, right] = pair.split('-')
            return {
              source: left.charCodeAt(0) - 'A'.charCodeAt(0),
              target: parseInt(right) - 1,
            }
          })

          questionData.rightAnswer = JSON.stringify(connections)
          allQuestionData.push(questionData)
        } else {
          // 普通题型模板处理
          if (!row['题型'] || !row['题干']) {
            console.warn('跳过无效行:', row)
            continue
          }

          const questionData = {
            questionType: getQuestionTypeValue(row['题型']),
            questionContent: safeEncode(row['题干'] || ''),
            rightAnswer: safeEncode(row['正确答案'] || ''),
            analysis: safeEncode(row['答案解析'] || ''),
            folderId: queryParams.value.folderId,
            options: [],
          }

          // 新增填空题校验
          if (questionData.questionType === 3) {
            // 检查是否有 ### 标记
            if (!row['题干']?.includes('###')) {
              MessagePlugin.error(`第${i + 1}行题目(填空题)的题干中缺少 ### 标记`)
              return
            }
            // 检查 HTML 标签
            if (isHtmlTag(row['题干'])) {
              MessagePlugin.error(`第${i + 1}行题目(填空题)的填空位置不能包含样式，请使用纯文本`)
              return
            }
          }

          // 处理普通题型的选项
          if ([1, 2, 4, 7].includes(questionData.questionType)) {
            // 保持原有的选项处理逻辑
            if (questionData.questionType === 7) {
              // 判断题 - 验证正确答案是否为"正确"或"错误"
              const validAnswers = ['正确', '错误']
              if (!validAnswers.includes(row['正确答案'])) {
                MessagePlugin.error(
                  `第${i + 1}行题目(判断题)的正确答案必须是"正确"或"错误"，当前值为: ${row['正确答案']}`,
                )
                return // 跳过当前行
              }

              // 判断题
              questionData.options = [
                {
                  optionContent: safeEncode('正确'),
                  rightFlag: row['正确答案'] === '正确' ? 1 : 0,
                  optionIndex: 0,
                },
                {
                  optionContent: safeEncode('错误'),
                  rightFlag: row['正确答案'] === '错误' ? 1 : 0,
                  optionIndex: 1,
                },
              ]
            } else if (questionData.questionType === 4) {
              // 排序题
              const sortedOptions = []
              for (let j = 1; j <= 5; j++) {
                const optionContent = row[`选项${j}`]
                if (optionContent) {
                  sortedOptions.push({
                    optionContent: safeEncode(optionContent),
                    rightFlag: 0,
                    optionIndex: j - 1,
                    sort: j - 1,
                  })
                }
              }
              questionData.options = sortedOptions
            } else {
              // 单选题和多选题
              for (let j = 1; j <= 5; j++) {
                const optionContent = row[`选项${j}`]
                if (optionContent) {
                  const rightAnswer = row['正确答案'] || ''
                  const rightFlag = rightAnswer.includes(
                    String.fromCharCode(64 + j),
                  )

                  questionData.options.push({
                    optionContent: safeEncode(optionContent),
                    rightFlag: rightFlag ? 1 : 0,
                    optionIndex: j - 1,
                  })
                }
              }
            }
          }
          allQuestionData.push(questionData)
        }

        file.uploadProgress = Math.floor(((i + 1) / rows.length) * 100)
      }
    }

    // 一次性提交所有题目数据
    await importQuestions(allQuestionData).then((response) => {
      MessagePlugin.success(response.msg)
      //proxy.$modal.msgSuccess(response.msg)
      importDialogVisible.value = false
      importList.value = []
      getList()
    })
  } catch (error) {
    console.error('导入失败:', error)

    // proxy.$modal.msgError(
    //   `导入失败：${error.message || '请检查文件格式是否正确'}`,
    // )
  }
}

const isAllUploaded = (list) => {
  return (
    list.length > 0 &&
    list.every((file) => !file.uploadProgress || file.uploadProgress === 100)
  )
}

// 添加下载模板方法
function downloadTemplate(type) {
  const templateName =
    type === 'matching' ? '连线题模板.xlsx' : '普通题模版.xlsx'
  window.location.href = `/files/${templateName}`
}

// 添加读取Excel文件的辅助函数
const readExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        resolve(workbook)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = (error) => reject(error)
    reader.readAsArrayBuffer(file)
  })
}

// 修改题型转换函数，利用已有的 questionTypes 数组
const getQuestionTypeValue = (typeText) => {
  const questionType = questionTypes.find((type) => type.label === typeText)
  return questionType ? questionType.value : 1 // 默认返回单选题类型
}

// 添加处理查看按钮的函数
function handleView(row) {
  handleQuestionData(row, true)
}

/** 修改按钮操作 */
function handleUpdate(row) {
  handleQuestionData(row, false)
}

// 整合的数据处理函数
function handleQuestionData(row, isViewMode) {
  reset()
  viewMode.value = isViewMode
  const questionId = row.questionId || ids.value

  getUserQuestion(questionId).then((response) => {
    // 解码富文本内容
    const { data } = response
    data.questionContent = safeDecode(data.questionContent || '')
    data.analysis = safeDecode(data.analysis || '')
    data.rightAnswer = safeDecode(data.rightAnswer || '')
    data.questionRemark = data.questionRemark || '' // 添加备注字段的处理

    // 处理选项数据
    if (data.options&& data.options.length > 0) {

      data.options = data.options
        .sort((a, b) => (a.optionIndex || a.sort) - (b.optionIndex || b.sort))
        .map((option) => ({
          ...option,
          optionContent: safeDecode(option.optionContent || ''),
          rightFlag: option.rightFlag == 1,
        }))

    }

    // 处理编程题的代码内容
    if (data.questionType === 8) {
      try {
        const codeData = JSON.parse(
          safeDecode(
            data.questionType === 8
              ? data.codeContent
              : data.questionContent || '{}',
          ),
        )
        data.code = codeData.code || ''
        data.language = codeData.language || 'javascript'
      } catch (e) {

        data.code = ''
        data.language = 'javascript'
      }
    }

    form.value = data


    open.value = true
    title.value = isViewMode ? '查看题目' : '修改题目'

    // 更新编辑器key，强制重新渲染
    editorKey.value = Date.now()

    // 如果是连线题，需要处理连线数据
    if (data.questionType === 5) {
      try {
        // 处理连线题数据
        const leftOptions = data.options
          .filter((opt) => opt.optionPosition == '1')
          .map((opt, index) => ({
            id: index,
            content: opt.optionContent,
          }))

        const rightOptions = data.options
          .filter((opt) => opt.optionPosition == '2')
          .map((opt, index) => ({
            id: index,
            content: opt.optionContent,
          }))

        let matchingPairs = []
        if (data.rightAnswer) {
          try {
            const pairs = JSON.parse(data.rightAnswer)
            matchingPairs = pairs.map((pair) => ({
              left: leftOptions[pair.source] || { id: pair.source },
              right: rightOptions[pair.target] || { id: pair.target },
            }))
          } catch (e) {
            console.error('解析连线对失败:', e)
          }
        }

        matchingData.value = {
          leftOptions,
          rightOptions,
          matchingPairs,
        }
      } catch (e) {
        console.error('处理连线题数据失败:', e)
      }
    }
  })
}

// 添加移动题目相关的响应式变量
const moveQuestionDialogVisible = ref(false)
const moveQuestionForm = ref({
  questionId: null,
  folderId: null,
})

// 添加处理移动题目的函数
function handleMoveQuestion(question) {
  moveQuestionForm.value.questionId = question.questionId
  moveQuestionForm.value.folderId = null
  moveQuestionDialogVisible.value = true
  buildFolderTree() // 确保树形数据是最新的

  // 禁用根目录和当前题目所在目录
  disableRootAndCurrentDirectory(question.folderId)
}

// 提交移动题目操作
async function submitMoveQuestion() {
  if (moveQuestionForm.value.folderId) {
    try {
      const targetFolderId =
        moveQuestionForm.value.folderId

      // 调用更新接口，只传递必要的字段
      await updateUserQuestion({
        questionId: moveQuestionForm.value.questionId,
        folderId: targetFolderId,
      })
      MessagePlugin.success('移动成功')
      moveQuestionDialogVisible.value = false

      // 恢复目录状态
      resetDirectoryDisabledState()

      getList() // 刷新题目列表
    } catch (error) {
      MessagePlugin.error(`移动失败：${error.message}`)
    }
  } else {
    MessagePlugin.warning('请选择目标目录')
  }
}

// 添加跳转到回收站的方法
function goToRecycleBin() {
  proxy.$router.push({
    path: '/resourceLibrary/userQuestionRecycleBin',
  })
}

// 在 script setup 中添加组件注册
const components = {
  WangEditor,
  Remove,
}

// 定义操作选项
const operationOptions = [
  { content: '查看', value: 'view' },
  { content: '编辑', value: 'edit' },
  { content: '移动', value: 'move' },
  { content: '删除', value: 'delete', style: 'color: var(--td-error-color)' },
]

// 处理操作点击
const handleOperationClick = (operation, item) => {
  switch (operation.value) {
    case 'view':
      handleView(item)
      break
    case 'edit':
      handleUpdate(item)
      break
    case 'move':
      handleMoveQuestion(item)
      break
    case 'delete':
      handleDelete(item)
      break
  }
}

// 添加分页处理函数
const handlePageChange = (page) => {
  queryParams.value.pageNum = page.current
  queryParams.value.pageSize = page.pageSize
  getList()
}

// 添加导入规则验证
const importRules = {
  file: [{ required: true, message: '请选择要导入的文件', trigger: 'change' }],
  importType: [
    { required: true, message: '请选择导入类型', trigger: 'change' },
  ],
}

// 在 script setup 中暴露这些变量
defineExpose({
  handlePageChange,
  importRules,
})

// 添加删除确认对话框相关变量和方法
const removeDialogVisible = ref(false)
const removeForm = ref({
  questionIds: [],
  resourceIds: [],
  type: '',
})

// 处理删除确认
function handleRemoveConfirm(form) {
  // 处理题目删除
  moveToRecycleBin(form.questionIds)
    .then(() => {
      getList()
      MessagePlugin.success('已移入回收站')

      removeDialogVisible.value = false
    })
    .catch(() => {})
}

// 处理删除取消
function handleRemoveClose() {
  removeDialogVisible.value = false
  removeForm.value = {
    questionIds: [],
    resourceIds: [],
    type: '',
  }
}

// 添加一个用于编辑器的key
const editorKey = ref(0)

// 添加一个计算属性来获取乱序的选项
function shuffledOptions(question) {
  if (!question.options || question.questionType !== 4) {
    return []
  }

  // 复制选项数组，避免修改原数组
  const options = [...question.options]

  // Fisher-Yates 洗牌算法打乱数组
  for (let i = options.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[options[i], options[j]] = [options[j], options[i]]
  }

  return options
}

// 添加 viewMatchingData ref
const viewMatchingData = ref({
  leftOptions: [],
  rightOptions: [],
  matchingPairs: [],
})

// 添加安全的编码和解码函数
function safeEncode(content) {
  if (!content) return ''
  try {
    // 检查内容是否已经被编码
    const isEncoded = (str) => {
      try {
        return decodeURIComponent(str) !== str
      } catch (e) {
        return false
      }
    }

    // 如果已经编码过，直接返回
    if (isEncoded(content)) {
      return content
    }
    return encodeURIComponent(content)
  } catch (e) {
    console.error('编码内容失败:', e)
    return content
  }
}

function safeDecode(content) {
  if (!content) return ''
  try {
    // 检查内容是否已经被编码
    const isEncoded = (str) => {
      try {
        // 尝试解码，如果解码后的结果与原字符串不同，说明是编码过的
        const decoded = decodeURIComponent(str)
        return decoded !== str
      } catch (e) {
        // 如果解码失败，说明可能包含特殊字符，此时返回 false
        return false
      }
    }

    // 如果内容已经被编码，则进行解码
    if (isEncoded(content)) {
      return decodeURIComponent(content)
    }
    
    // 如果内容未被编码或解码失败，直接返回原内容
    return content
  } catch (e) {
    console.error('解码内容失败:', e)
    return content
  }
}

// 添加语言映射函数，使用 optionUtil 中的编程语言列表
const mapLanguageToEditor = (language) => {
  // 使用特殊映射处理
  const languageMap = {
    html: 'markup',
    shell: 'bash',
    xml: 'markup',
  }

  // 将 programmingLanguageList 中的所有语言添加到映射中
  programmingLanguageList.forEach((item) => {
    // 只有当映射中没有特殊处理的情况下，才使用原始值
    if (!languageMap[item.value]) {
      languageMap[item.value] = item.value
    }
  })

  return languageMap[language] || language || 'javascript'
}

// 修改全选方法
function handleSelectAll() {
  // 检查是否所有题目都已选中
  const allSelected = bookQuestionList.value.every((item) => item.selected)

  // 如果全部已选中，则取消全选；否则全选
  bookQuestionList.value.forEach((item) => {
    item.selected = !allSelected
  })

  // 更新选中状态
  handleSelectionChange()
}

// 删除确认窗口相关变量
const deleteModalVisible = ref(false)

// 添加一个变量保存当前要删除的行
const currentDeleteRow = ref(null)

// 确认删除的方法
function confirmDelete(callback) {


  if (!currentDeleteRow.value) {

    MessagePlugin.error('删除失败：没有要删除的数据')
    callback(false)
    return
  }

  let questionIds = []

  // 检查是否是批量删除
  if (
    currentDeleteRow.value.type === 'batch' &&
    currentDeleteRow.value.questions
  ) {
    // 批量删除
    questionIds = currentDeleteRow.value.questions.map((q) => q.questionId)

  } else if (currentDeleteRow.value.questionId) {
    // 单个删除
    questionIds = [currentDeleteRow.value.questionId]

  } else {

    MessagePlugin.error('删除失败：无法确定要删除的题目ID')
    callback(false)
    return
  }

  // 确保有题目ID要删除
  if (questionIds.length === 0) {
    MessagePlugin.warning('没有找到要删除的题目')
    callback(false)
    return
  }

  // 使用正确的删除API函数
  moveToRecycleBin(questionIds)
    .then((response) => {
      if (response.code === 200) {
        MessagePlugin.success('已移入回收站')
        getList()
        callback(true) // 通知删除模态框关闭
      } else {
        MessagePlugin.error(response.msg || '删除失败')
        callback(false) // 删除失败不关闭模态框
      }
    })
    .catch((error) => {
      console.error('删除失败:', error)
      MessagePlugin.error(`删除失败: ${error.message || '未知错误'}`)
      callback(false)
    })
}

// 添加禁用根目录和当前目录的辅助函数
function disableRootAndCurrentDirectory(currentFolderId) {
  // 在树形数据中找到根目录和当前目录并禁用它们
  if (folderTreeData.value && folderTreeData.value.length > 0) {
    // 禁用根目录（通常是第一个元素）
    if (folderTreeData.value[0]) {
      folderTreeData.value[0].disabled = true
    }

    // 如果有当前目录ID，则在树中查找并禁用它
    if (currentFolderId) {
      disableDirectoryById(folderTreeData.value, currentFolderId)
    }
  }
}

// 递归查找并禁用指定ID的目录
function disableDirectoryById(directories, folderId) {
  if (!directories || !Array.isArray(directories)) return false

  for (let i = 0; i < directories.length; i++) {
    const dir = directories[i]
    if (
      dir.id === folderId ||
      dir.value === folderId ||
      dir.folderId === folderId
    ) {
      dir.disabled = true
      return true
    }

    // 递归检查子目录
    if (dir.children && dir.children.length > 0) {
      if (disableDirectoryById(dir.children, folderId)) {
        return true
      }
    }
  }

  return false
}

// 添加恢复目录状态的辅助函数
function resetDirectoryDisabledState() {
  if (folderTreeData.value && folderTreeData.value.length > 0) {
    resetDisabledStateRecursive(folderTreeData.value)
  }
}

// 递归重置目录的禁用状态
function resetDisabledStateRecursive(directories) {
  if (!directories || !Array.isArray(directories)) return

  for (const dir of directories) {
    dir.disabled = false
    if (dir.children && dir.children.length > 0) {
      resetDisabledStateRecursive(dir.children)
    }
  }
}

const viewMode = ref(false)

// 创建一个函数来处理题型变化
function handleQuestionTypeChange(newType) {

  if (newType === 7) {
    // 判断题
    form.value.options = [
      {
        optionContent: '正确',
        rightFlag: false,
        optionType: null,
        optionIndex: 0,
      },
      {
        optionContent: '错误',
        rightFlag: false,
        optionType: null,
        optionIndex: 1,
      },
    ]
  } else if (
    [1, 2].includes(newType) ){
   if (!form.value.options || form.value.options.length < 2) {
      // 单选或多选题，如果选项少于2个，则重置为默认4个选项
      form.value.options = [
        {
          optionContent: '',
          rightFlag: false,
          optionType: null,
          optionIndex: 0,
        },
        {
          optionContent: '',
          rightFlag: false,
          optionType: null,
          optionIndex: 1,
        },
        {
          optionContent: '',
          rightFlag: false,
          optionType: null,
          optionIndex: 2,
        },
        {
          optionContent: '',
          rightFlag: false,
          optionType: null,
          optionIndex: 3,
        },
      ]
    }
  }
  else {
    form.value.options = null
  }

  //强制刷新
}

// 添加填空题特有功能
function appendBlanks() {


  if (!form.value.questionContent) {
    form.value.questionContent = ''
  }
  form.value.questionContent += '###请输入正确答案###'

  // 获取题干编辑器实例
  const questionContentEditor = editorInstanceMap.value.get('questionContent')
  if (questionContentEditor) {

    questionContentEditor.insertText('###请输入正确答案###')
  }
}

// 添加填空题特有功能
function onMultipleFillInBlanksChange(event) {
  form.value.disorder = event ? 2 : 1
}

// 在 script setup 部分添加 continuousAdd ref
const continuousAdd = ref(false)
</script>
<style>
.blank-line {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
  margin: 0 5px;

  .blank-answer {
    color: #409eff;
    font-size: 14px;
    margin-bottom: 2px;
  }

  &::after {
    content: '';
    width: 100%;
    height: 1px;
    background-color: #000;
    display: block;
  }
}
</style>

<style scoped>
.tool {
  padding: 20px 0 0;
}

.top20 {
  margin-top: 20px;
}

.pagination {
  float: right;
  margin: 20px 0;
}

.app-container {
  padding: 20px;
}
.question-list {
  display: grid;
  grid-gap: 20px;
  padding: 10px;
  max-width: 100%;
  overflow-x: hidden;
}

.question-card {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 15px;
  background: #fff;
  width: 100%;
  box-sizing: border-box;
  overflow-wrap: break-word;
  word-wrap: break-word;
  min-width: 0;
  max-width: 100%;
  overflow-x: hidden;

  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;

    .question-info {
      display: flex;
      align-items: center;
      gap: 10px;

      .question-checkbox {
        margin: 0;
      }

      .t-tag {
        margin: 0;
      }
    }

    .operation {
      margin-left: auto;
    }
  }

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .question-content {
    max-width: 100%;
    overflow-x: hidden;

    .content-item {
      margin-bottom: 15px;

      .label {
        font-weight: bold;
        margin-bottom: 8px;
        color: #606266;
      }

      .content {
        color: #303133;
        line-height: 1.6;
        word-break: break-word;
        overflow-wrap: break-word;
      }

      :deep(.code-editor-container) {
        margin: 10px 0;
      }
    }

    .options-area {
      margin: 10px 0;

      .option-item {
        margin-bottom: 8px;
        padding: 5px 10px;

        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

.option-content {
  display: flex;
  align-items: center;

  .option-label {
    margin-right: 8px;
    font-weight: bold;
  }

  .correct-icon {
    margin-left: 8px;
    color: var(--td-success-color);
  }
}

.popover-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.blank-line {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
  margin: 0 5px;

  .blank-answer {
    color: #409eff;
    font-size: 14px;
    margin-bottom: 2px;
  }

  &::after {
    content: '';
    width: 100%;
    height: 1px;
    background-color: var(--td-text-color-primary);
    display: block;
  }
}

.options-editor {
  margin-bottom: 20px;

  .options-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .subtitle {
      font-weight: bold;
      color: #606266;
    }
  }

  .option-row {
    margin-bottom: 10px;
    align-items: center;

    .option-label {
      text-align: center;
      line-height: 32px;
      font-weight: bold;
    }
  }
}

.matching-editor {
  position: relative; /* 确保父元素是相对定位 */
}

.editor-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10; /* 确保叠加层在内容之上 */
  /* 其他样式如背景颜色、透明度等 */
}

.mb-10 {
  margin-bottom: 10px;
}

.template-download {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 20px;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.import-item {
  margin-bottom: 10px;

  .import-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;

    .import-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .t-icon {
        font-size: 20px;
        color: #909399;
      }

      .import-name {
        color: #606266;
      }
    }
  }
}

.options-editor {
  position: relative;
}
.editor-overlay {
  position: absolute;
  top: 40px;
  left: 0;
  right: 30px;
  bottom: 80px;
  background-color: rgba(255, 255, 255, 0.01);
  z-index: 10;
  cursor: not-allowed;
}

.operation {
  .more-btn {
    padding: 4px 8px;

    &:hover {
      background-color: var(--td-bg-color-container-hover);
      border-radius: var(--td-radius-small);
    }

    .t-icon {
      font-size: 16px;
      color: var(--td-text-color-secondary);
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.option-cell {
  display: flex;
  margin: 20px 0;
}
.option-label {
  font-weight: bold;
  margin-right: 20px;
}

.set-option {
  display: flex;
  margin: 20px 0;
  align-content: center;
  justify-content: space-between;
}

.set-option-left {
  display: flex;
}
.set-option-label {
  font-weight: bold;
  margin-right: 20px;
}

.fill-blanks-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: -20px;
}

.fill-blanks-item {
  display: flex;
  align-items: center;
}
</style>
