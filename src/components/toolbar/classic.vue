<template>
  <toolbar-scrollable ref="scrollableRef" class="umo-scrollable-container">
    <div class="umo-classic-menu">
      <div v-if="menus.length > 1" class="umo-virtual-group">
        <t-select
          v-model="currentMenu"
          :popup-props="{
            destroyOnClose: true,
            attach: container,
          }"
          size="small"
          auto-width
          borderless
          @change="toggoleMenu as any"
        >
          <template #prefixIcon>
            <icon name="menu" />
          </template>
          <t-option
            v-for="item in menus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </t-select>
      </div>
      <template v-if="currentMenu === 'base'">
        <div class="umo-virtual-group">
          <menus-toolbar-base-undo />
          <menus-toolbar-base-redo />
          <menus-toolbar-base-format-painter />
          <menus-toolbar-base-clear-format />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-base-heading />
          <menus-toolbar-base-font-family borderless />
          <menus-toolbar-base-font-size borderless />
          <menus-toolbar-base-bold />
          <menus-toolbar-base-italic />
          <menus-toolbar-base-underline />
          <menus-toolbar-base-strike />
          <menus-toolbar-base-subscript />
          <menus-toolbar-base-superscript />
          <menus-toolbar-base-color />
          <menus-toolbar-base-background-color />
          <menus-toolbar-base-highlight />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-base-ordered-list />
          <menus-toolbar-base-bullet-list />
          <menus-toolbar-base-indent />
          <menus-toolbar-base-outdent />
          <menus-toolbar-base-line-height />
          <menus-toolbar-base-margin />
          <menus-toolbar-base-align-dropdown />
          <menus-toolbar-base-quote />
          <menus-toolbar-base-code v-if="!disableItem('code')" />
          <menus-toolbar-base-select-all />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-base-import-word />
          <menus-toolbar-base-markdown />
          <menus-toolbar-base-search-replace />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-base-print v-if="!disableItem('print')" />
        </div>
        <div class="virtual-group is-slot">
          <slot name="toolbar_base" toolbar-mode="classic" />
        </div>
      </template>
      <template v-if="currentMenu === 'insert'">
        <div class="umo-virtual-group">
          <menus-toolbar-insert-link />
          <menus-toolbar-insert-image />
          <menus-toolbar-insert-video v-if="!disableItem('video')" />
          <menus-toolbar-insert-audio v-if="!disableItem('audio')" />
          <menus-toolbar-insert-file v-if="!disableItem('file')" />
          <menus-toolbar-insert-code-block v-if="!disableItem('code')" />
          <menus-toolbar-insert-symbol />
          <menus-toolbar-insert-math v-if="!disableItem('math')" />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-insert-hard-break />
          <menus-toolbar-insert-hr />
          <menus-toolbar-insert-toc />
          <!-- <menus-toolbar-insert-text-box /> -->
        </div>

        <!-- <menus-toolbar-insert-template /> -->

        <!--  <menus-toolbar-insert-expandReading v-if="!disableItem('expandReading')"/> -->
        <!--  <menus-toolbar-insert-remark /> -->

        <div class="umo-virtual-group">
          <!--  <menus-toolbar-insert-web-page />-->
          <menus-toolbar-insert-questions />
          <menus-toolbar-insert-bubble />
          <menus-toolbar-insert-games />
          <menus-toolbar-insert-courseware />
          <menus-toolbar-insert-simulation />
          <menus-toolbar-insert-arandvr />
          <menus-toolbar-insert-threeDimensional />
          <menus-toolbar-insert-expandReading
            v-if="!disableItem('expandReading')"
          />
          <menus-toolbar-insert-remark />
          <!-- <menus-toolbar-insert-interaction /> -->
        </div>
        <div class="virtual-group is-slot">
          <slot name="toolbar_insert" toolbar-mode="classic" />
        </div>
      </template>
      <template v-if="currentMenu === 'table'">
        <div class="umo-virtual-group">
          <menus-toolbar-table-insert />
          <menus-toolbar-table-template-table />
          <menus-toolbar-table-fix />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-table-cells-align />
          <menus-toolbar-table-cells-background />
          <menus-toolbar-table-border-color />
          <menus-toolbar-table-border-style />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-table-add-row-before :huge="false" />
          <menus-toolbar-table-add-row-after :huge="false" />
          <menus-toolbar-table-add-column-before :huge="false" />
          <menus-toolbar-table-add-column-after :huge="false" />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-table-delete-row :huge="false" />
          <menus-toolbar-table-delete-column :huge="false" />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-table-merge-cells :huge="false" />
          <menus-toolbar-table-split-cell :huge="false" />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-table-toggle-header-row :huge="false" />
          <menus-toolbar-table-toggle-header-column :huge="false" />
          <menus-toolbar-table-toggle-header-cell :huge="false" />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-table-next-cell :huge="false" />
          <menus-toolbar-table-previous-cell :huge="false" />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-table-delete />
        </div>
        <div class="virtual-group is-slot">
          <slot name="toolbar_table" toolbar-mode="classic" />
        </div>
      </template>
      <template v-if="currentMenu === 'tools'">
        <div class="umo-virtual-group">
          <menus-toolbar-tools-qrcode />
          <menus-toolbar-tools-barcode />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-tools-signature v-if="!disableItem('signature')" />
          <menus-toolbar-tools-seal v-if="!disableItem('seal')" />
          <menus-toolbar-base-search-replace />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-tools-diagrams v-if="!disableItem('diagrams')" />
          <!-- <menus-toolbar-tools-mind-map v-if="!disableItem('mind-map')" /> -->
          <menus-toolbar-tools-mermaid v-if="!disableItem('mermaid')" />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-tools-chinese-case
            v-if="!disableItem('chineseCase')"
          />
        </div>
        <div class="virtual-group is-slot">
          <slot name="toolbar_tools" toolbar-mode="classic" />
        </div>
      </template>
      <template v-if="currentMenu === 'page'">
        <div class="umo-virtual-group">
          <menus-toolbar-page-toggle-toc />
        </div>
        <div class="umo-virtual-group">
          <div class="virtual-group-row">
            <menus-toolbar-page-margin />
            <menus-toolbar-page-size />
            <menus-toolbar-page-orientation />
          </div>
        </div>
        <div
          v-if="!hidePageHeader || !hidePageFooter"
          class="umo-virtual-group"
        >
          <menus-toolbar-page-header v-if="!hidePageHeader" />
          <menus-toolbar-page-footer v-if="!hidePageFooter" />
        </div>
        <div class="umo-virtual-group">
          <menus-toolbar-page-break />
          <menus-toolbar-page-line-number />
          <!--<menus-toolbar-page-watermark /> -->
          <menus-toolbar-page-background />
        </div>
        <!--
      <div class="umo-virtual-group">
          <menus-toolbar-page-preview />
        </div>
     -->
        <div class="virtual-group is-slot">
          <slot name="toolbar_page" toolbar-mode="classic" />
        </div>
      </template>
      <template v-if="currentMenu === 'export'">
        <div class="umo-virtual-group">
          <menus-toolbar-export-image />
          <menus-toolbar-export-pdf />
          <menus-toolbar-export-word />
          <menus-toolbar-export-text />
        </div>
        <!--
        <div class="umo-virtual-group">
          <menus-toolbar-export-share v-if="!disableItem('share')" />
          <menus-toolbar-export-embed v-if="!disableItem('embed')" />
        </div>
      -->
        <div class="virtual-group is-slot">
          <slot name="toolbar_export" toolbar-mode="classic" />
        </div>
      </template>
    </div>
  </toolbar-scrollable>
</template>

<script setup lang="ts">
import { withSuppress } from '@/utils/functional'

const props = defineProps<{
  menus: {
    value: string
    label: string
  }[]
  currentMenu: string
}>()

const emits = defineEmits(['menu-change'])

const { container, options, hidePageHeader, hidePageFooter } = useStore()
const disableItem = (name: string) => {
  return options.value.toolbar?.disableMenuItems.includes(name)
}

// eslint-disable-next-line vue/no-dupe-keys
let currentMenu = $ref('')
watch(
  () => props.currentMenu,
  withSuppress(async (val) => {
    currentMenu = val
    await nextTick()
    scrollableRef?.update()
  }),
  { immediate: true },
)
const scrollableRef = $ref<{ update: () => void }>()
const toggoleMenu = async (menu: string) => {
  emits('menu-change', menu)
  await nextTick()
  scrollableRef?.update()
}
</script>

<style lang="less" scoped>
.umo-scrollable-container {
  padding: 10px;
}
.umo-classic-menu {
  display: flex;
  align-items: center;
  flex: 1;
  .umo-virtual-group {
    display: flex;
    align-items: center;
    &:empty {
      display: none;
    }
    &:not(:last-child),
    &.is-slot {
      &::before {
        content: '';
        display: block;
        height: 18px;
        width: 1px;
        background-color: var(--umo-border-color-light);
        margin: 0 10px;
      }
    }
    &:first-child::before {
      display: none;
    }
    :deep(.umo-menu-button .umo-button--shape-square) {
      .umo-icon {
        font-size: 14px;
      }
    }
    &-row {
      display: flex;
    }
  }
}
</style>
