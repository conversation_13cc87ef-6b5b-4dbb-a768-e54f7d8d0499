<template>
  <div v-if="$toolbar.show" class="umo-toolbar-container">
    <toolbar-ribbon
      v-if="$toolbar.mode === 'ribbon'"
      :menus="toolbarMenus"
      :current-menu="currentMenu"
      @menu-change="menuChange"
    >
      <template
        v-for="item in options.toolbar?.menus"
        :key="item"
        #[`toolbar_${item}`]="props"
      >
        <slot :name="`toolbar_${item}`" v-bind="props" />
      </template>
    </toolbar-ribbon>
    <toolbar-classic
      v-if="$toolbar.mode === 'classic'"
      :menus="toolbarMenus"
      :current-menu="currentMenu"
      @menu-change="menuChange"
    >
      <template
        v-for="item in options.toolbar?.menus"
        :key="item"
        #[`toolbar_${item}`]="props"
      >
        <slot :name="`toolbar_${item}`" v-bind="props" />
      </template>
    </toolbar-classic>
    <toolbar-source
      v-if="$toolbar.mode === 'source' && options.toolbar?.enableSourceEditor"
    />
    <div class="umo-toolbar-actions" :class="$toolbar.mode">
      <t-popup
        v-if="$toolbar.mode !== 'source' && editor?.isEditable"
        v-model="statusPopup"
        :attach="container"
        trigger="click"
        placement="bottom-right"
        @visible-change="(visible: boolean) => (statusPopup = visible)"
      >
        <t-button
          class="umo-toolbar-actions-button"
          variant="text"
          size="small"
          :class="{ active: statusPopup }"
        >
          <span class="umo-status">
            <span
              class="umo-status-online"
              :class="{ offline: !online }"
            ></span>
            <span class="umo-status-saved button-text">
              <span
                v-if="savedAt"
                v-text="t('save.savedAtText', { time: timeAgo(savedAt) })"
              ></span>
              <span v-else class="unsaved" v-text="t('save.unsaved')"></span>
            </span>
          </span>
        </t-button>
        <template #content>
          <div class="umo-document-status-container umo-status">
            <div>
              {{ t('save.network') }}
              {{ online ? t('save.online') : t('save.offline') }}
            </div>
            <div>
              {{ t('save.savedAt') }}
              <span
                v-if="savedAt"
                v-text="t('save.savedAtText', { time: timeAgo(savedAt) })"
              ></span>
              <span v-else v-text="t('save.unsaved')"></span>
            </div>
            <div class="umo-document-button-container">
              <t-button
                size="small"
                @click="saveContent"
                v-text="t('save.text')"
              ></t-button>
              <t-button
                size="small"
                variant="outline"
                @click="setContentFromCache"
                v-text="t('save.cache.text')"
              >
              </t-button>
            </div>
          </div>
        </template>
      </t-popup>
      <!-- <t-dropdown
        trigger="click"
        size="small"
        placement="bottom-right"
        :popup-props="{
          destroyOnClose: true,
          attach: container,
        }"
        @click="toggleToolbarMode"
      >
        <t-button
          class="umo-toolbar-actions-button"
          variant="text"
          size="small"
        >
          <icon name="expand-down" />
          <span class="umo-button-text">{{ t('toolbar.toggle') }}</span>
        </t-button>
        <template #dropdown>
          <t-dropdown-menu
            v-for="item in editorModeOptions"
            :key="item.value as string"
            :content="item.label"
            :value="item.value"
            :divider="item.divider"
            :active="item.value === $toolbar.mode"
          >
            <template #prefixIcon>
              <icon :name="item.prefixIcon" />
            </template>
          </t-dropdown-menu>
        </template>
      </t-dropdown> -->
    </div>
  </div>
  <tooltip v-else :content="t('toolbar.show')" placement="bottom-right">
    <div class="umo-show-toolbar" @click="$toolbar.show = true">
      <icon name="arrow-down" />
    </div>
  </tooltip>
</template>

<script setup lang="ts">
import type { DropdownOption } from 'tdesign-vue-next'

import { timeAgo } from '@/utils/time-ago'
const emits = defineEmits(['menu-change'])
const { container, options, editor, savedAt } = useStore()
const $toolbar = useState('toolbar')
let statusPopup = $ref(false)
const online = useOnline()

// 工具栏菜单
const defaultToolbarMenus = [
  { label: t('toolbar.base'), value: 'base' },
  { label: t('toolbar.insert'), value: 'insert' },
  { label: t('toolbar.table'), value: 'table' },
  { label: t('toolbar.tools'), value: 'tools' },
  { label: t('toolbar.page'), value: 'page' },
  { label: t('toolbar.export'), value: 'export' },
]
let toolbarMenus = defaultToolbarMenus
if (options.value.toolbar?.menus) {
  toolbarMenus = options.value.toolbar?.menus
    .map(
      (item: any) =>
        defaultToolbarMenus.filter((menu) => menu.value === item)[0],
    )
    .filter((ele) => ele.label !== t('toolbar.base'))
  //删除开始菜单
}
let currentMenu = $ref(toolbarMenus[0].value)
const menuChange = (menu: string) => {
  currentMenu = menu
  emits('menu-change', menu)
}
// 监听如果当前编辑元素为table则切换到table菜单
watch(
  () => editor.value?.isActive('table'),
  (val: boolean | undefined, oldVal: boolean | undefined) => {
    if (val) {
      currentMenu = 'table'
    } else if (!val && oldVal) {
      currentMenu = 'insert'
    }
  },
)

// 切换编辑器模式
const editorModeOptions = computed(() => {
  const modeOptions: {
    value: string
    label: string
    prefixIcon: string
    divider?: boolean
  }[] = [
    {
      label: t('toolbar.ribbon'),
      value: 'ribbon',
      prefixIcon: 'toolbar-ribbon',
    },
    {
      label: t('toolbar.classic'),
      value: 'classic',
      prefixIcon: 'toolbar-classic',
    },
    // {
    //  label: t('toolbar.hide'),
    //  value: 'hideToolbar',
    //  prefixIcon: 'hide-toolbar',
    //},
  ]
  if (options.value.toolbar?.enableSourceEditor) {
    modeOptions.splice(2, 0, {
      label: t('toolbar.source'),
      value: 'source',
      prefixIcon: 'toolbar-source',
      divider: true,
    })
  }
  return modeOptions
})

const toggleToolbarMode = ({ value }: DropdownOption) => {
  if (value === 'hideToolbar') {
    $toolbar.value.show = false
  } else {
    $toolbar.value.show = true
    $toolbar.value.mode = value as string
  }
}

// 保存文档
const saveContentMethod = inject('saveContent') as () => void
const saveContent = () => {
  saveContentMethod()
  statusPopup = false
}

// 从缓存中恢复文档
const setContentFromCache = () => {
  const document = useState('document')
  const { content } = document.value
  if (!content || content === '' || content === '<p></p>') {
    const dialog = useAlert({
      theme: 'info',
      header: t('save.cache.error.title'),
      body: t('save.cache.error.message'),
      onConfirm() {
        dialog.destroy()
      },
    })
    return
  }
  statusPopup = false
  editor.value?.chain().setContent(content, true).focus().run()
}
</script>

<style lang="less" scoped>
.umo-toolbar-container {
  display: flex;
  justify-content: space-between;
  user-select: none;
  border-bottom: solid 1px var(--umo-border-color);
  position: relative;
}
.umo-toolbar-actions {
  padding: 6px 10px;
  display: flex;
  align-items: center;
  &.ribbon {
    position: absolute;
    right: 0;
    top: 1px;
  }
  &-button {
    &.active {
      background-color: var(--umo-button-hover-background);
    }
    &:not(:last-child) {
      margin-right: 3px;
    }
    :deep(.umo-button__text) {
      display: flex;
      align-items: center;
      .umo-icon {
        margin-right: 3px;
      }
    }
  }
  @media screen and (max-width: 640px) {
    padding-left: 0;
    .umo-status-online {
      margin-right: 0;
    }
    .umo-button-text {
      display: none;
    }
  }
}
.umo-show-toolbar {
  cursor: pointer;
  position: absolute;
  right: 20px;
  font-size: 18px;
  padding: 3px 6px;
  z-index: 99;
  background-color: var(--umo-color-white);
  color: var(--umo-text-color-light);
  border-bottom-left-radius: var(--umo-radius);
  border-bottom-right-radius: var(--umo-radius);
  border: solid 1px var(--umo-border-color);
  border-top: none;
  &:hover {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.08);
    color: var(--umo-primary-color);
  }
}
.umo-status {
  font-size: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  &-online {
    width: 10px;
    height: 10px;
    background: rgb(26, 187, 26);
    border-radius: 50%;
    &.offline {
      background: rgb(187, 26, 26);
    }
  }
  &-saved {
    color: var(--umo-text-color-light);
    margin-left: 5px;
    .unsaved {
      color: var(--umo-error-color);
    }
  }
}
.umo-document-status-container {
  flex-direction: column;
  align-items: unset;
  padding: 12px 16px;
  color: var(--umo-text-color);
  min-width: 150px;
  cursor: default;
  .umo-document-button-container {
    margin: 8px 0 4px;
    display: flex;
    gap: 8px;
  }
}
</style>
