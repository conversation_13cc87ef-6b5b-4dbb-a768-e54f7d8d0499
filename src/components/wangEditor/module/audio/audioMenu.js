import { chooseFile } from '@/utils/file'

export default class AudioMenu {
  constructor() {
    this.title = '上传音频'
    this.iconSvg =
      '<svg class="icon" width="200px" height="200.00px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path fill="#FF7F7F" d="M849.408 6.656L411.648 140.8c-53.248 15.36-95.744 70.656-95.744 123.392v461.312S284.16 704 213.504 714.24C109.568 729.088 25.6 808.448 25.6 891.904s83.968 134.656 187.904 119.808c103.936-14.848 179.712-91.648 179.712-175.104v-445.44c0-36.864 44.544-52.736 44.544-52.736l387.072-121.344s43.008-14.336 43.008 25.088v367.616s-39.424-22.528-110.08-14.336c-103.936 12.8-187.904 90.624-187.904 174.08S653.824 905.728 757.76 893.44c103.936-12.8 187.904-90.624 187.904-174.08V74.752c-0.512-52.224-43.52-82.944-96.256-68.096z"  /></svg>'
    this.tag = 'button'
    this.showDropPanel = true
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive(editor) {
    // JS 语法
    return false
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue(editor) {
    return ''
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled(editor) {
    // JS 语法
    return false
  }

  // 点击菜单时触发的函数
  exec(editor, value) {
    // DropPanel menu ，这个函数不用写，空着即可
    if (this.isDisabled(editor)) {
      return
    }
    chooseFile(
      (file) => {
        editor.insertNode({
          type: 'audio',
          src: file.fileUrl,
          children: [{ text: '' }],
        })
      },{
        multiple: false,
        fileType: 'mp3,wav'
      }
    )
  }
}

export const audioMenuConf = {
  // 定义 menu key ：要保证唯一、不重复（重要）
  key: 'menu-bell-audio',
  factory() {
    return new AudioMenu()
  },
}
