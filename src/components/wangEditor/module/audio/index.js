import { Boot } from '@wangeditor/editor'
import { renderAudioConf } from './render-elem'
import { audioToHtmlConf } from './elem-to-html'
import { parseAudioHtmlConf } from './parse-elem-html'
import withAudio from './plugin'

function AudioModule() {
  Boot.registerRenderElem(renderAudioConf)
  Boot.registerElemToHtml(audioToHtmlConf)
  Boot.registerParseElemHtml(parseAudioHtmlConf)
  Boot.registerPlugin(withAudio)
}

export default AudioModule
