export default class FormulaMenu {
  // https://www.wangeditor.com/v5/development.html#selectmenu
  constructor() {
    this.title = '公式'
    this.iconSvg =
      '<svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M40 9l-3-3H8l18 18L8 42h29l3-3" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/></svg>'
    this.tag = 'select'
  }
  isActive(editor) {
    return false
  }
  getValue(editor) {
    return 'gs'
  }
  isDisabled(editor) {
    // JS 语法
    return false
  }
  exec(editor, value) {
    // editorWangRef.value = editor
    // editor 获取焦点
    if (value === 'chem') {
      // addChemistryFormula()
      editor.emit('formulaMenuClick', 'chem')
    } else if (value === 'math') {
      editor.emit('formulaMenuClick', 'math')
    } else if (value === 'latex') {
      editor.emit('formulaMenuClick', 'latex')
    }
  }
  getOptions(editor) {
    const options = [
      { value: 'gs', text: t('insert.questions.formula'), selected: true },
      { value: 'chem', text: t('insert.questions.chemicalFormula') },
      { value: 'latex', text: t('insert.questions.latexFormula') },
      { value: 'math', text: t('insert.questions.mathFormula') },
    ]
    return options
  }
}

export const formulaMenuConf = {
  // 定义 menu key ：要保证唯一、不重复（重要）
  key: 'menu-bell-math',
  factory() {
    return new FormulaMenu()
  },
}
