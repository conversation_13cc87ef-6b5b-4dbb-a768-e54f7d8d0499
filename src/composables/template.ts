import { getBookTemplate, setBookTemplate } from '@/api/book/book'
export const useTemplate = createGlobalState(() => {
  const pageTemplateId = ref<PageTemplate[]>([])
  // 获取页面模板
  const getPageTemplate = (id) => {
    return new Promise((resolve, reject) => {
      getBookTemplate(id)
        .then((res) => {
          pageTemplateId.value = res.data
          //localStorage.setItem('textBookTemplate', JSON.stringify(res.data))
          resolve()
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  const setLocalStorage = (item) => {
    if (item) {
      pageTemplateId.value = item
      setBookTemplate({
        chapterId: item.chapterId,
        templateId: item.templateId,
      }).then((res) => {

      })
    }
  }

  watch(
    () => pageTemplateId.value,
    (newValue) => {
      pageTemplateId.value = newValue
    },
  )

  return {
    pageTemplateId,
    getPageTemplate,
    setLocalStorage,
  }
})
