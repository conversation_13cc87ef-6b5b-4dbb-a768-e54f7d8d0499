//  1续写2缩写3扩写4润色5生成试题6生成总结7生成学习目标8生成脑图9生成大纲10匹配案例11文生语音12文生图片13百度图片增强14百度图像修复15百度视频生成16文字纠错17文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))18视频合规19文本合规20文本合规(上传黑白名单)21图片合规22音频合规23翻译
// ai指令类型
export enum AiPromtType {
  // 续写
  Continuation = 1,
  // 缩写
  Abbreviation = 2,
  // 扩写
  Expansion = 3,
  // 润色
  Polishing = 4,
  // 生成试题
  GenerateTestQuestions = 5,
  // 生成总结
  GenerateSummary = 6,
  // 生成学习目标
  GenerateLearningObjectives = 7,
  // 生成脑图
  GenerateMindMap = 8,
  // 生成大纲
  GenerateOutline = 9,
  // 匹配案例
  MatchCases = 10,
  // 文生语音
  TextToSpeech = 11,
  // 文生图片
  TextToImage = 12,
  // 百度图片增强
  BaiduImageEnhancement = 13,
  // 百度图像修复
  BaiduImageRepair = 14,
  // 百度视频生成
  BaiduVideoGeneration = 15,
  // 文字纠错
  TextErrorCorrection = 16,
  // 文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))
  TextErrorCorrectionWithLists = 17,
  // 视频合规
  VideoCompliance = 18,
  // 文本合规
  TextCompliance = 19,
  // 文本合规(上传黑白名单)20
  TextComplianceWithLists = 20,
  // 图片合规
  ImageCompliance = 21,
  // 音频合规
  AudioCompliance = 22,
  // 翻译
  Translation = 23,
  // 重写
  Rewrite = 24,
}

// 纠错类型
export enum ErrorCorrectionType {
  black_list = '黑名单',
  pol = '政治术语纠错',
  char = '别字纠错',
  word = '错别字',
  redund = '语法纠错-冗余',
  miss = '语法纠错-缺失',
  order = '语法纠错-乱序',
  dapei = '搭配纠错',
  punc = '标点纠错',
  idm = '成语纠错',
  org = '机构名纠错	',
  leader = '领导人职称纠错',
  number = '数字纠错',
  addr = '地名纠错',
  name = '全文人名纠错',
  grammar_pc = '句式杂糅&语义重复',
}
