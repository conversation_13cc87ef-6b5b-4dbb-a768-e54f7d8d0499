// 使用本组件的类型
export enum RCType {
  // 3d模型
  MOEL_3D = 0,
  // AR
  AR = 1,
  // VR
  VR = 2,
  // 虚拟仿真
  VIRTUAKL = 3,
  // 游戏
  GAME = 4,
  // 教学资源
  TEACH = 5,
  // 拓展阅读
  READ = 6,
  // 实训
  TRAINING = 7,
}

// 文件支持的格式
export const RCMimeType: Record<RCType, string[]> = {
  [RCType.MOEL_3D]: [
    '.obj',
    '.3ds',
    '.stl',
    'ply',
    '.gltf',
    '.glb',
    '.off',
    '.3dm',
    '.fbx',
    '.dae',
    '.wrl',
    '.3mf',
    '.ifc',
    '.brep',
    '.step',
    '.iges',
    '.fcstd',
    '.bim',
  ],
  [RCType.AR]: ['*'],
  [RCType.VR]: ['*'],
  [RCType.VIRTUAKL]: [
    '.STL',
    '.STEP',
    '.VTK',
    'VDL',
    '.VHD',
    '.QCOW2',
    '.OVA',
    '.RAW',
    '.VMDK',
    '.SDF',
  ],
  [RCType.GAME]: ['*'],
  [RCType.TEACH]: [
    '.doc',
    '.docx',
    '.ppt',
    '.pptx',
    '.pot',
    '.pps',
    '.ppa',
    '.xls',
    '.xlsx',
    '.xlsm',
    '.xlsb',
    '.csv',
    '.txt',
    '.pdf',
  ],
  [RCType.READ]: ['*'],
}
// 背景图片支持的格式
export const BgMimeTypes = ['image/jpeg', 'image/png', 'image/jpg']

// 默认背景图
export const RCBgUrl: Record<RCType, string> = {
  [RCType.MOEL_3D]:
    'http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png',
  [RCType.AR]: 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png',
  [RCType.VR]: 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png',
  [RCType.VIRTUAKL]:
    'http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png',
  [RCType.GAME]:
    'http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png',
  [RCType.TEACH]:
    'http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png',
  [RCType.READ]:
    'http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png',
  [RCType.TRAINING]:
    'http://dutp-test.oss-cn-beijing.aliyuncs.com/1738917920012.png',
}
