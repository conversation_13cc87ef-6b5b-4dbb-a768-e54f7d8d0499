// 使用本组件的类型
export enum InteractionType {
  // 投票
  VOTE = 0,
  // 词云
  WORD_CLOUD = 1,
  // 讨论
  DISCUSSION = 2,
  // 图片瀑布流
  IMAGE_WATER_FALL = 3,
}
// name
export const InteractionTypeName: {
  [key in InteractionType]: string
} = {
  [InteractionType.VOTE]: t('insert.interaction.options.vote'),
  [InteractionType.WORD_CLOUD]: t('insert.interaction.options.wordCloud'),
  [InteractionType.DISCUSSION]: t('insert.interaction.options.discussion'),
  [InteractionType.IMAGE_WATER_FALL]: t(
    'insert.interaction.options.imageWaterfall',
  ),
}
