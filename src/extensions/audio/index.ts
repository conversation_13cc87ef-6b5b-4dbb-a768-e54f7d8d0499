import { mergeAttributes, Node } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setAudio: {
      setAudio: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'audio',
  group: 'block',
  atom: true,
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      name: {
        default: null,
      },
      size: {
        default: null,
      },
      src: {
        default: null,
      },
      audioTitle: {
        default: '',
      },
      previewType: {
        default: 'audio',
      },
      duration: {
        default: 0,
      },
    }
  },
  parseHTML() {
    return [{ tag: 'audio' }]
  },
  renderHTML({ HTMLAttributes }) {
    return ['audio', mergeAttributes(HTMLAttributes)]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component)
  },
  addCommands() {
    return {
      setAudio:
        (options) =>
        ({ commands, editor }) => {
          return commands.insertContentAt(editor.state.selection.anchor, {
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
