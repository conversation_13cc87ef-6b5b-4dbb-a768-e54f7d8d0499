import { mergeAttributes, Node } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setAudioHanlin: {
      setAudioHanlin: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'audioHanlin',
  inline: true,
  group: 'inline',
  content: 'text*',
  atom: true,
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      name: {
        default: null,
      },
      size: {
        default: null,
      },
      src: {
        default: null,
      },
      audioTitle: {
        default: '',
      },
      previewType: {
        default: 'audio',
      },
      duration: {
        default: 0,
      },
    }
  },
  parseHTML() {
    return [{ tag: 'audioHanlin' }]
  },
  renderHTML({ HTMLAttributes }) {
    return ['audioHanlin', mergeAttributes(HTMLAttributes)]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component)
  },
  addCommands() {
    return {
      setAudioHanlin:
        (options) =>
        ({ commands, editor }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
