<template>
  <node-view-wrapper
    :id="node.attrs.id"
    ref="containerRef"
    class="umo-node-playAudio"
    :style="nodeStyle"
    @mouseenter="showChild"
    @mouseleave="cannelHideLayer"
  >
    <div @click="playAudio"><span class="iconAudio"></span></div>
    <div
      class="umo-node-container umo-hover-shadow umo-select-outline umo-node-audio"
      style="display: none"
    >
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1" @click="openDialogEdit">
              <div style="display: flex; align-items: center">
                <SettingIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.setting')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2" @click="handleDelNode">
              <div style="display: flex; align-items: center">
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.remove')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>

      <audio
        ref="audiorRef"
        :src="node.attrs.src"
        crossorigin="anonymous"
        preload="metadata"
        @loadedmetadata="loadedmetadata"
      ></audio>
      <div class="umo-node-audio-title __ellipsis">
        {{ node.attrs.audioTitle }}
      </div>
    </div>

    <t-dialog
      v-model:visible="audioEditPopup"
      attach="body"
      :header="t('insert.image.setting')"
      width="30%"
      :close-on-overlay-click="false"
      @confirm="onSubmit"
      @close="onClose"
      @cancel="onClose"
    >
      <div>
        <t-form ref="formValidatorStatus" :data="formData" :label-width="120">
          <!-- 链接地址-->
          <t-form-item
            :label="t('insert.fileDialog.audioBtn')"
            name="linkAddress"
          >
            <t-button @click="changeFile">
              {{ t('insert.fileDialog.audioBtn') }}
            </t-button>
          </t-form-item>
        </t-form>
      </div>
    </t-dialog>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import type { ReactiveVariable } from '@vue-macros/reactivity-transform/macros'
import { DeleteIcon, EllipsisIcon, SettingIcon } from 'tdesign-icons-vue-next'

import { useMouseOver } from '@/hooks/mouseOver'
import { chooseFile } from '@/utils/file'
import { mediaPlayer } from '@/utils/player'

const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor } = useStore()
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()
const containerRef = ref<HTMLElement | null>(null)
const audiorRef = $ref<ReactiveVariable<HTMLAudioElement> | any | null>(null)
let player = $ref<Plyr | null>(null)
let selected = $ref(false)

const nodeStyle = $computed(() => {
  const { nodeAlign, margin } = node.attrs
  const marginTop =
    margin?.top && margin?.top !== '' ? `${margin.top}px` : undefined
  const marginBottom =
    margin?.bottom && margin?.bottom !== '' ? `${margin.bottom}px` : undefined
  return {
    'justify-content': nodeAlign,
    marginTop,
    marginBottom,
  }
})

let audioEditPopup = $ref(false)

const formData = ref({})
const playAudio = () => {
  if (audiorRef) {
    audiorRef.play()
  }
}
const changeFile = () => {
  chooseFile(
    (file) => {
      formData.value = {
        src: file.fileUrl,
      }
    },
    {
      multiple: false,
      fileType:'.mp3,.wav',
    }
  )
}

const openDialogEdit = () => {
  audioEditPopup = true
  formData.value = {
    src: node.attrs.src,
  }
}

const handleDelNode = () => {
  deleteNode()
}
const onClose = () => {
  audioEditPopup = false
}
const onSubmit = () => {
  updateAttributes({
    ...formData.value,
  })
  onClose()
}

onMounted(() => {
  player = mediaPlayer(audiorRef)
})

const loadedmetadata = () => {
  updateAttributes({
    duration: parseInt(player?.duration),
  })
}

onBeforeUnmount(() => {
  if (player) {
    player?.destroy()
  }
})

onClickOutside(containerRef, () => {
  selected = false
})
</script>

<style lang="less" scoped>
.umo-node-playAudio {
  display: inline-block;
  cursor: pointer;
  position: relative;
}

.iconAudio {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: url('../../assets/icons/audioIcon.svg') no-repeat;
  background-size: 100% 100%;
  position: relative;
  top: 6px;
  padding: 0 5px;
}
</style>
