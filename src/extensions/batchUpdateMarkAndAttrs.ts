import { Extension } from '@tiptap/core'
import { type CommandProps } from '@tiptap/core'

const BatchUpdateMarkAndAttrs = Extension.create({
  name: 'batchUpdateMarkAndAttrs',

  addCommands() {
    return {
      batchUpdateMarkAndAttrs:
        ({ checkNode, handleNode }) =>
        ({ dispatch, state, tr }: CommandProps) => {
          if (dispatch) {
            const { doc } = state
            doc.descendants((node, pos, parent, index) => {
              // console.log(node, pos, parent, index)
              if (checkNode(node, pos, parent, index, tr)) {
                // 处理节点
                handleNode({ node, pos, parent, index }, { tr, state })
              }

              return true
            })
          }
          return true
        },
    }
  },
})

// 检测正文节点
const checkParagraph = (node, pos, parent, index) => {
  const {
    type,
    attrs: { backgroundImage, containerColor },
  } = node
  if (
    type.name === 'paragraph' &&
    parent?.type.name === 'page' &&
    !backgroundImage &&
    !containerColor
  ) {
    return true
  }
  return false
}

// 检测正文的文本节点
const checkParentIsParagraph = (node, pos, parent, index, tr) => {
  const {
    type,
    attrs: { backgroundImage, containerColor },
  } = parent
  if (
    node.isText &&
    type.name === 'paragraph' &&
    !backgroundImage &&
    !containerColor
  ) {
    const { depth } = tr.doc.resolve(pos)
    if (depth === 2) {
      return true
    }
  }
  return false
}

// 检测章头节点
const checkChapterHeader = (node, pos, parent, index) => {
  const type = node.type
  if (type.name === 'chapterHeader') {
    return true
  }
  return false
}

// 检测章头的文本节点
const checkLastParentIsChapterHeader = (node, pos, parent, index, tr) => {
  const { type } = parent
  if (node.isText && type.name === 'paragraph') {
    const { depth, path } = tr.doc.resolve(pos)
    const { type: nodeType } = path[path.length - 6]
    if (nodeType.name === "chapterHeader") {
      return true
    }
  }
  return false
}


// 检测章头的段落节点
const checkParentIsChapterHeader = (node, pos, parent, index, tr) => {
  const { type } = parent
  const { type: pType} = node
  if (type.name === 'chapterHeader' && pType.name === 'paragraph') {
      return true
  }
  return false
}

// 检测节头节点
const checkJointHeader = (node, pos, parent, index) => {
  const type = node.type
  if (type.name === 'jointHeader') {
    return true
  }
  return false
}


// 检测章头的文本节点
const checkLastParentIsJointHeader = (node, pos, parent, index, tr) => {
  const { type } = parent
  if (node.isText && type.name === 'paragraph') {
    const { depth, path } = tr.doc.resolve(pos)
    const { type: nodeType } = path[path.length - 6]
    if (nodeType.name === "jointHeader") {
      return true
    }
  }
  return false
}


// 检测章头的段落节点
const checkParentIsJointHeader = (node, pos, parent, index, tr) => {
  const { type } = parent
  const { type: pType} = node
  if (type.name === 'jointHeader' && pType.name === 'paragraph') {
      return true
  }
  return false
}

export {
  checkParagraph,
  checkParentIsParagraph,
  checkChapterHeader,
  checkParentIsChapterHeader,
  checkLastParentIsChapterHeader,
  checkJointHeader,
  checkLastParentIsJointHeader,
  checkParentIsJointHeader,
  BatchUpdateMarkAndAttrs as default,
}
