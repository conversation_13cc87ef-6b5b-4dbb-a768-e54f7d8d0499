import {
  type CommandProps,
  mergeAttributes,
  Node,
  type NodeViewProps,
} from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import { BLOCKVIEW } from '../page/node-names'
import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setBlock: {
      setBlock: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: BLOCKVIEW,
  group: 'block',
  content: 'block+',
  addAttributes() {
    return {
      id: {
        default: null,
      },
      // 背景图
      backgroundImage: {
        default: '',
      },
      backgroundColor: {
        default: '',
      },
      backgroundRepeat: {
        default: '',
      },
      // 背景图大小
      backgroundSize: {
        default: '',
      },
      backgroundPosition: {
        default: '',
      },
      borderTopWidth: {
        default: '',
      },
      borderTopColor: {
        default: '',
      },
      borderTopStyle: {
        default: '',
      },
      borderBottomWidth: {
        default: '',
      },
      borderBottomColor: {
        default: '',
      },
      borderBottomStyle: {
        default: '',
      },

      borderLeftWidth: {
        default: '',
      },
      borderLeftColor: {
        default: '',
      },
      borderLeftStyle: {
        default: '',
      },

      borderRightWidth: {
        default: '',
      },
      borderRightColor: {
        default: '',
      },
      borderRightStyle: {
        default: '',
      },

      borderRadius: {
        default: 0,
      },
      paddingTop: {
        default: 0,
      },
      paddingRight: {
        default: 0,
      },
      paddingBottom: {
        default: 0,
      },
      paddingLeft: {
        default: 0,
      },
      borderRadiusBottomLeft: {
        default: 0,
      },
      borderRadiusBottomRight: {
        default: 0,
      },
      borderRadiusTopLeft: {
        default: 0,
      },
      borderRadiusTopRight: {
        default: 0,
      },

      fitView: {
        default: false,
      },
      borderStyleValue: {
        default: false,
      },
      paddingLinkValue: {
        default: false,
      },
      raduisLinkValue: {
        default: false,
      },
    }
  },
  parseHTML() {
    return [{ tag: BLOCKVIEW }]
  },
  renderHTML({ HTMLAttributes }) {
    return [
      BLOCKVIEW,
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 
      0
    ]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      setBlock:
        (options) =>
        ({ commands, editor, dispatch, tr }: CommandProps) => {
          console.log(options)
          if (dispatch) {
            tr.replaceRangeWith(
              editor.state.selection.anchor,
              editor.state.selection.anchor,
              editor.schema.node(
                this.name,
                {
                  ...options,
                },
                editor.schema.nodes.paragraph.create(),
              ),
            )
          }
          return true
        },
    }
  },
})
