<template>

  <svg width="1em" height="1em" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_912_5495)">
      <path
        d="M0.666809 0V9.00198L6.90231 14.789H9.67673L13.1418 18.004V14.789H17.2975V3.21499L13.8284 0H0.666809ZM2.05204 1.286H13.1378V10.931H10.0181V13.1815L7.59294 10.931H4.81852L2.0441 8.35899L2.05204 1.286Z"
        :fill="currentColor" />
      <path d="M5.51312 3.53711H4.1279V7.3951H5.51312V3.53711Z" :fill="currentColor" />
      <path d="M9.32746 3.53711H7.94223V7.3951H9.32746V3.53711Z" :fill="currentColor" />
    </g>
    <defs>
      <clipPath id="clip0_912_5495">
        <rect width="1em" height="1em" fill="white" />
      </clipPath>
    </defs>
  </svg>


</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
  color: {
    type: String,
    default: '#000000',
  },

})
const currentColor = ref(props.color)
watch(() => props.color, (newVal) => {
  currentColor.value = newVal
})
</script>
