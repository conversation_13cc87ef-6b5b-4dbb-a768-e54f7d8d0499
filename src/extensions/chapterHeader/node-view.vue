<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view" @mouseenter="showChild"
    @mouseleave="cannelHideLayer">
    <div class="headerbg" :style="style">
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1">
              <div style="display: flex; align-items: center" @click="openSetting">
                <Edit2Icon />
                <span style="margin-left: 5px">{{
                  t('chapterHeader.setting')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2">
              <div style="display: flex; align-items: center" @click="deleteNode">
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.chapterHead.delete')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <div v-show="mousemove" class="toolbar">
        <t-tag theme="warning">章头不能与标题级别一同使用</t-tag>
      </div>
      <div class="header-title" :style="{ color: style.theme === 'light' ? '#fff' : '#333' }">
        <node-view-content />
      </div>
    </div>
    <t-dialog v-model:visible="settingVisible" attach="body" :header="t('chapterHeader.setting')" width="30%"
      :close-on-overlay-click="false" @confirm="onSubmit" @close="settingVisible = false"
      @cancel="settingVisible = false">
      <div>
        <t-form ref="formRef" :data="form" :label-width="120">
          <!-- 图片地址-->
          <t-form-item :label="t('chapterHeader.chapterHeaderUrl')" name="chapterHeaderUrl">
            <t-image v-if="form.chapterHeaderUrl" :src="form.chapterHeaderUrl" style="cursor: pointer"
              @click="changeImg" />

            <t-button v-else @click="changeImg">
              {{ t('chapterHeader.upload') }}
            </t-button>
          </t-form-item>
          <!-- 图片高度-->
          <t-form-item :label="t('chapterHeader.chapterHeaderHeight')" name="chapterHeaderHeight"
            :rules="[{ min: 0, message: t('chapterHeader.heightMax') }]">
            <t-input-number v-model="form.chapterHeaderHeight" :step="1" :min="0"></t-input-number>
          </t-form-item>
        </t-form>
        <div style="padding: 20px 0">
          <t-alert theme="info" message="章头图片建议尺寸为1742*520" />
        </div>
      </div>
    </t-dialog>
  </node-view-wrapper>
</template>
<script setup lang="ts">
import { NodeViewContent, nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { DeleteIcon, Edit2Icon, EllipsisIcon } from 'tdesign-icons-vue-next'

import { useMouseOver } from '@/hooks/mouseOver'
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { node, updateAttributes, deleteNode} = defineProps(nodeViewProps)
const { options, editor, templateObject } = useStore()
const { pageTemplateId } = useTemplate()
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()

let template = $ref(null)
const formRef = ref()
const form = ref({
  chapterHeaderHeight: template?.chapterHeaderHeight || '300',
})
let settingVisible = $ref(false)

const style = computed(() => {
  const style = {
    background: `url(${template?.chapterHeaderUrl}) no-repeat`,
    backgroundSize: 'cover',
    height: `${template?.chapterHeaderHeight / 2.5}px`,
    theme: template?.theme,
  }
  if (node.attrs.chapterHeaderUrl != null) {
    style.background = `url(${node.attrs.chapterHeaderUrl}) no-repeat`
  }
  if (node.attrs.chapterHeaderHeight != null) {
    style.height = `${node.attrs.chapterHeaderHeight / 2.5}px`
  }
  return style
})

onMounted(() => {
  template = pageTemplateId.value
})

watch(
  () => pageTemplateId.value,
  (val) => {
    template = val
  },
)

// 打开设置
const openSetting = () => {
  form.value = {
    chapterHeaderUrl: node.attrs.chapterHeaderUrl,
    chapterHeaderHeight:
      node.attrs.chapterHeaderHeight == null
        ? template?.chapterHeaderHeight
        : node.attrs.chapterHeaderHeight,
  }
  settingVisible = true
}

// 更换图片
const changeImg = () => {
  chooseFile((file) => {
    form.value.chapterHeaderUrl = file.fileUrl
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

const onSubmit = async () => {
  const validateResult = await formRef.value.validate()
  if (validateResult === true) {
    updateAttributes({
      level: form.value.level,
      chapterHeaderUrl: form.value.chapterHeaderUrl,
      chapterHeaderHeight:
        form.value.chapterHeaderHeight == 0
          ? 520
          : form.value.chapterHeaderHeight,
    })
    settingVisible = false
  }
}



</script>
<style lang="less" scoped>
.headerbg {
  width: 100%;
  background-size: auto 100%;
  display: flex;
  align-items: center;
  position: relative;

  .icon {
    position: absolute;
    right: 10px;
    top: 14px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .toolbar {
    position: absolute;
    right: 48px;
    top: 14px;
  }

  .header-title {
    outline: none;
    // padding: 0 20px;
    width: 100%;
  }
}

.footer-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  button {
    margin-left: 10px;
  }
}
</style>
