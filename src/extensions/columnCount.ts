import { Extension } from '@tiptap/core'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setColumnCount: {
      setColumnCount: (columnCount: any) => ReturnType
    }
    unsetColumnCount: {
      unsetColumnCount: () => ReturnType
    }
  }
}

export default Extension.create({
  name: 'columnCount',
  addOptions() {
    return {
      types: [],
      defaultColumnCount: 0,
    }
  },
  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          columnCount: {
            default: this.options.defaultColumnCount,
            parseHTML: (element) =>
              element.style.columnCount || this.options.defaultColumnCount,
            renderHTML: (attributes) => {
              if (attributes.columnCount === this.options.defaultColumnCount) {
                return {}
              }
              return { style: `column-count: ${attributes.columnCount}` }
            },
          },
        },
      },
    ]
  },
  addCommands() {
    return {
      setColumnCount:
        (columnCount) =>
          ({ commands }) => {
            return this.options.types.every((type: string) =>
              commands.updateAttributes(type, { columnCount }),
            )
          },
      unsetColumnCount:
        () =>
          ({ commands }) => {
            return this.options.types.every((type: string) =>
              commands.resetAttributes(type, 'columnCount'),
            )
          },
    }
  },
})
