import { mergeAttributes, Node } from '@tiptap/core'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setParagraphAlign: {
      setParagraphAlign: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'columnItem',
  group: 'columnItem',
  content: 'block+',
  isolating: true,
  addAttributes() {
    return {
      id: {
        default: null,
      },
      width: {
        default: null,
      },
      styleText: {
        default: null,
      },
      alignmentStyleText: {
        default: null,
      },
    }
  },
  parseHTML() {
    return [{ tag: 'columnItem' }]
  },
  renderHTML({ HTMLAttributes, node }) {
    const {
      width = node.attrs.width,
      styleText = node.attrs.styleText,
      alignmentStyleText = node.attrs.alignmentStyleText,
    } = HTMLAttributes
    return [
      'columnItem',
      mergeAttributes(HTMLAttributes, {
        class: 'column-item',
        style: `width:${width ? width : '100'}%;box-sizing: border-box; word-break: break-all; ${styleText ? styleText : ''} ${alignmentStyleText ? alignmentStyleText : ''} `,
      }),
      0,
    ]
  },
  addCommands() {
    return {
      // 设置段落对齐方式
      setParagraphAlign:
        (options: any) =>
        ({ commands }) => {
          return commands.updateAttributes(this.name, {
            alignmentStyleText: options.styleText,
          })
        },
    }
  },
})
