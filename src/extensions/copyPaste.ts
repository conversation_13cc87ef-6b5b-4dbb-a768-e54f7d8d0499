import { Extension } from '@tiptap/core'

import { Node as PMNode } from '@tiptap/pm/model'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    copyNode: {
      copyNode: () => ReturnType
    }

    pasteNode: {
      pasteNode: () => ReturnType
    }
  }
}

export interface CopyPasteNodeOptions {}

export interface CopyPasteNodeStorage {
  copyNode: PMNode
}

export const CopyPasteNode = Extension.create<
  CopyPasteNodeOptions,
  CopyPasteNodeStorage
>({
  name: 'CopyPasteNode',

  addStorage() {
    return {
      copyNode: null,
    }
  },

  addCommands() {
    return {
      copyNode:
        () =>
        ({ editor, tr, dispatch }) => {
          if (dispatch) {
            const $pos = tr.selection.$anchor
            if ($pos.depth == 1) {
              const node = $pos.doc.slice($pos.pos, $pos.pos + 1)
              editor.storage.CopyPasteNode.copyNode = node.content
            } else {
              const node = $pos.node(2)
              editor.storage.CopyPasteNode.copyNode = node
            }
          }
          return true
        },
      pasteNode:
        () =>
        ({ editor, tr, dispatch }) => {
          if (dispatch) {
            const $pos = tr.selection.$anchor
            const copyNode = editor.storage.CopyPasteNode.copyNode
            if (copyNode) {
              copyNode.descendants((node, pos) => {
                node.attrs.id = null
              })
              if ($pos.depth == 1) {
                const from = $pos.pos
                tr.insert(from, copyNode)
              } else {
                const from = $pos.before(2)
                tr.insert(from, copyNode)
              }
            }
          }
          return true
        },
    }
  },
})

export default CopyPasteNode
