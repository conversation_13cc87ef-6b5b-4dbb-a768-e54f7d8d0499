import { Extension, type Range, type Dispatch } from '@tiptap/core'
import { Decoration, DecorationSet } from '@tiptap/pm/view'
import {
  Plugin,
  Plugin<PERSON>ey,
  type EditorState,
  type Transaction,
} from '@tiptap/pm/state'
import { Node as PMNode } from '@tiptap/pm/model'
import {
  PARAGRAPH,
  PAGE,
  HEADING,
  BULLETLIST,
  LISTITEM,
  TASKLIST,
  TASKITEM,
  ORDEREDLIST,
  TABLE,
  TABLEHEADER,
  TABLE_ROW,
  TABLE_CELL,
  TEXT_BOX,
  IMAGE,
  IMAGEGALLERY,
  IMAGELAYOUT,
  BLOCK_QUOTE,
  TABLE_PLUS,
  ERROR_CORRECTION,
  BACKGROUNDIMG
} from '@/extensions/page/node-names'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    errorCorrection: {
      /**
       * @description 设置纠错列表
       */
      setErrorCorrectionList: (
        errorCorrectionList: Array<{
          index: number
          originIndex: number
          origin: string
          new: string
          type: string
          id: string
          adopted: boolean
        }>,
      ) => ReturnType
      setErrorCorrectionAdopted: (
        errorCorrectionList: Array<{
          index: number
          originIndex: number
          origin: string
          new: string
          type: string
          id: string
          adopted: boolean
        }>,
      ) => ReturnType
    }
  }
}

interface TextNodesWithPosition {
  text: string
  pos: number
}

interface ProcessedErrorCorrection {
  decorationsToReturn: DecorationSet
}
const whilteList = [
  PARAGRAPH,
  PAGE,
  HEADING,
  BULLETLIST,
  LISTITEM,
  TASKLIST,
  TASKITEM,
  ORDEREDLIST,
  TABLE,
  TABLEHEADER,
  TABLE_ROW,
  TABLE_CELL,
  TEXT_BOX,
  IMAGE,
  IMAGEGALLERY,
  IMAGELAYOUT,
  BLOCK_QUOTE,
  TABLE_PLUS,
  BACKGROUNDIMG
]
function processErrorCorrection(
  doc: PMNode,
  errorCorrectionList: Array<{
    index: number
    originIndex: number
    origin: string
    new: string
    type: string
    id: string
    adopted: boolean
  }>,
  errorCorrectionNotAdoptedResultClass: string,
  errorCorrectionAdoptedResultClass: string,
  insertText: (
    text: string,
    from?: number | undefined,
    to?: number | undefined,
  ) => Transaction,
): ProcessedErrorCorrection {
  const decorations: Decoration[] = []
  const result: Array<{
    from: number
    to: number
    originLen: number
    newLen: number
    adopted: boolean
  }> = []
  // console.log('开始纠错==============>')
  doc?.descendants((node, pos) => {
    let nodeTypeName = node.type.name
    if (node.attrs.id && whilteList.includes(nodeTypeName)) {
      // console.log('node', node, node.type.name, pos)
      if (nodeTypeName == PARAGRAPH || nodeTypeName == HEADING) {
        if (node.textContent.length > 0) {
          let curNodeErrorCorrectionList = errorCorrectionList.filter(
            (o) => o.id == node.attrs.id,
          )
          curNodeErrorCorrectionList.forEach((item) => {
            let textIndex = 0
            let errorCorrectionClass = errorCorrectionNotAdoptedResultClass
            if (item.adopted) {
              errorCorrectionClass = errorCorrectionAdoptedResultClass
            } else {
              errorCorrectionClass = errorCorrectionNotAdoptedResultClass
            }
            node.descendants((textNode, textPos) => {
              if (textNode.type.name == 'text') {
                if (
                  item.index >= textIndex &&
                  item.index < textIndex + textNode?.text.length
                ) {
                  let from = pos + textPos + 1 + item.index - textIndex
                  let to =
                    pos +
                    textPos +
                    1 +
                    item.index -
                    textIndex +
                    (item.adopted ? item.new.length : item.origin.length)
                  const decoration: Decoration = Decoration.inline(from, to, {
                    class: errorCorrectionClass,
                  })
                  result.push({
                    from,
                    to,
                    originIndex: item.originIndex,
                    originLen: item.origin.length,
                    newLen: item.new.length,
                    adopted: item.adopted,
                  })
                  // console.log('纠错', textNode, pos, textPos, decoration)
                  decorations.push(decoration)
                }
                textIndex += textNode.text.length
              }
            })
          })
        }
        return false
      }
      return true
    }
    return false
  })
  return {
    decorationsToReturn: DecorationSet.create(doc, decorations),
    result: result,
  }
}

export const errorCorrectionKey = new PluginKey(ERROR_CORRECTION)

export const ErrorCorrection = Extension.create({
  name: ERROR_CORRECTION,

  addOptions() {
    return {
      errorCorrectionAdoptedResultClass: 'adopted-result',
      errorCorrectionNotAdoptedResultClass: 'not-adopted-result',
    }
  },

  addStorage() {
    return {
      errorCorrectionList: [],
      result: [],
    }
  },

  addCommands() {
    return {
      setErrorCorrectionList:
        (errorCorrectionList = []) =>
        ({ editor, tr, dispatch }) => {
          // let changeIndex = 0
          // for (let i = 0; i < errorCorrectionList.length; i++) {
          //   const item = errorCorrectionList[i]
          //   item.index = item.originIndex + changeIndex
          //   if (item.adopted) {
          //     changeIndex += item.new.length - item.origin.length
          //   }
          // }
          editor.storage[ERROR_CORRECTION].errorCorrectionList = JSON.parse(
            JSON.stringify(errorCorrectionList),
          )
          // editor.storage[ERROR_CORRECTION].errorCorrectionList =
          //   errorCorrectionList
          return true
        },
      setErrorCorrectionAdopted:
        (newErrorCorrectionList = []) =>
        ({ editor, tr, dispatch }) => {
          if (dispatch) {
            let oldErrorCorrectionList =
              editor.storage[ERROR_CORRECTION].errorCorrectionList
            // console.log('纠错', oldErrorCorrectionList, newErrorCorrectionList)
            let result = editor.storage[ERROR_CORRECTION].result
            let changeIndex = 0
            for (let i = 0; i < newErrorCorrectionList.length; i++) {
              const newItem = newErrorCorrectionList[i]
              const oldItem = oldErrorCorrectionList[i]
              const originLen = newItem.origin.length
              const newLen = newItem.new.length
              // console.log(
              //   'index',
              //   newItem.index,
              //   newItem.originIndex + changeIndex,
              //   changeIndex,
              // )
              newItem.index = newItem.originIndex + changeIndex
              // console.log('纠错', newItem, oldItem)
              if (newItem.adopted && !oldItem.adopted) {
                // console.log('新纠错', newItem)
                // 新纠错
                changeIndex += newLen - originLen
                let res = result.find(
                  (item) => item.originIndex === newItem.originIndex,
                )
                // if (newItem.new && newItem.new != '') {
                tr.insertText(newItem.new, res.from, res.to)
                // } else {
                //   console.log('删除', res)
                //   tr.delete(res.from, res.to)
                // }
              } else if (!newItem.adopted && oldItem.adopted) {
                // 取消纠错
                // changeIndex += originLen - newLen
                let res = result.find(
                  (item) => item.originIndex === newItem.originIndex,
                )
                // console.log('取消纠错', res, newItem)
                // if (newItem.new){
                tr.insertText(newItem.origin, res.from, res.to)
                // } else {
                //   tr.delete(res.from, res.to)
                // }
              } else if (newItem.adopted && oldItem.adopted) {
                changeIndex += newLen - originLen
              }
            }
            editor.storage[ERROR_CORRECTION].errorCorrectionList = JSON.parse(
              JSON.stringify(newErrorCorrectionList),
            )
            // editor.storage[ERROR_CORRECTION].errorCorrectionList =
            //   newErrorCorrectionList
          }

          return true
        },
    }
  },

  addProseMirrorPlugins() {
    const editor = this.editor
    const {
      errorCorrectionAdoptedResultClass,
      errorCorrectionNotAdoptedResultClass,
    } = this.options

    return [
      new Plugin({
        key: errorCorrectionKey,
        state: {
          init: () => DecorationSet.empty,
          apply({ doc, docChanged, insertText }, oldState) {
            // console.log('apply', tr, value, oldState, newState)
            // return DecorationSet.empty
            const { errorCorrectionList } = editor.storage[ERROR_CORRECTION]

            // if (!docChanged) return oldState

            if (!errorCorrectionList || errorCorrectionList.length === 0) {
              return DecorationSet.empty
            }

            const { decorationsToReturn, result } = processErrorCorrection(
              doc,
              errorCorrectionList,
              errorCorrectionNotAdoptedResultClass,
              errorCorrectionAdoptedResultClass,
              insertText,
            )
            editor.storage[ERROR_CORRECTION].result = result
            return decorationsToReturn
          },
        },
        props: {
          decorations(state) {
            return this.getState(state)
          },
        },
      }),
    ]
  },
})

export default ErrorCorrection
