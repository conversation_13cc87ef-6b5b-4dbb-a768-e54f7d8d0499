import {
  type CommandProps,
  mergeAttributes,
  Node,
  type NodeViewProps,
} from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import { EXTENDED_READING } from '../page/node-names'
import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setExtendedReading: {
      setExtendedReading: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: EXTENDED_READING,
  group: 'block',
  content: 'block+',
  addAttributes() {
    return {
      id: {
        default: null,
      },
      name: {
        default: '',
      },
      isExpand: {
        default: true, //是否在阅读器展开
      },
    }
  },
  parseHTML() {
    return [{ tag: EXTENDED_READING }]
  },
  renderHTML({ HTMLAttributes }) {
    return [
      EXTENDED_READING,
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
      0
    ]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      setExtendedReading:
        (options) =>
        ({ commands, editor, dispatch, tr }: CommandProps) => {
          if (dispatch) {
            tr.replaceRangeWith(
              editor.state.selection.anchor,
              editor.state.selection.anchor,
              editor.schema.node(
                this.name,
                {
                  name: options.name,
                },
                editor.schema.nodes.paragraph.create(),
              ),
            )
          }
          return true
        },
    }
  },
})
