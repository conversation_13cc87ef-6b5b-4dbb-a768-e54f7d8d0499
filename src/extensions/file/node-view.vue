<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view" :style="nodeStyle"
    @mouseenter="showChild" @mouseleave="cannelHideLayer">
    <div class="umo-node-view-extended-reading">
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1">
              <div style="display: flex; align-items: center" @click="previewResource">
                <icon name="view" />
                <span style="margin-left: 5px">{{ t('file.preview') }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="3">
              <div style="display: flex; align-items: center" @click="changeFile">
                <FileImportIcon />
                <span style="margin-left: 5px">{{
                  t('rc.menu.changeFile')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2">
              <div style="display: flex; align-items: center" @click="delRC">
                <DeleteIcon />
                <span style="margin-left: 5px">{{ t('rc.menu.delete') }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <div class="extended-reading-template-bg"
        :style="`background: url(${template?.orderTemplateBgUrl ?? node.attrs.bgSrc}) no-repeat center;height:55px;background-size:100% 100%;`">
        <div class="extended-left">
          <div :class="template.theme == 'light' ? 'extended-iconDark' : 'extended-icon'"></div>
          <div class="extended-title" :style="{ color: template?.theme == 'light' ? '#fff' : '#333' }">文件</div>

          <div class="extended-name" :style="{ color: template?.theme == 'light' ? '#fff' : '#333' }">
            <t-tooltip :content="node.attrs.name" :show-arrow="false">
              {{ node.attrs.name }}
            </t-tooltip>
          </div>
        </div>
        <!-- <div class="extended-right">收起</div> -->
      </div>
    </div>
    <modal :visible="resourceShow" width="1100px" :header="node.attrs.name" :confirm-btn="null" :cancel-btn="null"
      @close="resourceShow = false">
      <div style="height: 650px; margin: 10px 0">
        <iframe :src="fileUrl" frameborder="0" width="100%" height="100%"></iframe>
      </div>
    </modal>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { Base64 } from 'js-base64'
import {
  DeleteIcon,
  EllipsisIcon,
  FileImportIcon,
} from 'tdesign-icons-vue-next'

import { OssService } from '@/utils/aliOss'
import { getFileIcon } from '@/utils/file'
const previewUrl = import.meta.env.VITE_ONLINE_PREVIEW
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor } = useStore()
const containerRef = ref(null)
let filePath = $ref('')
let fileUrl = $ref('')
const { pageTemplateId } = useTemplate()
const template = pageTemplateId.value
let mousemove = $ref(false)
let isMenuActive = $ref(false)
const nodeStyle = $computed(() => {
  const { nodeAlign, margin } = node.attrs
  const marginTop =
    margin?.top && margin?.top !== '' ? `${margin.top}px` : undefined
  const marginBottom =
    margin?.bottom && margin?.bottom !== '' ? `${margin.bottom}px` : undefined
  return {
    'justify-content': nodeAlign,
    marginTop,
    marginBottom,
  }
})

onMounted(() => {
  const fileIcon = getFileIcon(node.attrs.name)
  filePath = `${options.value.cdnUrl}/icons/file/${fileIcon}.svg`
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const delRC = () => {
  deleteNode()
}

const handleClickOutside = (e) => {
  if (!e.target.closest('.top-node-mu')) {
    mousemove = false
    isMenuActive = false
  }
}
let hideTimer: any = null
const showChild = () => {
  mousemove = true
  clearTimeout(hideTimer)
}

const cannelHideLayer = () => {
  hideTimer = setTimeout(() => {
    if (!isMenuActive) {
      mousemove = false
    }
  }, 200)
}

const keepOpen = () => {
  isMenuActive = true
  clearTimeout(hideTimer)
}

const supportPreviewTypes = ['imageLayout', 'video', 'audio']

const togglePreview = () => {
  const { attrs } = node
  editor.value?.commands.insertContent({
    type: attrs.previewType,
    attrs: {
      ...attrs,
      src: attrs.url,
    },
  })
}
// 预览资源
let resourceShow = $ref(false)
function previewResource() {
  fileUrl = previewUrl + encodeURIComponent(Base64.encode(node.attrs.url))
  resourceShow = true
}
const loading = ref(false)
function changeFile() {
  if (node.attrs.url === '#') {
    return useMessage('error', t(`rc.file.uploadChangeError`))
  }
  const { open, onChange } = useFileDialog({
    reset: true,
    multiple: false,
  })
  open()
  onChange(async (fileList) => {
    const files = Array.from(fileList ?? [])
    let name = ''
    loading.value = true
    name = files[0].name
    if (!files) {
      return
    }
    for (const file of files) {
      const { name } = file
      const suffix = name.split('.')[1].toLowerCase()

      const { size, name: filename } = file
      const { fileSize } = options.value.resourceCover as any
      if (fileSize !== 0 && size > fileSize) {
        useMessage('error', t(`rc.file.fileLimit`, { filename, fileSize }))
        return false
      }
      const res: any = await OssService(file)
      updateAttributes({
        url: res.url,
        name: name || res.result.name.split('.')[0],
      })
      loading.value = false
    }
  })
}
</script>

<style lang="less" scoped>
.umo-node-view {
  background-color: transparent;
}

.umo-node-view-extended-reading {
  position: relative;
  line-height: 30px;
  width: 100%;

  .icon {
    position: absolute;
    right: 10px;
    top: 14px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .extended-reading-template-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;

    .extended-left {
      display: flex;
      align-items: center;

      .extended-icon {
        width: 24px;
        height: 24px;
        background: url('@/assets/icons/fileNew.svg') no-repeat center;
        background-size: 100% 100%;
      }

      .extended-iconDark {
        width: 24px;
        height: 24px;
        background: url('@/assets/icons/file-light.svg') no-repeat center;
        background-size: 100% 100%;
      }

      .extended-title {
        padding: 0 10px;
        font-size: 16px;
        font-weight: 400;
      }

      .extended-name {
        color: #333;
        font-size: 16px;
        width: 450px;
        white-space: nowrap;
        /* 确保文本在一行内显示 */
        overflow: hidden;
        /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis;
        /* 使用省略号表示被截断的文本 */
      }
    }

    .extended-right {
      color: #666;
      font-size: 16px;
    }
  }

  .extended-reading-template-content {
    background-color: #f6f6f6;
    border: 1px solid #ebebeb;
    padding: 20px 16px;

  }
}
</style>
