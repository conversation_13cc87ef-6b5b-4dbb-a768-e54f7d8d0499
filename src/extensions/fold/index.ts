import {
  type CommandProps,
  mergeAttributes,
  Node,
  type NodeViewProps,
} from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import { FOLD } from '../page/node-names'
import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setFold: {
      setFold: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: FOLD,
  group: 'block',
  content: 'block+',
  addAttributes() {
    return {
      id: {
        default: null,
      },
      // 背景图
      iconUrl: {
        default: 'foldIcon1',
      },
      title: {
        default: '',
      },
      isOpen: {
        default: false,
      },
      isBg: {
        default: true,
      },
      fontColor: {
        default: '#000',
      },
      fontSize: {
        default: '14',
      },
      fontFamily: {
        default: '',
      },
      isStrong: {
        default: false,
      },
      expandText: {
        default: '展开',
      },
      collapseText: {
        default: '收起',
      },
      iconColor: {
        default: '#000',
      },
    }
  },
  parseHTML() {
    return [{ tag: FOLD }]
  },
  renderHTML({ HTMLAttributes }) {
    return [FOLD, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      setFold:
        (options) =>
        ({ commands, editor, dispatch, tr }: CommandProps) => {
         
          if (dispatch) {
            tr.replaceRangeWith(
              editor.state.selection.anchor,
              editor.state.selection.anchor,
              editor.schema.node(
                this.name,
                {
                  ...options,
                },
                editor.schema.nodes.paragraph.create(),
              ),
            )
          }
          return true
        },
    }
  },
})
