<template>
    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="26" height="26" fill="none" />
        <path
            d="M3.79123 2.20412C4.27678 1.93196 4.87697 1.93196 5.36252 2.20412L22.2151 11.6758C22.7046 11.9473 23.004 12.4485 23.0002 13C23.0002 13.5442 22.7004 14.052 22.2151 14.3242L5.35666 23.7959C4.87116 24.0679 4.27082 24.068 3.78537 23.7959C3.38216 23.5698 3.10721 23.1838 3.0256 22.7451C2.9927 22.5682 3.04881 22.39 3.14084 22.2354L8.63888 13.002L3.14084 3.76955C3.04877 3.6149 2.99284 3.43675 3.0256 3.25979C3.10726 2.81974 3.38318 2.43031 3.79123 2.20412Z"
            :fill="currentColor" />
    </svg>
</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
    color: {
        type: String,
        default: '#000000',
    },

})
const currentColor = ref(props.color)
watch(() => props.color, (newVal) => {
    currentColor.value = newVal
})
</script>