<template>
    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="26" height="26" fill="none" />
        <path d="M6.53118 5.90259H2L8.16 13L2 20.0975H6.53118L12.6912 13L6.53118 5.90259Z" :fill="currentColor" />
        <path d="M14.4513 2H7.42773L16.9766 13L7.42773 24H14.4513L24.0002 13L14.4513 2Z" :fill="currentColor" />
    </svg>
</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
    color: {
        type: String,
        default: '#000000',
    },

})
const currentColor = ref(props.color)
watch(() => props.color, (newVal) => {
    currentColor.value = newVal
})
</script>