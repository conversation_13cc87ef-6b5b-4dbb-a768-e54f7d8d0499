<template>



    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="26" height="26" fill="none" />
        <path
            d="M8.31117 5.88319L17.8237 5.88319L17.8237 3.00004L24 12.6784L17.8237 22.355L17.8237 19.3609L8.31117 19.3609L4.58343 19.3609C3.16263 19.3609 2 20.6298 2 22.1805L2 8.70272C2 7.15207 3.16263 5.88319 4.58343 5.88319L8.31117 5.88319Z"
            :fill="currentColor" />
    </svg>
</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
    color: {
        type: String,
        default: '#000000',
    },

})
const currentColor = ref(props.color)
watch(() => props.color, (newVal) => {
    currentColor.value = newVal
})
</script>