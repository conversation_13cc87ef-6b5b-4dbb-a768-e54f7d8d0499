import Image from '@tiptap/extension-image'
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'
import { type CommandProps, VueNodeViewRenderer } from '@tiptap/vue-3'
import { Base64 } from 'js-base64'
import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setImage: {
      setImage: (options: any, replace?: any) => ReturnType
    }
  }
}
export default Image.extend({
  atom: true,
  addOptions() {
    return {
      inline: false,
    }
  },
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      type: {
        default: 'image',
      },
      name: {
        default: null,
      },
      size: {
        default: null,
      },
      src: {
        default: null,
      },
      content: {
        default: null,
      },
      width: {
        default: null,
      },
      height: {
        default: 200,
      },
      left: {
        default: 0,
      },
      top: {
        default: 0,
      },
      angle: {
        default: null,
      },
      draggable: {
        default: false,
      },
      rotatable: {
        default: false,
      },
      equalProportion: {
        default: true,
      },
      flipX: {
        default: false,
      },
      flipY: {
        default: false,
      },
      uploaded: {
        default: false,
      },
      error: {
        default: false,
      },
      previewType: {
        default: 'image',
      },
      isShowImageTitle: {
        default: '0',
      },
      isShowNo: {
        default: 0,
      },
      number: {
        default: '',
      },
      linkAddress: {
        default: '',
      },
      formulaType: {
        default: '',
      },
      providerName: {
        default: '',
      },
      imageTitle: {
        default: t('insert.image.imageTitle'),
      },
    }
  },
  parseHTML() {
    return [{ tag: 'img' }]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView)
  },
  addCommands() {
    return {
      setImage:
        (
          options: { src: string; alt?: string; title?: string },
          replace?: boolean,
        ) =>
        ({ commands, editor }: CommandProps) => {
          if (options.content) {
            options.content = Base64.encode(options.content)
          }
          if (replace) {
            return commands.insertContent({
              type: this.name,
              attrs: options,
            })
          }

          return commands.insertContentAt(editor.state.selection.anchor, {
            type: this.name,
            attrs: options,
          })
        },
    }
  }
})
