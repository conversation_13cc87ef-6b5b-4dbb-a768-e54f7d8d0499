<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view" :class="{
    'umo-node-view-empty': node.attrs.draggable,
  }" :style="nodeStyle" @mouseenter="showChild" @mouseleave="cannelHideLayer">
    <div class="umo-node-container umo-node-image" :class="{
      'is-loading': node.attrs.src && isLoading,
      'is-error': node.attrs.src && error,
      'is-draggable': node.attrs.draggable,
      'umo-hover-shadow': !options.document?.readOnly,
      'umo-select-outline': !node.attrs.draggable,
    }">
      <div v-if="node.attrs.src && isLoading" class="loading" :style="{ height: `${node.attrs.height}px` }">
        <icon name="loading" class="loading-icon" />
        {{ t('node.image.loading') }}
      </div>
      <div v-else-if="node.attrs.src && error" class="error" :style="{ height: `${node.attrs.height}px` }">
        <icon name="image-failed" class="error-icon" />
        {{ t('node.image.error') }}
      </div>
      <drager v-else :selected="selected" :rotatable="true" :boundary="false" :draggable="Boolean(node.attrs.draggable) && !options.document?.readOnly
        " :disabled="options.document?.readOnly" :angle="node.attrs.angle" :width="Number(node.attrs.width)"
        :height="Number(node.attrs.height)" :left="Number(node.attrs.left)" :top="Number(node.attrs.top)"
        :min-width="14" :min-height="14" :max-width="maxWidth" :z-index="10"
        :equal-proportion="node.attrs.equalProportion" style="margin: 0 auto" @rotate="onRotate" @resize="onResize"
        @drag="onDrag" @click="selected = true">
        <div v-show="mousemove" class="top-node-mu" @click.stop="keepOpen">
          <t-dropdown :tooltip="false" trigger="click">
            <div style="
      width: 34px;
      height: 34px;      
      display: flex;
      justify-content: center;
     align-items: center;">
              <EllipsisIcon />
            </div>

            <template #dropdown>
              <t-dropdown-item :value="1">
                <div style="display: flex; align-items: center" @click="viewerImg">
                  <BrowseIcon />
                  <span style="margin-left: 5px">{{
                    t('insert.image.preview')
                  }}</span>
                </div>
              </t-dropdown-item>
              <t-dropdown-item :value="2">
                <div style="display: flex; align-items: center" @click="imageEdit">
                  <FileSearchIcon />
                  <span style="margin-left: 5px">{{
                    t('insert.image.setting')
                  }}</span>
                </div>
              </t-dropdown-item>
              <!-- <t-dropdown-item :value="3">
                <div
                  style="display: flex; align-items: center"
                  @click="imgCropper"
                >
                  <TransformIcon />
                  <span style="margin-left: 5px">{{
                    t('insert.image.crop')
                  }}</span>
                </div>
              </t-dropdown-item> -->
              <!-- <t-dropdown-item
                :value="4"
              >
                <div
                  style="display: flex; align-items: center"
                  @click="editFormula"
                >
                  <EditIcon />
                  <span style="margin-left: 5px">{{
                    t('insert.image.edit')
                  }}</span>
                </div>
              </t-dropdown-item> -->
              <t-dropdown-item :value="5">
                <div style="display: flex; align-items: center" @click="handleDelNode">
                  <DeleteIcon />
                  <span style="margin-left: 5px">{{
                    t('insert.image.remove')
                  }}</span>
                </div>
              </t-dropdown-item>
            </template>
          </t-dropdown>
        </div>
        <div class="node-view-image-body">
          <img ref="imageRef" :src="node.attrs.src" :style="{
            transform:
              node.attrs.flipX || node.attrs.flipY
                ? `rotateX(${node.attrs.flipX ? '180' : '0'}deg) rotateY(${node.attrs.flipY ? '180' : '0'}deg)`
                : 'none',
          }" :data-id="node.attrs.id" loading="lazy" object-fit="contain" @load="onLoad" />
        </div>
      </drager>
      <div class="title">
        <text v-if="node.attrs.isShowNo == 1" style="margin-right: 20px">{{
          node.attrs.number
        }}</text>
        <text v-if="node.attrs.isShowImageTitle == '1'">{{
          node.attrs.imageTitle || '标题图片'
        }}</text>
      </div>
    </div>
  </node-view-wrapper>
  <t-dialog v-model:visible="viewerVisible" :header="t('insert.image.preview')" attach="body" width="30%"
    :confirm-btn="null" :close-on-overlay-click="false">
    <imgView :img-url="node.attrs.src" :img-title="node.attrs.imageTitle"
      :is-show-image-title="node.attrs.isShowImageTitle" :link-address="''" :confirm-btn="null" />
  </t-dialog>
  <t-dialog v-model:visible="imageEditPopup" attach="body" :header="t('insert.image.setting')" width="30%"
    :close-on-overlay-click="false" @confirm="onSubmit" @opened="onOpen" @closed="onClose" @cancel="onClose">
    <div>
      <t-form ref="formValidatorStatus" :data="formData" :rules="rules" :label-width="120">
        <!-- 图片标题-->
        <t-form-item :label="t('insert.image.imageTitle')" name="imageTitle">
          <t-input v-model="formData.imageTitle" :placeholder="t('insert.image.imagePlaceholder')"></t-input>
        </t-form-item>
        <!-- 是否显示图号 -->
        <t-form-item :label="t('insert.image.isShowNo')" name="isShowNo">
          <t-radio-group v-model="formData.isShowNo">
            <t-radio :value="1" allow-uncheck>{{
              t('insert.image.yes')
            }}</t-radio>
            <t-radio :value="0">{{ t('insert.image.no') }}</t-radio>
          </t-radio-group>
        </t-form-item>
        <!-- 是否显示图片标题1 -->
        <t-form-item :label="t('insert.image.isShowImgTitle')" name="isShowGalleryTitle">
          <t-radio-group v-model="formData.isShowImageTitle">
            <t-radio value="1" allow-uncheck>{{
              t('insert.image.yes')
            }}</t-radio>
            <t-radio value="0">{{ t('insert.image.no') }}</t-radio>
          </t-radio-group>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
  <!-- <cropper-dialog
    v-model:cropper-visible="imgVisible"
    :url="node.attrs.src"
    :file-name="node.attrs.name"
    @save-cropper="saveCropper"
  ></cropper-dialog> -->

  <!-- 这里采用根据是否打开传参是因为，会预加载latex导致 latex报错-->
  <formula-dialog :visibility="formulaDialogVisibility"
    :provider-name="formulaDialogVisibility ? node.attrs.providerName : ''"
    :formula-type="formulaDialogVisibility ? node.attrs.formulaType : ''"
    :content="node.attrs.content ? Base64.decode(node.attrs.content) : null" @confirm="confirmOnFormulaEditorDialog"
    @cancel="formulaDialogVisibility = false"></formula-dialog>
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import Drager from 'es-drager'
import { Base64 } from 'js-base64'
import {
  BrowseIcon,
  DeleteIcon,
  FileSearchIcon,
} from 'tdesign-icons-vue-next'
import { EllipsisIcon } from 'tdesign-icons-vue-next'

// import cropperDialog from '@/components/cropperDialog/index.vue'
import imgView from '@/components/menus/toolbar/insert/components/image/imgView.vue'
import FormulaDialog from '@/components/menus/toolbar/modal/formulaDialog.vue'
import { getSelectionNode } from '@/extensions/selection'
import { useMouseOver } from '@/hooks/mouseOver'
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, imageViewer, getCaptionStyle, captionStyle } =
  useStore()
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()

const { isLoading, error } = useImage({ src: node.attrs.src })
let formulaDialogVisibility = $ref(false)
const imageEditPopup = ref(false)
const containerRef: any = ref(null)
const formData = ref({})
const imageRef = $ref<HTMLImageElement | null>(null)
let selected = $ref(false)
let maxWidth = $ref(0)
let maxHeight = $ref(0)

const nodeStyle = $computed(() => {
  const { nodeAlign, margin } = node.attrs
  const marginTop =
    margin?.top && margin?.top !== '' ? `${margin.top}px` : undefined
  const marginBottom =
    margin?.bottom && margin?.bottom !== '' ? `${margin.bottom}px` : undefined

  return {
    justifyContent: nodeAlign,
    marginTop,
    marginBottom,
    zIndex: selected ? 100 : node.attrs.draggable ? 95 : 0,

  }
})

function confirmOnFormulaEditorDialog({ url, width, height, name, langData }) {
  formulaDialogVisibility = false
  updateAttributes({
    src: url,
    content: Base64.encode(langData),
    width,
    height,
    imageTitle: name,
    name,
  })
}

function handleDelNode() {
  deleteNode()
}

const viewerVisible = ref(false)
const viewerImg = () => {
  const image = getSelectionNode(editor.value)
  imageViewer.value.current = image.attrs.id
  imageViewer.value.visible = true
  // viewerVisible.value = true
}

// 开启dialog
const imageEdit = () => {
  imageEditPopup.value = true
  formData.value = {
    imageTitle: node.attrs.imageTitle,
    linkAddress: node.attrs.src,
    isShowNo: Number(node.attrs.isShowNo),
    isShowImageTitle: node.attrs.isShowImageTitle,
  }
}

// const imgVisible = ref(false)
// const imgCropper = () => {
//   imgVisible.value = true
// }

const editFormula = () => {
  if (!node.attrs.formulaType) {
    useMessage('error', '旧数据不支持编辑')
    return
  }
  formulaDialogVisibility = true
}

const saveCropper = (url) => {
  updateAttributes({ src: url })
}

const onClose = () => {
  imageEditPopup.value = false
}

const onLoad = async () => {
  if (node.attrs.number == '') {
    getCaptionStyle().then((res) => {
      if (res.imageNumberType == 1) {
        updateAttributes({
          isShowNo: 1,
          number: '图1',
        })
      } else if (res.imageNumberType == 2) {
        updateAttributes({
          isShowNo: 1,
          number: '图1-1',
        })
      } else {
        updateAttributes({
          isShowNo: 0,
        })
      }
    })
  }

  if (node.attrs.width === null) {
    const { clientWidth = 1, clientHeight = 1 } = imageRef ?? {}
    maxWidth = containerRef.value?.$el.clientWidth
    const ratio = clientWidth / clientHeight
    maxHeight = containerRef.value?.$el.clientWidth / ratio
    updateAttributes({ width: (200 * ratio).toFixed(2) })
  }
  if ([null, 'auto', 0].includes(node.attrs.height)) {
    await nextTick()
    const { height } = imageRef?.getBoundingClientRect() ?? {}
    updateAttributes({ height: (height as number).toFixed(2) })
  }
}

const onRotate = ({ angle }: { angle: number }) => {
  updateAttributes({ angle })
}
const onResize = ({ width, height }: { width: number; height: number }) => {
  updateAttributes({
    width: width.toFixed(2),
    height: height.toFixed(2),
  })
}

const onOpen = () => {
  nextTick(() => {
    formValidatorStatus.value.clearValidate()
  })
}
const formValidatorStatus = ref(null)
const rules = reactive({
  imageTitle: [
    {
      required: true,
      message: t('insert.image.imagePlaceholder'),
      type: 'error',
      trigger: 'blur',
    },
  ],
})

const onSubmit = () => {
  const validateResult = formValidatorStatus.value.validate()
  validateResult.then((valid) => {
    if (valid == true) {
      const validProtocols = /^(?:(http|https|ftp|ftps|mailto):\/\/)|^www\./i
      if (
        formData.value.linkAddress != '' &&
        !validProtocols.test(formData.value.linkAddress)
      ) {
        useMessage('error', '请填写正确的链接地址')
        return false
      }
      if (formData.value.linkAddress.startsWith('www.')) {
        formData.value.linkAddress = `https://${formData.value.linkAddress}`
      }
      updateAttributes({
        imageTitle: formData.value.imageTitle,
        isShowNo: formData.value.isShowNo,
        isShowImageTitle: formData.value.isShowImageTitle,
        linkAddress: formData.value.linkAddress,
      })
      imageEditPopup.value = false
    }
  })
}

const onDrag = ({ left, top }: { left: number; top: number }) => {
  updateAttributes({ left, top })
}

onClickOutside(containerRef, () => {
  selected = false
})

const openImageViewer = () => {
  imageViewer.value.visible = true
  imageViewer.value.current = node.attrs.id
}

watch(
  () => node.attrs.equalProportion,
  async () => {
    await nextTick()
    const width = imageRef?.offsetWidth ?? 1
    const height = imageRef?.offsetHeight ?? 1
    updateAttributes({ width, height })
  },
)

watch(
  () => error.value,
  (errorValue: any) => {
    if (errorValue?.type) {
      updateAttributes({ error: errorValue.type === 'error' })
    } else {
      updateAttributes({ error: false })
    }
  },
)
</script>

<style lang="less">
.umo-node-view {
  .umo-node-image {
    max-width: 100%;
    width: auto;
    position: relative;
    z-index: 20;

    .top-node-mu {
      position: absolute;
      right: 0px;
      top: 0px;
      z-index: 99;
      cursor: pointer;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
      color: #fff;
      width: 34px;
      height: 34px;
    }

    .node-view-image-body {
      display: flex;
      flex-direction: column;
    }

    &.is-loading,
    &.is-error {
      outline: none !important;
      box-shadow: none !important;
    }

    &:not(.is-draggable) .es-drager {
      max-width: 100%;
      max-height: 100%;
    }

    img {
      display: block;
      max-width: 100%;
      max-height: 100%;
      width: 100%;
      height: 100%;
    }

    .title {
      margin: 5px 0;

      width: 100%;
      text-align: center;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--umo-text-color-light);
      font-size: 12px;
      gap: 10px;

      .loading-icon {
        color: var(--umo-primary-color);
        font-size: 22px;
        animation: turn 1s linear infinite;
      }
    }

    .error {
      width: 200px;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: var(--umo-text-color-light);
      font-size: 12px;

      .error-icon {
        font-size: 72px;
        margin: -8px 0 -2px;
      }
    }

    .uploading {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.1);

      span {
        display: block;
        position: absolute;
        background: rgba(0, 0, 0, 0.2);
        height: 4px;
        border-radius: 2px;
        top: 50%;
        left: 20%;
        right: 20%;
        transform: translateY(-50%);
        overflow: hidden;

        &:after {
          content: '';
          display: block;
          height: 100%;
          background-color: var(--umo-primary-color);
          animation: progress 1s linear infinite;
        }
      }
    }
  }
}

@keyframes turn {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes progress {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}
</style>
<style lang="less" scoped>
.cropper-container {
  width: 98%;
  height: 300px;
}
</style>
