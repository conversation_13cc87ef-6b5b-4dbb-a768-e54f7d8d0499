import { mergeAttributes, Node } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setImageGallery: {
      setImageGallery: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'imageGallery',
  group: 'block',
  content: '', // gallery 不应该包含其他内联内容

  addAttributes() {
    return {
      id: {
        default: null,
      },
      vnode: {
        default: true,
      },
      name: {
        default: null,
      },
      size: {
        default: null,
      },
      file: {
        default: null,
      },
      src: {
        default: null,
      },
      galleryTitle: {
        default: t('insert.image.galleryTitle'),
      },
      isShowNo: {
        default: 0,
      },
      isShowGalleryTitle: {
        default: '1',
      },
      isShowImgTitle: {
        default: '1',
      },
      isSwiperTitle: {
        default: '1',
      },
      imgList: {
        default: null,
      },
      columns: {
        default: 1,
      },
      number: {
        default: '',
      },
      interval: {
        default: 5,
      },
    }
  },

  parseHTML() {
    return [{ tag: 'imageGallery' }]
  },
  renderHTML({ HTMLAttributes }) {
    return ['imageGallery', mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return VueNodeViewRenderer(NodeView)
  },
  addCommands() {
    return {
      insertImageGallery:
        (options) =>
        ({ commands, editor }) => {
          return commands.insertContentAt(editor.state.selection.anchor, {
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
