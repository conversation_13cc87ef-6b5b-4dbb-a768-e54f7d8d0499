<template>
  <!--  ref="containerRef"-->
  <node-view-wrapper :id="node.attrs.id" class="umo-node-view" @dblclick="openImageViewer" @mouseenter="showChild"
    @mouseleave="cannelHideLayer">
    <div class="imageGallery-wrapper">
      <!-- 更多（三个点） -->
      <div v-show="mousemove" class="top-node-mu" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1">
              <div style="display: flex; align-items: center" @click="galleryEdit">
                <FileSearchIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.edit')
                }}</span>
              </div>
            </t-dropdown-item>

            <t-dropdown-item :value="2">
              <div style="display: flex; align-items: center">
                <Dashboard1Icon />
                <span style="margin-left: 5px">{{
                  t('insert.image.columnTip')
                }}</span>
              </div>
              <t-dropdown-menu style="margin-left: 100px">
                <t-dropdown-item @click="setColumn(1)">单张居中轮播</t-dropdown-item>
                <t-dropdown-item @click="setColumn(2)">2&nbsp;{{ t('insert.image.column') }}</t-dropdown-item>
                <t-dropdown-item @click="setColumn(3)">3&nbsp;{{ t('insert.image.column') }}</t-dropdown-item>
              </t-dropdown-menu>
            </t-dropdown-item>

            <t-dropdown-item :value="3">
              <div style="display: flex; align-items: center" @click="delGallery">
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.remove')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <!-- 画廊容器     -->
      <div class="images-content">
        <!--图片瀑布流组件-->
        <masonryLayout v-if="node.attrs.imgList" :images="node.attrs.imgList" :columns="node.attrs.columns"
          :is-show-no="node.attrs.isShowNo" :is-show-img-title="node.attrs.isShowImgTitle"
          :is-swiper-title="node.attrs.isSwiperTitle" :interval="node.attrs.interval" />
      </div>
      <div class="gallery-title">
        <text v-if="node.attrs.isShowNo == 1" style="margin-right: 20px">{{
          node.attrs.number
        }}</text>
        <text v-if="node.attrs.isShowGalleryTitle == '1'">{{
          node.attrs.galleryTitle
        }}</text>
      </div>
    </div>

    <!--编辑弹窗-->
    <t-dialog v-model:visible="galleryEditPopup" attach="body" :header="t('insert.image.galleryEdit')" width="1000"
      :close-on-overlay-click="false" placement="top" :top="40" @confirm="onSubmit" @close="onClose" @cancel="onClose">
      <div>
        <t-form ref="formValidatorStatus" :data="formData" :label-width="120">
          <!-- 画廊标题 -->
          <t-form-item :label="t('insert.image.galleryTitle')" name="galleryTitle">
            <t-input v-model="formData.galleryTitle" :placeholder="t('insert.image.galleryTitlePlaceholder')"></t-input>
          </t-form-item>
          <div class="image-list-tabs">
            <t-message theme="warning">可拖拽TAB页改变图片顺序</t-message>
            <t-tabs drag-sort :value="tabsStep" :addable="true" @drag-sort="onDragend" @change="onTabChange"
              @add="addTab">
              <t-tab-panel v-for="(item, index) of formData.imageList" :key="index" :value="index"
                :label="t('insert.image.text') + (index + 1)" :destroy-on-hide="false" :draggable="item.draggable">
                <div class="image-item">
                  <div class="image-item-imageBg" :style="{ backgroundImage: `url(${item.src})` }"></div>

                  <div class="image-item-tools">
                    <div class="image-item-tools-left">
                      <t-button variant="text" @click="imgCropper">
                        <template #icon>
                          <TransformIcon style="color: #0966b4" />
                        </template>
                        <span style="color: #0966b4" class="tdesign-demo-dropdown__text">
                          {{ t('insert.image.crop') }}
                        </span>
                      </t-button>
                      <t-dropdown :options="editUpdImgOption">
                        <t-button variant="text">
                          <template #icon>
                            <FileImageIcon style="color: #0966b4" />
                          </template>
                          <span style="color: #0966b4" class="tdesign-demo-dropdown__text">
                            {{ t('insert.image.replaceImage') }}
                          </span>
                        </t-button>
                      </t-dropdown>
                    </div>

                    <div v-if="formData.imageList.length != 1" class="image-item-tools-right">
                      <t-popconfirm theme="default" :content="t('insert.image.removeTip')" :confirm-btn="{
                        content: t('insert.image.comfirm'),
                        theme: 'warning',
                      }" :cancel-btn="{
                        content: t('insert.image.cancel'),
                        theme: 'default',
                        variant: 'outline',
                      }" @visible-change="removeChange">
                        <t-button theme="danger" variant="text">
                          <template #icon>
                            <DeleteIcon style="color: var(--umo-error-color)" />
                          </template>
                          {{ t('insert.image.remove') }}
                        </t-button>
                      </t-popconfirm>
                    </div>
                  </div>

                  <div class="image-item-info">
                    <t-form-item :label="t('insert.image.imageTitle')" required-mark>
                      <t-input v-model="item.imageTitle" :placeholder="t('insert.image.imagePlaceholder')"
                        style="width: 400px"></t-input>
                    </t-form-item>

                  </div>

                  <div class="image-item-info" style="width: 520px;text-align: left; padding:0 30px; margin:0 auto;">

                    <span style="display: inline-block;width:140px;text-align: center;">{{
                      t('insert.image.originalName')
                    }}
                      :</span>
                    <span style="text-align: left;">{{ item.name }}</span>

                  </div>


                </div>
              </t-tab-panel>
            </t-tabs>
          </div>

          <!-- 是否显示画廊标题 -->
          <t-form-item :label="t('insert.image.isShowGalleryTitle')" name="isShowGalleryTitle">
            <t-radio-group v-model="formData.isShowGalleryTitle">
              <t-radio value="1" allow-uncheck>{{
                t('insert.image.yes')
              }}</t-radio>
              <t-radio value="0">{{ t('insert.image.no') }}</t-radio>
            </t-radio-group>
          </t-form-item>
          <!-- 是否显示图号 -->
          <t-form-item :label="t('insert.image.isShowNo')" name="isShowNo">
            <t-radio-group v-model="formData.isShowNo">
              <t-radio :value="1" allow-uncheck>{{
                t('insert.image.yes')
              }}</t-radio>
              <t-radio :value="0">{{ t('insert.image.no') }}</t-radio>
            </t-radio-group>
          </t-form-item>
          <!-- 是否显示图片标题 -->
          <t-form-item :label="t('insert.image.isShowImgTitle')" name="isShowImgTitle">
            <t-radio-group v-model="formData.isShowImgTitle">
              <t-radio value="1" allow-uncheck>{{
                t('insert.image.yes')
              }}</t-radio>
              <t-radio value="0">{{ t('insert.image.no') }}</t-radio>
            </t-radio-group>
          </t-form-item>

          <t-form-item :label="t('insert.image.interval')">
            <t-input-number v-model="formData.interval" :min="1" />
          </t-form-item>
        </t-form>
      </div>
    </t-dialog>

    <cropper-dialog v-model:cropper-visible="imgVisible" :url="cropperImgUrl" :file-name="cropperImgName"
      @save-cropper="saveCropper"></cropper-dialog>
  </node-view-wrapper>
  <ResourceLibrary v-model:visible="show" :file-type="'1'" @insert-by-resource="insertByResource" />
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { FileImageIcon, TransformIcon } from 'tdesign-icons-vue-next'
import {
  Dashboard1Icon,
  DeleteIcon,
  EllipsisIcon,
  FileSearchIcon,
} from 'tdesign-icons-vue-next'

import cropperDialog from '@/components/cropperDialog/index.vue'
import masonryLayout from '@/components/menus/toolbar/insert/components/imageGallery/masonryLayout.vue'
import { useMouseOver } from '@/hooks/mouseOver'
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, imageViewer, getCaptionStyle, togglePopup, loading } =
  useStore()
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()

const show = ref(false)
const setColumn = (newColumns) => {
  if (newColumns === 1) {
    updateAttributes({ isSwiperTitle: '1', columns: newColumns })
  } else {
    updateAttributes({ columns: newColumns, isSwiperTitle: '0' })
  }
}

// 截图部分代码
const imgVisible = ref(false)
let cropperImgUrl = $ref('')
let cropperImgName = $ref('')
function imgCropper() {
  imgVisible.value = true
  cropperImgUrl = formData.value.imageList[tabsStep.value].src
  cropperImgName = formData.value.imageList[tabsStep.value].name
}
// 截取保存事件
const saveCropper = (url) => {
  formData.value.imageList[tabsStep.value].src = url
}
onMounted(async () => {
  if (node.attrs.number == '') {
    getCaptionStyle().then((res) => {
      if (res.imageNumberType == 1) {
        updateAttributes({
          isShowNo: 1,
          number: '图1',
        })
      } else if (res.imageNumberType == 2) {
        updateAttributes({
          isShowNo: 1,
          number: '图1-1',
        })
      } else {
        updateAttributes({
          isShowNo: 0,
        })
      }
    })
  }
})

// 编辑弹窗标识
const galleryEditPopup = ref(false)

// 编辑页图片页签标识
const tabsStep = ref(0)

// 编辑页上传插件显示图片名称
const showImageFileName = ref(true)

// 编辑页更换图片按钮选项
const editUpdImgOption = ref([
  {
    content: t('insert.image.local'),
    value: 1,
    onClick: () => upload(),
  },
  {
    content: t('insert.image.gallery'),
    value: 1,
    onClick: () => library(),
  },
])

// 表单数据
const formData = ref({
  // 画廊标题
  galleryTitle: '',
  // 是否显示图号 0否1是  默认显示
  isShowNo: 1,
  number: 1,
  // 是否显示画廊标题 0否1是 默认显示
  isShowGalleryTitle: '',
  // 是否显示图片标题 0否1是 默认显示
  isShowImgTitle: '',
  imageList: [],
  isSwiperTitle: '',
  interval: 1,
})

// 调用上传组件
const upload = () => {
  chooseFile((file) => {
    try {
      formData.value.imageList[tabsStep.value].src = file.fileUrl
      formData.value.imageList[tabsStep.value].name = file.originName
      formData.value.imageList[tabsStep.value].imageTitle = file.originName
    } catch (error) {
      useMessage('error', (error as Error).message)
    }
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

const library = () => {
  show.value = true
}

const insertByResource = (file) => {
  formData.value.imageList[tabsStep.value].src = file.fileUrl
  formData.value.imageList[tabsStep.value].name = file.fileName
  formData.value.imageList[tabsStep.value].imageTitle = file.fileName
}

// 删除图片
const removeVisible = ref(false)
const removeChange = (val, context = {}) => {
  if (context && context.trigger === 'confirm') {
    formData.value.imageList.splice(tabsStep.value, 1)
    updateAttributes({ imgList: formData.value.imageList })
    if (tabsStep.value == formData.value.imageList.length) {
      tabsStep.value = tabsStep.value - 1
    }
  } else {
    removeVisible.value = val
  }
}

// 编辑表单图片选项卡拖拽
const onTabChange = (newValue) => (tabsStep.value = newValue)
const onDragend = ({ currentIndex, targetIndex }) => {
  ;[
    formData.value.imageList[currentIndex],
    formData.value.imageList[targetIndex],
  ] = [
      formData.value.imageList[targetIndex],
      formData.value.imageList[currentIndex],
    ]
}

// 表单实例
const formValidatorStatus = ref(null)

// 开启dialog
const galleryEdit = () => {
  tabsStep.value = 0
  formData.value = {
    galleryTitle: node.attrs.galleryTitle,
    isShowNo: Number(node.attrs.isShowNo),
    number: node.attrs.number,
    isShowGalleryTitle: node.attrs.isShowGalleryTitle,
    isShowImgTitle: node.attrs.isShowImgTitle,
    imageList: node.attrs.imgList,
    isSwiperTitle: node.attrs.isSwiperTitle,
    interval: node.attrs.interval,
  }
  formData.value.imageList = JSON.parse(
    JSON.stringify(formData.value.imageList.filter((item) => item.src)),
  )
  galleryEditPopup.value = true
}

// 关闭dialog
const onClose = () => {
  galleryEditPopup.value = false
}

// 保存事件
const onSubmit = () => {
  let f = -1
  formData.value.imageList.forEach((element, index) => {
    if (element.src && !element.imageTitle) {
      f = index
    }
  })
  if (f != -1) {
    f++
    useMessage('error', `第${f}张图片标题不能为空`)
    return
  }


  console.log(formData.value.imageList)
  const images = formData.value.imageList.filter((item) => item.src)
  console.log(images)
  updateAttributes({
    galleryTitle: formData.value.galleryTitle,
    isShowNo: Number(formData.value.isShowNo),
    isShowGalleryTitle: formData.value.isShowGalleryTitle,
    isShowImgTitle: formData.value.isShowImgTitle,
    imgList: images,
    isSwiperTitle: node.attrs.isSwiperTitle,
    interval: formData.value.interval,
  })
  galleryEditPopup.value = false
}

// 删除画廊节点
function delGallery() {
  deleteNode()
}

// 双击预览
const openImageViewer = () => {
  imageViewer.value.visible = true
  imageViewer.value.current = node.attrs.id
}

// watch(
//   () => node.attrs.imgList,
//   () => {
//     if (node.attrs.imgList.length < 4) {
//       updateAttributes({ columns: node.attrs.imgList.length })
//     }
//   },
//   { immediate: true },
// )

const addTab = () => {
  formData.value.imageList.push({
    src: '',
    imageTitle: '',
    name: '',
  })
  tabsStep.value = formData.value.imageList.length - 1
  upload()
}
</script>

<style lang="less" scoped>
.umo-node-view {
  .imageGallery-wrapper {
    width: 100%;
    height: auto;
  }

  .top-node-mu {
    position: absolute;
    right: 0px;
    top: -5;
    z-index: 99;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .images-content {
    width: 100%;
    height: auto;
    border-radius: 5px;
  }

  .image-title {
    width: 100%;
    height: 30px;
    text-align: center;
    font-size: 12px;
  }

  .gallery-title {
    width: 100%;
    height: 22px;
    text-align: center;
    // font-size: 14px;
    margin-top: 20px;
  }

  .umo-node-image {
    max-width: 100%;
    width: auto;
    position: relative;
    z-index: 20;

    &.is-loading,
    &.is-error {
      outline: none !important;
      box-shadow: none !important;
    }

    &:not(.is-draggable) .es-drager {
      max-width: 100%;
      max-height: 100%;
    }

    img {
      display: block;
      max-width: 100%;
      max-height: 100%;
      width: 100%;
      height: 100%;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--umo-text-color-light);
      font-size: 12px;
      gap: 10px;

      .loading-icon {
        color: var(--umo-primary-color);
        font-size: 22px;
        animation: turn 1s linear infinite;
      }
    }

    .error {
      width: 200px;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: var(--umo-text-color-light);
      font-size: 12px;

      .error-icon {
        font-size: 72px;
        margin: -8px 0 -2px;
      }
    }

    .uploading {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.1);

      span {
        display: block;
        position: absolute;
        background: rgba(0, 0, 0, 0.2);
        height: 4px;
        border-radius: 2px;
        top: 50%;
        left: 20%;
        right: 20%;
        transform: translateY(-50%);
        overflow: hidden;

        &:after {
          content: '';
          display: block;
          height: 100%;
          background-color: var(--umo-primary-color);
          animation: progress 1s linear infinite;
        }
      }
    }
  }
}

.image-list-tabs {
  padding: 0 10px 20px;

  .image-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0;
    border: 1px dashed #999;
    border-radius: 4px;
    margin: 10px 0;

    .image-item-imageBg {
      max-height: 300px;
      max-width: 535px;
      background-repeat: no-repeat;
      background-size: contain;
      width: 100%;
      height: 300px;
    }

    .image-item-tools {
      width: 100%;
      margin: 0 30px;
      display: flex;
      justify-content: space-between;
    }

    .image-item-info {
      display: flex;
      padding: 20px 0;
    }
  }
}

@keyframes turn {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes progress {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}
</style>
<style lang="less" scoped>
.cropper-container {
  width: 98%;
  height: 300px;
}
</style>
