<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view" :class="{
    'umo-node-view-empty': true,
  }" :style="nodeStyle" @mouseenter="showChild" @mouseleave="cannelHideLayer">
    <div v-show="mousemove" class="top-node-mu" trigger="click" @click.stop="keepOpen">
      <t-dropdown class="rc-icon" :tooltip="false">
        <EllipsisIcon />
        <template #dropdown>
          <t-dropdown-item :value="1">
            <div style="display: flex; align-items: center" @click="viewerImg">
              <BrowseIcon />
              <span style="margin-left: 5px">{{
                t('insert.image.preview')
              }}</span>
            </div>
          </t-dropdown-item>
          <t-dropdown-item :value="2">
            <div style="display: flex; align-items: center" @click="imageEdit">
              <FileSearchIcon />
              <span style="margin-left: 5px">{{
                t('insert.image.setting')
              }}</span>
            </div>
          </t-dropdown-item>
          <t-dropdown-item :value="3">
            <div style="display: flex; align-items: center" @click="handleDelNode">
              <DeleteIcon />
              <span style="margin-left: 5px">{{
                t('insert.image.remove')
              }}</span>
            </div>
          </t-dropdown-item>
        </template>
      </t-dropdown>
    </div>
    <div class="umo-node-container umo-node-image" :class="{
      'is-loading': node.attrs.src && isLoading,
      'is-error': node.attrs.src && error,
      'umo-hover-shadow': !options.document?.readOnly,
      'is-draggable': node.attrs.draggable,
      'umo-select-outline': !node.attrs.draggable,
    }">
      <div v-if="node.attrs.src && isLoading" class="loading" :style="{ height: `${node.attrs.height}px` }">
        <icon name="loading" class="loading-icon" />
        {{ t('node.image.loading') }}
      </div>
      <div v-else-if="node.attrs.src && error" class="error" :style="{ height: `${node.attrs.height}px` }">
        <icon name="image-failed" class="error-icon" />
        {{ t('node.image.error') }}
      </div>
      <drager v-else :selected="selected" :rotatable="true" :boundary="false" :draggable="false"
        :disabled="options.document?.readOnly" :angle="node.attrs.angle" :width="Number(node.attrs.width)"
        :height="Number(node.attrs.height)" :left="Number(node.attrs.left)" :top="Number(node.attrs.top)"
        :min-width="14" :min-height="14" :max-width="maxWidth"
        :max-height="node.attrs.equalProportion ? maxHeight : undefined" :z-index="10"
        :equal-proportion="node.attrs.equalProportion" @rotate="onRotate" @resize="onResize" @resize-end="onResizeEnd"
        @click="selected = true">
        <img ref="imageRef" :src="node.attrs.src" :style="{
          transform:
            node.attrs.flipX || node.attrs.flipY
              ? `rotateX(${node.attrs.flipX ? '180' : '0'}deg) rotateY(${node.attrs.flipY ? '180' : '0'}deg)`
              : 'none',
        }" :data-id="node.attrs.id" loading="lazy" @load="onLoad" />
      </drager>
    </div>
    <t-dialog v-model:visible="viewerVisible" :header="t('insert.image.preview')" attach="body" width="30%"
      :confirm-btn="null" :close-on-overlay-click="false" @confirm="onSubmit" @close="onClose" @cancel="onClose">

      <imgView :img-url="node.attrs.src" :img-title="removeExtension(node.attrs.imageTitle)" :is-show-image-title="'1'"
        :link-address="node.attrs.linkAddress" />
    </t-dialog>
    <t-dialog v-model:visible="imageEditPopup" attach="body" :header="t('insert.image.setting')" width="30%"
      :close-on-overlay-click="false" @confirm="onSubmit" @close="onClose" @cancel="onClose">
      <div>
        <t-form ref="formValidatorStatus" :data="formData" :label-width="120">
          <!-- 图片标题123-->
          <t-form-item :label="t('insert.image.imageTitle')" name="imageTitle">
            <t-input v-model="formData.imageTitle" :placeholder="t('insert.image.imagePlaceholder')"></t-input>
          </t-form-item>
          <t-form-item :label="t('insert.image.originalName')" name="name">
            {{ node.attrs.name }}
          </t-form-item>
          <!-- 链接地址-->
          <t-form-item :label="t('insert.image.linkAddress')" name="linkAddress">
            <t-input v-model="formData.linkAddress" :placeholder="t('insert.image.linkAddressPlaceholder')"></t-input>
          </t-form-item>
          <!-- 更换图片 -->
          <t-form-item :label="t('insert.image.updateImage')" name="src">
            <t-button @click="resetImg">{{ t('insert.image.resetImage') }}</t-button>
          </t-form-item>
          <t-form-item :label="t('insert.image.resetResourceLibrary')" name="src">
            <t-button @click="library">{{ t('insert.image.resetResourceLibrary') }}</t-button>
          </t-form-item>
        </t-form>
      </div>
    </t-dialog>
    <ResourceLibrary v-model:visible="show" :file-type="'1'" @insert-by-resource="insertByResource" />
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import Drager from 'es-drager'
import {
  BrowseIcon,
  DeleteIcon,
  EllipsisIcon,
  FileSearchIcon,
} from 'tdesign-icons-vue-next'

import imgView from '@/components/menus/toolbar/insert/components/image/imgView.vue'
import { useMouseOver } from '@/hooks/mouseOver'
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, imageViewer } = useStore()
const { isLoading, error } = useImage({ src: node.attrs.src })
const show = ref(false)
const containerRef = ref(null)
const imageRef = $ref<HTMLImageElement | null>(null)
let selected = $ref(false)
let maxWidth = $ref(0)
let maxHeight = $ref(0)

// 表单数据
const formData = ref({})
// 编辑弹窗标识
const imageEditPopup = ref(false)

// 开启dialog
const imageEdit = () => {
  imageEditPopup.value = true
  formData.value = {
    imageTitle: node.attrs.imageTitle,
    linkAddress: node.attrs.linkAddress,
  }
}
// 关闭dialog
const onClose = () => {
  imageEditPopup.value = false
}

const mouse = ref(false)

function removeExtension(str) {
  const index = str.lastIndexOf('.');
  return index === -1 ? str : str.slice(0, index);
}

// 保存事件
const onSubmit = () => {
  const validProtocols = /^(?:(http|https|ftp|ftps|mailto):\/\/)|^www\./i
  if (
    formData.value.linkAddress != '' &&
    !validProtocols.test(formData.value.linkAddress)
  ) {
    useMessage('error', '请填写正确的链接地址')
    return false
  }
  if (formData.value.linkAddress.startsWith('www.')) {
    formData.value.linkAddress = `https://${formData.value.linkAddress}`
  }
  updateAttributes({
    imageTitle: formData.value.imageTitle,
    linkAddress: formData.value.linkAddress,
  })
  imageEditPopup.value = false
}

// 从资源库选择图片
const library = () => {
  show.value = true
}
const insertByResource = (file) => {
  const imageWidth = imageRef.naturalWidth > 697 ? 697 : imageRef.naturalWidth;
  let imageHeight = imageRef.naturalHeight;
  const ratio = imageRef.naturalWidth / imageHeight
  let height = Number(imageWidth) / Number(ratio);
  updateAttributes({
    src: file.fileUrl,
    imageTitle: file.name,
    width: imageWidth,
    height: height
  })
}


function handleDelNode() {
  deleteNode()
}

// 预览
const viewerVisible = ref(false)
const viewerImg = () => {
  viewerVisible.value = true
}

const nodeStyle = $computed(() => {
  const { nodeAlign, margin, top, left, nodeFloat } = node.attrs
  const marginTop =
    margin?.top && margin?.top !== '' ? `${margin.top}px` : undefined
  const marginBottom =
    margin?.bottom && margin?.bottom !== '' ? `${margin.bottom}px` : undefined
  const posTop = top !== '' ? `${top}px` : undefined
  const posLeft = left !== '' ? `${left}px` : undefined
  return {
    justifyContent: nodeAlign,
    float: nodeFloat,
    marginTop,
    marginBottom,
    top: posTop,
    left: posLeft,
  }
})

const onLoad = async () => {
  if (node.attrs.width === null) {
    const { clientWidth = 1, clientHeight = 1 } = imageRef ?? {}
    const ratio = clientWidth / clientHeight
    maxWidth = containerRef.value?.$el.clientWidth
    maxHeight = maxWidth / ratio
    updateAttributes({ width: (200 * ratio).toFixed(2) })
  }
  if ([null, 'auto', 0].includes(node.attrs.height)) {
    await nextTick()
    const { height } = imageRef?.getBoundingClientRect() ?? {}
    updateAttributes({ height: height.toFixed(2) })
  }
}

const onRotate = ({ angle }: { angle: number }) => {
  updateAttributes({ angle })
}
const onResize = ({ width, height }: { width: number; height: number }) => {
  updateAttributes({
    width: width.toFixed(2),
    height: height.toFixed(2),
  })
}

const onResizeEnd = ({ width, height }: { width: number; height: number }) => {
  if (node.attrs.width > 696) {
    updateAttributes({
      width: 696,
      height: height.toFixed(2),
    })
  }
}

const onDrag = ({ left, top }: { left: number; top: number }) => {
  updateAttributes({ left, top })
}

onClickOutside(containerRef, () => {
  selected = false
})

const openImageViewer = () => {
  imageViewer.value.visible = true
  imageViewer.value.current = node.attrs.id
}


// 更换图片
const resetImg = () => {
  chooseFile((file) => {
    updateAttributes({ src: file.fileUrl })
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

watch(
  () => node.attrs.equalProportion,
  async (equalProportion: boolean) => {
    await nextTick()
    const width = imageRef?.offsetWidth ?? 1
    const height = imageRef?.offsetHeight ?? 1
    updateAttributes({ width, height })
    maxHeight = equalProportion ? maxWidth / (width / height) : 0
  },
)
</script>

<style lang="less" scoped>
.umo-node-view {
  display: inline-flex !important;
  margin: 0;

  .umo-node-image {
    max-width: 100%;
    width: auto;
    position: relative;
    z-index: 20;

    &.is-loading,
    &.is-error {
      outline: none !important;
      box-shadow: none !important;
    }

    &:not(.is-draggable) .es-drager {
      max-width: 100%;
      max-height: 100%;
    }

    img {
      display: block;
      max-width: 100%;
      max-height: 100%;
      width: 100%;
      height: 100%;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--umo-text-color-light);
      font-size: 12px;
      gap: 10px;

      .loading-icon {
        color: var(--umo-primary-color);
        font-size: 22px;
        animation: turn 1s linear infinite;
      }
    }

    .error {
      width: 200px;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: var(--umo-text-color-light);
      font-size: 12px;

      .error-icon {
        font-size: 72px;
        margin: -8px 0 -2px;
      }
    }

    .uploading {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.1);

      span {
        display: block;
        position: absolute;
        background: rgba(0, 0, 0, 0.2);
        height: 4px;
        border-radius: 2px;
        top: 50%;
        left: 20%;
        right: 20%;
        transform: translateY(-50%);
        overflow: hidden;

        &:after {
          content: '';
          display: block;
          height: 100%;
          background-color: var(--umo-primary-color);
          animation: progress 1s linear infinite;
        }
      }
    }
  }

  .top-node-mu {
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 99;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 10px;
    height: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .umo-node-image {
    max-width: 100%;
    width: auto;
    position: relative;
    z-index: 20;
    top: 2px;

    &.is-loading,
    &.is-error {
      outline: none !important;
      box-shadow: none !important;
    }

    &:not(.is-draggable) .es-drager {
      max-width: 100%;
      max-height: 100%;
    }

    img {
      display: block;
      max-width: 100%;
      max-height: 100%;
      width: 100%;
      height: 100%;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--umo-text-color-light);
      font-size: 12px;
      gap: 10px;

      .loading-icon {
        color: var(--umo-primary-color);
        font-size: 22px;
        animation: turn 1s linear infinite;
      }
    }

    .error {
      width: 200px;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: var(--umo-text-color-light);
      font-size: 12px;

      .error-icon {
        font-size: 72px;
        margin: -8px 0 -2px;
      }
    }

    .uploading {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.1);

      span {
        display: block;
        position: absolute;
        background: rgba(0, 0, 0, 0.2);
        height: 4px;
        border-radius: 2px;
        top: 50%;
        left: 20%;
        right: 20%;
        transform: translateY(-50%);
        overflow: hidden;

        &:after {
          content: '';
          display: block;
          height: 100%;
          background-color: var(--umo-primary-color);
          animation: progress 1s linear infinite;
        }
      }
    }
  }
}

@keyframes turn {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes progress {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}
</style>
