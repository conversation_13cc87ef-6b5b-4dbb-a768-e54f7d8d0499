import Image from '@tiptap/extension-image'
import { type CommandProps, VueNodeViewRenderer } from '@tiptap/vue-3'
import {
  type CommandProps,
  mergeAttributes,
  Node,
  type NodeViewProps,
} from '@tiptap/core'
import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setImageInLine: {
      setImageInLine: (options: any, replace?: any) => ReturnType
    }
  }
}
export default Image.extend({
  atom: true,
  name: 'imageInLine',
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      name: {
        default: null,
      },
      size: {
        default: null,
      },
      src: {
        default: null,
      },
      width: {
        default: null,
      },
      height: {
        default: 200,
      },
      left: {
        default: 0,
      },
      top: {
        default: 0,
      },
      angle: {
        default: null,
      },
      draggable: {
        default: false,
      },
      rotatable: {
        default: false,
      },
      equalProportion: {
        default: true,
      },
      flipX: {
        default: false,
      },
      flipY: {
        default: false,
      },
      imageTitle: {
        default: t('insert.image.imageTitle'),
      },
      linkAddress: {
        default: '',
      },
      imgDescribe: {
        default: '',
      },
    }
  },

  addOptions() {
    return {
      inline: true,
    }
  },
  parseHTML() {
    return [{ tag: 'imageInLine' }]
  },
  renderHTML({ HTMLAttributes }) {
    return ['imageInLine', mergeAttributes(HTMLAttributes)]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView)
  },

  addCommands() {
    return {
      setImageInLine:
        (
          options: {
            src: string
            size?: number
            imageTitle?: string
            name?: string
            linkAddress?: string
          },
          replace?: boolean,
        ) =>
        ({ commands, editor }: CommandProps) => {
          if (replace) {
            return commands.insertContent({
              type: this.name,
              attrs: options,
            })
          }
          return commands.insertContentAt(editor.state.selection.anchor, {
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
