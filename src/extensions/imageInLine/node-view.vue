<template>
  <node-view-wrapper
    :id="node.attrs.id"
    ref="containerRef"
    class="umo-node-view"
  >
    <div class="umo-node-container umo-node-image">
      <t-space>
        <t-dropdown :min-column-width="112" :tooltip="false" trigger="click">
          <span class="imgInline-icon"></span>
          <template #dropdown>
            <t-dropdown-item :value="1"
              ><div
                style="display: flex; align-items: center"
                @click="viewerImg"
              >
                <BrowseIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.preview')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2"
              ><div
                style="display: flex; align-items: center"
                @click="imageEdit"
              >
                <FileSearchIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.setting')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="3">
              <div
                style="display: flex; align-items: center"
                @click="handleDelNode"
              >
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.remove')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </t-space>
    </div>
    <t-dialog
      v-model:visible="viewerVisible"
      :header="t('insert.image.preview')"
      attach="body"
      width="30%"
      :confirm-btn="null"
      :close-on-overlay-click="false"
      @confirm="onSubmit"
      @close="onClose"
      @cancel="onClose"
    >
      <imgView
        :img-url="node.attrs.src"
        :img-title="node.attrs.imageTitle"
        :isShowImageTitle="'1'"
        :link-address="node.attrs.linkAddress"
        :img-describe="node.attrs.imgDescribe"
      />
    </t-dialog>
    <t-dialog
      v-model:visible="imageEditPopup"
      attach="body"
      :header="t('insert.image.setting')"
      width="30%"
      :close-on-overlay-click="false"
      @confirm="onSubmit"
      @close="onClose"
      @cancel="onClose"
    >
      <div>
        <t-form
          ref="formValidatorStatus"
          :data="formData"
          :rules="rules"
          :label-width="120"
        >
          <!-- 图片标题-->
          <t-form-item :label="t('insert.image.imageTitle')" name="imageTitle">
            <t-input
              v-model="formData.imageTitle"
              :placeholder="t('insert.image.imagePlaceholder')"
            ></t-input>
          </t-form-item>
          <!-- 链接地址-->
          <t-form-item
            :label="t('insert.image.linkAddress')"
            name="linkAddress"
          >
            <t-input
              v-model="formData.linkAddress"
              :placeholder="t('insert.image.linkAddressPlaceholder')"
            ></t-input>
          </t-form-item>
          <!-- 描述-->
          <t-form-item
            :label="t('insert.image.imgDescribe')"
            name="imgDescribe"
          >
            <t-input
              v-model="formData.imgDescribe"
              maxlength="100"
              :placeholder="t('insert.image.imgDescribePlaceholder')"
            ></t-input>
          </t-form-item>
        </t-form>
      </div>
    </t-dialog>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import {
  BrowseIcon,
  DeleteIcon,
  EllipsisIcon,
  FileSearchIcon,
  ImageIcon,
} from 'tdesign-icons-vue-next'
import { reactive } from 'vue'

import imgView from '@/components/menus/toolbar/insert/components/image/imgView.vue'
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, imageViewer } = useStore()
const { isLoading, error } = useImage({ src: node.attrs.src })

const containerRef = ref(null)

// 表单验证
const formValidatorStatus = ref(null)
const rules = reactive({
  imageTitle: [
    {
      required: true,
      message: t('insert.image.imagePlaceholder'),
      type: 'error',
      trigger: 'blur',
    },
  ],
})
// 表单数据
const formData = ref({})
// 编辑弹窗标识
const imageEditPopup = ref(false)

// 开启dialog
const imageEdit = () => {
  formData.value = {
    imageTitle: node.attrs.imageTitle,
    linkAddress: node.attrs.linkAddress,
    imgDescribe: node.attrs.imgDescribe,
  }
  imageEditPopup.value = true
}

// 关闭dialog
const onClose = () => {
  imageEditPopup.value = false
}

// 保存事件
const onSubmit = () => {
  const validateResult = formValidatorStatus.value.validate()
  validateResult.then((valid) => {
    if (valid == true) {
      const validProtocols = /^(?:(http|https|ftp|ftps|mailto):\/\/)|^www\./i
      if (
        formData.value.linkAddress != '' &&
        !validProtocols.test(formData.value.linkAddress)
      ) {
        useMessage('error', '请填写正确的链接地址')
        return false
      }
      if (formData.value.linkAddress.startsWith('www.')) {
        formData.value.linkAddress = 'https://' + formData.value.linkAddress;
      }
      updateAttributes({
        imageTitle: formData.value.imageTitle,
        linkAddress: formData.value.linkAddress,
        imgDescribe: formData.value.imgDescribe,
      })
      imageEditPopup.value = false
    }
  })
}

// 删除节点
function handleDelNode() {
  deleteNode()
}

// 预览
const viewerVisible = ref(false)
const viewerImg = () => {
  viewerVisible.value = true
}
</script>

<style lang="less" scoped>
.umo-node-view {
  display: inline-flex !important;
  top: 5px;
  text-indent: 0 !important;
  margin: 0 !important;
  .umo-node-image {
    img {
      display: block;
      max-width: 100%;
      max-height: 100%;
      width: 100%;
      height: 100%;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--umo-text-color-light);
      font-size: 12px;
      gap: 10px;

      .loading-icon {
        color: var(--umo-primary-color);
        font-size: 22px;
        animation: turn 1s linear infinite;
      }
    }

    .error {
      width: 200px;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: var(--umo-text-color-light);
      font-size: 12px;

      .error-icon {
        font-size: 72px;
        margin: -8px 0 -2px;
      }
    }

    .uploading {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.1);

      span {
        display: block;
        position: absolute;
        background: rgba(0, 0, 0, 0.2);
        height: 4px;
        border-radius: 2px;
        top: 50%;
        left: 20%;
        right: 20%;
        transform: translateY(-50%);
        overflow: hidden;

        &:after {
          content: '';
          display: block;
          height: 100%;
          background-color: var(--umo-primary-color);
          animation: progress 1s linear infinite;
        }
      }
    }
  }
}

@keyframes turn {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes progress {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

.icon-container {
  position: relative;
  display: inline-block;
}

.icon-container .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色背景 */
  opacity: 0;
  transition: opacity 0.3s ease-in-out; /* 动画效果 */
  border-radius: 50%; /* 圆形蒙层 */
}

.icon-container:hover .overlay {
  opacity: 1; /* 鼠标移入时显示蒙层 */
}

.icon-container:hover component {
  transform: scale(1.1); /* 可选：放大图标 */
  transition: transform 0.3s ease-in-out; /* 动画效果 */
}
.imgInline-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url('@/assets/images/imgInline.svg') no-repeat;
  background-size: 100% 100%;
  margin-left: 4px;
}
</style>
