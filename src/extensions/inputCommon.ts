import { Mark } from '@tiptap/core'

/**
 * TextShadow mark extension to apply text-shadow CSS style to the selected text.
 */
export default Mark.create({
  name: 'inputCommon',

  addOptions() {
    return {
      HTMLAttributes: {}, // 默认的 HTML 属性
    }
  },
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      inputCommon: {
        default: {},
      },
    }
  },

  // 解析 HTML 中的 text-shadow 样式
  parseHTML() {
    return [
      {
        tag: 'span', // 使用 span 标签
        getAttrs: (node) => {
          // 检查是否有 text-shadow 样式
          const style = node.getAttribute('style')
          const textStrokeMatch = style
            ? style.match(/-webkit-text-stroke:\s*([^;]+)/)
            : null
          return textStrokeMatch ? { inputCommon: textStrokeMatch[1] } : null
        },
      },
    ]
  },

  // 渲染 HTML 输出文本，并应用 text-shadow 样式
  renderHTML({ HTMLAttributes }) {
    const { inputCommon, ...restAttributes } = HTMLAttributes
    if (!inputCommon) return null
    // 打印用于调试的 HTMLAttributes
    // 如果 textStroke 存在，应用它到 style 中

    // 返回带有样式的 span 标签
    //TODO HTMLAttributes取不到testShadow 暂时先用写死的样式 后续测试再考虑是否更改 dongyujian
    return [
      'span',
      {
        id: inputCommon.id,
        dataId: inputCommon.id,
      },
      0, // 这个 `0` 表示子节点的位置
    ]
  },

  // 定义命令
  addCommands() {
    return {
      // 设置 text-shadow 样式
      setInputCommon:
        (options) =>
        ({ commands }) => {
          return commands.setMark(this.name, { inputCommon: options })
        },
      // 移除 text-shadow 样式
      unsetInputCommon:
        (options) =>
        ({ commands }) => {
          return commands.unsetMark(this.name, { inputCommon: options })
        },
    }
  },
})
