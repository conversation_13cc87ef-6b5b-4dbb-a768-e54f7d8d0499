import {
  type CommandProps,
  mergeAttributes,
  Node,
  type NodeViewProps,
} from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import { InteractionType } from '@/enum/extensions/interaction'

import { INTERACTION } from '../page/node-names'
import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setInteraction: {
      setInteraction: (options: any) => ReturnType
    }
  }
}

export default Node.create({
  name: INTERACTION,
  inline: false,
  group: 'block',
  atom: true,
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      interactionType: {
        default: InteractionType.WORD_CLOUD,
      },
      themeContent: {
        default: '',
      },
      showEditModel: {
        default: false,
      },
      images: {
        default: () => [],
      },
      voteList: {
        default: () => [],
      },
    }
  },
  parseHTML() {
    return [{ tag: INTERACTION }]
  },
  renderHTML({ HTMLAttributes }) {
    return [
      INTERACTION,
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
    ]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      setInteraction:
        (options: any) =>
        ({ commands, editor }: CommandProps) => {

          return commands.insertContent({
            type: this.name,
            attrs: {
              ...options,
              showEditModel: true,
            },
          })
        },
    }
  },
})
