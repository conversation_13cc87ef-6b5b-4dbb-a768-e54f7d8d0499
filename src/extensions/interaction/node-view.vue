<template>
  <node-view-wrapper :id="node.attrs.id" class="umo-node-view">
    <div class="interaction">
      <div class="interaction-main">
        <div class="interaction-main-headers">
          <div class="interaction-main-title-type">
            {{ typeName(node.attrs.interactionType) }}
          </div>
          <div class="interaction-main-btns">
            <t-button
              style="margin-right: 10px"
              variant="outline"
              @click="openEditModel"
            >
              <template #icon>
                <Edit2Icon />
              </template>
              {{ t('insert.interaction.opt.edit') }}
            </t-button>

            <t-button variant="outline" @click="delInteraction">
              <template #icon>
                <DeleteIcon />
              </template>
              {{ t('insert.interaction.opt.del') }}
            </t-button>
          </div>
        </div>
        <div class="interaction-main-title">
          <span
            class="interaction-main-title-content"
            v-html="node.attrs.themeContent"
          ></span>
        </div>

        <div
          v-if="node.attrs.interactionType == 0"
          class="interaction-progressBar-list"
        >
          <div
            v-for="(item, index) in node.attrs.voteList"
            :key="node.attrs.id"
            class="interaction-progressBar-item"
          >
            <div class="interaction-progressBar-item-content">
              <div
                class="interaction-progressBar-item-title"
                contenteditable="true"
                :style="{ color: item.value != 0 ? '#fff' : '#333' }"
              >
                {{ item.name }}
              </div>
              <t-progress
                :theme="item.value > 9 ? 'plump' : 'line'"
                :percentage="item.value"
                size="large"
                :style="{ height: item.value > 9 ? '' : '20px' }"
              />
            </div>
            <div class="interaction-progressBar-item-delete">
              <DeleteIcon @click="delOption(item)" />
            </div>
          </div>

          <div class="interaction-progressBar-add">
            <t-button size="small" variant="outline" @click="() => addOption()">
              <template #icon> <PlusIcon /> </template
              >{{ t('insert.interaction.addOption') }}</t-button
            >
          </div>
        </div>

        <!-- <chartWordCloud :options="state.chartOptions" :id="node.attrs.id" /> -->
      </div>
    </div>

    <modal
      :header="InteractionTypeName[node.attrs.interactionType]"
      :visible="editModelVisibel"
      width="400px"
      :confirm-btn="t('insert.interaction.form.comfirm')"
      :cancel-btn="t('insert.interaction.form.cancel')"
      @confirm="confirmEdit"
      @close="closeEditModel"
    >
      <t-textarea
        v-model="themeContent"
        :placeholder="t('insert.interaction.placeholder')"
        :autosize="{ minRows: 3, maxRows: 10 }"
        :maxlength="50"
      />
    </modal>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { DeleteIcon, Edit2Icon, PlusIcon } from 'tdesign-icons-vue-next'

import { InteractionTypeName } from '@/enum/extensions/interaction'
const { node, updateAttributes } = defineProps(nodeViewProps)
const { proxy } = getCurrentInstance()
const { editor, options } = useStore()
const themeContent = ref('')
const editModelVisibel = ref(false)
const isInput = ref(false)
const inputRef = ref()
const inputValue = ref('')

const dataList = ref([])

const addOption = () => {
  // dataList.value.push({ name: `选项${node.attrs.voteList.length + 1}`, value: 0 })
  updateAttributes({
    voteList: [
      ...node.attrs.voteList,
      { name: `选项${node.attrs.voteList.length + 1}`, value: 0 },
    ],
  })
}

const typeName = computed(() => {
  return function (type) {
    switch (type) {
      case 0:
        return t('insert.interaction.options.vote')
      case 1:
        return t('insert.interaction.options.wordCloud')

      case 2:
        return t('insert.interaction.options.discussion')

      case 3:
        return t('insert.interaction.options.imageWaterfall')
      default:
        return t('insert.interaction.options.vote')
    }
  }
})

const delOption = (item) => {
  updateAttributes({
    voteList: node.attrs.voteList.filter((i) => i.name !== item.name),
  })
}

onMounted(() => {
  editModelVisibel.value = node.attrs.showEditModel
})
onBeforeMount(() => {
  const aa = document.querySelector('interaction-preview')
})

// 确认按钮
function confirmEdit() {
  if (!themeContent.value) {
    return useMessage('error', t('interaction.form.themeContent'))
  }

  updateAttributes({
    themeContent: themeContent.value,
    showEditModel: false,
    voteList: dataList.value,
  })
  editModelVisibel.value = false
}

// 编辑按钮
function openEditModel() {
  themeContent.value = node.attrs.themeContent
  editModelVisibel.value = true
}
// function look(e, e2) {
//   console.log('e', e, e2)
//   isInput.value = !isInput.value
//   console.log('isInput', proxy.$refs.inputRef)
//   proxy.$refs.inputRef.style.display = 'block'
//   console.log('isInput', isInput.value)
// }
// 删除按钮
function delInteraction() {
  editor.value?.chain().focus().deleteSelection().run()
}

//显示 input
function showInput(id: any) {
  const showDivShow = document.querySelector(`.interaction-preview-form${id}`)
  showDivShow.style.display = 'block'
}

// 显示input 提交
function sumbit(id: any) {
  const showDivShow = document.querySelector(`.interaction-preview-form${id}`)
  showDivShow.style.display = 'none'
  // inputList.value.push(inputValue.value)
}

// 关闭弹窗
function closeEditModel() {
  editModelVisibel.value = false
  isInput.value = false
}

const image =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAA1hJREFUeF7tm01W4zAMgCVzkGEWlFwAht2Ukww9CXCSMichs2PgAqEsJhykFs9OnXHT/MhJ7IaYbOC9Opb1WZZk2UGI/MHI9YcvAH0tIEmuTkHKG/W+BPyGJE/V/4QiF0DvQJRnby8Pffs372k52+0SEIv+iX4aOeqvIPkHTk7yLHtK+8hysgCjNAHcMoXlCgQC/HaFkSwu7zRYIA2Z8eQEmCogLrJYAJTitN2uAXHJGEhTkwLE5vmurY8kuVqSlGsA0DPe88lRiBXHKjoBqJlwmHHOeNXgrrPsKbcbjwR5Tz4BPmw2f1dtg2oE4GNA1kByJLo3pqplSfmPQ69Hm1rgpp9aAJ6VN7L1kgAhUpLysYdiLq80QqgFcH528ThwvbsMLlTbWggHAGaqfGl1r5vn7zbxPQAeHF6o2WXLqTrGEoBnR8QeYIiGuyikE6cSwGLxY+2QdIQYpz8ZROnr28t1CSCm2S/DX5GLpNoCopp9Q2BnBRrA+eJSJSFDUk9/5uqv51xFBAOA/MmZbs/KGWJydnFDiGrzEd2DAPcY5fq3/EDcAAByjNQB/t+QRQ4AYrcAwJnv/jojW9xOkCjFGLbATWagtsa4q8L6Lkl1muIxGiDRSgHwWZA8hl5smShEsReIMRs0lSENIEYrMFWhsiIUWTjUW+G9klhMzrC2JhiNL7DqgXsWEIsvUJ7fPpc8OBiZc4FEFUCqp9PxHI1VTL+sDtdlDbuwqLLD2RRKq6bfCmDnD9RFhVmkyLbXr0546wWJOWyU6ta9DeEYN0TYufrghg3r3g3AOPeDBuvi3AFD+YM8oElIoBsjzjq2vFCmul2ddi4B08Enigxs5dkW8IkgsK/HdYbB1uVQhMep5QjOyjtbwIQtoZfyvQFYG6cpWEJv5QcBmAgEJ4dXt6zZUWCCPmGw8oMtwPYJUtJtsEtWzCSnKwcYDYBZDur7gZEvVh/qMKLyowIorWH82+UlhK6NDWfGq20G+4A6oT52keoUx+VDCC4MLwBGjhCDwlwXCG8ARoLQete/SznO714BDHKOIzu7JhjeAfRxjj6c3dEBMJeE1/XuJRPkrDO7TeOnd4FMPkgY5EBRZ5FS0i/9kaUQKecTN06/rm2C+QDXgYVq/wUgFOmpyvkACUbjkux97g4AAAAASUVORK5CYII='
const maskImage = new Image()
maskImage.src = image
const state = reactive({
  chartOptions: {
    series: [
      {
        gridSize: 10,
        sizeRange: [10, 40], // 控制词语的大小范围

        rotationRange: [0, 0], // 控制词语的旋转角度范围

        rotationStep: 45, // 控制词语旋转的步长
        shape: 'circle', // 控制词云图的形状，可选值为 'circle', 'cardioid', 'diamond', 'triangle-forward', 'triangle', 'pentagon', 'star'

        drawOutOfBound: false, // 控制词云图是否允许词语超出绘制区域

        // 布局的时候是否有动画

        layoutAnimation: true,

        // 图的位置

        left: 'center',

        top: 'center',
        textStyle: {
          fontFamily: 'sans-serif',

          // fontWeight: 'bold',

          color() {
            return `rgb(${[
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160),
              Math.round(Math.random() * 160),
            ].join(',')})`
          },
        },
        emphasis: {
          focus: 'self',
          textStyle: {
            fontSize: 14, // 点击的词放大
          },
        },

        // 赋值给series里的词云轮廓图maskImage
        data: [
          { name: '互联网服务', value: 1000 },
          { name: '交通运输', value: 850 },
          { name: '公司', value: 800 },
          { name: '军工', value: 600 },
          { name: '医药', value: 900 },
        ],
      },
    ],
  },
})
</script>

<style lang="less">
.bestsellers-container {
  height: 38.56rem;
}

#charts-content {
  /* 需要设置宽高后才会显示 */
  width: 100%;
  height: 100%;
}

.umo-node-view {
  .interaction {
    width: 100%;
    border: 3px solid #d9d9d9;
    border-radius: 8px;
    background-color: #f1f1f1;
    padding: 20px;
    position: relative;

    .interaction-main-headers {
      display: flex;
      justify-content: space-between;

      .interaction-main-title-type {
        font-size: 18px;
        font-weight: bold;
      }
    }

    .interaction-progressBar-list {
      padding: 10px 20px;

      .interaction-progressBar-item {
        margin: 10px 0;
        display: flex;
        gap: 10px;

        .interaction-progressBar-item-content {
          position: relative;
          width: 96%;

          .umo-progress__bar {
            height: 20px;
          }

          .interaction-progressBar-item-title {
            position: absolute;
            left: 10px;
            top: 3px;
            color: #fff;
            z-index: 1;
            outline: none;
          }
        }

        .interaction-progressBar-item-delete {
        }
      }

      .interaction-progressBar-add {
        display: flex;
        justify-content: flex-end;
        cursor: pointer;
      }
    }

    .interaction-main-btns {
      display: flex;
      align-items: center;
      justify-content: center;

      .interaction-main-btns-edit {
        width: 20px;
        height: 20px;
        background-image: url(./edit.png);
        background-size: cover;
        cursor: pointer;
      }

      .interaction-main-btns-delete {
        width: 20px;
        height: 20px;
        background-image: url(./delete.png);
        background-size: cover;
        cursor: pointer;
      }
    }

    .interaction-main-title {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 500px;
      padding: 20px 0;
      font-size: 20px;
      color: #666;

      .interaction-main-title-content {
        width: 450px;
        margin: 0 auto;
        word-wrap: break-word;
        word-break: break-all;
        text-align: center;
      }
    }

    .interaction-content {
      width: 85%;
      word-wrap: break-word;
      word-break: break-all;
      // display: flex;
      // justify-content: flex-start;
      display: flex;
      // flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;

      span {
        min-width: 40px;
      }
    }

    .interaction-btns {
      width: 15%;
      // height: 50px;
      display: flex;
      padding-left: 5px;
      padding-right: 5px;
      justify-content: space-between;
      border-left: 1px solid #d9d9d9;
      align-items: center;

      .interaction-btns-edit {
        background: #fff;
        padding: 5px;
        border: 1px solid #d9d9d9;
      }

      .interaction-btns-del {
        background: #fff;
        padding: 5px;
        border: 1px solid #d9d9d9;
      }
    }
  }
}
</style>
