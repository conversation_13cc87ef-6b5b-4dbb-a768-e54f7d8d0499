import { mergeAttributes, Node } from '@tiptap/core'
// import { VueNodeViewRenderer } from '@tiptap/vue-3'

// import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setLayoutColumn: {
      setLayoutColumn: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'layoutColumn',
  group: 'block',

  content: 'columnItem{2,3}',

  defining: true,

  isolating: true,

  addAttributes() {
    return {
      id: {
        default: null,
      },
    }
  },
  parseHTML() {
    return [{ tag: 'layoutColumn' }]
  },
  renderHTML({ HTMLAttributes }) {
    return [
      'layoutColumn',
      mergeAttributes(HTMLAttributes, {
        class: 'layout-columns',
      }),
      0,
    ]
  },
  // addNodeView() {
  //   return VueNodeViewRenderer(NodeView as Component)
  // },
  addCommands() {
    return {
      setLayoutColumn:
        (options: { columnNum: number }) =>
        ({ commands, editor }) => {
          console.log('setLayoutColumn', options)
          const {
            left,
            right,
            center,
            columnNum,
            borderOpen,
            topPadding,
            bottomPadding,
            leftPadding,
            rightPadding,
            borderColor,
            borderWidth,
            borderStyle,
          } = options
          const defaultStyle = `padding:${topPadding}px ${rightPadding}px ${bottomPadding}px ${leftPadding}px;`
          if (columnNum === 2) {
            // 双栏
            let style = ''

            if (borderOpen) {
              style = `border-right:${borderWidth}px ${borderStyle} ${borderColor};padding:${topPadding}px ${rightPadding}px ${bottomPadding}px ${leftPadding}px;`
            } else {
              style = `padding:${topPadding}px ${rightPadding}px ${bottomPadding}px ${leftPadding}px;`
            }

            return commands.insertContent(
              `<layoutColumn ><columnItem width="${left}"  styleText="${style}"><p></p></columnItem><columnItem width="${right}" styleText="${defaultStyle}"><p></p></columnItem></layoutColumn>`,
            )
          } else {
            // 三栏\
            let style = ''
            if (borderOpen) {
              style = `border-left:${borderWidth}px ${borderStyle} ${borderColor};border-right:${borderWidth}px ${borderStyle} ${borderColor};padding:${topPadding}px ${rightPadding}px ${bottomPadding}px ${leftPadding}px;`
            } else {
              style = `padding:${topPadding}px ${rightPadding}px ${bottomPadding}px ${leftPadding}px;`
            }
            return commands.insertContent(
              `<layoutColumn><columnItem width="${left}" styleText="${defaultStyle}"><p></p></columnItem><columnItem width="${center}" styleText="${style}"><p></p></columnItem><columnItem width="${right}" styleText="${defaultStyle}"><p></p></columnItem></layoutColumn>`,
            )
          }
        },
    }
  },
})
