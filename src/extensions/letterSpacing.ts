import { Extension } from '@tiptap/core'

export default Extension.create({
  name: 'letterSpacing',

  addOptions() {
    return {
      types: ['textStyle'],  // 该扩展基于 textStyle 类型
      defaultLetterSpacing: '0px',  // 默认字间距为 0px
    }
  },

  // 添加全局属性，使得 `letterSpacing` 可以通过 `textStyle` 应用到文本上
  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          letterSpacing: {
            default: this.options.defaultLetterSpacing,
            parseHTML: (element) => element.style.letterSpacing || this.options.defaultLetterSpacing,
            renderHTML: (attributes) => {
              if (attributes.letterSpacing === this.options.defaultLetterSpacing) {
                return {}
              }
              return { style: `letter-spacing: ${attributes.letterSpacing}` }
            },
          },
        },
      },
    ]
  },

  // 添加命令来设置和取消字间距
  addCommands() {
    return {
      // 设置字间距
      setLetterSpacing:
        (letterSpacing) =>
          ({ chain }) => {
            return chain().setMark('textStyle', { letterSpacing }).run()
          },

      // 取消字间距
      unsetLetterSpacing:
        () =>
          ({ chain }) => {
            return chain()
              .setMark('textStyle', { letterSpacing: null })  // 取消字间距
              .removeEmptyTextStyle()  // 删除空的 textStyle
              .run()
          },
    }
  },
})
