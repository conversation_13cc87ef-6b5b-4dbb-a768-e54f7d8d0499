import { mergeAttributes, Node } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setLinks: {
      setLinks: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'links',
  inline: true,
  group: 'inline',
  atom: true,
  content: 'text*',
  addAttributes() {
    return {
      href: {
        default: '',
      },
      type: {
        //哪种类型得链接
        default: null,
      },
      attrs: { default: {} },
      quoteType: {
        //交叉引用和资源库得引用类型
        default: null,
      },
      fontSize: {
        default: '14px',
      },
      title: {
        default: '',
      },
    }
  },
  parseHTML() {
    return [{ tag: 'links' }]
  },
  renderHTML({ HTMLAttributes }) {
    return [
      'links',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
    ]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as any)
  },
  addCommands() {
    return {
      setLinks:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
