import { Extension } from '@tiptap/core'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setNodeFloat: {
      setNodeFloat: (options: any) => ReturnType
    }
    unsetNodeFloat: {
      unsetNodeFloat: () => ReturnType
    }
  }
}
export default Extension.create({
  name: 'nodeFloat',
  addOptions() {
    return {
      defaultAlignment: 'unset',
      alignments: ['left', 'unset', 'right'],
      types: ['image', 'imageIcon', 'video', 'audio', 'iframe', 'file'],
    }
  },
  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          nodeFloat: {
            default: this.options.defaultAlignment,
            parseHTML: (element) => {
              return (
                element.style.float || this.options.defaultAlignment
              )
            },
            renderHTML: (attributes) => {
              if (attributes.nodeFloat === this.options.defaultAlignment) {
                return {}
              }
              return { style: `float: ${attributes.nodeFloat}` }
            },
          },
        },
      },
    ]
  },
  addCommands() {
    return {
      setNodeFloat:
        (alignment) =>
        ({ commands }) => {
          if (!this.options.alignments.includes(alignment)) {
            return false
          }
          return this.options.types.every((type: string) =>
            commands.updateAttributes(type, { nodeFloat: alignment }),
          )
        },
      unsetNodeFloat:
        () =>
        ({ commands }) => {
          return this.options.types.every((type: string) =>
            commands.resetAttributes(type, 'nodeFloat'),
          )
        },
    }
  },
})
