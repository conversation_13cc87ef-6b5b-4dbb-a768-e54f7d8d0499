import { Editor, findParentNodeClosestToPos, getNodeType } from '@tiptap/core'
import { Fragment, Node, Schema, Slice } from '@tiptap/pm/model'
import { EditorState, Transaction } from '@tiptap/pm/state'
import { ReplaceStep } from '@tiptap/pm/transform'

import type {
  ComputedFn,
  NodesComputed,
  PageState,
  SplitInfo,
  SplitParams,
} from '@/extensions/page/types'

import {
  getAbsentHtmlH,
  getBreakPos,
  getDefault,
  getDomHeight,
  getId,
} from './core'
import {
  BULLETLIST,
  CASSIE_BLOCK_EXTEND,
  HEADING,
  IMAGEGALLERY,
  IMAGELAYOUT,
  INTERACTION,
  LIST_TYPE,
  LISTITEM,
  ORDEREDLIST,
  PAGE,
  PARAGRAPH,
  RESOURCE_COVER,
  TABLE,
  TABLE_PLUS,
  TABLE_ROW,
  TASKITEM,
  TASKLIST,
} from './node-names'
import { findParentNodeFarOffToPosWithinPage } from './utils'

export const sameListCalculation: ComputedFn = (
  splitContex,
  node,
  pos,
  parent,
  dom,
) => {
  const { height: pHeight, margin } = getDomHeight(dom)
  if (splitContex.isOverflow(pHeight)) {
    splitContex.addHeight(margin)
    return true
  }
  splitContex.addHeight(pHeight)
  return false
}
let count = 1

export const sameItemCalculation: ComputedFn = (
  splitContex,
  node,
  pos,
  parent,
  dom,
) => {
  const chunks = splitContex.splitResolve(pos)
  const { height: pHeight, margin } = getDomHeight(dom)
  if (splitContex.isOverflow(pHeight)) {
    if (pHeight > splitContex.getHeight()) {
      splitContex.addHeight(margin)
      return true
    }
    if (parent?.firstChild === node) {
      splitContex.setBoundary(chunks[chunks.length - 2][2], chunks.length - 2)
    } else {
      splitContex.setBoundary(pos, chunks.length - 1)
    }
  } else {
    splitContex.addHeight(pHeight)
  }
  return false
}
export const singleComponentPageComputed: ComputedFn = (
  splitContex,
  node,
  pos,
  parent,
  dom,
) => {
  // console.log('resourceCover', node, pos, parent, dom)
  const { height: pHeight } = getDomHeight(dom)
  if (!splitContex.isOverflow(pHeight)) {
    splitContex.addHeight(pHeight)
    return false
  }
  // chunks 结构[[_Node, 0, 0], [_Node, 25, 398]] 第一个是node, 第二个是 node 的 index，第三个是节点开始位置
  // 因为文档没有，在源码中prosemirror-model包下，resolvedpos.ts:218行
  const chunks = splitContex.splitResolve(pos)
  // console.log('resourceCover', chunks, pos, chunks.length - 1)
  splitContex.setBoundary(pos, chunks.length - 1)
  return false
}
export const defaultNodesComputed: NodesComputed = {
  [ORDEREDLIST]: sameListCalculation,
  [BULLETLIST]: sameListCalculation,
  [LISTITEM]: sameItemCalculation,
  [TASKLIST]: sameListCalculation,
  [TASKITEM]: sameItemCalculation,

  [TABLE_ROW]: (splitContex, node, pos, parent, dom) => {
    const chunks = splitContex.splitResolve(pos)
    if (splitContex.isOverflow(0)) {
      if (count > 1) {
        count = 1
        splitContex.setBoundary(chunks[chunks.length - 2][2], chunks.length - 2)
      } else {
        splitContex.setBoundary(pos, chunks.length - 1)
        count += 1
      }
      return false
    }
    const { height: pHeight } = getDomHeight(dom)
    if (splitContex.isOverflow(pHeight)) {
      if (pHeight > splitContex.getHeight()) {
        splitContex.addHeight(pHeight)
        return false
      }
      //如果当前行是list的第一行并且已经超过分页高度 直接返回上一层级的切割点
      if (parent?.firstChild === node) {
        splitContex.setBoundary(chunks[chunks.length - 2][2], chunks.length - 2)
      } else {
        //如果不是第一行 直接返回当前行的切割点
        splitContex.setBoundary(pos, chunks.length - 1)
      }
    } else {
      splitContex.addHeight(pHeight)
    }
    return false
  },
  [TABLE]: (splitContex, node, pos, parent, dom) => {
    const { height: pHeight, margin } = getDomHeight(dom)
    //如果列表的高度超过分页高度 直接返回继续循环 tr 或者li
    if (splitContex.isOverflow(pHeight)) {
      splitContex.addHeight(margin)
      return true
    }
    //没有超过分页高度 累加高度
    splitContex.addHeight(pHeight)
    return false
  },
  [TABLE_PLUS]: singleComponentPageComputed,
  [HEADING]: (splitContex, node, pos, parent, dom) => {
    const { height: pHeight } = getDomHeight(dom)
    if (!splitContex.isOverflow(pHeight)) {
      splitContex.addHeight(pHeight)
      return false
    }

    const chunks = splitContex.splitResolve(pos)
    if (pHeight > splitContex.getHeight()) {
      const point = getBreakPos(node, dom, splitContex)
      if (point) {
        splitContex.setBoundary(pos + point, chunks.length)
        return false
      }
    } else {
      //直接返回当前段落
      splitContex.setBoundary(pos, chunks.length - 1)
    }
    return false
  },

  [PARAGRAPH]: (splitContex, node, pos, parent, dom) => {
    const { height: pHeight } = getDomHeight(dom)
    if (!splitContex.isOverflow(pHeight)) {
      splitContex.addHeight(pHeight)
      return false
    }
    // console.log('paragraph', node, pos, parent, dom)
    const chunks = splitContex.splitResolve(pos)
    if (pHeight > splitContex.getDefaultHeight()) {
      const point = getBreakPos(node, dom, splitContex)
      if (point) {
        splitContex.setBoundary(pos + point, chunks.length)
        return false
      }
    }
    if (parent?.firstChild === node) {
      splitContex.setBoundary(chunks[chunks.length - 2][2], chunks.length - 2)
      return false
    }
    // console.log('paragraph', chunks, pos, chunks.length - 1)
    splitContex.setBoundary(pos, chunks.length - 1)
    return false
  },
  [PAGE]: (splitContex, node, pos, parent, dom, startIndex, forcePageId, i) => {
    return startIndex === i && parent?.type.name === 'doc'
  },
  [RESOURCE_COVER]: singleComponentPageComputed,
  [INTERACTION]: singleComponentPageComputed,
  [IMAGEGALLERY]: singleComponentPageComputed,
  [IMAGELAYOUT]: singleComponentPageComputed,
  ['default']: (splitContex, node, pos, parent, dom) => {
    const { height: pHeight } = getDomHeight(dom)
    // console.log('default', node.type.name, pHeight)
    if (!splitContex.isOverflow(pHeight)) {
      splitContex.addHeight(pHeight)
      return false
    }

    const chunks = splitContex.splitResolve(pos)
    if (parent?.firstChild === node) {
      const chunksNext = splitContex.splitResolve(pos + node.nodeSize)
      splitContex.setBoundary(pos + node.nodeSize, chunksNext.length - 1)
      return false
    }
    splitContex.setBoundary(pos, chunks.length - 1)
    return false
  },
}

/**
 * 分页上下文类
 */
export class SplitContext {
  #doc: Node //文档
  #accumolatedHeight = 0 //累加高度
  #pageBoundary: SplitInfo | null = null //返回的切割点
  #height = 0 //分页的高度
  #paragraphDefaultHeight = 0 //p标签的默认高度
  attributes: Record<string, any> = {}
  schema: Schema

  /**
   * 获取文档
   * @returns 文档
   */
  getDoc() {
    return this.#doc
  }

  /**
   * 构造函数
   * @param doc 文档
   * @param height 分页高度
   * @param paragraphDefaultHeight p标签的默认高度
   */
  constructor(
    schema: Schema,
    doc: Node,
    height: number,
    paragraphDefaultHeight: number,
  ) {
    this.#doc = doc
    this.#height = height
    this.#paragraphDefaultHeight = paragraphDefaultHeight
    this.schema = schema
  }

  getHeight() {
    return this.#height
  }

  /**
   * 获取默认高度
   * @returns 默认高度
   */
  getDefaultHeight() {
    return this.#paragraphDefaultHeight
  }

  /**
   * 判断是否溢出
   * @param height 增加的高度
   * @returns 是否溢出
   */
  isOverflow(height: number) {
    return this.#accumolatedHeight + height >= this.#height
  }

  /**
   * 增加高度
   * @param height 增加的高度
   */
  addHeight(height: number) {
    this.#accumolatedHeight += height
  }

  /**
   * 设置切割点
   * @param pos 切割点位置
   * @param depth 切割点深度
   */
  setBoundary(pos: number, depth: number) {
    this.#pageBoundary = {
      pos,
      depth,
    }
  }

  /**
   * 获取切割点
   * @returns 切割点
   */
  pageBoundary() {
    return this.#pageBoundary
  }

  /**
   * 解析切割点
   * @param pos 切割点位置
   * @returns 解析结果
   */
  splitResolve(pos: number) {
    // @ts-ignore
    const array = this.#doc.resolve(pos).path
    // console.log('splitResolve', this.#doc.resolve(pos))
    const chunks = []
    if (array.length <= 3) return array
    const size = 3
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  /**
   * 获取最后一页
   * @returns 最后一页
   */
  lastPage() {
    return this.#doc.lastChild
  }
}

/*
 * PageComputedContext 分页核心计算class
 * */
export class PageComputedContext {
  nodesComputed: NodesComputed
  state: EditorState
  tr: Transaction
  pageState: PageState
  editor: Editor
  restDomIds: string[] = []
  forcePageId: string | null
  startIndex: number

  constructor(
    editor: Editor,
    nodesComputed: NodesComputed,
    pageState: PageState,
    state: EditorState,
  ) {
    this.editor = editor
    this.nodesComputed = nodesComputed
    this.tr = state.tr
    this.state = state
    this.pageState = pageState
    this.restDomIds = []
    this.forcePageId = null
    this.startIndex = 0
  }

  //核心执行逻辑
  run() {
    const { selection, doc } = this.state
    const { inserting, deleting, runState, splitPage } = this.pageState
    if (!runState) return null
    this.prepare()
    if (splitPage) return this.forceSplit()
    if (!inserting && deleting && selection.$head.node(1) === doc.lastChild)
      return this.tr
    if (inserting || deleting) {
      const startTime = performance.now()
      this.computed()
      this.checkNodeAndFix()
    }

    return this.tr
  }

  prepare() {
    const { doc } = this.tr
    const { selection } = this.state
    // @ts-ignore
    this.startIndex = doc.content.findIndex(selection.head).index
  }

  computed() {
    this.splitDocument()
    return this.tr
  }

  /**
   * 文档开始加载的时候进行初始化分页
   */
  initComputed() {
    this.splitDocument()
    return this.tr
  }

  /**
   * @description 递归分割page
   */
  splitDocument() {
    const { schema } = this.state
    for (;;) {
      // 获取最后一个page计算高度，如果返回值存在的话证明需要分割
      const splitInfo = this.getNodeHeight()
      if (!splitInfo) return
      const type = getNodeType(PAGE, schema)
      this.lift({
        pos: splitInfo.pos,
        depth: splitInfo.depth,
        typesAfter: [{ type }],
        schema,
      } as SplitParams)
      this.startIndex += 1
    }
  }

  forceSplit() {
    const { selection, schema } = this.state
    const { $anchor } = selection
    const splitInfo = { pos: $anchor.pos, depth: $anchor.depth }
    const parentNode = findParentNodeFarOffToPosWithinPage(
      selection.$from,
      (node) => [TABLE_PLUS, ...LIST_TYPE].includes(node.type.name),
    )
    if (parentNode) {
      splitInfo.pos = parentNode.start
      splitInfo.depth = parentNode.depth
      if (
        parentNode.node.type.name === TABLE ||
        parentNode.node.type.name === TABLE_PLUS
      ) {
        splitInfo.pos = parentNode!.start + parentNode!.node.content.size + 1
        const parentTablePlusNode = findParentNodeClosestToPos(
          this.state.doc.resolve(parentNode!.pos),
          (node) => [TABLE_PLUS].includes(node.type.name),
        )
        splitInfo.depth = 1
        if (parentTablePlusNode) {
          splitInfo.pos = splitInfo.pos + 1
          splitInfo.depth = 1
        }
      }
    }
    const type = getNodeType(PAGE, schema)
    this.lift({
      pos: splitInfo.pos,
      depth: splitInfo.depth,
      typesAfter: [{ type }],
      schema,
      force: true,
    })
    return this.tr
  }

  /**
   * @description 分页主要逻辑 修改系统tr split方法 添加默认 extend判断 默认id重新生成
   * <AUTHOR>
   * @method splitPage 分割页面
   * @param pos
   * @param depth
   * @param typesAfter
   * @param schema
   */
  lift({ pos, depth = 1, typesAfter, schema, force = false }: SplitParams) {
    const { tr } = this
    const $pos = tr.doc.resolve(pos)
    let before = Fragment.empty
    let after = Fragment.empty
    for (
      let d = $pos.depth, e = $pos.depth - depth, i = depth - 1;
      d > e;
      d--, i--
    ) {
      //新建一个和 $pos.node(d) 一样的节点 内容是 before
      before = Fragment.from($pos.node(d).copy(before))
      const typeAfter = typesAfter?.[i]
      const n = $pos.node(d)
      let na = $pos.node(d).copy(after)
      //处理id重复的问题
      if (na?.attrs.id) {
        let extend = {}
        if (na.attrs.extend === false) {
          extend = { extend: true }
        }
        //重新生成id
        const attr = Object.assign({}, n.attrs, { id: getId(), ...extend })
        // @ts-ignore
        na = schema.nodes[n.type.name].createAndFill(attr, after)
      }
      after = Fragment.from(
        typeAfter
          ? typeAfter.type.create(
              {
                id: getId(),
                pageNumber: na?.attrs.pageNumber + 1,
                force,
              },
              after,
            )
          : na,
      )
      // console.log('before==>', before)
      // console.log('after==>', after)
    }
    // console.log('before', before)
    // console.log('after', after)
    // console.log('pos', pos, pos)
    // console.log('旧tr', tr)
    tr.step(
      new ReplaceStep(pos, pos, new Slice(before.append(after), depth, depth)),
    )
    // console.log('新tr', tr)
    this.tr = tr
  }

  /**
   * desc 检查并修正分页造成的段落分行问题
   */
  checkNodeAndFix() {
    let { tr } = this
    const { doc } = tr
    const { schema } = this.state
    let beforeBolck: Node | null = null
    let beforePos = 0
    doc.descendants((node, pos, parentNode, i) => {
      if (node.type === schema.nodes[PARAGRAPH] && node.attrs.extend === true) {
        if (beforeBolck === null) {
          beforeBolck = node
          beforePos = pos
        } else {
          // console.log('beforeBolck: ' + beforeBolck)
          const mappedPos = tr.mapping.map(pos)
          if (beforeBolck.type === schema.nodes[PARAGRAPH]) {
          } else {
            tr = tr.step(
              new ReplaceStep(mappedPos - 1, mappedPos + 1, Slice.empty),
            )
          }
          return false
        }
      }
      //如果进入了新的扩展块 直接 清除 上次的 记录
      if (node.type === schema.nodes[CASSIE_BLOCK_EXTEND]) {
        beforeBolck = null
        beforePos = 0
        return true
      }
    })
    this.tr = tr
    return this.tr
  }

  /**
   * @description 获取需要分页的点 然后返回
   * <AUTHOR>
   * @method getNodeHeight 获取节点高度
   */
  getNodeHeight() {
    const { doc } = this.tr
    const { bodyOptions } = this.pageState
    const splitContex = new SplitContext(
      this.state.schema,
      doc,
      bodyOptions?.bodyHeight,
      getDefault(),
    )
    const { nodesComputed } = this
    const { restDomIds } = this
    const { forcePageId } = this
    const { startIndex } = this
    let breakDescendants = false
    doc.descendants((node, pos, parentNode, i) => {
      if (breakDescendants || node.attrs.id === forcePageId) {
        breakDescendants = true
        return false
      }

      if (!splitContex.pageBoundary()) {
        let dom = document.getElementById(node.attrs.id)
        if (
          restDomIds.includes(node.attrs.id) ||
          (!dom && node.type.name !== PAGE)
        ) {
          dom = getAbsentHtmlH(node, this.state.schema)
        }
        return (nodesComputed[node.type.name] || nodesComputed['default'])(
          splitContex,
          node,
          pos,
          parentNode,
          // @ts-ignore
          dom,
          startIndex,
          forcePageId,
          i,
        )
      }
      return false
    })
    return splitContex.pageBoundary() ? splitContex.pageBoundary() : null
  }
}
