export const TEXT = 'text'
export const PARAGRAPH = 'paragraph'
export const PAGE = 'page'
export const EXTEND = 'Extend'
export const CASSIE_BLOCK = 'Node'
export const CASSIE_BLOCK_EXTEND = CASSIE_BLOCK + EXTEND
export const CC = 'CC'
export const HEADING = 'heading'
export const BULLETLIST = 'bulletList'
export const LISTITEM = 'listItem'
export const LAYOUTCOLUMN = 'layoutColumn'
export const TASKLIST = 'taskList'
export const TASKITEM = 'taskItem'
export const ORDEREDLIST = 'orderedList'
export const TABLE = 'table'
export const TABLEHEADER = 'tableHeader'
export const TABLE_ROW = 'tableRow'
export const TABLE_CELL = 'tableCell'
export const TEXT_BOX = 'textBox'
export const TEXT_BORDER = 'textBorder'
export const IMAGE = 'image'
export const IFRAME = 'iframe'
export const FILE = 'file'
export const CODE_BLOCK = 'codeBlock'
export const AUDIO = 'audio'
export const TOC = 'toc'
export const VIDEO = 'video'
export const HORIZONTALRULE = 'horizontalrule'
export const PAGEBREAK = 'pagination'
export const LIST_TYPE = [
  TABLE,
  ORDEREDLIST,
  BULLETLIST,
  TASKLIST,
  LAYOUTCOLUMN,
]
export const LIST_MEDIA = [VIDEO, IMAGE]
export const PAPERWRAPPING = 'paperWrapping'
//画廊
export const IMAGEGALLERY = 'imageGallery'
export const IMAGE_INLINE = 'imageInLine'
export const IMAGE_ICON = 'imageIcon'

export const IMAGELAYOUT = 'imageLayout'
export const RESOURCE_COVER = 'resourceCover'

export const INTERACTION = 'interaction'

export const BLOCK_QUOTE = 'blockquote'
export const QUESTIONS = 'questions'
export const TABLE_PLUS = 'tablePlus'
export const ERROR_CORRECTION = 'errorCorrection'

export const BUBBLEINLINE = 'bubbleInline'
export const TEST_PAPER = 'papers'
export const PSYCHOLOGY_HEALTH = 'psychologyHealth'

export const EXTENDED_READING = 'extendedReading'
export const BLOCKVIEW = 'blockView'
export const FOLD = 'fold'
export const SURROUND = 'surround'

export const BACKGROUNDIMG = 'backgroundImg'
