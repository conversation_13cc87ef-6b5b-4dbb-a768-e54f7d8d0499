import { mergeAttributes, Node } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'
const { options } = useStore()
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setPaperWrapping: {
      setPaperWrapping: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'paperWrapping',
  group: 'block',
  atom: true,
  addAttributes() {
    return {
      id: {
        default: null,
      },
      name: {
        default: null,
      },
      size: {
        default: null,
      },
      src: {
        default: null,
      },
    }
  },
  parseHTML() {
    return [{ tag: 'paperWrapping' }]
  },
  renderHTML({ HTMLAttributes }) {
    return ['paperWrapping', mergeAttributes(HTMLAttributes)]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component)
  },
  addCommands() {
    return {
      setPaperWrapping:
        (options: { name?: string; src: string; size?: number } = {}) =>
        ({ commands, editor }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
