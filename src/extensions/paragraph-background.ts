import {
  Extension,
  getMarkType,
  getNodeType,
  getSchemaTypeNameByName,
} from '@tiptap/core'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setBackgroundUrl: {
      setBackgroundUrl: (url: string) => ReturnType
    }
    setContainerColor: {
      setContainerColor: (color: string) => ReturnType
    }
    setBackgroundSize: {
      setBackgroundSize: (size: string) => ReturnType
    }
    setBackgroundBorder: {
      setBackgroundBorder: (border: object) => ReturnType
    }
    moveBackground: {
      moveBackground: (position: string) => ReturnType
    }
    updateAttributesReturnSuccessAndFail: {
      updateAttributesReturnSuccessAndFail: (
        typeOrName: string,
        attributes: any,
      ) => ReturnType
    }
  }
}

export default Extension.create({
  name: 'paragraphBackground',
  addOptions() {
    return {
      types: ['bulletList', 'orderedList', 'heading', 'paragraph'],
    }
  },
  addGlobalAttributes() {
    return [
      {
        types: ['bulletList', 'orderedList', 'heading', 'paragraph'],
        attributes: {
          backgroundImage: {
            default: '',
            parseHTML: (element) => {
              return element.style.backgroundImage || ''
            },
            renderHTML: (attributes) => {
              if (!attributes.backgroundImage) {
                return {}
              }

              return {
                style: `background-image:url('${attributes.backgroundImage}'); background-repeat:no-repeat;`,
              }
            },
          },
          backgroundRepeat: {
            default: 'no-repeat',
          },
          //   attributes: {
          containerColor: {
            default: '',
            parseHTML: (element) => {
              return element.style.backgroundColor || ''
            },
            renderHTML: (attributes) => {
              if (!attributes.containerColor) {
                return {}
              }

              return {
                style: `background-color: ${attributes.containerColor};`,
              }
            },
          },

          backgroundSize: {
            default: '',
            parseHTML: (element) => {
              return element.style.backgroundSize || ''
            },
            renderHTML: (attributes) => {
              if (!attributes.backgroundSize) {
                return {}
              }

              // if (
              //   attributes.backgroundSize === 'del' &&
              //   attributes.backgroundImage
              // ) {
              //   attributes.backgroundImage = ''
              //   return {
              //     style: `background-image: none`,
              //   }
              // } else
              if (
                attributes.backgroundSize === 'lay' &&
                attributes.backgroundImage
              ) {
                return {
                  style: `background-size: 100% 100%; background-repeat:no-repeat;`,
                }
              } else {
                if (attributes.backgroundImage) {
                  return {
                    style: `background-size: ${attributes.backgroundSize}; background-repeat:no-repeat;`,
                  }
                }
              }
            },
          },

          backgroundBorder: {
            default: '',
            parseHTML: (element) => {
              return element.style.border || ''
            },
            renderHTML: (attributes) => {
              if (!attributes.backgroundBorder) {
                return {}
              }
              return {
                style: `border: ${attributes.backgroundBorder.borderWidth}px ${attributes.backgroundBorder.borderStyle} ${attributes.backgroundBorder.borderColor};border-radius:${attributes.backgroundBorder.borderRadius}px;padding:${attributes.backgroundBorder.borderPadding}px;`,
              }
            },
          },
        },
      },
    ]
  },

  addCommands() {
    return {
      // 删除背景
      // moveBackground: (position) => ({ chain, commands }) => {

      // }
      setBackgroundUrl:
        (url) =>
        ({ chain, commands }) => {
          // return this.options.types.every((type: string) =>
          //   commands.updateAttributes(type, { backgroundImage: url }),
          // )
          let op = false
          for (const type of this.options.types) {
            if (
              commands.updateAttributesReturnSuccessAndFail(type, {
                backgroundImage: url,
              })
            ) {
              op = true
              break
            }
          }
          return op
        },

      setContainerColor:
        (color) =>
        ({ chain, commands }) => {
          // return this.options.types.every((type: string) =>
          //   commands.updateAttributes(type, { containerColor: color }),
          // )
          let op = false
          for (const type of this.options.types) {
            if (
              commands.updateAttributesReturnSuccessAndFail(type, {
                containerColor: color,
              })
            ) {
              op = true
              break
            }
          }
          return op
        },

      setBackgroundSize:
        (size) =>
        ({ chain, commands }) => {
          // return this.options.types.every((type: string) =>
          //   commands.updateAttributes(type, { backgroundSize: size }),
          // )
          // 直接给节点添加属性可能会导致第二次添加背景图片失败，所以这里采用直接删除背景图片属性的方式
          if (size === 'del') {
            return this.options.types
              .map((type: string) =>
                commands.resetAttributes(type, 'backgroundImage'),
              )
              .every((response: any) => response)
          }
          let op = false
          console.log('====>', size, this.options.types)
          for (const type of this.options.types) {
            if (
              commands.updateAttributesReturnSuccessAndFail(type, {
                backgroundSize: size,
              })
            ) {
              op = true
              break
            }
          }
          return op
        },
      setBackgroundBorder:
        (border) =>
        ({ chain, commands }) => {
          let op = false
          for (const type of this.options.types) {
            if (
              commands.updateAttributesReturnSuccessAndFail(type, {
                backgroundBorder: border,
              })
            ) {
              op = true
              break
            }
          }
          return op
        },

      // 重写底层updateAttributes方法, 没有成功更新对应类型节点就返回false
      updateAttributesReturnSuccessAndFail:
        (typeOrName, attributes = {}) =>
        ({ tr, state, dispatch }) => {
          let nodeType: any = null
          let markType: any = null
          const schemaType = getSchemaTypeNameByName(
            typeof typeOrName === 'string' ? typeOrName : typeOrName.name,
            state.schema,
          )
          if (!schemaType) {
            return false
          }
          if (schemaType === 'node') {
            nodeType = getNodeType(typeOrName, state.schema)
          }
          if (schemaType === 'mark') {
            markType = getMarkType(typeOrName, state.schema)
          }
          if (dispatch) {
            // console.log('====>', nodeType, attributes)
            let op = false
            tr.selection.ranges.forEach((range) => {
              const from = range.$from.pos
              const to = range.$to.pos
              state.doc.nodesBetween(from, to, (node, pos) => {
                if (nodeType && nodeType === node.type) {
                  op = true
                  tr.setNodeMarkup(pos, undefined, {
                    ...node.attrs,
                    ...attributes,
                  })
                  return false
                }
                if (markType && node.marks.length) {
                  node.marks.forEach((mark) => {
                    if (markType === mark.type) {
                      op = true
                      const trimmedFrom = Math.max(pos, from)
                      const trimmedTo = Math.min(pos + node.nodeSize, to)
                      tr.addMark(
                        trimmedFrom,
                        trimmedTo,
                        markType.create({
                          ...mark.attrs,
                          ...attributes,
                        }),
                      )
                      return false
                    }
                  })
                }
              })
            })
            if (op) {
              return true
            } else {
              return false
            }
          }
          return true
        },
    }
  },
})
