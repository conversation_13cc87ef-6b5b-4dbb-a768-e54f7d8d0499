import { mergeAttributes, Node, type NodeViewProps } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    psychologyHealth: {
      psychologyHealth: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'psychologyHealth',
  group: 'block',
  atom: true,
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      width: {
        default: null,
      },
      height: {
        default: 200,
      },
      psychologyHealth: {
        default: null,
      },
      isExpand: {
        default: false, // 阅读器默认不展开
      },
    }
  }, // 添加属性
  parseHTML() {
    return [
      {
        tag: 'psychologyHealth',
      },
    ]
  },
  renderHTML({ HTMLAttributes }) {
    return ['psychologyHealth', mergeAttributes(HTMLAttributes)]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      psychologyHealth:
        (options) =>
          ({ commands, editor }) => {
            return commands.insertContentAt(editor.state.selection.anchor, {
              type: this.name,
              attrs: options,
            })
          },
    }
  },
})
