<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view-rc">
    <div @mouseenter="showChild" @mouseleave="cannelHideLayer">
      <div v-show="mousemove" class="top-node-mu" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="2">
              <div style="display: flex; align-items: center" @click="previewQuiz(node)">
                <!--                <FileSearchIcon />-->
                <!--                <el-icon-view/>-->
                <BrowseIcon />

                <!-- 查看按钮 -->
                <span style="margin-left: 5px">{{ t('insert.psychologyHealth.detail') }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2">
              <div style="display: flex; align-items: center" @click="previewDetail(node)">
                <FileSearchIcon />
                <!-- 预览按钮 -->
                <span style="margin-left: 5px">{{ t('insert.psychologyHealth.priview') }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="4">
              <div style="display: flex; align-items: center" @click="handelDelNode">
                <DeleteIcon />
                <!-- 删除按钮 -->
                <span style="margin-left: 5px">{{
                  t('insert.questions.delBtnText')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <div class="extended-reading-template-bg"
        :style="`background: url(${template?.orderTemplateBgUrl ?? node.attrs.bgSrc}) no-repeat center;height:55px;background-size:100% 100%;`">
        <div class="extended-left">
          <div class="extended-icon">
            <img :src="template.theme == 'light' ? healthDark : healthLight" class="icon-file" />
          </div>
          <div class="extended-title" :style="{ color: template.theme == 'light' ? '#fff' : '#333' }">{{
            localQuizData.scaleName }}
          </div>
        </div>
      </div>

      <!-- <div class="quiz-questions-list">
        <div class="list-item-img">
          <img :src="paperIcon" alt="" />
        </div>
        <div class="list-item-info">
          <div class="list-item-info-title">{{ localQuizData.paperTitle }}</div>
        </div>
        <div class="list-item-info-time">
          <div class="list-item-info-time-text">
            <Calendar1Icon />{{ localQuizData.createTime }}
          </div>
        </div>
      </div> -->
    </div>
  </node-view-wrapper>
  <!--  <t-drawer-->
  <!--    v-model:visible="showEditQuizPopup"-->
  <!--    :close-btn="true"-->
  <!--    :footer="false"-->
  <!--    attach="body"-->
  <!--    size="80%"-->
  <!--    mode="overlay"-->
  <!--    placement="right"-->
  <!--    confirm-btn="更新"-->
  <!--    :header="-->
  <!--      paperType == '1'-->
  <!--        ? t('insert.testPaper.editPaper')-->
  <!--        : t('insert.schoolAssignment.editPaper')-->
  <!--    "-->
  <!--    class="new-question-drawer"-->
  <!--    :destroy-on-close="true"-->
  <!--    @cancel="showEditQuizPopup = false"-->
  <!--  >-->
  <!--    <template-paper-editSteps-update-->
  <!--      v-if="showEditQuizPopup"-->
  <!--      :type="'edit'"-->
  <!--      :paper-id="localQuizData.paperId"-->
  <!--      :paper-type="localQuizData.paperType.toString()"-->
  <!--      class="quiz-update-drawer"-->
  <!--      @confirm="onQuizDataSave"-->
  <!--    />-->
  <!--  </t-drawer>-->

  <t-dialog v-model:visible="testPaperShow" attach="body" :header="t('insert.image.setting')" width="20%"
    :close-on-overlay-click="false" @confirm="onSubmit" @close="onClose" @cancel="onClose">
    <div>是否在阅读器中展开： <t-switch v-model="formData.isExpand" /></div>
  </t-dialog>

  <template-psychologyHealth-preview v-model="previewQuizVisibility"
    :paper-id="localQuizData?.scaleId"></template-psychologyHealth-preview>

  <template-psychologyHealth-detail v-model="previewDetailVisibility" :data="localQuizData"
    :paper-id="localQuizData?.scaleId">
  </template-psychologyHealth-detail>


</template>

<script setup>
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import {
  BrowseIcon,
  DeleteIcon,
  EllipsisIcon,
  FileSearchIcon
} from 'tdesign-icons-vue-next'

import healthDark from '@/assets/icons/psychologyHealthDark.svg'
import healthLight from '@/assets/icons/psychologyHealthLight.svg'
import { useMouseOver } from '@/hooks/mouseOver'
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()
const { pageTemplateId } = useTemplate()
const template = pageTemplateId.value
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const showEditQuizPopup = $ref(false)
let localQuizData = $ref({})
const formData = $ref({
  isExpand: node.attrs.isExpand,
})
let previewQuizVisibility = $ref(false)
let previewDetailVisibility = $ref(false)
let testPaperShow = $ref(false)
function previewQuiz() {
  previewQuizVisibility = true
}

function previewDetail() {
  previewDetailVisibility = true
}

const onClose = () => {
  testPaperShow = false
}

const onOpen = () => {
  testPaperShow = true
}

const onSubmit = () => {
  updateAttributes({
    isExpand: formData.isExpand,
  })
  testPaperShow = false
}
function handelDelNode() {
  deleteNode()
}

onMounted(() => {
  localQuizData = JSON.parse(JSON.stringify(node.attrs.psychologyHealth))
})
</script>

<style lang="less" scoped>
@import '@/assets/styles/_common.less';

.top-node-mu {
  margin-bottom: -10px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.questions-list {
  .question-collection {
    margin-bottom: 24px;

    .collection-header {
      margin-bottom: 16px;

      h3 {
        color: #303133;
        font-size: 18px;
        font-weight: 500;
      }
    }
  }

  .question-item {
    margin-bottom: 20px;

    .question-header {
      margin-bottom: 15px;

      .question-index {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-right: 10px;
      }

      .question-score {
        color: #409eff;
        margin-left: 8px;
        font-size: 14px;
      }
    }
  }
}

.umo-node-view-rc {
  position: relative;

  .top-node-mu {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 99;
    font-size: 24px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    padding: 5px;
    color: #fff;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .extended-reading-template-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;

    .extended-left {
      display: flex;
      align-items: center;

      .extended-icon {
        width: 24px;
        height: 24px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .extended-title {
        padding: 0 10px;
        font-size: 16px;
        font-weight: 400;
      }

      .extended-name {
        color: #333;
        margin-left: 20px;
        width: 450px;
        white-space: nowrap;
        /* 确保文本在一行内显示 */
        overflow: hidden;
        /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis;
        /* 使用省略号表示被截断的文本 */
      }
    }

    .extended-right {
      color: #666;
      font-size: 16px;
    }
  }

  .extended-reading-template-content {
    background-color: #f6f6f6;
    border: 1px solid #ebebeb;
    padding: 20px 16px;
    margin-top: 10px;
  }
}

ul {
  list-style: none;
  padding: 0;
}

li {
  margin: 4px 0;
}

label {
  cursor: pointer;
}
</style>
<style lang="less">
.bellCss .umo-node-view-rc * {
  text-indent: 0;
}

.bellCss .umo-node-view-rc .question-item {
  .data-item {
    padding: 20px 10px;
    font-size: 14px;

    label {
      min-width: fit-content;
    }
  }
}
</style>
