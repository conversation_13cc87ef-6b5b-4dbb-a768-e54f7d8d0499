import { type CommandProps, mergeAttributes, Node } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setremarkInline: {
      setremarkInline: (options: any) => ReturnType
    }
  }
}

export default Node.create({
  name: 'remarkInline',
  inline: true,
  group: 'inline',
  content: '',
  atom: true,
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      remarkIcon: {
        default: `${import.meta.env.VITE_ONLINE_PREVIEW}aHR0cDovL2R1dHAtdGVzdC5vc3MtY24tYmVpamluZy5hbGl5dW5jcy5jb20vdGVzdC5wZGY%3D`,
      },
      remarkContent: {
        default: true,
      },
      remarkUrl: {
        default: '',
      },
      remarkType: {
        default: '1',
      },
    }
  },
  parseHTML() {
    return [{ tag: 'remarkInline' }]
  },
  renderHTML({ HTMLAttributes }) {
    return [
      'remarkInline',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
    ]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView)
  },
  addCommands() {
    return {
      setremarkInline:
        (options: {
          remarkContent?: string
          remarkUrl?: string
          remarkTitle?: string
          remarkType?: string
          remarkIcon?: string
        }) =>
        ({ commands, editor }: CommandProps) => {
          const op = false
        
          // 默认值
          return op
        },
    }
  },
})
