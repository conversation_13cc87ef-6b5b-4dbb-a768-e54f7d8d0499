<template>
    <node-view-wrapper  class="umo-node-remark" >
      <t-popconfirm theme="default"
        :visible="visible"
        min-width="900px"
        @visible-change="onVisibleChange"
        :confirm-btn="{
          content: t('insert.remark.remove'),
          theme: 'danger',
        }"
        :cancel-btn="{
          content: t('insert.remark.edit'),
          theme: 'default',
        }">
        <template #icon>
          <icon name="cps-icon-tips" :url="newSvgUrl"/>
        </template>
        <template #content>
          <div style="display: flex;justify-content: space-between"><text>张三</text><text>2024-11-18 15:38</text></div>
          <hr>
          <div style="margin-top:30px;min-width:200px">{{ node.attrs.remarkContent }}</div>
        </template>
        <t-button variant="text" ghost class="ghost-button" style="borderBottom:none;">
          <i class="remark-icon"></i>
        </t-button>
      </t-popconfirm>

      <t-dialog v-model:visible="dialogVisible" attach="body" :header="t('insert.remark.edit')" width="1100"
                @opened="onOpen" @confirm="onSubmit" @close="onClose" @cancel="onClose" :closeOnOverlayClick="false">
        <div style="width: 1000px;">
          <t-form ref="formValidatorStatus" :data="formData" :rules="rules" :label-width="120" @reset="onReset">
            <t-form-item :label="t('insert.remark.text')" name="remark">
              <t-textarea
                v-model="formData.remark"
                :placeholder="t('insert.remark.placeholder')"
                name="remark"
                :autosize="{ minRows: 5, maxRows: 7 }"
              />
            </t-form-item>
          </t-form>
        </div>
      </t-dialog>
    </node-view-wrapper>
</template>

<script setup>
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { Icon } from 'tdesign-icons-vue-next';
const newSvgUrl = '';
const { node, updateAttributes } = defineProps(nodeViewProps)
const { options, editor,toggleRemarkPopup,remarkVisible } = useStore()
const containerRef = ref(null)
const remarkShow=ref(false)
const dialogVisible = ref(false)
const formData = ref({
  remark: '',
})

const formValidatorStatus = ref(null)
const rules = {
  remark: [{ required: true, message: t('insert.remark.placeholder'), type: 'error', trigger: 'blur' }],
};
const visible = ref(false);
const onVisibleChange = (val, context = {}) => {
  if (context && context.trigger === 'cancel') {
    visible.value = false;
    dialogVisible.value = true;
    formData.value.remark = node.attrs.remarkContent;
  }else if (context && context.trigger === 'confirm') {
    editor.value?.commands.deleteSelectionNode() // addAttributes() 的 vnode:
  } else {
    visible.value = val;
  }
};

const onClose = () => {
  dialogVisible.value = false;
}

const onOpen = () => {
    nextTick(() => {
        formValidatorStatus.value.clearValidate();
    })
}

const showPopup = (item) => {
  dialogVisible.value = true;
}

const onReset = (firstFormItem) => {
    formValidatorStatus.value.resetFields();
};

onMounted(() => {
    formValidatorStatus.value.validate();
});

const onSubmit = () => {
  const validateResult = formValidatorStatus.value.validate();
  validateResult.then(valid =>{
    if (valid == true){
      updateAttributes({ remarkContent: formData.value.remark})
      dialogVisible.value = false
    }
  })
}


</script>
<style lang="less" scoped>
.umo-node-remark{
  display: inline-block;
  .ghost-button{
    display: flex;
    align-items: center;
    justify-content: center;
  border-radius: 0;
  color:var(--umo-primary-color);
  border-bottom: 1px solid var(--umo-primary-color);
  &:hover{
    border-left: none;
    border-right: none;
    border-top: none;
  }

  .remark-icon{
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url('@/assets/icons/tips.svg') no-repeat;
    background-size: 100% 100%;
    margin-left: 4px;
  }
}
}



</style>