import {
  type CommandProps,
  mergeAttributes,
  Node,
  type NodeViewProps,
} from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import { Base64 } from 'js-base64'

import { RCBgUrl, RCMimeType, RCType } from '@/enum/extensions/RC'

import { RESOURCE_COVER } from '../page/node-names'
import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setResourceCover: {
      setResourceCover: (options: any) => ReturnType
    }
    setResourceCoverByLibrary: {
      setResourceCoverByLibrary: (options: any, file: any) => ReturnType
    }
  }
}
const { options: defaultOptions } = useStore()
export default Node.create({
  name: RESOURCE_COVER,
  inline: false,
  group: 'block',
  atom: true,
  addAttributes() {
    return {
      id: {
        default: null,
      },
      rcType: {
        default: RCType.TEACH,
      },
      bgSrc: {
        default:
          'https://dlchhc-b2b.oss-cn-beijing.aliyuncs.com/images/1692943187718.png',
      },
      url: {
        default: '',
      },
      orgUrl: {
        default: '',
      },
      title: {
        default: '我是标题',
      },
      file: {
        default: null,
      },
      bgColor: {
        default: '#ffffff',
      },
      readingContent: {
        default: '',
      },
      filename: {
        default: '',
      },
      size: {
        default: 0,
      },
    }
  },
  parseHTML() {
    return [{ tag: RESOURCE_COVER }]
  },
  renderHTML({ HTMLAttributes }) {
    return [
      RESOURCE_COVER,
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
    ]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      setResourceCover:
        (options: {
          id?: string
          rcType: RCType
          bgSrc?: string
          url?: string
          title?: string
          bgColor?: string
          file?: File
          readingContent?: string
          filename?: string
          size?: number
        }) =>
        ({ commands, editor }: CommandProps) => {
          let op = false

          options.bgSrc = RCBgUrl[options.rcType]
          if (!options.bgSrc) {
            options.bgSrc = RCBgUrl[RCType.TEACH]
          }
          if (
            options.rcType === RCType.GAME ||
            options.rcType === RCType.AR ||
            options.rcType === RCType.VR ||
            options.rcType === RCType.VIRTUAKL ||
            options.rcType === RCType.TRAINING
          ) {
            if (!options.url) {
              useMessage('error', t('rc.url'))
              return false
            }
            options.orgUrl = options.url
            if (options.rcType === RCType.AR || options.rcType === RCType.VR) {
              options.url = `${options.url}/?bgColor=${options.bgColor?.slice(1)}`
            }
            // 游戏,ar,vr不是文件，直接插入
            op = commands.insertContent({
              type: this.name,
              attrs: options,
            })
          } else if (options.rcType === RCType.READ) {
            op = commands.insertContent({
              type: this.name,
              attrs: options,
            })
          } else {
            options.url = '#'
            options.title = t('rc.file.uploading')
            // 上传文件
            const { open, onChange } = useFileDialog({
              accept: RCMimeType[options.rcType as RCType].join(','),
              reset: true,
              multiple: false,
            })
            open()
            onChange((fileList) => {
              const files = Array.from(fileList ?? [])
              if (!files) {
                return
              }
              for (const file of files) {
                let name = file.name
                let suffix = name.split('.')[1].toLowerCase()
                if (
                  RCMimeType[options.rcType as RCType].indexOf('.' + suffix) ===
                  -1
                ) {
                  useMessage('error', t(`rc.file.fileType`, { name, suffix }))
                  return false
                }
                options.file = file
              }
              const { size, name: filename } = options.file as File
              const { fileSize } = defaultOptions.value.resourceCover as any
              options.filename = filename
              options.size = size
              options.title = filename
              if (fileSize !== 0 && size > fileSize) {
                useMessage(
                  'error',
                  t(`rc.file.fileLimit`, { filename, fileSize }),
                )
                return false
              }
              op = editor.commands.insertContentAt(
                editor.state.selection.anchor,
                {
                  type: this.name,
                  attrs: options,
                },
              )
            })
          }

          return op
        },
      setResourceCoverByLibrary:
        (options: {
          id?: string
          rcType: RCType
          bgSrc?: string
          url?: string
          title?: string
          bgColor?: string
          file?: File
          readingContent?: string
          filename?: string
          size?: number
        }) =>
        ({ commands, editor }: CommandProps) => {
          options.bgSrc = RCBgUrl[options.rcType]
          if (!options.bgSrc) {
            options.bgSrc = RCBgUrl[RCType.TEACH]
          }

          if (!options.url) {
            useMessage('error', t('rc.url'))
            return false
          }

          options.orgUrl = options.url
          if (options.rcType === RCType.AR || options.rcType === RCType.VR) {
            options.url = `${options.url}/?bgColor=${options.bgColor?.slice(1)}`
          }

          const { fileSize } = defaultOptions.value.resourceCover as any

          if (
            options.rcType == RCType.TEACH ||
            options.rcType == RCType.MOEL_3D
          ) {
            options.url =
              import.meta.env.VITE_ONLINE_PREVIEW +
              encodeURIComponent(Base64.encode(options.url))
          }

          let filename = options.filename
          if (options.size && fileSize !== 0 && options.size > fileSize) {
            useMessage('error', t(`rc.file.fileLimit`, { filename, fileSize }))
          }
          editor.commands.insertContentAt(editor.state.selection.anchor, {
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
