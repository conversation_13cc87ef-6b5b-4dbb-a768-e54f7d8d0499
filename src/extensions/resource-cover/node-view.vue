<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view-rc" @mouseenter="showChild"
    @mouseleave="cannelHideLayer">
    <div class="rc-content">
      <!--  -->
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1">
              <div style="display: flex; align-items: center" @click="previewResource">
                <FileSearchIcon />
                <span style="margin-left: 5px">{{ t('rc.menu.preview') }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item v-if="
              node.attrs.rcType == RCType.TEACH ||
              node.attrs.rcType == RCType.MOEL_3D
            " :value="2">
              <div style="display: flex; align-items: center" @click="changeFile">
                <FileImportIcon />
                <span style="margin-left: 5px">{{
                  t('rc.menu.changeFile')
                }}</span>
              </div>
            </t-dropdown-item>

            <t-dropdown-item :value="4">
              <div style="display: flex; align-items: center" @click="setting">
                <SettingIcon />
                <span style="margin-left: 5px">{{ t('rc.menu.setting') }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="5">
              <div style="display: flex; align-items: center" @click="delRC">
                <DeleteIcon />
                <span style="margin-left: 5px">{{ t('rc.menu.delete') }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <!-- 3d -->
      <div v-if="node.attrs.rcType == '0'" class="rc-3d">
        <div class="text">
          {{ node.attrs.title }}
        </div>
      </div>
      <!-- AR/VR -->
      <div v-else-if="node.attrs.rcType == '1' || node.attrs.rcType == '2'" class="rc-av">
        <div class="text">
          {{ node.attrs.title }}
        </div>
      </div>

      <!-- 游戏 -->
      <div v-else-if="node.attrs.rcType == '4'" class="rc-game">
        <div class="text">
          {{ node.attrs.title }}
        </div>
      </div>
      <div v-else class="rc-main"
        :style="`background: url(${template?.orderTemplateBgUrl ?? node.attrs.bgSrc}) no-repeat center;width:100%;height:55px;background-size:100% 100%;`">
        <div class="rc-bg">
          <div class="rc-icon"
            :style="`background: url(${template?.theme === 'light' ? rcTypeName(node.attrs.rcType).lightIcon : rcTypeName(node.attrs.rcType).darkIcon}) no-repeat;background-size: cover;`">
          </div>
          <div class="rc-h1" :style="`color:${template?.theme === 'light' ? '#fff' : '#333'}`">
            {{ rcTypeName(node.attrs.rcType).type }}
          </div>
          <div class="rc-title" :style="`color:${template?.theme === 'light' ? '#fff' : '#333'}`">
            {{ node.attrs.title }}
          </div>
        </div>

        <!-- <div class="rc-btn-group">
          <div
            v-if="node.attrs.rcType === RCType.GAME"
            :style="`color:${template?.theme === 'light' ? '#333' : '#fff'}`"
            @click="previewResource"
          >
            {{ t('rc.runGame') }}
          </div>
          
        </div> -->
      </div>

      <!-- <button
            v-if="node.attrs.rcType === RCType.GAME"
            class="rc-btn"
            :class="template?.id == 1 ? 'blue-btn' : 'or-btn'"
            @click="previewResource"
          >
            {{ t('rc.runGame') }}
          </button> -->
    </div>

    <!-- 设置弹窗 -->
    <modal :visible="titleModelVisibel" :header="t('rc.form.header')"
      :width="node.attrs.rcType === RCType.READ ? '1000px' : '500px'" :footer="false" :close-on-overlay-click="false"
      @close="titleModelVisibel = false">
      <div>
        <t-form ref="settingFormRef" :data="setttingForm" label-width="60px" @submit="submit">
          <t-form-item :label="t('rc.form.title')" name="title" :rules="[
            {
              required: true,
              message: t('rc.form.titleRequired'),
              type: 'error',
            },
          ]">
            <t-input v-model="setttingForm.title" :placeholder="t('rc.form.titlePlaceholder')" :maxlength="20" />
          </t-form-item>
          <t-form-item v-if="
            node.attrs.rcType !== RCType.READ &&
            node.attrs.rcType !== RCType.MOEL_3D &&
            node.attrs.rcType !== RCType.TEACH
          " :label="t('rc.form.url')" name="url" :rules="[
            {
              required: true,
              message: t('rc.form.urlTip'),
              type: 'error',
            },
            {
              url: {
                protocols: ['http', 'https', 'ftp'],
                require_protocol: true,
              },
              message: t('rc.form.urlTip'),
            },
          ]">
            <t-input v-model="setttingForm.url" required-mark clearable :placeholder="t('rc.form.urlPlaceholder')" />
            <t-tooltip :content="admin_content" :show-arrow="false">
              <t-icon name="error-circle" size="16px" style="color: #0006; margin-left: 10px" />
            </t-tooltip>
          </t-form-item>
          <t-form-item v-if="
            node.attrs.rcType === RCType.VR || node.attrs.rcType === RCType.AR
          " :label="t('insert.ARandVR.color')" name="bgColor">
            <div style="
                width: 100%;
                height: 32px;
                border-radius: 3px;
                border: 1px solid #dcdcdc;
                cursor: pointer;
              " :style="{ backgroundColor: `${setttingForm.bgColor}` }" @click="dialogColorVisible = true"></div>
          </t-form-item>
          <t-form-item v-if="node.attrs.rcType === RCType.READ">
            <wangEditor v-if="titleModelVisibel" :text-content="setttingForm.readingContent" :height-props="500"
              @update-text-content="doUpdateItemContent" />
          </t-form-item>
          <div class="footer-btn-group">
            <t-button type="submit" theme="primary">确定</t-button>
          </div>
        </t-form>
      </div>
    </modal>

    <!-- 扩展阅读预览弹窗 -->
    <modal :visible="readModelVisibel" :header="node.attrs.title + ''" width="900px" :confirm-btn="null"
      :cancel-btn="null" @close="readModelVisibel = false">
      <div style="display: flex; justify-content: center; align-items: center">
        <div style="width: 800px; height: 500px" v-html="node.attrs.readingContent"></div>
      </div>
    </modal>

    <!-- 颜色选择弹窗 -->
    <modal :visible="dialogColorVisible" :header="undefined" width="300px" :confirm-btn="null" :cancel-btn="null"
      @close="dialogColorVisible = false">
      <color-picker :default-color="setttingForm.bgColor" @change="colorChange" />
    </modal>

    <!-- 3D，ARVR,游戏预览 -->
    <modal :visible="resourceShow" width="1100px" :header="node.attrs.title" :confirm-btn="null" :cancel-btn="null"
      @close="resourceShow = false">
      <div style="height: 650px; margin: 10px 0">
        <iframe :src="node.attrs.url" frameborder="0" width="100%" height="100%"></iframe>
      </div>
    </modal>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { Base64 } from 'js-base64'
import {
  DeleteIcon,
  EllipsisIcon,
  FileImportIcon,
  FileSearchIcon,
  SettingIcon,
} from 'tdesign-icons-vue-next'

import dask3d from '@/assets/resources/dark/3d.svg'
import daskAr from '@/assets/resources/dark/arvr.svg'
import daskGame from '@/assets/resources/dark/game.svg'
import daskTraining from '@/assets/resources/dark/practical.svg'
import daskRead from '@/assets/resources/dark/read.svg'
import daskTeach from '@/assets/resources/dark/teach.svg'
import daskSimulation from '@/assets/resources/dark/virtual.svg'
//3d
import light3d from '@/assets/resources/light/3d.svg'
import lightAr from '@/assets/resources/light/arvr.svg'
import lightGame from '@/assets/resources/light/game.svg'
import lightTraining from '@/assets/resources/light/practical.svg'
import lightRead from '@/assets/resources/light/read.svg'
import lightTeach from '@/assets/resources/light/teach.svg'
import lightSimulation from '@/assets/resources/light/virtual.svg'
import colorPicker from '@/components/color-picker.vue'
import wangEditor from '@/components/wangEditor/index.vue'
import { RCMimeType, RCType } from '@/enum/extensions/RC'
import { useMouseOver } from '@/hooks/mouseOver'
import { OssService } from '@/utils/aliOss'
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()
const admin_content = ref(import.meta.env.VITE_APP_ADMIN_CONTENT)
const { pageTemplateId } = useTemplate()
const { node, updateAttributes } = defineProps(nodeViewProps)
const { editor, options, loading } = useStore()
const titleModelVisibel = ref(false)
const readModelVisibel = ref(false)
const dialogColorVisible = ref(false)

const resourceShow = ref(false)
let template = $ref(null)
const setttingForm = ref({
  title: '',
  url: '',
  readingContent: '',
  bgColor: '#333',
})
const settingFormRef = ref()
const previewUrl = import.meta.env.VITE_ONLINE_PREVIEW

onMounted(() => {
  if (
    node.attrs.file &&
    (node.attrs.rcType == RCType.TEACH || node.attrs.rcType == RCType.MOEL_3D)
  ) {
    OssService(node.attrs.file)
      .then((res: any) => {
        updateAttributes({
          url: previewUrl + encodeURIComponent(Base64.encode(res.url)),
          file: null,
        })
      })
      .catch((error: any) => {
        useMessage('error', (error as Error).message)
      })
    setTimeout(() => {
      updateAttributes({
        file: null,
      })
    }, 200)
  }

  template = pageTemplateId.value
})

const rcTypeName = computed(() => {
  return function (name: number | string) {
    switch (name) {
      case 0:
        return {
          type: t('insert.threeDimensional.text'),
          lightIcon: light3d,
          darkIcon: dask3d,
        }
      case 1:
        return {
          type: t('insert.ARandVR.text'),
          lightIcon: lightAr,
          darkIcon: daskAr,
        }
      case 2:
        return {
          type: t('insert.ARandVR.text'),
          lightIcon: lightAr,
          darkIcon: daskAr,
        }
      case 3:
        return {
          type: t('insert.simulation.text'),
          lightIcon: lightSimulation,
          darkIcon: daskSimulation,
        }
      case 4:
        return {
          type: t('insert.games.text'),
          lightIcon: lightGame,
          darkIcon: daskGame,
        }
      case 5:
        return {
          type: t('insert.courseware.text'),
          lightIcon: lightTeach,
          darkIcon: daskTeach,
        }
      case 6:
        return {
          type: t('insert.expandReading'),
          lightIcon: lightRead,
          darkIcon: daskRead,
        }
      case 7:
        return {
          type: t('insert.training.text'),
          lightIcon: lightTraining,
          darkIcon: daskTraining,
        }
      default:
        return ''
    }
  }
})

watch(
  () => pageTemplateId.value,
  (val) => {
    template = val
  },
)

// 预览资源
function previewResource() {
  if (node.attrs.rcType == RCType.READ) {
    readModelVisibel.value = true
    return
  }

  if (!node.attrs.url || node.attrs.url === '#') {
    return useMessage('error', '文件正在上传或者文件上传错误')
  }

  if (
    node.attrs.rcType === RCType.AR ||
    node.attrs.rcType === RCType.VR ||
    node.attrs.rcType === RCType.MOEL_3D ||
    node.attrs.rcType === RCType.GAME
  ) {
    resourceShow.value = true
    return
  }

  window.open(node.attrs.url)
}

// 同步更新内容
const doUpdateItemContent = (cellTextContentVal: string) => {
  setttingForm.value.readingContent = cellTextContentVal
}

// 更换文件
function changeFile() {
  if (node.attrs.url === '#') {
    return useMessage('error', t(`rc.file.uploadChangeError`))
  }
  const { open, onChange } = useFileDialog({
    reset: true,
    multiple: false,
    accept: RCMimeType[node.attrs.rcType as RCType].join(','),
  })
  open()
  onChange(async (fileList) => {
    debugger
    const files = Array.from(fileList ?? [])
    let name = ''
    loading.value = true
    name = files[0].name
    if (!files) {
      return
    }
    for (const file of files) {
      const { name } = file
      const suffix = name.split('.')[1].toLowerCase()
      if (!RCMimeType[node.attrs.rcType as RCType].includes(`.${suffix}`)) {
        useMessage('error', t(`rc.file.fileType`, { name, suffix }))
        return false
      }
      const { size, name: filename } = file
      const { fileSize } = options.value.resourceCover as any
      if (fileSize !== 0 && size > fileSize) {
        useMessage('error', t(`rc.file.fileLimit`, { filename, fileSize }))
        return false
      }
      const res: any = await OssService(file)
      updateAttributes({
        url: previewUrl + encodeURIComponent(Base64.encode(res.url)),
        title: name || res.result.name.split('.')[0],
      })
      loading.value = false
    }
  })
}

// 打开设置
function setting() {
  let { url } = node.attrs
  if (node.attrs.rcType === RCType.AR || node.attrs.rcType === RCType.VR) {
    url = node.attrs.orgUrl
  }
  setttingForm.value = {
    title: node.attrs.title || node.attrs.filename,
    url,
    readingContent: node.attrs.readingContent,
    bgColor: node.attrs.bgColor,
  }
  titleModelVisibel.value = true
}

// 删除
function delRC() {
  editor.value?.chain().focus().deleteSelection().run()
}

// 保存设置
async function submit() {
  const valid = await settingFormRef.value.validate({
    showErrorMessage: true,
  })
  if (valid === true) {
    if (node.attrs.rcType === RCType.AR || node.attrs.rcType === RCType.VR) {
      const url = `${setttingForm.value.url}/?bgColor=${node.attrs.bgColor?.slice(1)}`
      updateAttributes({
        title: setttingForm.value.title,
        url,
        orgUrl: setttingForm.value.url,
        readingContent: setttingForm.value.readingContent,
        bgColor: setttingForm.value.bgColor,
      })
    } else {
      updateAttributes({
        title: setttingForm.value.title,
        url: setttingForm.value.url,
        readingContent: setttingForm.value.readingContent,
        bgColor: setttingForm.value.bgColor,
      })
    }

    titleModelVisibel.value = false
  }
}

// 更换颜色
const colorChange = (color: string) => {
  if (color.includes('rgb')) {
    setttingForm.value.bgColor = rgbToHex(color)
  } else {
    setttingForm.value.bgColor = color
  }
  dialogColorVisible.value = false
}

// rgb转hex
const rgbToHex = (rgb: string): string => {
  // 将输入的RGB字符串分割成数组
  rgb = rgb.slice(4)
  const rgbArray = rgb
    .slice(0, rgb.length - 1)
    .split(',')
    .map(Number)
  if (
    rgbArray.length !== 3 ||
    rgbArray.some((val) => isNaN(val) || val < 0 || val > 255)
  ) {
    return ''
  }
  // 将每个RGB值转换为十六进制并拼接
  return `#${rgbArray
    .map((val) => {
      const hex = val.toString(16)
      return hex.length === 1 ? `0${hex}` : hex
    })
    .join('')}`
}
</script>

<style lang="less" scoped>
.umo-node-view-rc {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .rc-content {
    position: relative;
    line-height: 30px;
    width: 100%;

    .rc-3d {
      position: relative;

      width: 100%;
      height: 392px;
      background-image: url('@/assets/resources/bg/3d-bg.png');
      background-size: cover;
      display: flex;
      justify-content: center;
      align-items: center;

      .text {
        font-size: 36px;
        line-height: 52px;
        color: #333;
        width: 70%;
        margin: -20px auto 0;
        text-align: center;
      }
    }

    .rc-av {
      position: relative;

      width: 100%;
      height: 392px;
      background-image: url('@/assets/resources/bg/vr-bg.png');
      background-size: cover;
      display: flex;
      justify-content: center;
      align-items: center;

      .text {
        font-size: 36px;
        line-height: 52px;
        color: #333;
        width: 70%;
        margin: -20px auto 0;
        text-align: center;
      }
    }

    .rc-game {
      position: relative;

      width: 100%;
      height: 392px;
      background-image: url('@/assets/resources/bg/game-bg.png');
      background-size: cover;
      display: flex;
      justify-content: center;
      align-items: center;

      .text {
        font-size: 36px;
        line-height: 52px;
        color: #333;
        width: 70%;
        margin: -20px auto 0;
        text-align: center;
      }
    }

    .icon {
      position: absolute;
      right: 10px;
      top: 14px;
      z-index: 99;
      font-size: 24px;
      cursor: pointer;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
      color: #fff;
      padding: 5px;
      width: 18px;
      height: 18px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .rc-main {
      .rc-bg {
        display: flex;
        align-items: center;
        height: 54px;
        padding: 0 20px;

        .rc-icon {
          width: 24px;
          height: 24px;
          background-color: #333;
          background-size: contain;
        }

        .rc-h1 {
          font-weight: 500;
          font-size: 16px;
          color: #333;

          margin: 0 10px;
        }

        .rc-title {
          color: #0966b4;
          font-size: 16px;
          white-space: nowrap;
          overflow: hidden;
          width: 330px;
          text-overflow: ellipsis;
        }

        .rc-btn-group {
          margin: 0 20px;
          flex: 1;
          justify-content: flex-end;
          display: flex;
          cursor: pointer;

          .rc-btn {
            border-radius: 57px;
            padding: 5px 20px;
            cursor: pointer;
            margin-top: 0px;
          }

          .blue-btn {
            background: linear-gradient(180deg, #fcfeff 0%, #9bd4ff 100%);
            box-shadow: 0px 4px 4px 0px rgba(5, 86, 154, 0.28);
            border: 1px solid rgba(9, 102, 180, 0.5);
            color: #0966b4;
          }

          .or-btn {
            background: linear-gradient(180deg, #ffcec9 0%, #ffa37c 100%);
            box-shadow: 0px 4px 4px 0px rgba(5, 86, 154, 0.28);
            border: 1px solid #ff955a;
            color: #fff;
          }
        }
      }
    }

    .rc-icon {
      // display: none;
    }
  }

  .rc-bottom {
    text-align: center;
    padding: 10px 0;

    .rc-title {
      color: black;
      outline: none;
    }
  }
}

.footer-btn-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

// .umo-node-view-rc:hover {
//   cursor: pointer;
//   .rc-icon {
//     display: block;
//   }
// }</style>
