import { type Editor, Extension, findParentNode } from '@tiptap/core'
import { type NodeSelection, Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'
import { Decoration, DecorationSet } from '@tiptap/pm/view'

import { LIST_TYPE } from '@/extensions/page/node-names'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setCurrentNodeSelection: {
      setCurrentNodeSelection: () => ReturnType
    }
    deleteSelectionNode: {
      deleteSelectionNode: () => ReturnType
    }
    deletePage: {
      deletePage: () => ReturnType
    }
  }
}
export default Extension.create({
  name: 'selection',
  addProseMirrorPlugins() {
    const { editor } = this

    return [
      new Plugin({
        key: new PluginKey('selection'),
        props: {
          decorations(state) {
            if (state.selection.empty) {
              return null
            }

            if (editor.isFocused) {
              return null
            }

            return DecorationSet.create(state.doc, [
              Decoration.inline(state.selection.from, state.selection.to, {
                class: 'umo-text-selection',
              }),
            ])
          },
        },
      }),
    ]
  },
  addCommands() {
    return {
      setCurrentNodeSelection:
        () =>
        ({ editor, chain }) => {
          const parentNode = findParentNode((node) =>
            LIST_TYPE.includes(node.type.name),
          )(editor.state.selection)
          if (parentNode) {
            return chain().setNodeSelection(parentNode.pos).run()
          }
          const { $anchor, node } = editor.state.selection as NodeSelection
          const pos = node?.attrs?.vnode
            ? $anchor.pos
            : $anchor.pos - $anchor.parentOffset - 1
          return chain().setNodeSelection(pos).run()
        },
      deleteSelectionNode:
        () =>
        ({ editor, chain }) => {
          const node = getSelectionNode(editor)
          if (!node) {
            return false
          }
          if (node.attrs.vnode) {
            if (
              editor.isActive('image') ||
              editor.isActive('video') ||
              editor.isActive('audio') ||
              editor.isActive('file')
            ) {
              const { options } = useStore()
              const { id, src } = node.attrs
              options.value.onFileDelete?.(id, src)
            }
            chain().focus().deleteSelection().run()
            return true
          }
          if (editor.isActive('table')) {
            chain().focus().deleteTable().run()
            return true
          }
          return chain().focus().deleteNode(node.type.name).run()
        },
      deletePage:
        () =>
        ({ editor, commands, chain, dispatch }) => {
          if (dispatch) {
            const node = getSelectionNode(editor)
            // 获取到选择节点后 记录id  去json 中删除  对象
            if (node) {
              const { id } = node.attrs
              if (id) {
                const json = editor.getJSON()
                const { content } = json
                console.log('~~~~~~~~~', content)
                if (content && content.length > 1) {
                  const pageNumber = findPageNumberById(content, id)
                  content.splice(pageNumber - 1, 1)
                  commands.setContent(json)
                } else {
                  const dialog = useAlert({
                    theme: 'danger',
                    header: t('page.deletePage.titleError'),
                    body: t('page.deletePage.messageError'),
                    onConfirm() {
                      dialog.destroy()
                    },
                  })
                  return false
                }
              }
            }
          }
          return true
        },
    }
  },
})

export function findPageNumberById(arr, targetId) {
  for (const page of arr) {
    if (page.type === 'page') {
      const { content } = page
      // 递归检查content中的元素
      if (!content) return false
      const found = checkContentForId(content, targetId)

      if (found) {
        return page.attrs.pageNumber
      }
    }
  }
  return null // 如果没有找到，返回null
}

function checkContentForId(content, targetId) {
  for (const item of content) {
    if (item.attrs && item.attrs.id === targetId) {
      return true
    }
    // 如果item有content属性，递归检查
    if (item.content && Array.isArray(item.content)) {
      if (checkContentForId(item.content, targetId)) {
        return true
      }
    }
  }
  return false
}

export function getSelectionNode(editor: Editor) {
  const { node } = editor.state.selection as NodeSelection

  if (node) {
    return node
  }

  const parentNode = findParentNode((node) =>
    LIST_TYPE.includes(node.type.name),
  )(editor.state.selection)
  const { $anchor } = editor.state.selection
  if (parentNode) {
    return $anchor.node(parentNode.depth)
  }
  editor.commands.selectParentNode()
  return (editor.state.selection as NodeSelection).node
}
export function getSelectionText(editor: Editor) {
  const { from, to, empty } = editor.state.selection
  if (empty) {
    return ''
  }
  return editor.state.doc.textBetween(from, to, '')
}
