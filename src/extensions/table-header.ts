import TableHeader from '@tiptap/extension-table-header'

export default TableHeader.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      align: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-align'),
        renderHTML: ({ align }) => ({ 'data-align': align }),
      },
      backgroundColor: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-bg-color'),
        renderHTML: ({ backgroundColor }) => {
          const attrs: { 'data-bg-color': any; style?: string } = {
            'data-bg-color': backgroundColor,
          }
          if (backgroundColor) {
            attrs.style = `background-color: ${backgroundColor}`
          }
          return attrs
        },
      },
      borderColor: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-bg-color'),
        renderHTML: ({ borderColor }) => {
          const attrs: { 'data-border-color': any; style?: string } = {
            'data-border-color': borderColor,
          }
          if (borderColor) {
            attrs.style = `border-color: ${borderColor}!important;border-width:3px;`
          }
          return attrs
        },
      },
      borderStyle: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-border-style'),
        renderHTML: ({ borderStyle }) => {
          const attrs: { 'data-border-style': any; style?: string } = {
            'data-border-style': borderStyle,
          }
          if (borderStyle) {
            attrs.style = `border: ${borderStyle};`
          }
          return attrs
        },
      },
    }
  },
})
