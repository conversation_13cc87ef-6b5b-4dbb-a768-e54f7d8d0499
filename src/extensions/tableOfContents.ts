import { Extension as ExtensionFactory } from '@tiptap/core'
import { v4 as generateUUID } from 'uuid'
import { Plugin as PMPlugin, <PERSON>lugin<PERSON><PERSON> as PMPlugin<PERSON><PERSON> } from '@tiptap/pm/state'

// 目录插件：自动为标题元素生成唯一ID并维护目录结构
const TableOfContentsPlugin = ({
  getId: customIdGenerator,
  anchorTypes: allowedNodeTypes = ['heading'],
}) =>
  new PMPlugin({
    key: new PMPluginKey('tableOfContent'),
    // 在事务追加阶段检查文档变化，自动补充缺失的目录ID
    appendTransaction(transactions, oldState, newState) {
      const transaction = newState.tr
      let isModified = false

      if (transactions.some((tr) => tr.docChanged)) {
        const existingIds = []

        // 遍历文档节点处理标题元素
        newState.doc.descendants((node, nodePos) => {
          const existingTocId = node.attrs['data-toc-id']

          if (
            allowedNodeTypes.includes(node.type.name) &&
            node.textContent.trim() !== ''
          ) {
            // 生成或修复目录ID
            if (!existingTocId || existingIds.includes(existingTocId)) {
              const newTocId = customIdGenerator
                ? customIdGenerator(node.textContent)
                : generateUUID()

              transaction.setNodeMarkup(nodePos, undefined, {
                ...node.attrs,
                'data-toc-id': newTocId,
                id: newTocId,
              })
              isModified = true
            }
            existingIds.push(existingTocId)
          }
        })
      }
      return isModified ? transaction : null
    },
  })

// 在指定层级中查找最后一个标题（用于层级计算）
const getLastHeadingOnLevel = (headings, targetLevel) => {
  let foundHeading = headings.filter((h) => h.level === targetLevel).pop()
  if (targetLevel !== 0) {
    return foundHeading || getLastHeadingOnLevel(headings, targetLevel - 1)
  }
}

// 计算当前标题的显示层级（考虑嵌套关系）
const calculateHeadingLevel = (currentHeading, existingHeadings) => {
  const lastHeading = existingHeadings.at(-1)
  let calculatedLevel = 1

  // 查找最近的上级标题
  const nearestParent = [...existingHeadings]
    .reverse()
    .find((h) => h.originalLevel <= currentHeading.node.attrs.level)

  const baseLevel = nearestParent?.level || 1

  // 根据层级关系确定当前级别
  if (currentHeading.node.attrs.level > (lastHeading?.originalLevel || 1)) {
    calculatedLevel = (lastHeading?.level || 1) + 1
  } else if (
    currentHeading.node.attrs.level < (lastHeading?.originalLevel || 1)
  ) {
    calculatedLevel = baseLevel
  } else {
    calculatedLevel = lastHeading?.level || 1
  }

  return calculatedLevel
}

// 生成线性递增的目录索引（1, 2, 3...）
const generateLinearIndex = (currentHeading, existingHeadings) => {
  const lastHeading = existingHeadings.at(-1)
  return lastHeading ? (lastHeading.itemIndex || 1) + 1 : 1
}

// 生成层级化索引（1.1, 1.2...）
const generateHierarchicalIndex = (
  currentHeading,
  existingHeadings,
  calculatedLevel,
) => {
  const targetLevel = calculatedLevel || currentHeading.node.attrs.level
  const relevantHeadings = existingHeadings.filter(
    (h) => h.level <= targetLevel,
  )

  const lastSameLevel = relevantHeadings.at(-1)
  return lastSameLevel?.level === targetLevel
    ? (lastSameLevel.itemIndex || 1) + 1
    : 1
}

// 防抖函数优化性能
const debounce = (callback, delay) => {
  let timeoutId
  return (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => callback(...args), delay)
  }
}

// 计算高度偏移量
const calculateHeightOffset = (domNode) => {
  if (!domNode) return 0;
  let offsetTop = domNode.offsetTop
  // console.log('domNode',domNode, domNode.offsetParent)
  while(!domNode?.offsetParent?.classList.contains('ProseMirror') && domNode.offsetParent) {
    // console.log('domNode',domNode, domNode.offsetParent)
    domNode = domNode.offsetParent
    offsetTop += domNode.offsetTop
  }
  return offsetTop || 0;
}

// 更新目录项激活状态（根据滚动位置）
const updateActiveStates = (tocItems, context) => {
  const { editor, anchorTypes, storage, onUpdate } = context
  const activeIds = []
  let currentActiveId = null

  if (editor.isDestroyed) return tocItems

  // 检测当前滚动位置对应的激活项
  editor.state.doc.descendants((node, pos) => {
    if (anchorTypes.includes(node.type.name) && !node.attrs.isNotAsCatalog) {
      const domNodePos = editor.view.domAtPos(pos + 1)
      const domNode =domNodePos.node
      // const domNodeP = domNode.parentElement
      let offsetTop = calculateHeightOffset(domNode)
      // console.log('domNode',domNodePos, storage.scrollPosition >= offsetTop, storage.scrollPosition , offsetTop)
      
      if (storage.scrollPosition >= offsetTop) {
        currentActiveId = node.attrs['data-toc-id']
        // console.log('---------------currentActiveId', currentActiveId)
        activeIds.push(currentActiveId)
      }
    }
  })

  // 更新激活状态
  const updatedItems = tocItems.map((item) => ({
    ...item,
    isActive: item.id === currentActiveId,
    isScrolledOver: activeIds.includes(item.id),
  }))

  if (onUpdate) {
    const isInitial = storage.content.length === 0
    onUpdate(updatedItems, isInitial)
  }

  return updatedItems
}

// 核心目录生成逻辑
const generateTableOfContents = (context) => {
  const { editor, storage, onUpdate, getIndexFn, getLevelFn, anchorTypes } =
    context
  if (editor.isDestroyed) return

  const headings = []
  let tocItems = []
  const domNodes = []

  // 收集所有符合条件的标题节点
  editor.state.doc.descendants((node, pos) => {
    if (anchorTypes.includes(node.type.name) && !node.attrs.isNotAsCatalog) {
      headings.push({ node, pos })
    }
  })

  headings.forEach((heading, index) => {
    if (heading.node.textContent.trim() === '') return

    const domNode = editor.view.domAtPos(heading.pos + 1).node
    domNodes.push(domNode)

    // 计算层级和索引
    const originalLevel = heading.node.attrs.level
    const previousHeading = headings[index - 1]

    const calculatedLevel = getLevelFn(heading, tocItems)
    const itemIndex = getIndexFn(heading, tocItems, calculatedLevel)

    // 构建目录项数据结构
    const tocEntry = {
      itemIndex,
      id: heading.node.attrs['data-toc-id'],
      originalLevel,
      level: calculatedLevel,
      textContent: heading.node.textContent,
      pos: heading.pos,
      editor,
      isActive: false,
      isScrolledOver: storage.scrollPosition >= domNode.offsetTop,
      node: heading.node,
      dom: domNode,
    }

    tocItems = previousHeading ? [...tocItems, tocEntry] : [tocEntry] // 处理第一个元素
  })

  // 更新存储并触发回调
  tocItems = updateActiveStates(tocItems, context)
  storage.anchors = domNodes
  storage.content = tocItems

  const tr = editor.state.tr.setMeta('toc', tocItems)
  editor.view.dispatch(tr)
}

// 主扩展定义
const TableOfContents = ExtensionFactory.create({
  name: 'tableOfContents',

  // 扩展存储结构
  addStorage: () => ({
    content: [], // 目录项列表
    anchors: [], // 对应的DOM节点
    scrollHandler: null, // 滚动事件处理器
    scrollPosition: 0, // 当前滚动位置
  }),

  // 为标题元素添加ID属性
  addGlobalAttributes() {
    return [
      {
        types: this.options.anchorTypes || ['heading'],
        attributes: {
          id: {
            default: null,
            renderHTML: (attrs) => ({ id: attrs.id }),
            parseHTML: (element) => element.id || null,
          },
          'data-toc-id': {
            default: null,
            renderHTML: (attrs) => ({ 'data-toc-id': attrs['data-toc-id'] }),
            parseHTML: (element) => element.dataset.tocId || null,
          },
        },
      },
    ]
  },

  // 扩展配置项
  addOptions: () => ({
    onUpdate: () => {}, // 目录更新回调
    getId: (content) => generateUUID(), // ID生成策略
    scrollParent: typeof window !== 'undefined' ? () => window : undefined, // 滚动容器
    anchorTypes: ['heading'], // 支持的标题类型
    getLevel: calculateHeadingLevel, // 层级计算方法
    getIndex: generateLinearIndex, // 索引生成方法
  }),

  // 注册编辑器命令
  addCommands() {
    return {
      updateTableOfContents:
        () =>
        ({ dispatch }) => {
          if (dispatch) {
            generateTableOfContents({
              editor: this.editor,
              storage: this.storage,
              onUpdate: this.options.onUpdate?.bind(this),
              getIndexFn: this.options.getIndex || generateLinearIndex,
              getLevelFn: this.options.getLevel || calculateHeadingLevel,
              anchorTypes: this.options.anchorTypes,
            })
          }
          return true
        },
    }
  },

  // 事务监听（文档变化时更新目录）
  onTransaction({ transaction }) {
    if (transaction.docChanged && !transaction.getMeta('toc')) {
      generateTableOfContents({
        editor: this.editor,
        storage: this.storage,
        onUpdate: this.options.onUpdate?.bind(this),
        getIndexFn: this.options.getIndex || generateLinearIndex,
        getLevelFn: this.options.getLevel || calculateHeadingLevel,
        anchorTypes: this.options.anchorTypes,
      })
    }
  },

  // 初始化逻辑
  onCreate() {
    // 初始化DOM节点ID
    const { tr } = this.editor.state
    const existingIds = []

    this.editor.state.doc.descendants((node, pos) => {
      if (
        this.options.anchorTypes.includes(node.type.name) &&
        node.textContent.trim() !== ''
      ) {
        const existingId = node.attrs['data-toc-id']
        if (!existingId || existingIds.includes(existingId)) {
          const newId = this.options.getId
            ? this.options.getId(node.textContent)
            : generateUUID()

          tr.setNodeMarkup(pos, undefined, {
            ...node.attrs,
            'data-toc-id': newId,
            id: newId,
          })
        }
        existingIds.push(existingId)
      }
    })

    this.editor.view.dispatch(tr)

    // 初始化目录数据
    generateTableOfContents({
      editor: this.editor,
      storage: this.storage,
      onUpdate: this.options.onUpdate?.bind(this),
      getIndexFn: this.options.getIndex || generateLinearIndex,
      getLevelFn: this.options.getLevel || calculateHeadingLevel,
      anchorTypes: this.options.anchorTypes,
    })

    // 设置滚动监听
    this.storage.scrollHandler = debounce(() => {
      const scrollContainer =
        typeof this.options.scrollParent === 'function'
          ? this.options.scrollParent()
          : this.options.scrollParent

      const scrollPosition =
        scrollContainer instanceof HTMLElement
          ? scrollContainer.scrollTop
          : scrollContainer?.scrollY || 0

      this.storage.scrollPosition = scrollPosition + 400

      this.storage.content = updateActiveStates(this.storage.content, {
        editor: this.editor,
        anchorTypes: this.options.anchorTypes,
        storage: this.storage,
        onUpdate: this.options.onUpdate?.bind(this),
      })
    }, 100)

    // 注册滚动事件
    const scrollContainer =
      typeof this.options.scrollParent === 'function'
        ? this.options.scrollParent()
        : this.options.scrollParent

    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', this.storage.scrollHandler)
    }
  },

  // 清理逻辑
  onDestroy() {
    const scrollContainer =
      typeof this.options.scrollParent === 'function'
        ? this.options.scrollParent()
        : this.options.scrollParent

    if (scrollContainer) {
      scrollContainer.removeEventListener('scroll', this.storage.scrollHandler)
    }
  },

  // 注册ProseMirror插件
  addProseMirrorPlugins() {
    return [
      TableOfContentsPlugin({
        getId: this.options.getId,
        anchorTypes: this.options.anchorTypes,
      }),
    ]
  },
})

export {
  TableOfContents as default,
  TableOfContentsPlugin,
  debounce,
  calculateHeadingLevel as getHeadlineLevel,
  generateHierarchicalIndex as getHierarchicalIndexes,
  getLastHeadingOnLevel,
  generateLinearIndex as getLinearIndexes,
}
