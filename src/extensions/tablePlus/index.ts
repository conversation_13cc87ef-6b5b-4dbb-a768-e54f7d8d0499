import { type CommandProps, mergeAttributes, Node } from '@tiptap/core'
import { createTable } from '@tiptap/extension-table'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import { shortId } from '@/utils/short-id'

import { TABLE_PLUS } from '../page/node-names'
import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setTablePlus: {
      setTablePlus: (options: any) => ReturnType
    }
    updateTableTemplate: {
      updateTableTemplate: (options: any) => ReturnType
    }
  }
}
const { options: defaultOptions } = useStore()
export default Node.create({
  name: TABLE_PLUS,
  group: 'block',
  content: 'table',
  addAttributes() {
    return {
      id: {
        default: shortId(),
      },
      name: {
        default: '表格模板',
      },
      isShowTitle: {
        default: true,
      },
      isTitlePosition: {
        default: false,
      },
      hanlin: {
        default: 'center',
      },
      templateId: {
        default: '',
      },
      isShowNo: {
        default: 0,
      },
      number: {
        default: '',
      },
      tableTitleFamily: {
        default: 'SimSun',
      },
      tableTitleSize: {
        default: '18',
      },
      tableTitleColor: {
        default: '#333',
      },
    }
  },
  parseHTML() {
    return [{ tag: TABLE_PLUS }]
  },
  renderHTML({ HTMLAttributes }) {
    return [TABLE_PLUS, mergeAttributes(HTMLAttributes), 0]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView)
  },
  addCommands() {
    return {
      setTablePlus:
        ({
          rows = 3,
          cols = 3,
          withHeaderRow = true,
          name = '表格模板',
          isShowTitle = true,
          isTitlePosition,
          json,
          templateId,
        } = {}) =>
        ({ editor, tr, dispatch, commands }: CommandProps) => {
          if (dispatch) {
            let tableNode: any
            if (json) {
              tableNode = editor.schema.nodeFromJSON(JSON.parse(json))
            } else {
              tableNode = createTable(editor.schema, rows, cols, withHeaderRow)
            }
            tr.replaceRangeWith(
              editor.state.selection.anchor,
              editor.state.selection.anchor,
              editor.schema.node(
                this.name,
                {
                  name,
                  isShowTitle,
                  isTitlePosition,
                  templateId,
                },
                tableNode,
              ),
            )
          }
          return true
        },
      updateTableTemplate:
        (json: string) =>
        ({ tr, dispatch, commands }) => {
          if (dispatch) {
            const newAttrs = JSON.parse(json)
            let headNewAttrs: Record<string, any> = []
            const colNewAttrs: Record<string, any> = []
            for (let i = 0; i < newAttrs.content.length; i++) {
              const row = newAttrs.content[i]
              if (row.content[0].type === 'tableHeader') {
                headNewAttrs = row.content
              } else {
                colNewAttrs.push(row.content)
              }
            }
            const headLen = headNewAttrs.length
            const colLen = colNewAttrs.length
            // console.log('newAttrs', newAttrs, headNewAttrs, colNewAttrs)
            tr.selection.ranges.forEach((range) => {
              const from = range.$from.pos
              const to = range.$to.pos
              tr.doc.nodesBetween(from, to, (node, pos) => {
                if ('table' === node.type.name) {
                  let rowPos = pos + 1
                  let hasHeader = false
                  for (let i = 0; i < node.childCount; i++) {
                    const row = node.child(i)
                    let cellPos = rowPos + 1
                    for (let j = 0; j < row.childCount; j++) {
                      const cell = row.child(j)
                      if (i == 0 && cell.type.name === 'tableHeader') {
                        hasHeader = true
                        // console.log('headLen % j', headLen, j, j % headLen)
                        // console.log(
                        //   '===>',
                        //   i,
                        //   j,
                        //   headNewAttrs[j % headLen].attrs,
                        // )
                        // console.log(
                        //   'cell',
                        //   tr.doc.nodeAt(cellPos),
                        //   tr.doc.nodeAt(cellPos + 1),
                        // )
                        tr.setNodeMarkup(cellPos, undefined, {
                          ...headNewAttrs[j % headLen].attrs,
                        })
                      } else {
                        const colAtts =
                          colNewAttrs[(hasHeader ? i + 1 : i) % colLen][
                            j % colNewAttrs[i % colLen].length
                          ].attrs
                        // console.log(
                        //   'col===>',
                        //   i,
                        //   j,
                        //   (hasHeader ? i + 1 : i) % colLen,
                        //   j % colNewAttrs[(hasHeader ? i + 1 : i) % colLen].length,
                        //   colAtts,
                        // )
                        tr.setNodeMarkup(cellPos, undefined, {
                          ...colAtts,
                        })
                      }
                      cellPos += cell.nodeSize
                    }
                    rowPos += row.nodeSize
                  }

                  return false
                }
              })
            })
            // console.log('tr', tr)
          }
          return true
        },
    }
  },
  addKeyboardShortcuts() {
    return {
      Tab: () => this.editor.chain().focus().goToNextCell().run(),
      'Shift-Tab': () => this.editor.chain().focus().goToPreviousCell().run(),
    }
  },
})
