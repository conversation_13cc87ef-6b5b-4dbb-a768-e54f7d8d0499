<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" :style="nodeStyle" :template-id="node.attrs.templateId"
    @mouseenter="showChild" @mouseleave="cannelHideLayer">
    <div class="umo-hover-shadow umo-select-outline umo-node-tablePlus">
      <div style="padding: 10px 0" class="umo-node-title" contenteditable="false"
        :style="{ textAlign: node.attrs.hanlin }">
        <text v-if="node.attrs.isShowNo === 1 && !node.attrs.isTitlePosition"
          :style="`margin-right: 20px;font-size:${node.attrs.tableTitleSize}px;font-family:${node.attrs.tableTitleFamily};color:${node.attrs.tableTitleColor}`">{{
            node.attrs.number }}</text>
        <text v-if="node.attrs.isShowTitle && !node.attrs.isTitlePosition"
          :style="`margin-right: 20px;font-size:${node.attrs.tableTitleSize}px;font-family:${node.attrs.tableTitleFamily};color:${node.attrs.tableTitleColor}`">{{
            node.attrs.name || '表格标题'
          }}</text>
      </div>
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon style="color: 24px" />
          <template #dropdown>
            <t-dropdown-item :value="1" @click="openDialogEdit">
              <div style="display: flex; align-items: center">
                <SettingIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.setting')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2" @click="delTablePlus">
              <div style="display: flex; align-items: center">
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.remove')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <node-view-content />

      <div v-if="node.attrs.isShowTitle && node.attrs.isTitlePosition" class="umo-node-title" contenteditable="false"
        style="padding: 10px 0" :style="{ textAlign: node.attrs.hanlin }">
        <text v-if="node.attrs.isShowNo === 1 && node.attrs.isTitlePosition"
          :style="`margin-right: 20px;font-size:${node.attrs.tableTitleSize}px;font-family:${node.attrs.tableTitleFamily};color:${node.attrs.tableTitleColor}`">{{
            node.attrs.number
          }}</text>
        <text v-if="node.attrs.isShowTitle && node.attrs.isTitlePosition"
          :style="`margin-right: 20px;font-size:${node.attrs.tableTitleSize}px;font-family:${node.attrs.tableTitleFamily};color:${node.attrs.tableTitleColor}`">{{
            node.attrs.name || '表格标题'
          }}</text>
      </div>
    </div>
  </node-view-wrapper>
  <t-dialog v-model:visible="galleryEditPopup" attach="body" :header="t('insert.image.setting')" width="950px"
    :close-on-overlay-click="false" @confirm="onSubmit" @close="onClose" @cancel="onClose">
    <div>
      <t-form ref="formValidatorStatus" :data="formData" :label-width="120" label-align="top">
        <!-- 图片标题-->
        <t-form-item :label="t('table.modalTitle')" name="imageTitle">
          <t-input v-model="formData.imageTitle" :placeholder="t('table.modalPlaceholder')"
            :status="formDataError.imageTitle ? 'default' : 'error'"></t-input>
        </t-form-item>
        <!-- 表格标题样式编辑 -->
        <div class="table-title-style">
          <div class="table-title-style-title">{{ t('table.templateTable.titleStyle') }}</div>
          <div class="table-title-bg">
            <div class="table-title-item">
              <t-form-item :label="t('table.templateTable.titleFamily')" name="tableTitleFamily">
                <t-select v-model="formData.tableTitleFamily"
                  :placeholder="t('table.templateTable.titleFamilyPlaceholder')" style="width: 200px;"
                  @change="fontFamilyChange">
                  <t-option-group v-for="(group, index) in allFonts" :key="index" :label="group.label" :divider="false">
                    <t-option v-for="item in group.children" :key="item.value ?? ''" class="umo-font-family-item"
                      :value="item.value ?? ''" :label="l(item.label)">
                      <span :style="{ fontFamily: item.value ?? 'SimSun' }" v-text="l(item.label)"></span>
                    </t-option>
                  </t-option-group>
                </t-select>
              </t-form-item>
            </div>
            <div class="table-title-item">
              <t-form-item :label="t('table.templateTable.titleSize')" name="tableTitleSize">
                <t-input-number v-model="formData.tableTitleSize" :min="12" :max="34" :step="1"
                  :placeholder="t('table.templateTable.tableTitleSizeplaceholder')" />
              </t-form-item>
            </div>
            <div class="table-title-item">
              <t-form-item :label="t('table.templateTable.titleColor')" name="tableTitleColor">
                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                  <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                    <div
                      :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.tableTitleColor};margin-right:10px;`"
                      :class="`${formData.tableTitleColor == 'transparent' ? 'transparent' : ''}`"></div>
                    <div class="umo-color-picker-more-menu"
                      :style="`border-bottom: 3px solid ${formData.tableTitleColor};}`">
                      <span v-text="t('table.templateTable.titleColor')"></span>
                    </div>
                  </div>
                  <template #content>
                    <div style="padding: 10px">
                      <color-picker :default-color="defaultColor" @change="titleColorChange" />
                    </div>
                  </template>
                </t-popup>
              </t-form-item>
            </div>
          </div>
        </div>



        <!-- 标题显示位置 -->
        <div class="table-title-style">
          <div class="table-title-style-title">{{ t('table.templateTable.titleOther') }}</div>
          <div class="table-title-bg-postion">
            <div class="table-title-postion-item">
              <t-form-item :label="t('table.titlePosition')" name="isTitlePosition">
                <t-radio-group v-model="formData.isTitlePosition">
                  <t-radio :value="false" allow-uncheck>{{
                    t('table.templateTable.titleTop')
                  }}</t-radio>
                  <t-radio :value="true">{{ t('table.templateTable.titleBottom') }}</t-radio>
                </t-radio-group>
              </t-form-item>
            </div>
            <div class="table-title-postion-item">
              <!-- 标题横向位置显示 -->
              <t-form-item :label="t('table.titleHanlin')" name="titleHanlin">
                <t-radio-group v-model="formData.titleHanlin" default-value="2">
                  <t-radio value="left">{{ t('table.left') }}</t-radio>
                  <t-radio value="center">{{ t('table.center') }}</t-radio>
                  <t-radio value="right">{{ t('table.right') }}</t-radio>
                </t-radio-group>
              </t-form-item>
            </div>

            <div class="table-title-postion-item">
              <!-- 是否显示表号-->
              <t-form-item :label="t('table.isShowTableNo')" name="isShowNo">
                <t-radio-group v-model="formData.isShowNo">
                  <t-radio :value="1" allow-uncheck>{{
                    t('insert.image.yes')
                  }}</t-radio>
                  <t-radio :value="0">{{ t('insert.image.no') }}</t-radio>
                </t-radio-group>
              </t-form-item>
            </div>

            <div class="table-title-postion-item">
              <t-form-item :label="t('table.isShowTableTitle')" name="isShowGalleryTitle">
                <t-radio-group v-model="formData.isShowTitle">
                  <t-radio :value="true" allow-uncheck>{{
                    t('insert.image.yes')
                  }}</t-radio>
                  <t-radio :value="false">{{ t('insert.image.no') }}</t-radio>
                </t-radio-group>
              </t-form-item>
            </div>
          </div>

        </div>




        <!-- 链接地址-->

        <t-form-item :label="t('table.templateTable.selectTemplate')">
          <div class="table-template-list">
            <div v-for="(item, index) in dataList" :key="item.templateId" class="table-template-item">
              <div class="mask">
                <div class="table-template-item-img-button">
                  <t-button style="width: 120px" @click="() => onUse(item)">{{
                    t('table.templateTable.use')
                  }}</t-button>
                </div>
              </div>

              <div class="table-template-item-img">
                <div class="tdesign-demo-image-viewer__ui-image">
                  <img alt="test" :src="item.coverUrl" class="tdesign-demo-image-viewer__ui-image--img" />
                </div>
                <div class="table-template-item-info">
                  <div class="table-template-item-info-name">
                    <i class="icon"></i>
                    {{ item.templateName }}
                    <t-tag v-if="formData.templateId == item.templateId" theme="success"
                      style="margin-left: 10px">已使用</t-tag>
                  </div>
                </div>
              </div>
              <!--  -->
            </div>
          </div>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { NodeViewContent, nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { DeleteIcon, EllipsisIcon, SettingIcon } from 'tdesign-icons-vue-next'

import { getTemplateList } from '@/api/table/table'
import { useMouseOver } from '@/hooks/mouseOver'

const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, getCaptionStyle } = useStore()
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()

const containerRef = ref<HTMLElement | null>(null)
let selected = $ref(false)

const dataList = ref([])
const getList = () => {
  getTemplateList().then((res) => {
    dataList.value = res.data
  })
}
const nodeStyle = $computed(() => {
  const { nodeAlign, margin } = node.attrs
  const marginTop =
    margin?.top && margin?.top !== '' ? `${margin.top}px` : undefined
  const marginBottom =
    margin?.bottom && margin?.bottom !== '' ? `${margin.bottom}px` : undefined
  return {
    'justify-content': nodeAlign,
    marginTop,
    marginBottom,
  }
})

let galleryEditPopup = $ref(false)
let tempateTable = $ref(null)
const onUse = (item) => {
  tempateTable = item.templateJson
  formData.templateId = item.templateId
  MessagePlugin.success('使用成功')
}

const formData = reactive({
  imageTitle: '',
  linkAddress: '',
  isShowTitle: true, //显示 true   隐藏:false
  isShowNo: 0, //1 显示  0 隐藏
  isTitlePosition: false, //上:false   下:true
  templateId: '',
  titleHanlin: 'center',
  tableTitleFamily: '',
  tableTitleSize: '18',
  tableTitleColor: '',
})
const formDataError = reactive({
  imageTitle: true,
  linkAddress: true,
})
const openDialogEdit = () => {
  getList()
  tempateTable = null
  galleryEditPopup = true
  formData.imageTitle = node.attrs.name
  formData.linkAddress = node.attrs.src
  formData.isShowTitle = node.attrs.isShowTitle
  formData.isShowNo = Number(node.attrs.isShowNo)
  formData.isTitlePosition = node.attrs.isTitlePosition
  formData.templateId = node.attrs.templateId
  formData.titleHanlin = node.attrs.hanlin
  formData.tableTitleFamily = node.attrs.tableTitleFamily
  formData.tableTitleSize = node.attrs.tableTitleSize
  formData.tableTitleColor = node.attrs.tableTitleColor
}

const delTablePlus = () => {
  deleteNode()
}
const onClose = () => {
  galleryEditPopup = false
}
const onSubmit = () => {
  if (!formData.imageTitle) {
    formDataError.imageTitle = false
    return
  }
  formDataError.imageTitle = true

  if (tempateTable) {
    editor.value?.chain().focus().updateTableTemplate(tempateTable).run()
  }
  console.log('formData', formData)
  updateAttributes({
    name: formData.imageTitle,
    isShowTitle: formData.isShowTitle,
    isShowNo: Number(formData.isShowNo),
    isTitlePosition: formData.isTitlePosition,
    templateId: formData.templateId,
    hanlin: formData.titleHanlin,
    tableTitleFamily: formData.tableTitleFamily,
    tableTitleSize: formData.tableTitleSize,
    tableTitleColor: formData.tableTitleColor,
  })
  onClose()
}

onMounted(() => {
  if (node.attrs.number == '') {
    getCaptionStyle().then((res) => {
      if (res.tableNumberType == 1) {
        updateAttributes({
          isShowNo: 1,
          number: '表1',
        })
      } else if (res.tableNumberType == 2) {
        updateAttributes({
          isShowNo: 1,
          number: '表1-1',
        })
      } else {
        updateAttributes({
          isShowNo: 0,
        })
      }
    })
  }
})

onClickOutside(containerRef, () => {
  selected = false
})



// 表格标题颜色字号字体添加
const defaultColor = ref('transparent')
const titleColorChange = (value) => {
  formData.tableTitleColor = value
}

const usedFonts = $ref<string[]>([])
const $recent = useState('recent')
const allFonts = computed(() => {
  const all = [
    {
      label: t('base.fontFamily.all'),
      children: options.value.dicts?.fonts ?? [],
    },
  ]
  // 通过字体值获取字体列表
  const getFontsByValues = (values: string[]) => {
    return values.map(
      (item) =>
        options.value.dicts?.fonts.find(
          ({ value }: { value: string }) => value === item,
        ) ?? {
          label: item,
          item,
        },
    )
  }
  if ($recent.value.fonts.length > 0) {
    all.unshift({
      label: t('base.fontFamily.recent'),
      children: getFontsByValues($recent.value.fonts) as any,
    })
  }
  if (usedFonts.length > 0) {
    all.unshift({
      label: t('base.fontFamily.used'),
      children: getFontsByValues(usedFonts) as any,
    })
  }
  return all
})

const fontFamilyChange = (e) => {
  formData.tableTitleFamily = e
}


</script>

<style lang="less" scoped>
.umo-node-tablePlus {
  position: relative;
  border-radius: var(--umo-radius);
  outline: solid 1px var(--umo-border-color);
  padding: 10px;

  .icon {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .umo-node-title {
    text-align: center;
    padding: 10px 0 0;
  }
}

.table-template-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin: 20px 0;

  .table-template-item {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    border-radius: 4px;
    background-color: #fff;
    padding: 18px;
    position: relative;
    cursor: pointer;
    z-index: 1;

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      opacity: 0;
      transition: all 0.3s;
      z-index: 99;

      .table-template-item-img-button {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);

        border-radius: 4px;
      }
    }

    &:hover {
      .mask {
        opacity: 1;
      }
    }

    .table-template-item-info {
      padding-top: 20px;

      .table-template-item-info-name {
        display: flex;
        align-items: center;
        color: #333;

        .icon {
          display: inline-flex;
          width: 16px;
          height: 16px;
          background-image: url('@/assets/images/tableTemplateIcon.svg');
          background-size: contain;
          margin-right: 5px;
        }
      }
    }

    .tdesign-demo-image-viewer__ui-image {
      width: 172px;
      height: 142px;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
      }

      .table-template-item-img-mask {
        position: absolute;
        top: 5px;
        right: 0px;
        width: 30px;
        height: 30px;
        cursor: pointer;
      }

      .table-template-item-img-button {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);

        border-radius: 4px;

        span {
          width: 160px;
        }
      }
    }
  }
}


.table-title-style {
  border: 1px solid #e5e5e5;
  position: relative;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;

  .table-title-style-title {
    position: absolute;
    top: -10px;
    left: 20px;
    background-color: #fff;
    font-weight: bold;
    font-size: 16px;
  }

  .table-title-bg {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;

    .table-title-item {}
  }

  .table-title-bg-postion {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;

    .table-title-postion-item {}
  }

}
</style>
