import { mergeAttributes, Node, type NodeViewProps } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    papers: {
      papers: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'papers',
  group: 'block',
  atom: true,
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      width: {
        default: null,
      },
      height: {
        default: 200,
      },
      paperInfo: {
        default: null,
      },
      isExpand: {
        default: false, // 阅读器默认不展开
      },
      type: {
        default: 'paper',
      },
    }
  }, // 添加属性
  parseHTML() {
    return [
      {
        tag: 'papers',
      },
    ]
  },
  renderHTML({ HTMLAttributes }) {
    return ['papers', mergeAttributes(HTMLAttributes)]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      papers:
        (options) =>
        ({ commands, editor }) => {
          return commands.insertContentAt(editor.state.selection.anchor, {
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
