import { Mark } from '@tiptap/core'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setTextBorder: {
      setTextBorder: (options: any) => ReturnType
    }
  }
}

export default Mark.create({
  name: 'textBorder',

  addOptions() {
    return {
      HTMLAttributes: {},
    }
  },
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      textBorder: {
        default: {},
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'span',
        getAttrs: (node) => {
          // 检查是否有 border 样式
          const style = node.getAttribute('style')
          const textBorderMatch = style
            ? style.match(/border:\s*([^;]+)/)
            : null
          return textBorderMatch ? { textBorder: textBorderMatch[1] } : null
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const { textBorder } = HTMLAttributes
    if (
      !textBorder &&
      !textBorder.borderWidth &&
      !textBorder.borderStyle &&
      !textBorder.borderColor
    )
      return null as any
    return [
      'span',
      {
        style: `border:${textBorder.borderWidth} ${textBorder.borderStyle} ${textBorder.borderColor}`,
      },
      0,
    ]
  },

  addCommands() {
    return {
      // 设置文字边框
      setTextBorder:
        (options: any) =>
        ({ commands }) => {
          return commands.setMark(this.name, { textBorder: options })
        },
    }
  },
})
