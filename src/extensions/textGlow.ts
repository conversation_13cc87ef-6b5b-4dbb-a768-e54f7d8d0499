import { Mark, mergeAttributes } from '@tiptap/core';

/**
 * textGlow mark extension to apply text-shadow CSS style to the selected text.
 */
export default Mark.create({
  name: 'textGlow',

  addOptions() {
    return {
      HTMLAttributes: {}, // 默认的 HTML 属性
    };
  },

  // 解析 HTML 中的 text-shadow 样式
  parseHTML() {
    return [
      {
        tag: 'span', // 使用 span 标签
        getAttrs: (node) => {
          // 检查是否有 text-shadow 样式
          const style = node.getAttribute('style');
          const textGlowMatch = style ? style.match(/text-shadow:\s*([^;]+)/) : null;
          return textGlowMatch ? { textGlow: textGlowMatch[1] } : null;
        },
      },
    ];
  },

  // 渲染 HTML 输出文本，并应用 text-shadow 样式
  renderHTML({ HTMLAttributes }) {
    const { textGlow, ...restAttributes } = HTMLAttributes;

    // 打印用于调试的 HTMLAttributes

    // 如果 textGlow 存在，应用它到 style 中
    const style = textGlow ? `text-shadow: ${textGlow};` : '';

    // 合并其他属性到 style 中
    const existingStyle = restAttributes.style || ''; // 获取已有的样式（如果有的话）
    const mergedStyle = existingStyle ? `${existingStyle} ${style}` : style;

    // 打印用于调试的 mergedStyle// 打印合并后的样式

    // 返回带有样式的 span 标签
    //TODO HTMLAttributes取不到testShadow 暂时先用写死的样式 后续测试再考虑是否更改 dongyujian
    return [
      'span',
      {style:"text-shadow:0 0 5px rgba(255, 255, 0, 0.8)," +
          "0 0 10px rgba(255, 255, 0, 0.8)," +
          "0 0 20px rgba(255, 255, 0, 0.8)," +
          "0 0 30px rgba(255, 255, 0, 1)," +
          "0 0 40px rgba(255, 255, 0, 1)," +
          "0 0 50px rgba(255, 255, 0, 1);"},
      0, // 这个 `0` 表示子节点的位置
    ];
  },

  // 定义命令
  addCommands() {
    return {
      // 设置 text-shadow 样式
      setTextGlow: () => ({ commands }) => {
        return commands.setMark(this.name);
      },
      // 切换 text-shadow 样式
      toggleTextGlow: () => ({ commands }) => {
        return commands.toggleMark(this.name);
      },
      // 移除 text-shadow 样式
      unsetTextGlow: () => ({ commands }) => {
        return commands.unsetMark(this.name);
      },
    };
  },

});


