import { Mark, mergeAttributes } from '@tiptap/core';

/**
 * TextShadow mark extension to apply text-shadow CSS style to the selected text.
 */
export default Mark.create({
  name: 'textShadow',

  addOptions() {
    return {
      HTMLAttributes: {}, // 默认的 HTML 属性
    };
  },

  // 解析 HTML 中的 text-shadow 样式
  parseHTML() {
    return [
      {
        tag: 'span', // 使用 span 标签
        getAttrs: (node) => {
          // 检查是否有 text-shadow 样式
          const style = node.getAttribute('style');
          const textShadowMatch = style ? style.match(/text-shadow:\s*([^;]+)/) : null;
          return textShadowMatch ? { textShadow: textShadowMatch[1] } : null;
        },
      },
    ];
  },

  // 渲染 HTML 输出文本，并应用 text-shadow 样式
  renderHTML({ HTMLAttributes }) {
    const { textShadow, ...restAttributes } = HTMLAttributes;

    // 打印用于调试的 HTMLAttributes
   
    // 如果 textShadow 存在，应用它到 style 中
    const style = textShadow ? `text-shadow: ${textShadow};` : '';

    // 合并其他属性到 style 中
    const existingStyle = restAttributes.style || ''; // 获取已有的样式（如果有的话）
    const mergedStyle = existingStyle ? `${existingStyle} ${style}` : style;

    // 打印用于调试的 mergedStyle
    

    // 返回带有样式的 span 标签
    //TODO HTMLAttributes取不到testShadow 暂时先用写死的样式 后续测试再考虑是否更改 dongyujian
    return [
      'span',
      {style:"text-shadow:2px 2px 2px"},
      0, // 这个 `0` 表示子节点的位置
    ];
  },

  // 定义命令
  addCommands() {
    return {
      // 设置 text-shadow 样式
      setTextShadow: () => ({ commands }) => {
        return commands.setMark(this.name);
      },
      // 切换 text-shadow 样式
      toggleTextShadow: () => ({ commands }) => {
        return commands.toggleMark(this.name);
      },
      // 移除 text-shadow 样式
      unsetTextShadow: () => ({ commands }) => {
        return commands.unsetMark(this.name);
      },
    };
  },

});


