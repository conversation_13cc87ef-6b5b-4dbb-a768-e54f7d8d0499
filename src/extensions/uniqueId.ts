// 导入所需模块和工具函数
import {
  Extension as Extension,
  findChildren as findChildren,
  combineTransactionSteps as combineTransactionSteps,
  getChangedRang<PERSON> as getChangedRanges,
  findChildrenInRange as findChildrenInRange,
} from '@tiptap/core'
import { Slice as Slice, Fragment as Fragment } from '@tiptap/pm/model'
import {
  Plugin as ProseMirrorPlugin,
  PluginKey as PluginKey,
} from '@tiptap/pm/state'
import { v4 as generateUUID } from 'uuid'

/**
 * 数组去重工具函数
 * @param {Array} arr - 需要去重的数组
 * @returns {Array} 去重后的数组
 */
function deduplicate(t) {
  //   return (function (t, e = JSON.stringify) {
  //     const r = {}
  //     return t.filter((t) => {
  //       const n = e(t)
  //       return !Object.prototype.hasOwnProperty.call(r, n) && (r[n] = !0)
  //     })
  //   })(t.filter((e, r) => t.indexOf(e) !== r))
  //   console.log('----t---->', t)
  // 第一步：过滤出所有重复出现的元素（保留非首次出现的元素）
  const duplicateElements = t.filter((item, index) => t.indexOf(item) !== index)

  // 第二步：对重复元素进行深比较去重
  const seen = new Map()
  let tt = duplicateElements.filter((item) => {
    const key = JSON.stringify(item)
    return seen.has(key) ? false : (seen.set(key, true), true)
  })
  //   console.log('----tt---->', tt)
  return tt
}

// 创建唯一ID扩展
const UniqueIDExtension = Extension.create({
  name: 'uniqueID',
  priority: 10000, // 设置高优先级确保在其他扩展之后运行

  // 扩展配置选项
  addOptions() {
    return {
      attributeName: 'id', // 存储ID的属性名
      types: [], // 需要添加ID的节点类型
      generateID: generateUUID, // ID生成函数
      filterTransaction: null, // 自定义事务过滤器
    }
  },

  // 添加全局属性
  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          [this.options.attributeName]: {
            default: null,
            // 从HTML数据属性解析ID
            parseHTML: (element) =>
              element.getAttribute(`data-${this.options.attributeName}`),
            // 将ID渲染到HTML数据属性
            renderHTML: (attributes) =>
              attributes[this.options.attributeName]
                ? {
                    [`data-${this.options.attributeName}`]:
                      attributes[this.options.attributeName],
                  }
                : {},
          },
        },
      },
    ]
  },

  // 创建扩展时的初始化逻辑
  onCreate() {
    // 如果存在协同编辑扩展则跳过初始化
    if (
      this.editor.extensionManager.extensions.find(
        (ext) => ext.name === 'collaboration',
      )
    )
      return

    const { view: editorView, state: editorState } = this.editor
    const { tr: transaction, doc: document } = editorState
    const {
      types: nodeTypes,
      attributeName: idAttr,
      generateID: idGenerator,
    } = this.options

    // 遍历文档，为已有节点生成ID
    findChildren(
      document,
      (node) =>
        nodeTypes.includes(node.type.name) && node.attrs[idAttr] === null,
    ).forEach(({ node, pos }) => {
      // console.log('----id---->', node, pos)
      transaction.setNodeMarkup(pos, undefined, {
        ...node.attrs,
        [idAttr]: idGenerator(),
      })
    })

    transaction.setMeta('addToHistory', false)
    editorView.dispatch(transaction)
  },

  // 添加ProseMirror插件
  addProseMirrorPlugins() {
    let dragSourceParent = null // 拖拽源父元素
    let isPasteOrDrop = false // 标记是否粘贴/拖拽操作

    return [
      new ProseMirrorPlugin({
        key: new PluginKey('uniqueID'),

        // 处理事务追加逻辑
        appendTransaction: (transactions, oldState, newState) => {
          const hasChanges =
            transactions.some((tr) => tr.docChanged) &&
            !oldState.doc.eq(newState.doc)
          const shouldFilter = this.options.filterTransaction?.some?.(
            (tr) => !this.options.filterTransaction(tr),
          )

          // 如果是协同编辑事务则跳过
          const isCollaboration = transactions.find((tr) =>
            tr.getMeta('y-sync$'),
          )
          if (isCollaboration) return

          if (!hasChanges || shouldFilter) return

          const { tr: transaction } = newState
          const {
            types: nodeTypes,
            attributeName: idAttr,
            generateID: idGenerator,
          } = this.options
          const combinedSteps = combineTransactionSteps(
            oldState.doc,
            transactions,
          )
          const { mapping } = combinedSteps

          // 处理变更范围内的节点
          getChangedRanges(combinedSteps).forEach(({ newRange }) => {
            const nodesInRange = findChildrenInRange(
              newState.doc,
              newRange,
              (node) => nodeTypes.includes(node.type.name),
            )

            // console.log('----nodesInRange---->', nodesInRange)
            const existingIDs = nodesInRange
              .map(({ node }) => node.attrs[idAttr])
              .filter((id) => id !== null)
            // const existingIDNodes = nodesInRange
            //   .map(({ node }) => ({
            //     id: node.attrs[idAttr],
            //     // type: node.type.name,
            //   }))
            //   .filter((id) => id !== null)
            nodesInRange.forEach(({ node, pos }, index) => {
              const currentNode = transaction.doc.nodeAt(pos)
              const currentID = currentNode?.attrs[idAttr]

              // 生成新节点ID
              if (currentID === null) {
                return transaction.setNodeMarkup(pos, undefined, {
                  ...node.attrs,
                  [idAttr]: idGenerator(),
                })
              }

              const nextNode = nodesInRange[index + 1]

              // 处理空节点合并情况 没懂为什么要有这个判断，id为什么要继承
              //   if (nextNode && node.content.size === 0) {
              //     console.log('----nextNode---->', nextNode, node)
              //     transaction.setNodeMarkup(nextNode.pos, undefined, {
              //       ...nextNode.node.attrs,
              //       [idAttr]: currentID,
              //     })
              //     existingIDs[index + 1] = currentID
              //     return
              //   }

              // 检查ID冲突
              const uniqueIDs = deduplicate(existingIDs)
              const { deleted } = mapping.invert().mapResult(pos)

              if (deleted && uniqueIDs.includes(currentID)) {
                // console.log(
                //   '----deleted---->',
                //   existingIDs,
                //   currentID,
                //   existingIDNodes,
                //   node,
                // )
                transaction.setNodeMarkup(pos, undefined, {
                  ...node.attrs,
                  [idAttr]: idGenerator(),
                })
              }
            })
          })

          return transaction.steps.length ? transaction : undefined
        },

        // 处理拖拽开始事件
        view(editorView) {
          const handleDragStart = (event) => {
            dragSourceParent = editorView.dom.parentElement.contains(
              event.target,
            )
              ? editorView.dom.parentElement
              : null
          }

          window.addEventListener('dragstart', handleDragStart)
          return {
            destroy() {
              window.removeEventListener('dragstart', handleDragStart)
            },
          }
        },

        // 处理DOM事件
        props: {
          handleDOMEvents: {
            drop: (view, event) => {
              // 标记非复制操作
              const isMoveOperation = !['copyMove', 'copy'].includes(
                event.dataTransfer?.effectAllowed,
              )
              if (
                dragSourceParent === view.dom.parentElement &&
                isMoveOperation
              ) {
                isPasteOrDrop = true
              }
              dragSourceParent = null
              return false
            },
            paste: () => {
              isPasteOrDrop = true
              return false
            },
          },

          // 处理粘贴内容
          transformPasted: (slice) => {
            if (!isPasteOrDrop) return slice

            const { types: nodeTypes, attributeName: idAttr } = this.options

            // 递归处理节点内容
            const processContent = (content) => {
              const nodes = []
              content.forEach((node) => {
                if (node.isText) {
                  nodes.push(node)
                  return
                }

                if (!nodeTypes.includes(node.type.name)) {
                  nodes.push(node.copy(processContent(node.content)))
                  return
                }

                // 重置粘贴节点的ID
                const newNode = node.type.create(
                  { ...node.attrs, [idAttr]: null },
                  processContent(node.content),
                  node.marks,
                )
                nodes.push(newNode)
              })
              return Fragment.from(nodes)
            }

            isPasteOrDrop = false
            return new Slice(
              processContent(slice.content),
              slice.openStart,
              slice.openEnd,
            )
          },
        },
      }),
    ]
  },
})

export { UniqueIDExtension as UniqueID, UniqueIDExtension as default }
