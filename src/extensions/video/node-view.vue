<template>
  <node-view-wrapper :id="node.attrs.id" class="umo-node-view" @mouseenter="showChild" @mouseleave="cannelHideLayer">
    <div v-show="mousemove" class="icon" @click.stop="keepOpen">
      <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
        <EllipsisIcon />
        <template #dropdown>
          <t-dropdown-item :value="1">
            <div style="display: flex; align-items: center" @click="videoEdit">
              <SettingIcon />
              <span style="margin-left: 5px">{{
                t('insert.image.setting')
              }}</span>
            </div>
          </t-dropdown-item>
          <t-dropdown-item :value="2">
            <div style="display: flex; align-items: center" @click="handleDelNode">
              <DeleteIcon />
              <span style="margin-left: 5px">{{
                t('insert.image.remove')
              }}</span>
            </div>
          </t-dropdown-item>
        </template>
      </t-dropdown>
    </div>

    <div class="video-head-bg"
      :style="`background: url(${template?.orderTemplateBgUrl ?? node.attrs.bgSrc}) no-repeat center;height:55px;background-size:cover`">
      <div class="video-head-left">
        <div class="video-head-icon">
          <img :src="template?.theme == 'dark' ? videoIconDrak : videoIcon" class="icon-file" />
        </div>
        <div class="video-head-title" :style="`color:${template?.theme == 'dark' ? '#333' : '#fff'}`">视频</div>

        <div class="video-head-name" :style="`color:${template?.theme == 'dark' ? '#333' : '#fff'}`">
          <t-tooltip :content="node.attrs.videoTitle" :show-arrow="false">
            {{ node.attrs.videoTitle }}
          </t-tooltip>
        </div>
      </div>
    </div>

    <div ref="containerRef" class="umo-node-container umo-node-video" :style="nodeStyle">
      <drager :rotatable="false" :boundary="false" :width="Number(node.attrs.width)" :height="Number(node.attrs.height)"
        :min-width="300" :min-height="200" :max-width="maxWidth" :max-height="maxHeight" :equal-proportion="false"
        @resize="onResize">
        <video ref="videoRef" :src="node.attrs.src" preload="metadata" controls crossorigin="anonymous"
          controlslist="nodownload" @loadedmetadata="loadedmetadata" @canplay="onLoad"></video>
      </drager>
    </div>

  </node-view-wrapper>

  <t-dialog v-model:visible="videoEditPopup" attach="body" :header="t('insert.image.setting')" width="20%"
    :close-on-overlay-click="false" @confirm="onSubmit" @close="onClose" @cancel="onClose">
    <div>
      <t-form ref="formValidatorStatus" :data="formData" :label-width="120">
        <!-- 视频标题-->
        <t-form-item :label="t('insert.fileDialog.videoName')" name="videoTitle">
          <t-input v-model="formData.videoTitle" :placeholder="t('insert.fileDialog.videoTip')"></t-input>
        </t-form-item>
        <!-- 是否展开收起 -->
        <t-form-item :label="t('insert.fileDialog.isExpand')" name="isExpand">
          <t-switch v-model="formData.isExpand"></t-switch>
        </t-form-item>
        <!-- 链接地址-->
        <t-form-item :label="t('insert.fileDialog.videoBtn')">
          <t-button @click="changeFile">{{
            t('insert.fileDialog.videoBtn')
          }}</t-button>
        </t-form-item>
        <t-form-item :label="t('insert.fileDialog.fromLibrary')">
          <t-button @click="library">{{
            t('insert.fileDialog.fromLibrary')
          }}</t-button>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
  <ResourceLibrary v-model:visible="show" :file-type="'3'" @insert-by-resource="insertByResource" />
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import Drager from 'es-drager'
import { DeleteIcon, EllipsisIcon, SettingIcon } from 'tdesign-icons-vue-next'

import videoIconDrak from '@/assets/resources/dark/video.svg'
import videoIcon from '@/assets/resources/light/video.svg'
import { useMouseOver } from '@/hooks/mouseOver'
import { chooseFile } from '@/utils/file'
import { mediaPlayer } from '@/utils/player'
const show = ref(false)
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { pageTemplateId } = useTemplate()
const template = pageTemplateId.value
const { options, editor } = useStore()
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()

const containerRef = ref(null)
const videoRef = $ref<HTMLVideoElement | null>(null)
let player = $ref<Plyr | null>(null)
let maxWidth = $ref(0)
let maxHeight = $ref(0)
// 编辑弹窗标识
const videoEditPopup = ref(false)
// 表单数据
const formData = ref({})

const nodeStyle = $computed(() => {
  const { nodeAlign, margin } = node.attrs
  const marginTop =
    margin?.top && margin?.top !== '' ? `${margin.top}px` : undefined
  const marginBottom =
    margin?.bottom && margin?.bottom !== '' ? `${margin.bottom}px` : undefined
  return {
    'justify-content': nodeAlign,
    marginTop,
    marginBottom,
  }
})

onMounted(async () => {
  await nextTick()
  player = mediaPlayer(videoRef)
})

const changeFile = () => {
  chooseFile(
    (file) => {
      formData.value = {
        src: file.fileUrl,
        size: file.fileSize,
        videoTitle: file.originName,
        name: file.originName,
        isExpand: formData.value.isExpand,
      }
    }, {
    multiple: false,
    fileType: '.mp4',
    fileSize: 400,
  }
  )
}


// 从资源库选择图片
const library = () => {
  show.value = true
}
const insertByResource = (file) => {
  formData.value = {
    src: file.fileUrl,
    size: file.fileSize,
    videoTitle: file.name,
    name: file.name,
    isExpand: formData.value.isExpand,

  }
}

const handleDelNode = () => {
  deleteNode()
}

// 关闭dialog
const onClose = () => {
  videoEditPopup.value = false
}

const loadedmetadata = () => {
  updateAttributes({
    duration: parseInt(player?.duration),
  })
}

const videoEdit = () => {
  videoEditPopup.value = true
  formData.value = {
    videoTitle: node.attrs.videoTitle,
    src: node.attrs.src,
    size: node.attrs.size,
    name: node.attrs.name,
    isExpand: node.attrs.isExpand,
  }
}

const onSubmit = () => {
  if (!formData.value.videoTitle) {
    return MessagePlugin.error('视频名不能为空')
  }
  updateAttributes({
    ...formData.value,
  })
  videoEditPopup.value = false
}




const onLoad = () => {
  // const VideoWidth = videoRef.clientWidth
  // const VideoHeight = videoRef.clientHeight
  // console.log('VideoWidth', VideoWidth, 'VideoHeight', VideoHeight)
  const { clientWidth = 0, clientHeight = 0 } = videoRef ?? {}
  maxWidth = containerRef.value?.clientWidth ?? 0
  const ratio = clientWidth / clientHeight
  maxHeight = containerRef.value?.clientWidth / ratio
  // console.log('maxWidth', maxWidth, 'maxHeight', maxHeight, 'clientWidth', clientWidth, 'clientHeight', clientHeight)
  if (node.attrs.width === null) {
    updateAttributes({
      width: (400 * ratio).toFixed(2),
      height: containerRef.value?.clientHeight,
    })
  }
}
const onResize = ({ width, height }: { width: number; height: number }) => {
  updateAttributes({ width, height })
}

onBeforeUnmount(() => {
  if (player) {
    player?.destroy()
  }
})
</script>

<style lang="less" scoped>
.umo-node-view {
  max-width: 100%;
  max-height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  .umo-node-container {
    max-height: 100%;
    display: flex;
  }

  .icon {
    position: absolute;
    right: 10px;
    top: 14px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .video-head-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    padding: 0 20px;

    .video-head-left {
      display: flex;

      align-items: center;

      .video-head-icon {
        width: 24px;
        height: 24px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .video-head-title {
        padding: 0 10px;
        font-size: 16px;
        font-weight: 400;
      }

      .video-head-name {
        color: #333;
        width: 450px;
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .video-head-right {
      color: #666;
      font-size: 16px;
    }
  }

  .umo-node-video {
    max-width: 100%;
    pointer-events: none;
    border-radius: var(--umo-radius);

    ::v-deep(.es-drager) {
      max-width: 100%;
      max-height: 100%;

      .es-drager-dot {
        pointer-events: auto;
      }

      .plyr {
        max-height: 100%;
      }

      video {
        display: block;
        border-radius: var(--umo-radius);
        overflow: hidden;
        pointer-events: auto;
        outline: none;
        max-width: 100%;
        max-height: 100%;
        width: 100%;
        height: 100%;
      }
    }

    ::v-deep(.plyr) {
      pointer-events: auto;
    }
  }
}
</style>
