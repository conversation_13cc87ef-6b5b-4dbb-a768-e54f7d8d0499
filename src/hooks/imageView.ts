export const useImageView = () => {
  const imgUrl = ref('')
  const visibleImg = ref(false)
  onMounted(() => {
    console.log('onMounted 逻辑只执行一次')
    const imgElements = document.querySelectorAll('.data-item img')
    console.log('imgElements', imgElements)

    // 先移除所有旧事件监听器，避免重复绑定
    imgElements.forEach((img) => {
      img.removeEventListener('click', handleClick)
    })

    // 再绑定新的事件监听器
    imgElements.forEach((img) => {
      img.addEventListener('click', () => handleClick(img.src))
    })

    window.addEventListener('beforeunload', () => {
      // 关闭标签页时清除标记（避免影响下次打开页面）
      sessionStorage.removeItem('v')
    })

    // 清理函数（防止内存泄漏）
    onBeforeUnmount(() => {
      imgElements.forEach((img) => {
        img.removeEventListener('click', handleClick)
        sessionStorage.removeItem('v')
      })
    })
  })

  const handleClick = (src) => {
    console.log('handleClick', src)
    const getValue = sessionStorage.getItem('v')
    if (getValue != '1') {
      sessionStorage.setItem('v', '1')
      imgUrl.value = src
      visibleImg.value = true
    }
  }

  const closeViewer = () => {
    sessionStorage.removeItem('v')
    visibleImg.value = false
  }

  return {
    imgUrl,
    visibleImg,
    closeViewer,
  }
}
