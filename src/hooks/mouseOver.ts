export const useMouseOver = () => {
  let mousemove = ref(false)
  let hideTimer = null
  let isMenuActive = ref(false)

  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
  })
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })

  const showChild = () => {
    mousemove.value = true
    clearTimeout(hideTimer)
  }
  const keepOpen = () => {
    isMenuActive.value = true
    clearTimeout(hideTimer)
  }
  const handleClickOutside = (e) => {
    if (!e.target.closest('.top-node-mu')) {
      mousemove.value = false
      isMenuActive.value = false
    }
  }
  const cannelHideLayer = () => {
    hideTimer = setTimeout(() => {
      if (!isMenuActive.value) {
        mousemove.value = false
      }
    }, 200)
  }

  return {
    cannelHideLayer,
    showChild,
    mousemove,
    keepOpen,
  }
}
