{"welcome": "%cThanks for using Dutp Editor%cCurrent version: v{version}, More info: {homepage}", "version": {"text": "Update log"}, "toolbar": {"ribbon": "Professional", "classic": "Classic", "toggle": "<PERSON><PERSON><PERSON>", "show": "Show Toolbar", "hide": "<PERSON><PERSON>", "source": "Edit Source Code", "base": "Home", "insert": "Resource Components", "table": "Table", "tools": "Expansion Tools", "page": "Page Settings", "export": "Export File"}, "themeStyle": {"text": "themeStyle", "title": "title", "headerTip": "The unit is uniformly in px (pixels)", "titleLevel": {"one": "1级", "two": "2级", "three": "3级", "four": "4级", "five": "5级", "six": "6级"}, "family": "family", "size": "size", "color": "color", "bp": "BP", "beforeParagraph": "before Paragraph", "ap": "AP", "AfterParagraph": "After Paragraph", "alignment": {"text": "Alignment", "left": "left", "center": "center", "right": "right", "justify": "justify"}, "indent": {"text": "Indent", "yes": "yes", "no": "no"}, "lineHeight": {"text": "Line Height", "stext": "LH"}, "italic": {"text": "italic", "yes": "yes", "no": "no"}, "underline": {"text": "underline", "stext": "UL", "yes": "yes", "no": "no"}, "mainBody": "Main Body", "spacing": "Spacing", "chapterHead": "Chapter Head", "sectionHeader": "Section Header"}, "base": {"insertParagraph": "Insert Paragraph", "punctuation": {"leftBracket": "[", "rightBracket": "]"}, "align": {"title": "Alignment", "left": "Left", "center": "Center", "right": "Right", "justify": "Justify", "distributed": "Distributed", "copy": "Copy", "paste": "Paste", "cut": "Cut"}, "float": {"left": "left", "right": "right", "cancel": "cancel"}, "add": "Add", "bgColor": "<PERSON> Pattern", "bold": "Bold", "fontBorder": {"text": "Font Border", "BorderThickness": "Border thickness", "BorderDash": "Border style", "BorderColor": "Border color", "BorderBtn": "submit", "solid": "solid", "dashed": "dashed", "dotted": "dotted"}, "clearFormat": "Clear Text Formatting", "code": "Code", "color": "Font Color", "fontFamily": {"text": "Select Font", "all": "Common Fonts", "recent": "Recently Used", "used": "Used Fonts", "default": "<PERSON><PERSON><PERSON>", "unsupport": "This font may not display properly, possibly due to it not being installed on this device"}, "fontSize": {"text": "Font Size", "increase": "Increase Font Size", "decrease": "Decrease Font Size", "default": "<PERSON><PERSON><PERSON>", "42pt": "First", "36pt": "Sub-first", "26pt": "First Level", "24pt": "Sub-first Level", "22pt": "Second Level", "18pt": "Sub-second Level", "16pt": "Third Level", "15pt": "Sub-third Level", "14pt": "Fourth Level", "12pt": "Sub-fourth Level", "10_5pt": "Fifth Level", "9pt": "Sub-fifth Level", "7_5pt": "Sub-sixth Level", "13_5px": "18px(<PERSON><PERSON><PERSON>)"}, "formatPainter": {"text": "Format Painter", "tip": "Format Painter: Double click to reuse, press ESC to exit"}, "heading": {"text": "Title {level}", "paragraph": "Normal", "tip": "Set heading, can generate document map based on headings"}, "highlight": {"text": "Highlight", "clear": "Clear Highlight", "yellowBg": "Yellow Background", "purpleBg": "Purple Background", "greenBg": "Green Background", "blueBg": "Blue Background", "red": "Red Text", "green": "Green Text"}, "textEffect": {"text": "Text Effect", "default": "No Effect", "outline": "Text Outline", "shadow": "Text Shadow", "glow": "Text Glow"}, "letterSpacing": {"text": "Letter Spacing", "0px": "0", "1px": "1", "2px": "2", "3px": "3", "4px": "4", "5px": "5"}, "correct": "Correct", "incorrect": "Incorrect", "indent": "Increase Indent", "italic": "Italic", "lineHeight": {"text": "Line Height", "default": " (<PERSON><PERSON><PERSON>)"}, "outdent": "Decrease Indent", "margin": {"text": "Vertical Spacing", "top": "Top", "bottom": "Bottom", "default": "<PERSON><PERSON><PERSON>", "reset": "Auto Spacing"}, "importWord": {"text": "Import Word", "loadScript": {"title": "Error Message", "message": "Loading the transformed script, please try again later."}, "limitSize": "File size cannot exceed 5MB.", "converting": "Converting Word document, please wait...", "convertError": "Error parsing Word document", "importError": "Error importing Word document"}, "importPDF": {"text": "Import PDF"}, "markdown": {"text": "<PERSON><PERSON>", "enable": "Enable Markdown", "disable": "Disable Markdown", "message": "This action will reset the editor, and after switching, current history will be lost, unable to undo, etc. Confirm now?", "toggle": "Switch Now"}, "columnLayout": {"text": "Column", "oneColumn": "One Column", "twoColumn": "Two Columns", "threeColumn": "Three Columns"}, "quote": "Quote", "redo": "Redo", "selectAll": "Select All", "layoutColumn": {"text": "Column Layout", "twoColumn": "Two Column", "threeColumn": "Three Columns", "tips": "The total width of the columns is 100", "left": "left width", "center": "center width", "right": "right width", "borderOpen": "Open Border", "borderColor": "Border Color", "borderWidth": "Border Width", "borderStyle": "Border Style", "padding": "Padding", "linkage": "Padding Linkage", "openlinkage": "open Linkage", "closelinkage": "close Linkage", "topPadding": "top", "bottomPadding": "bottom", "leftPadding": "left", "rightPadding": "right", "layoutColumnMargin": "Layout Column <PERSON>"}, "strike": "Strikethrough", "subscript": "Subscript", "superscript": "Superscript", "underline": "Underline", "undo": "Undo", "backgroundImage": "Background Image", "backgroundMove": "Background Move", "backgroundBorder": {"text": "Background Border", "borderStyle": "Border Style", "borderWidth": "Border Width", "borderColor": "Border Color", "borderRadius": "Border Radius", "submit": "submit", "padding": "Padding", "borderWidthError": "The border thickness cannot be 0"}, "containerColor": "Background Color", "backgroundOption": "Background Options", "backgroundOptions": {"remove": "Remove Background", "fill": "Fill Background", "adapt": "Adapt Background", "stretch": "Stretch Background", "tile": "Tile Background", "widthStretching": "Width Stretching", "heightStretching": "Height Stretching"}}, "fileTypeList": {"img": "images", "video": "video", "audio": "audio", "threeD": "3D Model", "AR": "AR/VR", "tech": "Technology", "simulation": "Simulation"}, "ai": {"knowledgeGraph": "knowledge Graph", "intelligentProofreading": "Intelligent Proofreading", "intelligentInfo": {"adopt": "<PERSON><PERSON><PERSON>", "adoptInfo": "Found a total of {number} errors", "again": "Again", "errorType": "Error Type", "original": "Original", "suggested": "Suggested", "title": "Smart Matching", "title2": "AI makes life more efficient and better"}, "matchCase": "Match Case", "matchCaseInfo": {"historicalRecords": "historical records", "inputPlaceholder": "Please enter the content you want to search", "inputSearch": "Please enter a topic, up to a maximum of 10000 words", "generate": "Generate", "generateResults": "Generate Results", "export": "export", "copy": "copy", "smartMatching": "Smart Matching", "ad": "AI makes life more efficient and better"}, "generateBrainMaps": "Generate Brain Maps", "generateLearningObjectives": "Generate Learning Objectives", "generateLearningObjectivesInfo": {"title": "Generate Learning Objectives", "ad": "AI makes life more efficient and better"}, "generateSummary": "Generate Summary", "generateSummaryInfo": {"title": "Generate Summary", "ad": "AI makes life more efficient and better"}, "generateOutline": "Generate Outline", "generateOptions": {"title": "Generate outline", "ad": "AI makes life more efficient and better"}, "AIPolishing": "AIPolishing", "polishing": {"copy": "copy", "cancel": "cancel", "polishingResults": "polishing results", "polishingTxtNumber": "{number} words have been generated", "noData": "nodata", "err": "Please make the audio, sir"}, "AIVoicePackage": {"title": "AI Voice Package", "ad": "AI makes life more efficient and better", "play": "play", "speed": "speed", "generate": "generate", "tip": "Please input the voice content, up to a maximum of 10000 words", "insert": "insert"}, "AIVideo": "AIVideo", "videoInfo": {"videoTitle": "AI Video", "videoTitleAd": "AI makes life more efficient and better", "used": "Used Video"}, "AIimage": "AIimage", "imageInfo": {"placeholder": "Please enter AI image description keywords", "illustrationResults": "Illustration Results", "more": "Load more", "insert": "insert", "cancel": "cancel"}, "AITestQuestions": "AITest Questions", "AITestQuestionsInfo": {"title": "AITest Questions", "ad": "AI makes life more efficient and better", "questionSettingRules": "questions setting rules", "questionType": "questions type", "number": "number", "addQuestionType": "add question type"}}, "insert": {"audio": {"text": "Audio", "local": "Local", "HanlinAudio": "Hanlin Audio", "library": "Audio Library"}, "codeBlock": "Code", "date": "Chinese Date", "emoji": "<PERSON><PERSON><PERSON>", "file": "File", "hardBreak": "LineBreak", "chemicalFormula": "Chemical Formula", "mathFormula": "Math Formula", "latexFormula": "Latex Formula", "formulaEdit": "Formula Edit", "formula": {"chemicalText": "Chemical Formula", "mathText": "Math Formula", "tip": "Convert to inline formula or not", "cancel": "cancel", "submit": "submit"}, "training": {"text": "training", "titltTip": "training title", "tip": "link address", "placeholder": "Please enter the link address", "titleplaceholder": "Please enter the training name", "nameTip": "Please enter the training name", "gameTip": "Please enter the correct link address", "insert": "submit", "cancel": "cancel"}, "chapterHead": {"title": "Chapter Head", "text": "Chapter Head", "label": "Chapter Head Name", "placeholder": "Please enter the Chapter Head Name", "bgColor": "Chapter Head Background Color", "cancel": " Cancel", "insert": "Confirm", "textAlign": "Alignment", "bgImg": "Background Image", "upload": "Upload Background Image", "errorTxt": "Chapter Head Name cannot be empty", "editText": "Edit", "delete": "delete"}, "jointHeader": {"title": "Section Head", "text": "Section Head", "label": "Section Head Name", "placeholder": " Please enter the Section Head Name", "bgColor": " Section Head Background Color", "cancel": "Cancel", "insert": "Confirm", "textAlign": "Alignment", "bgImg": "Background Image", "upload": "Upload Background Image", "errorTxt": "Section Head Name cannot be empty", "editText": "Edit", "delete": "delete"}, "paperWarrapper": {"text": "paperWarrapper", "upload": "Upload cover image locally", "ResourceLibrary": "Resource library"}, "testPaper": {"text": "testPaper", "labelName": "testPaper name", "placeholderName": "Please enter the name of the exam paper", "questions": "Number of test questions", "total": "Total score", "references": "Number of references", "view": "view", "search": "Search", "editPaper": "编辑试卷", "addTestPaper": "添加试卷"}, "psychologyHealth": {"text": "psychologyHealth", "labelName": "psychologyHealth name", "placeholderName": "Please enter the name of the exam paper", "search": "Search", "addPsychologyHealth": "添加心理测试", "view": "view", "priview": "preview", "detail": "view", "add": "add", "edit": "edit"}, "schoolAssignment": {"text": "homework", "labelName": "homework name", "placeholderName": "Please enter the homework name", "questions": "Number of test questions", "total": "Total score", "references": "Number of references", "view": "view", "search": "Search", "addAssignment": "add Assignment", "isExpand": "Expand or not"}, "surround": {"text": "surround", "type": "surround type", "left": "surround left", "rightTop": "surround right top", "leftTop": "surround left top", "spacing": "surround spacing", "imageUrl": "surround image url", "surroundTitle": "surround title", "color": "surround color", "uploadImage": "upload surround image", "titleplaceholder": "please upload surround title", "typeTip": "please select surround type", "imgTip": "please upload surround image", "isShowImageTitle": "drawing No", "isShowNo": "display"}, "fileDialog": {"videoName": "video name", "videoBtn": "video change", "videoTip": "Please select a video file", "audioName": "audio name", "audioTip": "Please select an audio file", "audioBtn": "audio change", "isExpand": "Expand or not", "fromLibrary": "From the library"}, "block": {"text": "block", "uploadText": "upload Text", "borderTop": "borderTop", "borderBottom": "borderBottom", "borderLeft": "borderLeft", "borderRight": "borderRight", "backgroundImage": "Background Image", "backgroundImageSetting": "Background Image Setting", "remove": "Remove Background", "cover": "Cover", "adjust": "Adjust", "stretch": "stretch", "tile": "tile", "widthStretching": "widthStretching", "heightStretching": "heightStretching", "auto": "auto", "backgroundColor": "Background Color", "backgroundImagePosition": "Background Image Position", "backgroundRepeat": "backgroundImageRepeat", "repeat": "repeat", "repeatX": "repeatX", "repeatY": "repeatY", "noRepeat": "noRepeat", "borderImage": "Border Image", "leftTop": "leftTop", "leftCenter": "leftCenter", "leftBottom": "leftBottom", "centerTop": "centerTop", "center": "center", "centerBottom": "centerBottom", "rightTop": "rightTop", "rightCenter": "rightCenter", "rightBottom": "rightBottom", "borderWidth": "Border Width", "borderColor": "Border Color", "borderStyle": "Border Style", "dotted": "dotted", "solid": "solid", "double": "double", "dashed": "dashed", "borderRadius": "Border Radius", "blockPadding": "Block Padding", "fitView": "<PERSON><PERSON><PERSON><PERSON>", "borderRadiusTopLeft": "border radius top left", "borderRadiusTopRight": "border radius top right", "borderRadiusBottomLeft": "border radius bottom left", "borderRadiusBottomRight": "border radius bottom right", "paddingTop": "padding top", "paddingBottom": "padding bottom", "paddingLeft": "padding left", "paddingRight": "padding right", "padding": "Padding", "tip": "Fill in the first text box, and the other text boxes will be automatically filled in"}, "backgroundImg": {"uploadText": "upload Image", "text": "text box", "uploadImage": "upload Image", "justification": "justification", "leftTop": "leftTop", "leftBottom:": "leftBottom", "rightTop": "rightTop", "rightBottom": "rightBottom", "center": "center", "content": "content", "paddingHeader": "padding setting", "paddingTop": "padding top", "paddingBottom": "padding bottom", "paddingLeft": "padding left", "paddingRight": "padding right", "equalProportion": "equalProportion", "scalingProportionally": "Scaling proportionally", "freeProportionally": "Free scaling", "openDrag": "open Drag", "noDrag": "colse Drag", "uploadImg": "Please upload an image", "backgroundEqualProportion": "background EqualProportion"}, "fold": {"text": "fold", "title": "fold title", "placeholder": "Please enter the fold title", "iconText": "Icon", "iconOther": "Other Icon", "other": "other", "uploadIcon": "upload Icon", "uploadIconTips": "Upload Icon Size is 1:1", "isOpen": "Open or not", "error": "Title cannot be empty", "isBg": "Background show", "fontSize": "FontSize", "fontColor": "Font Color", "fontWeight": "Font Weight", "fontFamily": "Font Family", "expand": "Expand Text", "collapseText": "Collapse Text", "iconColor": "Icon Color"}, "hr": {"text": "Divider", "title": "Divider Style", "color": "Divider Color", "signle": "Single Line", "double": "Double Thin Lines", "dotted": "Dotted Line", "dashed": "Dashed Line", "dashedDouble": "Double Dashed Line", "signleBold": "Thick Line", "doubleBoldTop": "Thick & Thin Lines (Top)", "doubleBoldBottom": "Thin & Thick Lines (Bottom)", "wavy": "Wavy Line"}, "image": {"text": "Image", "layoutImages": "Layout Images", "inlineImages": "Inline Images", "icon": "Icon", "imageGallery": "Image Gallery", "local": "Local", "gallery": "Gallery", "galleryEdit": "Gallery Edit", "galleryTitle": "GalleryTitle", "galleryTitlePlaceholder": "Please enter a GalleryTitle", "imageTitle": "Image Title", "imagePlaceholder": "Please enter a Image Title", "linkAddress": "Link Address", "linkAddressPlaceholder": "Please enter a Link Address", "isShowNo": "Show Number", "isShowGalleryTitle": "Show Gallery Title", "isShowImgTitle": "Show Image Title", "isAsCatalog": "Is As Catalogue", "level": "level", "levelMax": "Directory levels are between 1 and 6", "levelRequired": "Directory level cannot be empty", "replaceImage": "Replace Image", "edit": "Edit", "preview": "Preview", "remove": "Remove", "setting": "Setting", "yes": "Yes", "no": "No", "column": "Column", "columnTip": "Image Column Count", "removeTip": "Confirm Delete?", "comfirm": "comfirm", "cancel": "cancel", "crop": "Crop", "cropImg": "Crop Image", "imgDescribe": "Describe", "imgDescribePlaceholder": "Please enter a Describe", "selectImage": "Please Select Image", "loading": "Uploading Image, Please Wait...", "tips": "Only One Image Can Be Selected", "interval": "Interval", "originalName": "Original Name", "updateImage": "Update Image", "resetImage": "Reset Image", "resetResourceLibrary": "Reset Resource Library"}, "link": {"text": "Link", "title1": "Insert Link", "title2": "Insert Link", "insert": "Insert", "hrefText": "Link Text:", "hrefTextTip": "Enter or select link text", "href": "Link URL:", "hrefTip": "Enter the link URL, usually starting with http:// or https://", "ok": "OK", "selectDirectory": "Select Directory", "hrefTitle": "Link Title", "chaptersInThisBook": "Chapters In ThisBook", "websiteLink": "Website Link", "crossReferencing": "Cross Referencing", "resourceLibrary": "Resource Library", "quoteType": "Reference Type", "quoteContent": "Reference Content", "quoteView": "Quote View", "quoteSerarch": "Quote Search", "linkDel": "Delete Link", "linkEdit": "Edit Link", "linkOpen": "Open Link", "Image": "Image", "3d": "3D", "ar": "AR/VR", "simulation": "Simulation", "game": "Game", "teachingResources": "Teaching Resources", "video": "Video", "audio": "Audio"}, "math": "<PERSON><PERSON><PERSON>", "questions": {"markAsRightAnswer": "Set as the correct answer", "generalQuestion": "Ordinary question type", "matchingQuestion": "Wired question type", "text": "questions", "title": "questions", "formula-btn-text": "insert", "editText": "edit", "saveBtnText": "save", "backBtnText": "back", "collapseBtnText": "collapse", "expandBtnText": "expand", "delBtnText": "delete", "testDescText": "Test", "formula": "Formula", "chemicalFormula": "Chemical Formula", "mathFormula": "Math Formula", "latexFormula": "Latex Formula", "update": "update", "questionText": "Question", "addFillItem": "Add fill in the blank items", "pleasefillinthquestion": "Please fill in the question stem", "options": "Options", "pleasefillintheoption": "Please fill in the option", "addOptions": "Add options", "answer": "Answer", "sortAnswer": "Sort answer", "addSort": "Add sort", "referenceAnswer": "Reference answer", "connection": "connection", "analysis": "Analysis", "programmingLanguage": "Programming Language", "code": "Code", "case": "Case", "remark": "Remark", "add": "Add", "successAdded": "Added successfully!", "successAddedAndContinue": "Added successfully! You can continue to add new questions.", "continuousAdd": "Continuous Add", "cancel": "Cancel", "bookIdEmpty": "BookId must be a number", "chapterIdEmpty": "ChapterId must be a number", "textbookGeneralEmpty": "The textbook general folder cannot be empty", "successGenerated": "Successfully generated textbook topic", "completeQuestion": "Please fill in the complete question information", "fillContent": "Please fill in the content", "fillCode": "Please fill in the code", "correctAnswer": "The correct answer cannot be empty"}, "bubble": {"text": "bubble", "title": "bubble", "bubbleType": "bubbleType", "bubbleTitle": "bubbleTitle", "bubbleContent": "bubbleContent", "bubbleUrl": "bubbleLink", "bubbleTitlePlaceholder": "Please enter a title", "bubbleUrlPlaceholder": "Please enter the link address", "bubbleText": "TextBubble", "bubbleIcon": "IconBubble", "bubbleIconSelect": "Please select icon", "bubbleFontColor": "FontColor", "bubbleBackgroundColor": "BackgroundColor", "bubbleBgColor": "bubbleBgColor", "colorgroup": "ColorGroup", "cancel": "cancel", "submit": "submit", "bubbleEdit": "Edit bubbles", "bubbleDelete": "Delete bubbles", "bubbleContentSelect": "Description type", "bubbleContentText": "Text Content", "bubbleContentImage": "Image Content", "bubbleContentImagePlaceholder": "Please upload a picture", "bubbleContentTxtPlaceholder": "Please enter the text content", "notice": "Note: It cannot be used across paragraphs"}, "courseware": {"text": "courseware", "local": "local", "library": "ResourceLibrary", "tips": "You can only select a single file to upload"}, "threeDimensional": {"text": "3D", "local": "local", "library": "ResourceLibrary", "tips": "You can only select a single file to upload"}, "ARandVR": {"text": "AR/VR", "local": "local", "library": "ResourceLibrary", "titltTip": "AR/VR name", "tip": "AR/VR address", "placeholder": "Please enter the AR/VR address", "nameTip": "Please enter the AR/VR name", "gameTip": "Please enter the correct link address", "titleplaceholder": "Please enter the AR/VR name", "insert": "submit", "color": "background color", "cancel": "cancel", "confirm": "confirm", "tips": "You can only select a single file to upload"}, "simulation": {"text": "simulation", "local": "local", "library": "ResourceLibrary", "titltTip": "simulation name", "tip": "simulation address", "placeholder": "Please enter the simulation address", "nameTip": "Please enter the simulation name", "simulationTip": "Please enter the correct link address", "titleplaceholder": "Please enter the simulation name ", "insert": "submit", "cancel": "cancel", "confirm": "confirm", "tips": "You can only select a single file to upload"}, "games": {"text": "game", "titltTip": "Game name", "tip": "Game address", "placeholder": "Please enter the game address", "nameTip": "Please enter the game name", "gameTip": "Please enter the correct link address", "titleplaceholder": "Please enter the game name", "insert": "submit"}, "expandReading": "Expand Reading", "footerNote": {"text": "Footer Note", "title": "Footer Note", "placeholder": "Enter the footer note"}, "remark": {"text": "Remark", "title": "Create Remark", "placeholder": "Please enter remark", "edit": "Edit", "remove": "Remove", "push": "release", "cancel": "cancel", "confirm": "confirm", "pushSuccess": "release success"}, "aligned": {"text": "aligned", "top": "top", "center": "center", "bottom": "bottom"}, "interaction": {"text": "interaction", "options": {"vote": "Vote", "wordCloud": "Word Cloud", "discussion": "Discussion", "imageWaterfall": "Image Waterfall"}, "addOption": "Add Option", "viewResults": "View Results", "continueSubmitting": "Continue Submitting", "form": {"comfirm": "comfirm", "cancel": "cancel", "themeContent": "Theme Content can not be empty"}, "opt": {"del": "delete", "edit": "edit"}, "imageWaterfall": {"imageText": "Please enter the content", "uploadImageTips": "Please click to upload your image", "continueUpload": "Continue uploading", "imageSubmit": "Submit"}, "discussion": {"tips": "Participate in group discussions and share your opinions", "viewResults": "View Results", "discussionPlaceholder": "Please enter the discussion content"}}, "symbol": "Symbol", "template": "Template", "backgroundImage": "Background Image", "containerColor": "Background Color", "textBox": "Text Box", "toc": "Document Map", "video": {"text": "Video", "local": "Local", "library": "Video Library", "formLibrary": "From Video Library"}, "web": {"text": "Web Page", "insert": "Insert", "title": "Insert Web Page", "tip": "Some web pages may not support embedding.", "placeholder": "Enter a web address, starting with http:// or https://"}, "library": {"fileName": "File Name", "fileType": "File Type", "fileTypePlaceholder": "Please enter the file type", "fileNamePlaceholder": "Please enter the file name", "search": "search", "upload": "upload", "addImage": "Add Image", "selectImage": "Select Image", "addVideo": "Add Video", "selectVideo": "Select Video", "addAudio": "Add Audio", "selectAudio": "Select Audio", "avandvr": "AR/VR", "simulation": "simulation", "threeD": "3D Model", "addModel": "Add Virtual Simulation/3D Model", "selectModel": "Select Virtual Simulation/3D Model", "addCourseware": "Add Courseware", "selectCourseware": "Select Courseware", "imagePreview": "Image Preview", "audioPreview": "Audio Preview", "videoPreview": "Video Preview", "filePreview": "File Preview", "imageTips": "Support Image Formats Such as jpg, png, gif", "videoTips": "Only mp4 and avi files are allowed, and they must not exceed 500MB", "audioTips": "Only mp3, wav, and mpeg files are allowed, and they must not exceed 50MB", "modelTips": "Support multiple 3D model and virtual simulation file formats, and they must not exceed 500MB", "coursewareTips": "Support files in formats such as PDF, PPT, Word, etc., and they must not exceed 100MB", "remove": "Remove", "confirm": "Confirm", "cancel": "Cancel", "myResource": "My Resource", "textbookResources": "textbook resources", "linkTitle": "Link Title"}}, "table": {"addColumnAfter": "Insert Column After", "addColumnBefore": "Insert Column Before", "addRowAfter": "Insert Row Below", "addRowBefore": "Insert Row Above", "borderColor": "Border Color", "borderStyle": {"text": "border Style", "borderWidth": "borderWidth", "borderCss": "borderStyle", "borderUse": "used"}, "openTableMode": "Open Table Mode", "cellAlign": {"text": "Alignment", "tip": "Cell Alignment", "lt": "Top Left", "ct": "Top Center", "rt": "Top Right", "jt": "Top Justify", "lm": "Middle Left", "cm": "Middle Center", "rm": "Middle Right", "jm": "Middle Justify", "lb": "Bottom Left", "cb": "Bottom Center", "rb": "Bottom Right", "jb": "Bottom Justify"}, "cellBgColor": {"text": "Background", "tip": "Cell Background Color"}, "deleteColumn": {"text": "Delete Column", "title": "Delete Table Column", "message": "This operation will delete the selected table column. Continue?", "delete": "Delete Now", "success": "Deleted Successfully"}, "deleteRow": {"text": "Delete Row", "title": "Delete Table Row", "message": "This operation will delete the selected table row. Continue?", "delete": "Delete Now", "success": "Deleted Successfully"}, "delete": {"text": "Delete Table", "title": "Delete Current Table", "message": "This operation will delete the selected table. Continue?", "delete": "Delete Now", "success": "Deleted Successfully"}, "fix": {"text": "Fix Table", "tip": "Click to automatically fix table when it's disordered"}, "templateTable": {"text": "Table Template", "tip": "Insert Table Template", "label": "Template Name", "search": "Search", "reset": "Reset", "use": "Used", "already": "Already Used", "placeholder": "Please enter a template name", "titleStyle": "Title Style", "titleSize": "Title Size", "titleFamily": "Title Font Family", "titleColor": "Title Color", "titleOther": "Table Other Attribute Settings", "titleFamilyPlaceholder": "Please enter the title font family", "tableTitleSizeplaceholder": "Please enter the title size", "titleTop": "title in the top display", "titleBottom": "title in the bottom display", "selectTemplate": "Select Template"}, "insert": {"text": "Insert Table", "tip": "Insert New Table", "property": "Table Properties", "withHeader": "Include Header", "rows": "Number of Rows:", "cols": "Number of Columns:", "create": "Create Table"}, "mergeCells": "Merge Cells", "nextCell": "Next Cell", "prevCell": "Previous Cell", "splitCell": "Split Cell", "toggleHeaderCell": "Header Cell", "toggleHeaderRow": "Header Row", "toggleHeaderColumn": "Header <PERSON>", "modalTitle": "Table Properties", "modalPlaceholder": "Please enter a template name", "isShowTableNo": "display table number", "isShowTableTitle": "display table title", "titlePosition": "Title Position", "titleHanlin": "Hanlin Title", "left": "Left", "center": "Center", "right": "Right"}, "editor": {"text": "编辑器", "preview": "预览", "fullscreen": "全屏", "exitFullscreen": "退出全屏", "copy": "复制", "copySuccess": "复制成功", "copyError": "复制失败", "addQuestionItem": "Add Question", "updateQuestionItem": "编辑题目", "noQuizSelected": "您还没选择任何试卷"}, "tools": {"barcode": {"text": "Barcode", "edit": "Edit Barcode", "title": "Barcode", "format": "Format", "font": "Font Style", "lineColor": "Barcode & Text Color", "bgColor": "Barcode Background Color", "more": "More", "width": "Width:", "height": "Height:", "margin": "Margin:", "displayValue": "Display Text", "displayValueText": "Show Text", "textContent": "Text Content:", "textContentTip": "Overrides barcode content text", "textPosition": "Text Position:", "top": "Top", "bottom": "Bottom", "textMargin": "Vertical Distance:", "fontSize": "Font Size:", "placeholder": "Enter content to convert into barcode", "error": "Your input may not comply with the current barcode standard constraints, please check.", "preview": "Preview", "renderError": "No preview available", "notEmpty": "Barcode content cannot be empty"}, "chineseCase": {"text": "Chinese Case", "tip": "Chinese Case Conversion"}, "diagrams": {"text": "Diagram", "edit": "Edit Diagram", "loading": "Loading..."}, "mermaid": {"text": "Mermaid", "edit": "Edit Mermaid", "placeholder": "Enter Mermaid code", "preview": "Preview", "notEmpty": "Mermaid content cannot be empty"}, "mindMap": "Mind Map", "qrcode": {"text": "QR Code", "edit": "Edit QR Code", "level": "QR Code Error Correction Level", "levelL": "Low", "levelM": "Medium", "levelQ": "Quartile", "levelH": "High", "padding": "padding:", "paddingTip": "white space padding", "width": "Size:", "widthTip": "QR Code size in pixels", "color": "QR Code Color", "bgColor": "Color of background", "placeholder": "Enter content to convert into QR Code", "preview": "Preview", "renderError": "QR Code generation failed", "notEmpty": "QR Code content cannot be empty"}, "seal": {"text": "Official Seal", "title": "Insert Official Seal", "insert": "Insert", "tip": "Select a photocopy or photo of a solid-color background seal; it will be automatically extracted. All operations are completed locally without sending data to the server, so feel free to use.", "insertTip": "Click here to select seal image", "converting1": "Loading component...", "converting2": "Loading component: {percentage}%", "converting3": "Extracting image, please wait...", "convertError": "Electronic seal extraction failed, please retry", "notEmpty": "Please select a seal"}, "signature": {"text": "E-Signature", "title": "Insert Electronic Signature", "clear": "Clear", "lineWidth": "<PERSON>", "lineColor": "Pen Color", "smooth": "Show Brush Strokes", "reset": "Reset Options", "tip": "Please sign in the this area", "notEmpty": "No signature detected"}}, "questions": {"single": "single", "multiple": "multiple", "fillInTheBlanks": "fill in the blanks", "judge": "judge", "orderby": "order by", "connection": "connection", "shortAnswer": "short answer", "programmingFill": "programming fill in the blanks", "programmingAnswer": "programming answer", "answerSupportRandomOrder": "When there are multiple fill in the blank items, the answers can be out of order", "showTitle": "显示标题", "hideTitle": "隐藏标题"}, "page": {"bg": {"text": "Background", "custom": "Custom Page Background", "default": "<PERSON><PERSON><PERSON>", "color1": "Eye-friendly\nGreen", "color2": "Elegant\nYellow", "color3": "Cloudy\nBlue", "color4": "Sky Blue", "color5": "Dark Night\nBlack"}, "break": "Page Break", "footer": {"show": "Show Footer", "hide": "<PERSON><PERSON>"}, "header": {"show": "Show Header", "hide": "<PERSON><PERSON>"}, "lineNumber": "Line Numbers", "margin": "<PERSON><PERSON>", "orientation": {"text": "Orientation", "landscape": "Portrait", "portrait": "Landscape"}, "preview": "Presentation", "size": {"text": "Size", "cm": "CM", "custom": "Custom Settings"}, "watermark": {"text": "Watermark", "fontFamily": "Watermark Font", "fontSize": "Watermark Size", "fontColor": "Watermark Color", "content": "Watermark Text", "type": "Watermark Style", "compact": "Compact", "spacious": "Spacious", "clear": "Remove Watermark"}, "detetePage": {"text": "delete Page", "titleError": "Error", "messageError": "The last page cannot be deleted", "deleteConfirm": "Confirm delete current page?", "deleteError": "After deletion, the data cannot be recovered, please be careful to execute the deletion action."}, "text": "Page"}, "export": {"embed": {"text": "Embed", "title": "Embed Code", "tip": "After copying, manually adjust the iframe width and height for optimal viewing.", "copy": "Copy", "copied": "Code copied to clipboard"}, "image": {"text": "Image", "jpg": "JPG", "png": "PNG", "error": {"title": "Error Message", "message": "Failed to export image, please try again or refresh the page."}}, "share": {"text": "Share", "tip": "Copy the link and send it to those you want to share with.", "copy": "Copy Link", "copied": "Link copied to clipboard"}, "pdf": {"text": "PDF Document", "title": "Export PDF Document", "message": "Before exporting the PDF document, make sure that all images on the pages have finished loading. Then, in the print dialog box that appears, select 'Printer - Save as PDF', and click 'Save' button to save the document. If node backgrounds are not displayed, please check the 'Background graphics' option in the print dialog box.", "confirm": "I Understand"}, "word": {"exportWord": "Export Word Document", "content": "Before Exporting A Word Document PleaseEnsure That All Images On The Pages Have Been Loaded Then Click The Export Button The Download Process Takes Time Please Be Patient And Check The Progress In The Browser Download Center"}, "text": "Text"}, "list": {"bullet": {"text": "Bulleted List", "disc": "Filled Circle", "circle": "Open Circle", "square": "Filled Square", "info": "The list numbering editor cannot modify the numbers. If you want to view the real-time effect, use the mobile reader."}, "ordered": {"text": "Numbered List", "property": "List Properties", "startAt": "Starting Number:", "decimal": "Numbers", "decimalLeadingZero": "Numbers with Leading Zero", "lowerRoman": "Lowercase Roman Numerals", "upperRoman": "Uppercase Roman Numerals", "lowerLatin": "Lowercase Alphabet", "upperLatin": "Uppercase Alphabet", "tradChineseInformal": "Traditional Chinese Counting", "simpChineseFormal": "Formal Uppercase Chinese Counting"}, "task": {"text": "To-Do List", "split": "Add Task", "sink": "Convert to Sub-task", "lift": "Remove as Task"}}, "colorPicker": {"default": "Default Color", "standard": "Standard Colors", "recent": "Recently Used", "more": "More Colors"}, "blockMenu": {"insert": "Insert Content", "select": "Insert Node", "template": "Insert Template", "toogleNode": "Switch Block Type", "common": "Common actions", "backgroundTitle": "Set image background", "clearFormat": "Clear formatting", "duplicate": "Duplicate", "pasteNode": "PasteNode", "copy": "Copy", "cut": "Cut", "delete": "Delete"}, "bubbleMenu": {"code": {"copy": {"text": "Copy Code", "success": "Code copied to clipboard"}, "cut": {"text": "cut Code", "success": "Code cut to clipboard"}, "languages": "Languages", "lineNumbers": "Show Line Numbers", "themes": {"text": "Theme", "light": "Light Theme", "dark": "Dark Theme"}, "wordWrap": "Word Wrap", "runState": "Enable Run"}, "file": {"download": "Download File", "downloadMatchingQuestionTmpl": "下载连线题型模板", "downloadGeneralQuestionTmpl": "下载连线题型模板"}, "image": {"open": "Open in a new window", "draggable": "Image Floating", "flipX": "Flip Horizontally", "flipY": "Flip Vertically", "preview": "Preview Image", "proportion": "Lock Image Aspect Ratio", "removeBg": "Remove Image Background", "removingBg": "Removing Image Background...", "reset": "Reset Image Position", "rotateCC": "Rotate Counter-Clockwise 90°", "rotateC": "Rotate Clockwise 90°", "upload": "Upload Image"}, "textBox": {"background": "Background Color", "border": "Border Settings", "borderColor": "Border Color", "borderStyle": "Border Style", "solid": "Solid", "dashed": "Dashed", "dotted": "Dotted", "noBorder": "No Border"}, "delete": "Delete"}, "assistant": {"text": "AI Assistant", "placeholder": "Enter or select a command...", "send": "Send", "exit": "Exit AI Assistant", "commands": "Common Commands:", "result": "AI Generation Result:", "replace": "Replace", "insertAfter": "Insert After", "insertBelow": "Insert Below", "copy": "Copy Generated Result", "copySuccess": "Result copied to clipboard", "rewrite": "Rewrite", "delete": "Delete Result", "error": {"title": "Error Message", "message": "An error occurred with the AI Document Assistant result. Please try again!"}}, "comment": {"text": "Comment"}, "pageOptions": {"title": "Page Settings", "size": {"text": "<PERSON>", "width": "Width:", "height": "Height:", "custom": "Custom"}, "margin": {"text": "<PERSON>", "default": "<PERSON><PERSON><PERSON>", "narrow": "<PERSON>rrow", "moderate": "Moderate", "wide": "Wide", "top": "Top:", "right": "Right:", "bottom": "Bottom:", "left": "Left:"}}, "source": {"loading": "Loading Source Editor..."}, "document": {"untitled": "Untitled Document"}, "search": {"text": "Search", "title": "Search and Replace", "searchText": "Search...", "replaceText": "Replace...", "caseSensitive": "Case Sensitive", "search": "Search", "replace": "Replace", "replaceAll": "Replace All"}, "toc": {"title": "Document Map", "show": "Document Map", "hide": "Hide Document Map", "empty": "No Document Map Available"}, "spellcheck": {"enable": "Enable Spell Check", "disable": "Disable Spell Check"}, "pagination": {"title": "Page Break", "disable": "Exit Page Break", "toggle": "Double Click to Switch Paging"}, "template": {"systemIcon": "systemIcon", "localIcon": "localIcon", "myselfIcon": "materials", "templateIcon": "Template", "backgroundBorder": "backgroundBorder", "backgroundIcon": "Icon", "add": "Add", "del": "delete"}, "shortcut": {"title": "Keyboard Shortcuts", "text": "Text", "commonlyUsed": "Common", "copy": "Copy", "paste": "Paste", "pasteAsText": "Paste as Plain Text", "paragraph": "Insert New Paragraph", "hardBreak": "Line Break", "format": "Text", "page": "Page", "pageZoomIn": "Zoom In", "pageZoomOut": "Zoom Out", "pageZoomReset": "Reset Zoom to Default", "pageAutoWidth": "Fit to <PERSON> Width", "markdown": "<PERSON><PERSON>"}, "changeLocale": {"title": "Switch Language", "message": "After changing the language, the page will refresh. Please save your document content first. Are you sure you want to switch now?", "confirm": "Switch"}, "resetAll": {"title": "Reset Editor", "message": "This action will clear all editor settings and current content, and refresh the page. It's recommended to save your content before resetting. Are you sure you want to reset now?", "reset": "Reset"}, "feedback": "Issues / Feedback", "poweredBy": "Powered by Dutp Editor", "wordCount": {"characters": "Characters", "title": "Document Statistics", "input": "Characters Typed", "selection": "Selected Characters", "limit": "Character Limit", "currentPage": "Current Page", "totalPage": "Total Pages"}, "preview": {"title": "Presentation", "disable": "Exit Presentation", "countdown": {"title": "Set Countdown", "select": "Select", "selectTip": "Select the countdown duration", "custom": "Custom", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "whenEnd": "When End", "showEndMessage": "Show end message", "exitPreview": "Exit presentation mode", "start": "Start", "cancel": "Cancel", "1hour": "1 Hour", "45minutes": "45 Minutes", "30minutes": "30 Minutes", "15minutes": "15 Minutes", "10minutes": "10 Minutes", "5minutes": "5 Minutes", "error": "Please set a valid countdown time", "endCountdown": "Countdown has ended", "remaining": "Remaining time"}, "laserPointer": "<PERSON><PERSON>"}, "fullscreen": {"title": "Fullscreen", "disable": "Exit Fullscreen"}, "zoom": {"zoomIn": "Zoom In", "zoomOut": "Zoom Out", "level": "Zoom Level: ", "autoWidth": "Best Fit Width", "autoWidthError": "Error calculating automatic page width", "reset": "Back to 100%"}, "node": {"codeBlock": {"menu": "<PERSON><PERSON>"}, "image": {"loading": "Loading image...", "error": "Image load failed"}, "textBox": {"tip": "Double-click to edit"}}, "file": {"uploading": "Uploading file...", "download": "Download File", "limit": "File \"{filename}\" exceeds the limit of {size}MB", "notAllow": {"title": "Error Message", "message": "Insertion of this file type is not allowed in the current document."}, "maxFiles": "You can upload up to 6 images."}, "save": {"text": "Save", "saved": "Saved", "unsaved": "Unsaved", "network": "Network Status:", "online": "Online", "offline": "Offline", "saving": "Saving document...", "savedAt": "Saved at:", "savedAtText": "Saved at {time}", "failed": "Failed to save document", "success": "Document saved successfully", "error": "Error saving document", "cache": {"text": "<PERSON>ore from Cache", "error": {"title": "Error Message", "message": "There is no content in the cache!"}}}, "print": {"text": "Print", "title": "Print Document", "message": "Before printing the document, please ensure that all images on the pages have finished loading. If node backgrounds are not displayed, please check the 'Background graphics' option in the print dialog box.", "confirm": "Print Now"}, "time": {"justNow": "Just now", "past": "{n} ago", "yesterday": "Yesterday", "day": "{n} days", "hour": "{n} hours", "minute": "{n} minutes", "second": "{n} seconds"}, "dialog": {"cancel": "Cancel"}, "rc": {"url": "The link cannot be empty", "file": {"uploading": "File uploading...", "uploadChangeError": "Cannot change during file uploading", "fileLimit": "File \"{filename}\" exceeds the limit of {size}MB", "fileType": "File \"{name}\" type {suffix} 不符合要求", "imgSize": "Image\"{filename}\" exceeds the limit of {size}MB"}, "menu": {"preview": "preview", "changeFile": "changeFile", "changeCover": "changeCover", "edit": "edit", "setting": "setting", "delete": "delete"}, "runGame": "runGame", "view": "view", "form": {"comfirm": "comfirm", "cancel": "cancel", "header": "setting", "title": "title", "titlePlaceholder": "please enter the title", "titleRequired": "Title cannot be empty", "titleLimit": "The title length cannot exceed 20 characters", "showTitle": "display the title", "showTitleRequired": "Display title switch", "showTitleSwitchTure": "show", "showTitleSwitchFalse": "hide", "url": "url", "urlTip": "Please enter a legal url", "urlPlaceholder": "please enter a URL"}}, "headerTools": {"back": "Back", "textbookName": "Textbook Name", "importWord": "Import Word", "importPdf": "Import Pdf", "setUp": {"title": "setUp", "headerTitle": "Set automatic save time", "formLabel": "Auto save", "minute": "minute", "submit": "submit", "cancel": "cancel"}, "recycleBin": "Recycle Bin", "save": "Save", "word": {"header": "Tips", "body": "The Current Chapter Already Contains Content Do You Want To Replace The Original Content Or Add New Content After The Original Content", "cancelBtn": "Replace", "confirmBtn": "Addition"}, "preview": {"text": "Preview", "phonePreview": "Phone", "tabletPreview": "Tablet", "desktopPreview": "Desktop"}, "print": "Print", "previewReader": "Preview in Reader"}, "newToc": {"catalogue": "Catalogue", "newChapter": "New Chapter", "knowledgeGraph": "Graph", "search": "Search", "placeholder": "Please Enter Keywords", "aResult": "A Result"}, "containerTool": {"commonComponents": "Components", "textbookTemplate": "Template", "aiComponents": "AI", "titleLevel": "Title Level", "typeface": "Typeface", "cuttingBoard": "Cutting Board", "paragraph": "Paragraph"}, "forms": {"fields": {"knowledgeReference": "knowledge Indication"}}, "jointHeader": {"setting": "setting", "level": "level", "levelMax": "Directory levels are between 1 and 6", "levelRequired": "Directory level cannot be empty", "jointHeaderUrl": "pictures address", "jointHeight": "picture height", "heightMax": "Minimum height value 150", "upload": "upload"}, "chapterHeader": {"setting": "setting", "chapterHeaderUrl": "pictures address", "chapterHeaderHeight": "picture height", "heightMax": "Minimum height value 300", "upload": "upload"}}