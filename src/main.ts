/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2025-02-27 16:16:26
 * @FilePath: \dutp-editor\src\main.ts
 * @Description:
 *
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import type { UmoEditorOptions } from '@/types'
import disableDevtool from 'disable-devtool';
import App from './app.vue'
import { useUmoEditor } from './components'
import 'prismjs/themes/prism-tomorrow.css'
import router from './router'
import TDesign from 'tdesign-vue-next'
import 'tdesign-vue-next/es/style/index.css'
const app = createApp(App)

const options = {}
function checkDomain() {
    return window.location.hostname.endsWith('.dutp.cn');
  }
const openDev = sessionStorage.getItem('OPEN_DEV');

if (openDev && openDev == '1') {

} else {
  if (checkDomain()) {
    disableDevtool({
      ondevtoolopen: function() {
        window.location.href = 'https://editor.dutp.cn';
      }
    });
  }
}
app.use(useUmoEditor, options as unknown as UmoEditorOptions)
app.use(router)
app.use(TDesign)
app.mount('#app')
