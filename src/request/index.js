import axios from 'axios'
import { DialogPlugin, MessagePlugin, NotifyPlugin } from 'tdesign-vue-next'

import cache from '../utils/cache.js'
import { blobValidate, tansParams } from '../utils/dutp.js'
import errorCode from './errorCode.js'
import { getToken } from './token.js'
const request = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  headers: {
    'Content-type': 'application/json;charset=utf-8',
  },
  // 超时
  timeout: 600000,
})
// //请求拦截
request.interceptors.request.use(
  (config) => {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false

    if (getToken() && !isToken) {
      config.headers['Authorization'] = `Bearer ${getToken()}` // 让每个请求携带自定义token 请根据实际情况自行修改
    }

    if (config.method === 'get' && config.params) {
      let url = `${config.url}?${tansParams(config.params)}`
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    if (
      !isRepeatSubmit &&
      (config.method === 'post' || config.method === 'put')
    ) {
      const requestObj = {
        url: config.url,
        data:
          typeof config.data === 'object'
            ? JSON.stringify(config.data)
            : config.data,
        time: new Date().getTime(),
      }
      const requestSize = Object.keys(JSON.stringify(requestObj)).length // 请求数据大小
      const limitSize = 5 * 1024 * 1024 // 限制存放数据5M
      if (requestSize >= limitSize) {
        console.warn(
          `[${config.url}]: ` +
            '请求数据大小超出允许的5M限制，无法进行防重复提交验证。',
        )
        return config
      }
      const sessionObj = cache.session.getJSON('sessionObj')
      if (
        sessionObj === undefined ||
        sessionObj === null ||
        sessionObj === ''
      ) {
        cache.session.setJSON('sessionObj', requestObj)
      } else {
        const s_url = sessionObj.url // 请求地址
        const s_data = sessionObj.data // 请求数据
        const s_time = sessionObj.time // 请求时间
        const interval = 1000 // 间隔时间(ms)，小于此时间视为重复提交
        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          const message = '数据正在处理，请勿重复提交'
          console.warn(`[${s_url}]: ${message}`)
          return Promise.reject(new Error(message))
        } else {
          cache.session.setJSON('sessionObj', requestObj)
        }
      }
    }
    return config
  },
  (error) => {
   
    return Promise.reject(error)
  },
)
export const isRelogin = { show: false }
// //相应拦截
request.interceptors.response.use(
  (response) => {
    const code = response.data.code || 200
    // 获取错误信息
    const msg = errorCode[code] || response.data.msg || errorCode['default']
    if (response.config.url.includes('/book/task/download/')) {
      return response
    }
    // 二进制数据则直接返回
    if (
      response.request.responseType === 'blob' ||
      response.request.responseType === 'arraybuffer'
    ) {
      return response.data
    }
    if (code === 401) {
      if (!isRelogin.show) {
        isRelogin.show = true
        const messageBox = DialogPlugin.confirm({
          header: '系统提示',
          body: '登录状态已过期，您可以继续留在该页面，或者重新登录',
          theme: 'warning',
          attach: 'body',
          placement: 'center',
          showInAttachedElement: false,
          onCancel: () => {
            messageBox.destroy()
          },
          onClose: () => {
            messageBox.destroy()
          },
          onConfirm: () => {
            isRelogin.show = false
            useStore()
              .LoginOut()
              .then(() => {
                location.href = '/index'
              })
              .catch(() => {
                isRelogin.show = false
              })
          },
        })
      }
      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
    } else if (code === 500) {
      MessagePlugin.error(msg)
      return Promise.reject(new Error(msg))
    } else if (code === 601) {
      MessagePlugin.error(msg)

      return Promise.reject(new Error(msg))
    } else if (code !== 200) {
      NotifyPlugin.error({ title: msg })
      return Promise.reject('error')
    } else {
      return Promise.resolve(response.data)
    }
  },
  (error) => {
    return Promise.reject(error)
  },
)

export function download(url, params, filename, config) {
  //downloadLoadingInstance = ElLoading.service({ text: "正在下载数据，请稍候", background: "rgba(0, 0, 0, 0.7)", })
  return request
    .post(url, params, {
      transformRequest: [
        (params) => {
          return tansParams(params)
        },
      ],
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        authorization: getToken(),
      },

      responseType: 'blob',
      ...config,
    })
    .then(async (data) => {
      const isBlob = blobValidate(data)
      if (isBlob) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        const resText = await data.text()
        const rspObj = JSON.parse(resText)
        const errMsg =
          errorCode[rspObj.code] || rspObj.msg || errorCode['default']
        //  ElMessage.error(errMsg);
      }
      // downloadLoadingInstance.close();
    })
    .catch((r) => {
      console.error(r)
      //  ElMessage.error('下载文件出现错误，请联系管理员！')
      //   downloadLoadingInstance.close();
    })
}
export default request
