import Cookies from 'js-cookie'
const Token<PERSON>ey = 'Editor-Token'
const ManageTokenKey = 'Manage-Token'

export const getToken = () => {
  let token = Cookies.get(TokenKey)
  const href = window.location.href
  // console.log(window.location, search, !token && search.includes('formType'))
  if (href.includes('formType')) {
    let formType = href.split('formType=')[1].split('&')[0]
    if (formType == 2) {
      token = Cookies.get(ManageTokenKey)
    }
  }
  return token
}

export const setToken = (token) => {
  return Cookies.set(TokenKey, token)
}
export const setManageToken = (token) => {
  return Cookies.set(ManageTokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function removeManageToken() {
  return Cookies.remove(ManageTokenKey)
}
