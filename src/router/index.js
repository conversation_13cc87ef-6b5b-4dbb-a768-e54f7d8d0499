/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-04 08:23:45
 * @LastEditTime: 2025-02-14 10:47:51
 * @FilePath: \dutp-editor\src\router\index.js
 * @Description:
 *
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import { createRouter, createWebHashHistory } from 'vue-router'

import adminIndex from '@/views/home.vue'
import Home from '@/views/index.vue'
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/editor',
      name: 'index',
      component: Home,
    },
    {
      path: '',
      redirect: '/login',
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/login.vue'),
    },
    {
      path: '/forgetPassword',
      name: 'forgetPassword',
      component: () => import('@/views/forgetPassword.vue'),
    },
    {
      path: '/home',
      name: 'home',
      component: adminIndex,
      children: [
        {
          path: '/pages/list',
          name: 'list',
          component: () => import('@/components/template/pages/list.vue'),
          meta: { activeMenu: 'list', expanded: 'book' },
        },
        {
          path: '/pages/detail',
          name: 'detail',
          component: () => import('@/components/template/pages/detail.vue'),
          meta: { activeMenu: 'list', expanded: 'book' },
        },
        {
          path: '/pages/taskCenter',
          name: 'taskCenter',
          component: () => import('@/components/template/pages/taskCenter.vue'),
          meta: { activeMenu: 'taskCenter', expanded: 'book' },
        },
        {
          path: '/pages/chapter',
          name: 'chapter',
          component: () => import('@/components/template/pages/chapter.vue'),
          meta: { activeMenu: 'chapter', expanded: 'book' },
        },
        {
          path: '/pages/backup',
          name: 'backup',
          component: () => import('@/components/template/pages/backup.vue'),
          meta: { activeMenu: 'backup', expanded: 'book' },
        },
        {
          path: '/pages/bookApprove',
          name: 'bookApprove',
          component: () =>
            import('@/components/template/pages/bookApprove.vue'),
          meta: { activeMenu: 'backup', expanded: 'book' },
        },
        {
          path: '/pages/bookApproveRelease',
          name: 'bookApproveRelease',
          component: () =>
            import('@/components/template/pages/bookApproveRelease.vue'),
          meta: { activeMenu: 'backup', expanded: 'book' },
        },
        {
          path: '/pages/processDetail',
          name: 'processDetail',
          component: () =>
            import('@/components/template/pages/processDetail.vue'),
          meta: { activeMenu: 'backup', expanded: 'book' },
        },
        {
          path: '/pages/approvePage',
          name: 'approvePage',
          component: () =>
            import('@/components/template/components/approvePage.vue'),
          meta: { activeMenu: 'chapter', expanded: 'book' },
        },
        {
          path: '/resourceLibrary/myResource',
          name: 'myResource',
          component: () =>
            import('@/components/template/resourceLibrary/myResource.vue'),
          meta: { activeMenu: 'myResource', expanded: 'resourceLibrary' },
        },
        {
          path: '/resourceLibrary/textbook',
          name: 'textbook',
          component: () =>
            import('@/components/template/resourceLibrary/textbook.vue'),
          meta: { activeMenu: 'textbook', expanded: 'resourceLibrary' },
        },

        {
          path: '/resourceLibrary/recycleBin',
          name: 'recycleBin',
          component: () =>
            import(
              '@/components/template/resourceLibrary/pages/recycleBin.vue'
            ),
          meta: { activeMenu: 'myResource', expanded: 'resourceLibrary' },
        },
        {
          path: '/resourceLibrary/userQuestionRecycleBin',
          name: 'userQuestionRecycleBin',
          component: () =>
            import('@/components/template/resourceLibrary/recycle.vue'),
          meta: { activeMenu: 'userQuestion', expanded: 'resourceLibrary' },
        },
        {
          path: '/resourceLibrary/myPsychologyHealth',
          name: 'myPsychologyHealth',
          component: () =>
            import('@/components/template/resourceLibrary/myPsychologyHealth/index.vue'),
          meta: { activeMenu: 'myPsychologyHealth', expanded: 'resourceLibrary' },
        },
        {
          path: '/resourceLibrary/userQuestion',
          name: 'userQuestion',
          component: () =>
            import('@/components/template/resourceLibrary/userQuestion.vue'),
          meta: { activeMenu: 'userQuestion', expanded: 'resourceLibrary' },
        },
        {
          path: '/resource/bookQuestion',
          name: 'bookQuestion',
          component: () =>
            import(
              '@/components/template/resourceLibrary/bookQuestion/index.vue'
            ),
          meta: { activeMenu: 'userQuestion', expanded: 'resourceLibrary' },
        },
        {
          path: '/resource/bookQuestionRecycle',
          name: 'bookQuestionRecycle',
          component: () =>
            import(
              '@/components/template/resourceLibrary/bookQuestion/recycle.vue'
            ),
          meta: { activeMenu: 'userQuestion', expanded: 'resourceLibrary' },
        },
        {
          path: '/pages/pushTextBooks',
          name: 'pushTextBooks',
          component: () =>
            import('@/components/template/pages/pushTextBooks.vue'),
          meta: { activeMenu: 'list', expanded: 'book' },
        },
        {
          path: '/user/userInfo',
          name: 'userInfo',
          component: () => import('@/views/user/userinfo.vue'),
          meta: { activeMenu: 'list', expanded: 'book' },
        },
        {
          path: '/resourceLibrary/paper',
          name: 'paper',
          component: () => import('@/components/template/paper/index.vue'),
          meta: { activeMenu: 'paper', expanded: 'resourceLibrary' },
        },
        {
          path: '/resourceLibrary/paper/edit',
          name: 'paperEdit',
          component: () => import('@/components/template/paper/edit.vue'),
          meta: { activeMenu: 'paper', expanded: 'resourceLibrary' },
        },
        {
          path: '/resourceLibrary/paper/recycle',
          name: 'paperRecycle',
          component: () => import('@/components/template/paper/recycle.vue'),
          meta: { activeMenu: 'paper', expanded: 'resourceLibrary' },
        },
        {
          path: '/message/myMessage',
          name: 'myMessage',
          component: () => import('../views/message/myMessage/index.vue'),
          meta: { activeMenu: 'myMessage', expanded: 'message' },
        },
      ],
    },
    {
      path: '/bookPreview',
      name: 'bookPreview',
      component: () => import('../views/book/bookPreview/index.vue'),
    },
    {
      path: '/chapterDetail',
      name: 'chapterDetail',
      component: () => import('../views/book/chapterDetail/index.vue'),
    },
  ],
})

export default router
