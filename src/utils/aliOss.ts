import { uploadResource } from '@/api/book/bookResource.js'
import { uploadFile } from '@/api/file'

export const OssService = async (
  file: any,
  { progressCallback = null, folderId = null, hash = null} = {},
) => {
  if (!file) {
    return Promise.reject('请选择文件')
  }
  const route: any = getEditorParams()
  const formData = new FormData()
  formData.append('file', file)
  try {
    const data = await uploadFile(formData)
    // 编辑器资源需要保存资源库
    if (route.chapterId) {
      const bookRes = {
        fileName: file.name,
        fileUrl: data.data.url,
        fileSize: file.size,
        chapterId: route.chapterId,
        folderId: folderId,
        hash: hash
      }
      try {
        uploadResource(bookRes)
      } catch (err) {
        console.error("上传资源到资源库失败：",err)
      }
    }
    return { url: data.data.url, result: data, originName: file.name }
  } catch (err) {
    console.error("上传文件失败：",err)
    return Promise.reject(err)
  }
}

function getEditorParams() {
  const url = window.location.href
  let hashPart = url
  if (hashPart.includes('/editor?')) {
    hashPart = hashPart.split('?')[1]
    const paramPairs = hashPart.split('&')
    const result = {}
    paramPairs.forEach((pair) => {
      const parts = pair.split('=')
      if (parts.length === 2) {
        result[parts[0]] = parts[1]
      }
    })
    return result
  }
  return {}
}
