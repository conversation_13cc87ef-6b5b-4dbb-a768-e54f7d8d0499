/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2025-02-05 13:32:32
 * @LastEditTime: 2025-02-25 11:16:23
 * @FilePath: \dutp-editor\src\utils\chapterEditWebSocket.js
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import { getToken } from '@/request/token.js'
export default class ChapterEditWebSocket {
  constructor() {
    // WebSocket 实例
    this.socket = null
    // 自定义事件处理器
    this.eventHandlers = {}
  }

  /**
   * 初始化 WebSocket 连接
   */
  connect() {
    const url = import.meta.env.VITE_APP_WS_BASE_API + `/chapterContentEditor`
    this.socket = new WebSocket(url, [getToken()])

    // 设置携带 cookie
    this.socket.binaryType = 'blob'

    // 监听连接成功事件
    this.socket.onopen = () => {
      console.log('WebSocket 连接已建立')
      this.triggerEvent('open')
    }

    // 监听消息事件
    this.socket.onmessage = (event) => {
      const message = JSON.parse(event.data)
      // console.log('收到消息:', message)

      // 根据消息类型触发对应事件
      // const type = message.resultCode
      this.triggerEvent('message', message)
    }

    // 监听连接关闭事件
    this.socket.onclose = (event) => {
      // console.log('WebSocket 连接已关闭:', event)
      this.triggerEvent('close', event)
    }

    // 监听错误事件
    this.socket.onerror = (error) => {
      // console.error('WebSocket 发生错误:', error)
      this.triggerEvent('error', error)
    }
  }

  /**
   * 关闭 WebSocket 连接
   */
  disconnect() {
    if (this.socket) {
      this.socket.close()
      console.log('WebSocket 连接已手动关闭')
    }
  }

  /**
   * 发送消息到后端
   * @param {Object} message 消息对象
   */
  sendMessage(message) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message))
      // console.log('消息已发送:', message)
    } else {
      console.error('WebSocket 未连接，无法发送消息:', message)
    }
  }

  /**
   * 添加自定义事件监听
   * @param {string} type 消息类型
   * @param {Function} handler 消息处理函数
   */
  on(type, handler) {
    if (!this.eventHandlers[type]) {
      this.eventHandlers[type] = []
    }
    this.eventHandlers[type].push(handler)
  }

  /**
   * 触发事件
   * @param {string} type 消息类型
   * @param {Object} data 消息数据
   */
  triggerEvent(type, data) {
    const handlers = this.eventHandlers[type]
    if (handlers) {
      handlers.forEach((handler) => handler(data))
    }
  }
}
