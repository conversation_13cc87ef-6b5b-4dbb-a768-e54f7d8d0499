/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-12-04 15:53:54
 * @LastEditTime: 2024-12-10 11:27:37
 * @FilePath: \dutp-editor\src\utils\createRichText.js
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import html2canvas from 'html2canvas';
import { uuid } from '@/utils/quetions-utils'
/**需要保留到行内的样式*/
const retainStyle = [
  'background',
  'background-color',
  'background-image',
  'background-origin',
  'background-position',
  'background-position-x',
  'background-position-y',
  'background-repeat',
  'background-size',
  'border',
  'border-bottom',
  'border-bottom-color',
  'border-bottom-left-radius',
  'border-bottom-right-radius',
  'border-bottom-style',
  'border-bottom-width',
  'border-collapse',
  'border-color',
  'border-inline',
  'border-inline-color',
  'border-inline-end',
  'border-inline-end-color',
  'border-inline-end-style',
  'border-inline-end-width',
  'border-inline-start',
  'border-inline-start-color',
  'border-inline-start-style',
  'border-inline-start-width',
  'border-inline-style',
  'border-inline-width',
  'border-left',
  'border-left-color',
  'border-left-style',
  'border-left-width',
  'border-radius',
  'border-right',
  'border-right-color',
  'border-right-style',
  'border-right-width',
  'border-style',
  'border-top',
  'border-top-color',
  'border-top-left-radius',
  'border-top-right-radius',
  'border-top-style',
  'border-top-width',
  'border-width',
  'box-shadow',
  'box-sizing',
  'color',
  'column-count',
  'column-fill',
  'column-gap',
  'column-rule',
  'column-rule-color',
  'column-rule-style',
  'column-rule-width',
  'column-span',
  'column-width',
  "display",
  'flex',
  'flex-direction',
  'flex-flow',
  'flex-grow',
  'flex-shrink',
  'flex-wrap',
  'float',
  'font-family',
  'font-size',
  'font-style',
  'font-weight',
  'height',
  'max-height',
  'min-height',
  'justify-content',
  'justify-items',
  'justify-self',
  'left',
  'line-height',
  'list-style',
  'list-style-image',
  'list-style-position',
  'list-style-type',
  'margin-bottom',
  'margin-left',
  'margin-right',
  'margin-top',
  'object-fit',
  'outline-color',
  'outline-offset',
  'outline-style',
  'outline-width',
  'overflow',
  'overflow-x',
  'overflow-y',
  'padding',
  'padding-bottom',
  'padding-left',
  'padding-right',
  'padding-top',
  'right',
  'text-align',
  'text-decoration',
  'text-shadow',
  "text-overflow",
  'top',
  'width',
  "white-space",
  "position"
]

/**需要替换成图片的自定义节点的id名称*/
const replaceIdName = [
  '__audio__',
  '__video__',
  "__interaction__",//词云
  "__resourceCover__"
]

/**异步执行标签转图片的的任务计数*/
class CountTaskTool {
  constructor() {
    this.taskLs = {}
  }
  init() {
    this.taskLs = {}
  }
  addTask(key) {
    this.taskLs[key] = false
  }
  complete(key) {
    this.taskLs[key] = true
  }
  run(callback) {
    let timer = setInterval(() => {
      if (!Object.values(this.taskLs).includes(false)) {
        callback && callback()
        clearInterval(timer)
      }
    })
  }

  /**自定义导出word逻辑的需要再人物队列里等待*/
  async awaitTask(callback) {
    const taskKey = uuid()
    this.addTask(taskKey)
    await callback()
    task.complete(taskKey)
  }

  /**标签解析成图片或者word的时候,img标签sec为中文路径不识别需要转换为base64*/
  async urlToBase64(url) {
    const response = await fetch(url);
    if (!response.ok) {
      return url
    }
    const blob = await response.blob();
    return await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = () => reject('失败');
      reader.readAsDataURL(blob);
    });
  }
}
const task = new CountTaskTool()

const createNode = (nodeName, content) => {
  if (!content) {
    return document.createElement(nodeName)
  } else {
    const DIV = document.createElement(nodeName)
    DIV.innerHTML = content
    return DIV
  }
}

const clearPX = (str) => {
  return str.replace(new RegExp('px', 'g'), '')
}

/**需要特殊处理的标签*/
const special = async (element, parentNode) => {

  if (element.localName === 'img') {
    task.awaitTask(async () => {
      element.height = clearPX(parentNode.style.height)
      element.src = await task.urlToBase64(element.src)
    })

  } else if (element.id === '__audio__') {
    task.awaitTask(async () => {
      const duration = element.getAttribute('duration')
      const title = element.getAttribute('title')
      const src = element.firstChild.src
      const audioPanel = ` <div style="padding-bottom: 5px;box-sizing: border-box;background-color: #f6f6f6;">
      <div
        style="display: flex;justify-content: space-between;align-items: center;height: 50px;background-color: #cecccc;padding: 0 20px;box-sizing: border-box;border-radius: 50px;">
        <section style="margin-top: 3px;"><svg t="1733447005762" class="icon" viewBox="0 0 1024 1024" version="1.1"
            xmlns="http://www.w3.org/2000/svg" p-id="4281" width="15" height="15">
            <path d="M896 512L128 1024V0z" p-id="4282"></path>
          </svg></section>
        <section style="margin-left: 15px;">
          0:00/${duration}
        </section>
        <section style="flex:1;height: 2px;background-color: #000;border-radius: 2px;margin: 0 20px;">
        </section>
        <section>
          <svg t="1733447097684" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
            p-id="5272" width="15" height="15">
            <path
              d="M218.495828 334.609871c-0.390903-0.019443-0.773619-0.019443-1.164522-0.019443L100.022553 334.590428c-21.744233 0-39.087227 17.448394-39.087227 39.001269l0 273.866415c0 21.551852 17.505699 38.999223 39.087227 38.999223l117.308753 0c0.057305 0 0.113587 0 0.171915 0l0 0.153496 287.22056 185.975668c6.824429 5.842055 15.691377 9.354042 25.370831 9.354042 21.590737 0 39.096437-17.505699 39.096437-39.095413 0-1.794879-0.124843-3.551896-0.354064-5.270027L568.836985 183.473685c0.229221-1.718131 0.354064-3.475148 0.354064-5.269004 0-21.590737-17.505699-39.096437-39.096437-39.096437-8.895601 0-17.105586 2.977821-23.682375 7.979742L218.495828 334.609871z"
              fill="#000" p-id="5273"></path>
            <path
              d="M757.858012 953.491133l0.085958 0.075725c123.876332-107.514689 202.211445-266.13329 202.211445-443.041442 0-177.214121-78.603219-336.062965-202.851011-443.61654l-0.11461 0.11461c-4.963035-3.817955-11.17655-6.109138-17.925255-6.109138-16.197914 0-29.322839 13.133112-29.322839 29.321816 0 6.757914 2.28095 12.981662 6.109138 17.926278l-0.333598 0.342808c0.821715 0.706081 1.641383 1.393743 2.462075 2.119267 1.173732 1.202385 2.452865 2.329045 3.817955 3.321652 110.054535 96.710622 179.51349 238.550071 179.51349 396.578224 0 158.02713-69.458955 299.866578-179.51349 396.577201-1.36509 0.99363-2.644223 2.118244-3.817955 3.321652-0.600681 0.533143-1.212618 1.048889-1.822508 1.564635l0.229221 0.230244c-4.152577 5.058203-6.643304 11.530614-6.643304 18.593474 0 16.188704 13.124925 29.321816 29.322839 29.321816C746.317165 960.134437 752.798786 957.651896 757.858012 953.491133z"
              fill="#000" p-id="5274"></path>
            <path
              d="M713.998085 729.35433l0.23843 0.24764c55.380308-56.43022 89.532129-133.76454 89.532129-219.077577 0-85.409229-34.228569-162.800853-89.704045-219.249493l-0.268106 0.267083c-4.886287-3.64604-10.966773-5.821589-17.543561-5.821589-16.197914 0-29.322839 13.133112-29.322839 29.321816 0 6.566556 2.166339 12.657274 5.822612 17.544585l-0.162706 0.170892c0.773619 0.782829 1.547239 1.584078 2.310625 2.38635 0.075725 0.076748 0.152473 0.171915 0.23843 0.248663 43.3626 45.587268 69.983911 107.248629 69.983911 175.132716 0 67.884087-26.621311 129.544425-69.983911 175.131693-0.085958 0.077771-0.162706 0.171915-0.23843 0.24764-0.706081 0.74599-1.422396 1.471514-2.13871 2.214435l0.144286 0.134053c-3.751441 4.926196-5.976108 11.092639-5.976108 17.754363 0 16.188704 13.124925 29.321816 29.322839 29.321816C702.925912 735.328391 709.072913 733.113957 713.998085 729.35433z"
              fill="#000" p-id="5275"></path>
          </svg>
        </section>
        <section>
          <svg t="1733447123283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
            p-id="6288" width="15" height="15">
            <path d="M512 298.6496a85.3504 85.3504 0 1 0 0-170.6496 85.3504 85.3504 0 0 0 0 170.6496z" fill="#000"
              p-id="6289"></path>
            <path d="M512 512m-85.3504 0a85.3504 85.3504 0 1 0 170.7008 0 85.3504 85.3504 0 1 0-170.7008 0Z" fill="#000"
              p-id="6290"></path>
            <path d="M512 896a85.3504 85.3504 0 1 0 0-170.7008 85.3504 85.3504 0 0 0 0 170.7008z" fill="#000" p-id="6291">
            </path>
          </svg>
        </section>
      </div>
      ${title ? '<div style="text-align: center;">' + title + '</div>' : ''}
    </div>`
      element.innerHTML = audioPanel
      const canvas = await html2canvas(element)
      const dataUrl = canvas.toDataURL('image/png');
      element.insertAdjacentHTML('afterend', `<a href='${src}'><img src='${dataUrl}'height='${title ? 74 : 47}' /></a>`)
      element.remove()
    })

  } else if (element.id === '__video__') {
    task.awaitTask(async () => {
      const h = element.style.height
      const w = element.style.width
      const title = element.getAttribute('title')
      const src = element.getAttribute('src')
      const theFirstFrame = element.getAttribute('theFirstFrame')
      const videoPanel = `<div style="background-color: #000;position: relative;overflow: hidden;height:${h};">
    <div style="flex: 1;height:100%;text-align: center;">
      <img src="${theFirstFrame}" alt="" style="width:100%;height:${h};" >
    </div>
    <div
      style="display: flex;justify-content: space-between;align-items: center;padding: 0 10px;box-sizing: border-box;height: 45px;width: 100%;position: absolute;bottom: 20px;background: linear-gradient(to top, rgba(0,0,0,0.5),transparent );">
      <section style="margin-top: 3px;"><svg t="1733447005762" class="icon" viewBox="0 0 1024 1024" version="1.1"
          xmlns="http://www.w3.org/2000/svg" p-id="4281" width="15" height="15">
          <path d="M896 512L128 1024V0z" p-id="4282" fill="#f6f6f6"></path>
        </svg></section>
      <section style="margin-left: 15px;color: #f6f6f6;font-size: 14px;">
        0:00/00:16
      </section>
      <section style="flex:1;border-radius: 2px;margin: 0 20px;">
      </section>
      <section style="width: 30px;">
        <svg t="1733447097684" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="5272" width="15" height="15">
          <path
            d="M218.495828 334.609871c-0.390903-0.019443-0.773619-0.019443-1.164522-0.019443L100.022553 334.590428c-21.744233 0-39.087227 17.448394-39.087227 39.001269l0 273.866415c0 21.551852 17.505699 38.999223 39.087227 38.999223l117.308753 0c0.057305 0 0.113587 0 0.171915 0l0 0.153496 287.22056 185.975668c6.824429 5.842055 15.691377 9.354042 25.370831 9.354042 21.590737 0 39.096437-17.505699 39.096437-39.095413 0-1.794879-0.124843-3.551896-0.354064-5.270027L568.836985 183.473685c0.229221-1.718131 0.354064-3.475148 0.354064-5.269004 0-21.590737-17.505699-39.096437-39.096437-39.096437-8.895601 0-17.105586 2.977821-23.682375 7.979742L218.495828 334.609871z"
            fill="#f6f6f6" p-id="5273"></path>
          <path
            d="M757.858012 953.491133l0.085958 0.075725c123.876332-107.514689 202.211445-266.13329 202.211445-443.041442 0-177.214121-78.603219-336.062965-202.851011-443.61654l-0.11461 0.11461c-4.963035-3.817955-11.17655-6.109138-17.925255-6.109138-16.197914 0-29.322839 13.133112-29.322839 29.321816 0 6.757914 2.28095 12.981662 6.109138 17.926278l-0.333598 0.342808c0.821715 0.706081 1.641383 1.393743 2.462075 2.119267 1.173732 1.202385 2.452865 2.329045 3.817955 3.321652 110.054535 96.710622 179.51349 238.550071 179.51349 396.578224 0 158.02713-69.458955 299.866578-179.51349 396.577201-1.36509 0.99363-2.644223 2.118244-3.817955 3.321652-0.600681 0.533143-1.212618 1.048889-1.822508 1.564635l0.229221 0.230244c-4.152577 5.058203-6.643304 11.530614-6.643304 18.593474 0 16.188704 13.124925 29.321816 29.322839 29.321816C746.317165 960.134437 752.798786 957.651896 757.858012 953.491133z"
            fill="#f6f6f6" p-id="5274"></path>
          <path
            d="M713.998085 729.35433l0.23843 0.24764c55.380308-56.43022 89.532129-133.76454 89.532129-219.077577 0-85.409229-34.228569-162.800853-89.704045-219.249493l-0.268106 0.267083c-4.886287-3.64604-10.966773-5.821589-17.543561-5.821589-16.197914 0-29.322839 13.133112-29.322839 29.321816 0 6.566556 2.166339 12.657274 5.822612 17.544585l-0.162706 0.170892c0.773619 0.782829 1.547239 1.584078 2.310625 2.38635 0.075725 0.076748 0.152473 0.171915 0.23843 0.248663 43.3626 45.587268 69.983911 107.248629 69.983911 175.132716 0 67.884087-26.621311 129.544425-69.983911 175.131693-0.085958 0.077771-0.162706 0.171915-0.23843 0.24764-0.706081 0.74599-1.422396 1.471514-2.13871 2.214435l0.144286 0.134053c-3.751441 4.926196-5.976108 11.092639-5.976108 17.754363 0 16.188704 13.124925 29.321816 29.322839 29.321816C702.925912 735.328391 709.072913 733.113957 713.998085 729.35433z"
            fill="#f6f6f6" p-id="5275"></path>
        </svg>
      </section>
      <section style="width: 30px;">
        <svg t="1733462885096" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="4261" width="15" height="15">
          <path
            d="M181 357.5V181.2h176.4c14.3 0 25.9-11.6 25.9-25.9v-31.1c0-14.3-11.6-25.9-25.9-25.9H118c-11 0-20 9-20 20v239.4c0 14.3 11.6 25.9 25.9 25.9H155c14.4-0.1 26-11.7 26-26.1zM668.6 181.2H845v176.4c0 14.3 11.6 25.9 25.9 25.9H902c14.3 0 25.9-11.6 25.9-25.9V118.2c0-11-9-20-20-20H668.6c-14.3 0-25.9 11.6-25.9 25.9v31.1c0 14.3 11.6 26 25.9 26zM357.4 845.2H181V668.8c0-14.3-11.6-25.9-25.9-25.9H124c-14.3 0-25.9 11.6-25.9 25.9v239.4c0 11 9 20 20 20h239.4c14.3 0 25.9-11.6 25.9-25.9v-31.1c-0.1-14.4-11.7-26-26-26zM845 668.8v176.4H668.6c-14.3 0-25.9 11.6-25.9 25.9v31.1c0 14.3 11.6 25.9 25.9 25.9H908c11 0 20-9 20-20V668.8c0-14.3-11.6-25.9-25.9-25.9H871c-14.4 0-26 11.6-26 25.9z"
            p-id="4262" fill="#f6f6f6"></path>
        </svg>
      </section>
      <section style="width: 30px;">
        <svg t="1733447123283" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="6288" width="15" height="15">
          <path d="M512 298.6496a85.3504 85.3504 0 1 0 0-170.6496 85.3504 85.3504 0 0 0 0 170.6496z" fill="#f6f6f6"
            p-id="6289"></path>
          <path d="M512 512m-85.3504 0a85.3504 85.3504 0 1 0 170.7008 0 85.3504 85.3504 0 1 0-170.7008 0Z"
            fill="#f6f6f6" p-id="6290"></path>
          <path d="M512 896a85.3504 85.3504 0 1 0 0-170.7008 85.3504 85.3504 0 0 0 0 170.7008z" fill="#f6f6f6"
            p-id="6291">
          </path>
        </svg>
      </section>
    </div>
    <div
      style="width: calc(100% - 40px);margin: 10px; position: absolute;bottom: -15px;height: 30px;">
      <section style="height: 4px;border-radius: 4px;background-color: #eee;">
      </section>
    </div>
    ${title ? '<div style="width:100%;text-align: center;color: #fff;position: absolute;z-index: 1;bottom: 0;">' + title + '</div>' : ''}
  </div>
`
      element.innerHTML = videoPanel
      const canvas = await html2canvas(element)
      const dataUrl = canvas.toDataURL('image/png');
      element.insertAdjacentHTML('afterend', `<a href='${src}'><img src='${dataUrl}' height='${clearPX(h)}' width='${clearPX(w) * 0.83}' /></a>`)
      element.remove()
    })
  } else if (element.id === '__interaction__') {
    task.awaitTask(async () => {
      const w = element.style.width
      const canvas = await html2canvas(element)
      const dataUrl = canvas.toDataURL('image/png');
      element.insertAdjacentHTML('afterend', `<img src='${dataUrl}' width='${clearPX(w) * 0.86}' />`)
      element.remove()
    })
  } else if (element.id === '__resourceCover__') {
    task.awaitTask(async () => {
      const src = element.getAttribute('src')
      const base64Url = await task.urlToBase64(src)
      const title = element.getAttribute('title')
      const fileUrl = element.getAttribute('fileUrl')
      const showTitle = element.getAttribute('showTitle')
      const resourceCoverPanel = `
      <a href='${fileUrl}'><img src="${base64Url}" width='625' height='200'
    alt="">
    ${showTitle == 'true' ? '<div style="text-align: center;">' + title + '</div>' : ''}
    </a>
    `
      element.innerHTML = resourceCoverPanel
    })

  }
}
let timer = null
const lineStyle = (element, parentNode, callback) => {
  if (element.nodeName !== '#text' && element.nodeName !== '#comment') {
    const allStyle = window.getComputedStyle(element)
    let styleText = ''
    for (let i = 0; i < allStyle.length; i++) {
      const key = allStyle[i]
      if (retainStyle.includes(key) && allStyle.getPropertyValue(key)) {
        styleText += `${key}:${allStyle.getPropertyValue(key)};`
      }
    }
    element.style = styleText
    special(element, parentNode)
  }

  if (element.childNodes.length && !replaceIdName.includes(element.id)) {
    Array.from(element.childNodes).forEach((ele) => {
      lineStyle(ele, element, callback)
    })
  }

  if (timer) clearTimeout(timer)
  timer = setTimeout(() => {
    callback()
  }, 100)

  return element
}

export const getInnerHtml = (element) => {
  return new Promise((resolve) => {
    task.init()
    const DIV = createNode('div')
    Array.from(element).forEach((ele) => {
      DIV.appendChild(lineStyle(ele, false, () =>
        task.run(() => resolve(DIV.innerHTML))
      ))
    })
  })
}
