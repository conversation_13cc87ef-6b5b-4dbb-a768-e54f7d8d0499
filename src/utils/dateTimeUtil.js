// 日期格式化v2: 2025年01月08日 星期五 8:00 上午
export function dateFormatter(date) {
  try {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    if (date) {
      // 获取年份、月份和日期
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      // 获取星期几
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      const weekday = weekdays[date.getDay()];
      // 获取小时、分钟和上午/下午
      const hours = date.getHours();
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const ampm = hours < 12 ? '上午' : '下午';
      const displayHours = hours % 12 || 12; // 转换为12小时制
      // 格式化时间
      const time = `${displayHours}:${minutes} ${ampm}`;
      // 返回格式化的日期字符串
      return `${year}年${month}月${day}日 ${weekday} ${time}`;
    }
  } catch (error) {
    return '';
  }
}

export function dateFormatterHSap(date) {
  try {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    if (date) {
      // 获取小时、分钟和上午/下午
      const hours = date.getHours();
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const ampm = hours < 12 ? 'am' : 'pm';
      const displayHours = hours % 12 || 12; // 转换为12小时制
      // 格式化时间
      const time = `${displayHours}:${minutes} ${ampm}`;
      // 返回格式化的日期字符串
      return `${time}`;
    }
  } catch (error) {
    return '';
  }
}

// 获取当前时间  yyyy-MM-dd HH:mm:ss
export function getNowTime() {
  const date = new Date();
  // 转换为北京时间（UTC+8）
  const localTime = new Date(date.getTime() + date.getTimezoneOffset() * 60000 + 3600000 * 8);
  return `${localTime.getFullYear()}-${(localTime.getMonth()+1).toString().padStart(2, '0')}-${localTime.getDate().toString().padStart(2, '0')} ${localTime.getHours().toString().padStart(2, '0')}:${localTime.getMinutes().toString().padStart(2, '0')}:${localTime.getSeconds().toString().padStart(2, '0')}`;
}
