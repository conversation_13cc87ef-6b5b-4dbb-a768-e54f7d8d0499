import * as XLSX from 'xlsx'
import { checkResourceIsExists } from '@/api/resource/userResource.js'
import { OssService } from '@/utils/aliOss'
import { DialogPlugin } from 'tdesign-vue-next';
const { loading } = useStore()
const fileTypes: Record<string, string[]> = {
  ai: ['ai', 'eps'],
  app: ['app'],
  axure: ['rp'],
  // prettier-ignore
  book: ['mobi', 'oeb', 'lit', 'xeb', 'ebx', 'rb', 'pdb', 'epub', 'azw3', 'hlp', 'chm', 'wdl', 'ceb', 'abm', 'pdg', 'caj'],
  css: ['css', 'less', 'sass'],
  dmg: ['dmg'],
  excel: [
    'csv',
    'fods',
    'ods',
    'ots',
    'xls',
    'xlsm',
    'xlsx',
    'xlt',
    'xltm',
    'xltx',
    'et',
    'ett',
  ],
  exe: ['exe'],
  html: ['htm', 'html', 'mht'],
  // prettier-ignore
  img: ['png', 'bmp', 'jpg', 'jpeg', 'gif', 'webp', 'tga', 'exif', 'fpx', 'svg', 'hdri', 'raw', 'ico', 'jfif', 'dib', 'pbm', 'pgm', 'ppm', 'rgb'],
  java: ['jar', 'java'],
  js: ['js', 'jsx', 'ts', 'tsx'],
  json: ['json'],
  keynote: ['key'],
  md: ['md', 'markdown'],
  // prettier-ignore
  music: ['au', 'aif', 'aiff', 'aifc', 'rmi', 'mp3', 'mid', 'cda', 'wav', 'wma', 'ra', 'ram', 'snd', 'mida', 'ogg', 'ape', 'flac', 'aac'],
  numbers: ['numbers'],
  pages: ['pages'],
  pdf: ['pdf'],
  php: ['php'],
  pkg: ['pkg'],
  // prettier-ignore
  ppt: ['fodp', 'odp', 'otp', 'pot', 'potm', 'potx', 'pps', 'ppsm', 'ppsx', 'ppt', 'pptm', 'pptx', 'dps', 'dpt', 'wpp'],
  psd: ['psd'],
  python: ['python'],
  sh: ['sh'],
  sketch: ['sketch'],
  sql: ['sql'],
  text: ['text', 'txt'],
  video: ['mp4', 'avi', 'mov', 'rmvb', 'rm', 'flv', 'mpeg', 'wmv', 'mkv'],
  vue: ['vue'],
  // prettier-ignore
  word: ['doc', 'docm', 'docx', 'dot', 'dotm', 'dotx', 'epub', 'fodt', 'odt', 'ott', 'rtf', 'wps', 'wpt'],
  xmind: ['xmind'],
  // prettier-ignore
  zip: ['zip', 'rar', 'tar', 'gz', 'gzip', 'uue', 'bz2', 'iso', '7z', 'z', 'ace', 'lzh', 'arj', 'cab'],
  font: ['eot', 'otf', 'fon', 'font', 'ttf', 'ttc', 'woff', 'woff2'],
}

export const getFileExtname = (filename: string) => {
  const splitFileName = filename.split('.')
  return splitFileName[splitFileName.length - 1]
}

export const getFileIcon = (filename: string) => {
  const extname = getFileExtname(filename)
  for (const type of Object.keys(fileTypes)) {
    if (fileTypes[type].includes(extname)) {
      return type
    }
  }
  return 'common'
}

export const readExcelFile = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook: any = XLSX.read(data, { type: 'array' })
        resolve(workbook)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = (error) => reject(error)
    reader.readAsArrayBuffer(file)
  })
}

export const chooseFile = (
  optCallback: (file: any) => void,
  { multiple = false, fileType = '.jpg,.png,.jpeg,.gif', fileSize = 20, optPreChekck = null } = {},
) => {
  const accept = fileType
  const { open, onChange } = useFileDialog({
    accept,
    reset: true,
    multiple,
  })
  open()
  onChange(async (fileList) => {
    const files = Array.from(fileList ?? [])
    if (!files) {
      return
    }
    if (multiple) {
      const returnFiles: any[] = []
      loading.value = true
      // 多选
      for (let i = 0; i < files.length; i++) {
        let file = files[i]
        const { size, name: filename } = file
        const suffix = getFileExtension(filename) ?? 'png'
        // 文件类型
        if (fileType != '*' && !fileType.includes(`.${suffix}`)) {
          loading.value = false
          useMessage('error', '文件类型不正确')
          return false
        }

        // 文件大小
        if (size > fileSize * 1024 * 1024) {
          loading.value = false
          useMessage('error', `文件大小不能超过${fileSize}MB`)
          return false
        }
        let tHash = null
        let tAllow = true
        let tUrl = null
        let tName = null
        if (optPreChekck) {
          const { allow = true, hash, url } = (await optPreChekck(file)) || {}
          tHash = hash
          tAllow = allow
          tUrl = url
          tName = getFileName(url)
        }
        // console.log("hash=>",tHash, tAllow)
        if (tAllow) {
          const res: any = await OssService(file, {
            hash: tHash,
          })
          tUrl = res.url
          tName = res.result.data.name
        }

        returnFiles.push({
            fileUrl: tUrl,
            fileSize: size,
            fileName: tName,
            originName: filename,
          })
      }
      loading.value = false
      optCallback && optCallback(returnFiles)
    } else {
      // 单选
      const file = files[0]
      const { size, name: filename } = file
      const suffix = getFileExtension(filename) ?? 'png'
      // 文件类型
      if (fileType != '*' && !fileType.includes(`.${suffix}`)) {
        useMessage('error', '文件类型不正确')
        return false
      }

      // 文件大小
      if (size > fileSize * 1024 * 1024) {
        useMessage('error', `文件大小不能超过${fileSize}MB`)
        return false
      }
      loading.value = true
      let tHash = null
      let tAllow = true
      let tUrl = null
      let tName = null
      if (optPreChekck) {
        const { allow , hash, url } = (await optPreChekck(file)) || {}
        tHash = hash
        tAllow = allow
        tUrl = url
        tName = getFileName(url)
      }
      // console.log("hash=>",tHash)
      if (tAllow) {
        const res: any = await OssService(file, {
          hash: tHash,
        })
        tUrl = res.url
        tName = res.result.data.name 
      }
      loading.value = false
      optCallback &&
          optCallback({
            fileUrl: tUrl,
            fileSize: size,
            fileName: tName,
            originName: filename,
          })
    }
  })
}

// 默认的重复上传检测函数
export const defaultOptPreChekck = async (
  file,
  { confirmBtn = '使用该图', cancelBtn = '重新上传' } = {},
) => {
  const formData = new FormData()
  formData.append('file', file)
  let r = {
    allow: true,
    hash: null,
    url: null,
  }
  try {
    const res = await checkResourceIsExists(formData)
    r.hash = res.data.hash
    let fileUrl = res.data.url
    r.url = fileUrl
    if (res.data.exist) {
      await new Promise((resolve) => {
        const confirmDia = DialogPlugin({
          header: '重复上传提醒',
          body: (h) => {
            return h(
              'div',
              {
                style: {
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '10px',
                },
              },
              [
                h('span', null, '该文件已经上传过，是否再次上传？'),
                h('img', {
                  src: fileUrl,
                  style: {
                    width: '100px',
                    height: '100px',
                  },
                }),
              ],
            )
          },
          confirmBtn: confirmBtn,
          cancelBtn: cancelBtn,
          zIndex: 3550,
          closeBtn: null,
          closeOnEscKeydown: false,
          closeOnOverlayClick: false,
          onConfirm: ({ e }) => {
            r.allow = false
            resolve(true)
            confirmDia.destroy()
          },
          onCancel: ({ e, trigger }) => {
            r.allow = true
            resolve(false)
            confirmDia.destroy()
          },
        })
      })
    } else {
      r.allow = true
    }
  } catch (error) {
    console.error(error)
  }
  // console.log("r->", r)
  return r
}

// 获取文件扩展名
export function getFileExtension(filename) {
  const basename = filename.split(/[/\\]/).pop()
  const lastDotIndex = basename.lastIndexOf('.')
  // 检查点是否存在且在末尾前
  if (lastDotIndex === -1 || lastDotIndex === basename.length - 1) {
    return 'png'
  }
  return basename.substring(lastDotIndex + 1).toLowerCase()
}

// 通过url获取文件名
function getFileName(url) {
  return url?.split('/')?.pop() || 'default.png'
}
/**
 * 将Base64编码的字符串转换为File对象。
 * 这个函数接受一个Base64编码的字符串，一个可选的文件名和一个可选的MIME类型，
 * 并返回一个代表原始数据的File对象。
 *
 * @param base64 Base64编码的字符串。
 * @param fileName 文件名，默认为"file.txt"。
 * @param mimeType MIME类型，默认为"text/plain"。
 * @returns 返回一个File对象，包含解码后的数据。
 */
export const base64ToFile = (
  base64: string,
  fileName = 'a.svg',
  mimeType = 'image/svg+xml',
) => {
  const base64String = base64.replace(/^data:.+;base64,/, '')

  const fixedBase64 = base64String.replace(/-/g, '+').replace(/_/g, '/')
  const padding = '='.repeat((4 - (fixedBase64.length % 4)) % 4)
  const validBase64 = fixedBase64 + padding

  const byteCharacters = atob(validBase64)

  // 将字节序列分割成512字节的块，并将每个块转换为Uint8Array，以便创建Blob对象。
  const byteArrays = []

  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512)
    const byteNumbers = new Array(slice.length)

    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i)
    }

    byteArrays.push(new Uint8Array(byteNumbers))
  }

  // 使用前面创建的字节数组块创建Blob对象。
  const blob = new Blob(byteArrays, { type: mimeType })

  return new File([blob], fileName, { type: mimeType })
}
