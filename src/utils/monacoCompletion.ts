// monaco-java-completion.ts

import * as monaco from 'monaco-editor'

/**
 * Java 代码补全
 */
export const javaProvider = {
  triggerCharacters: [' ', '.', '(', '='],
  provideCompletionItems: () => {
    const keywords = [
      'public',
      'private',
      'protected',
      'class',
      'interface',
      'extends',
      'implements',
      'static',
      'final',
      'void',
      'int',
      'long',
      'double',
      'float',
      'boolean',
      'char',
      'String',
      'if',
      'else',
      'switch',
      'case',
      'default',
      'for',
      'while',
      'do',
      'break',
      'continue',
      'return',
      'new',
      'this',
      'super',
      'import',
      'package',
      'try',
      'catch',
      'finally',
      'throw',
      'throws',
    ]

    const classesAndMethods = [
      {
        label: 'ArrayList',
        kind: monaco.languages.CompletionItemKind.Class,
        insertText: 'ArrayList<$1> list = new ArrayList<>();',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'Java 内置类',
      },
      {
        label: 'ArrayList.add()',
        kind: monaco.languages.CompletionItemKind.Method,
        insertText: 'list.add($1);',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '添加元素',
      },
      {
        label: 'HashMap',
        kind: monaco.languages.CompletionItemKind.Class,
        insertText: 'HashMap<$1, $2> map = new HashMap<>();',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'Java 内置类',
      },
      {
        label: 'HashMap.put()',
        kind: monaco.languages.CompletionItemKind.Method,
        insertText: 'map.put($1, $2);',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '放入键值对',
      },
      {
        label: 'System.out.println()',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'System.out.println($1);',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '打印信息到控制台',
      },
      {
        label: 'Scanner',
        kind: monaco.languages.CompletionItemKind.Class,
        insertText: 'Scanner scanner = new Scanner(System.in);',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '读取控制台输入',
      },
      {
        label: 'main方法',
        kind: monaco.languages.CompletionItemKind.Snippet,
        insertText: 'public static void main(String[] args) {\n\t$0\n}',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '主函数模板',
      },
    ]

    const suggestions = [
      ...keywords.map((keyword) => ({
        label: keyword,
        kind: monaco.languages.CompletionItemKind.Keyword,
        insertText: keyword,
        detail: 'Java 关键字',
      })),
      ...classesAndMethods,
    ]

    return { suggestions }
  },
}

/**
 * php 代码补全
 */
export const phpProvider = {
  triggerCharacters: ['$', '-', '>'],
  provideCompletionItems: () => {
    const keywords = [
      'echo',
      'print',
      'if',
      'else',
      'elseif',
      'while',
      'for',
      'foreach',
      'switch',
      'case',
      'break',
      'continue',
      'function',
      'class',
      'public',
      'private',
      'protected',
      'static',
      'var',
      'const',
      'return',
      'new',
      'try',
      'catch',
      'finally',
      'throw',
      'global',
      'isset',
      'empty',
      'array',
      'include',
      'require',
      'require_once',
      'include_once',
    ]

    const builtins = [
      {
        label: 'var_dump()',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'var_dump($1);',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '打印变量信息',
      },
      {
        label: 'print_r()',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'print_r($1);',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '打印数组信息',
      },
      {
        label: 'array()',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'array($1);',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '数组函数',
      },
    ]

    const suggestions = [
      ...keywords.map((kw) => ({
        label: kw,
        kind: monaco.languages.CompletionItemKind.Keyword,
        insertText: kw,
        detail: 'PHP 关键字',
      })),
      ...builtins,
    ]

    return { suggestions }
  },
}

export const pythonProvider = {
  triggerCharacters: ['.', ' ', '(', '='],
  provideCompletionItems: () => {
    const keywords = [
      'def',
      'class',
      'import',
      'from',
      'as',
      'if',
      'elif',
      'else',
      'for',
      'while',
      'break',
      'continue',
      'return',
      'try',
      'except',
      'finally',
      'with',
      'pass',
      'yield',
      'raise',
      'lambda',
      'global',
      'nonlocal',
      'assert',
      'del',
      'print',
      'input',
      'len',
      'range',
      'open',
      'str',
      'int',
      'float',
      'list',
      'dict',
      'set',
      'tuple',
    ]

    const suggestions = keywords.map((keyword) => ({
      label: keyword,
      kind: monaco.languages.CompletionItemKind.Keyword,
      insertText: keyword,
      detail: 'Python 关键字/内置函数',
    }))

    return { suggestions }
  },
}

export const csharpProvider = {
  triggerCharacters: [' ', '.', '(', '='],
  provideCompletionItems: () => {
    const keywords = [
      'public',
      'private',
      'protected',
      'class',
      'interface',
      'enum',
      'static',
      'void',
      'int',
      'string',
      'bool',
      'float',
      'double',
      'var',
      'if',
      'else',
      'switch',
      'case',
      'default',
      'for',
      'while',
      'do',
      'break',
      'continue',
      'return',
      'try',
      'catch',
      'finally',
      'throw',
      'using',
      'namespace',
      'new',
    ]

    const snippets = [
      {
        label: 'Console.WriteLine()',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'Console.WriteLine($1);',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '输出到控制台',
      },
      {
        label: 'Main 方法',
        kind: monaco.languages.CompletionItemKind.Snippet,
        insertText: 'static void Main(string[] args)\n{\n\t$0\n}',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '主函数',
      },
      {
        label: '类模板',
        kind: monaco.languages.CompletionItemKind.Snippet,
        insertText: 'public class $1\n{\n\t$0\n}',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'C# 类模板',
      },
    ]

    const suggestions = [
      ...keywords.map((k) => ({
        label: k,
        kind: monaco.languages.CompletionItemKind.Keyword,
        insertText: k,
        detail: 'C# 关键字',
      })),
      ...snippets,
    ]

    return { suggestions }
  },
}

export const cppProvider = {
  triggerCharacters: [' ', '.', '(', '='],
  provideCompletionItems: () => {
    const keywords = [
      'int',
      'float',
      'double',
      'char',
      'void',
      'long',
      'short',
      'unsigned',
      'signed',
      'const',
      'static',
      'volatile',
      'struct',
      'union',
      'typedef',
      'enum',
      'if',
      'else',
      'switch',
      'case',
      'default',
      'for',
      'while',
      'do',
      'break',
      'continue',
      'return',
      'include',
      'define',
      'sizeof',
    ]

    const snippets = [
      {
        label: 'printf',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'printf("$1\\n");',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '标准输出',
      },
      {
        label: 'scanf',
        kind: monaco.languages.CompletionItemKind.Function,
        insertText: 'scanf("$1", &$2);',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '标准输入',
      },
      {
        label: 'main函数',
        kind: monaco.languages.CompletionItemKind.Snippet,
        insertText: 'int main() {\n\t$0\n\treturn 0;\n}',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: 'C 主函数',
      },
      {
        label: 'struct',
        kind: monaco.languages.CompletionItemKind.Snippet,
        insertText: 'struct $1 {\n\t$2;\n};',
        insertTextRules:
          monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        detail: '结构体模板',
      },
    ]

    const suggestions = [
      ...keywords.map((k) => ({
        label: k,
        kind: monaco.languages.CompletionItemKind.Keyword,
        insertText: k,
        detail: 'C 关键字',
      })),
      ...snippets,
    ]

    return { suggestions }
  },
}
