export const bookTemplateStateOptions = [
  {
    value: '1',
    label: '启用',
  },
  {
    value: '2',
    label: '禁用',
  },
]

export const publishStatusOptions = [
  {
    value: 1,
    label: '已发行',
  },
  {
    value: 2,
    label: '未发行',
  },
]

export const bookStatusOptions = [
  {
    value: 1,
    label: '即将上市',
  },
  {
    value: 1,
    label: '已上架',
  },
  {
    value: 2,
    label: '已下架',
  },
]

export const trialApplyStatusOptions = [
  {
    value: 0,
    label: '未审核',
  },
  {
    value: 1,
    label: '已通过',
  },
  {
    value: 2,
    label: '未通过',
  },
  {
    value: 3,
    label: '已过期',
  },
]

export const publishShelfStateOptions = [
  {
    value: '1',
    label: '已上架',
  },
  {
    value: '2',
    label: '未上架',
  },
  {
    value: '3',
    label: '已回收',
  },
]

export const teacherAuthStateOptions = [
  {
    value: '0',
    label: '待审核',
  },
  {
    value: '1',
    label: '已通过',
  },
  {
    value: '2',
    label: '已拒绝',
  },
]

export const userTypeOption = [
  {
    value: '0',
    label: '读者',
  },
  {
    value: '1',
    label: '学生',
  },
  {
    value: '2',
    label: '教师',
  },
]

export const headUpType = [
  {
    value: '1',
    label: '个人',
  },
  {
    value: '2',
    label: '企业',
  },
]

export const bookTypeList = [
  {
    value: 1,
    label: '公开教材',
  },
  {
    value: 2,
    label: '校本教材',
  },
]

export const bookNatureTypeList = [
  {
    value: 1,
    label: '常规教材',
  },
  {
    value: 2,
    label: '主教材',
  },
  {
    value: 3,
    label: '副教材',
  },
]

//#region 教材相关
// 教材销售状态 上架状态 1已上架 2未上架 3召回 4即将上架
export const bookShelfStatusOptions = [
  {
    value: 1,
    label: '已上架',
  },
  {
    value: 2,
    label: '未上架',
  },
  {
    value: 3,
    label: '召回',
  },
  {
    value: 4,
    label: '即将上架',
  },
]

//公开教材出版状态 出版状态 1未出版 2已出版
export const bookPublishStatusOptions = [
  {
    value: 1,
    label: '未出版',
  },
  {
    value: 2,
    label: '已出版',
  },
]
//校本教材出版状态 出版状态 1已创建 2已完成
export const bookSchoolStatusOptions = [
  {
    value: 1,
    label: '已创建',
  },
  {
    value: 2,
    label: '已完成',
  },
]

// 纠错类型 1错别字 2逻辑错误 3内容错误 4图片错误 5其他
export const feedbackTypeList = [
  {
    value: 1,
    label: '错别字',
  },
  {
    value: 2,
    label: ' 逻辑错误',
  },
  {
    value: 3,
    label: '内错误',
  },
  {
    value: 4,
    label: '图片错误',
  },
  {
    value: 5,
    label: '其他',
  },
]

export const feedbackStatusList = [
  {
    value: 0,
    label: '待处理',
  },
  {
    value: 1,
    label: '已处理',
  },
]

//#endregion

export const kewordStatusOptions = [
  {
    value: 1,
    label: '启用',
  },
  {
    value: 2,
    label: '禁用',
  },
]

// 0不需处理1未处理2通过3驳回
export const statusList = [
  {
    value: 1,
    label: '待审核',
  },
  {
    value: 2,
    label: '已通过',
  },
  {
    value: 3,
    label: '已驳回',
  },
]

export const purchaseCodeState = [
  {
    value: '',
    label: '全部',
  },
  {
    value: '1',
    label: '未占用',
  },
  {
    value: '2',
    label: '已占用',
  },
  {
    value: '3',
    label: '已使用',
  },
  {
    value: '4',
    label: '已过期',
  },
  {
    value: '5',
    label: '已作废',
  },
]

export const bookPushTextBooksStatus = [
  {
    value: '1',
    label: '成功',
  },
  {
    value: '2',
    label: '失败',
  },
]

// 日 月 年
export const timeTypeList = [
  {
    value: 'day',
    label: '日',
  },
  {
    value: 'month',
    label: '月',
  },
  {
    value: 'year',
    label: '年',
  },
]

// 文件类型
export const fileTypeList = [
  {
    value: '1',
    label: t('fileTypeList.img'),
  },
  {
    value: '2',
    label: t('fileTypeList.audio'),
  },
  {
    value: '3',
    label: t('fileTypeList.video'),
  },
  {
    value: '4',
    label: t('fileTypeList.simulation'),
  },
  {
    value: '5',
    label: t('fileTypeList.AR'),
  },
  {
    value: '6',
    label: t('fileTypeList.threeD'),
  },
  {
    value: '8',
    label: t('fileTypeList.tech'),
  },
]

export function getOptionDesc(option_list, value) {
  if (!option_list) {
    return ''
  }
  const selectOptionList = option_list.filter((item) => item.value == value)
  if (selectOptionList.length == 0) {
    return ''
  }
  return selectOptionList[0].desc || selectOptionList[0].label || ''
}

export const bookTemplateTypeList = [
  { value: 1, label: '公共基础' },
  { value: 2, label: '电子信息' },
  { value: 3, label: '装备制造' },
  { value: 4, label: '交通运输' },
  { value: 5, label: '土木建筑' },
  { value: 6, label: '财经商贸' },
  { value: 7, label: '管理服务' },
  { value: 8, label: '旅游' },
  { value: 9, label: '外语' },
  { value: 10, label: '其他' },
]

export const bookTemplateSortList = [
  { value: 1, label: '最新上传' },
  { value: 2, label: '最早上传' },
  { value: 3, label: '最近更新' },
  { value: 4, label: '标题名称（A-Z）' },
]

export const bookTemplateThemeList = [
  { value: 1, label: '#e24b3a' },
  { value: 2, label: '#f56e59' },
  { value: 3, label: '#ffc11b' },
  { value: 4, label: '#2dbc7b' },
  { value: 5, label: '#00fbfd' },
  { value: 6, label: '#468fe4' },
  { value: 7, label: '#947fff' },
  { value: 8, label: '#d5d5d5' },
]
