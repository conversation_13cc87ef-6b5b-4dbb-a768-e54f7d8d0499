import { QuestionTypeSecondLevelEnum, uuid } from './quetions-utils'

// 题目类型是否支持单选题、多选题
export function optionSelectQuestionTypeSupported(item: string) {
  return (
    [
      QuestionTypeSecondLevelEnum.SingleChoiceQuestion.value,
      QuestionTypeSecondLevelEnum.MultipleChoiceQuestion.value,
    ].indexOf(item) >= 0
  )
}
// 题目类型是否支持判断题
export function trueOrFalseQuestionSupported(item: string) {
  return [QuestionTypeSecondLevelEnum.TrueOrFalse.value].indexOf(item) >= 0
}

// 题目类型是否支持排序
export function sortingQuestionSupported(item: string) {
  return [QuestionTypeSecondLevelEnum.Sorting.value].indexOf(item) >= 0
}

// 题目类型是否支持简答题
export function descriptiveAnswerQuestionSupported(item: string) {
  return [QuestionTypeSecondLevelEnum.SimpleQuestion.value].indexOf(item) >= 0
}

// 题目类型是否支持填空题、编程填空
export function fillingBlanksQuestionSubFunctionSupported(item: string) {
  return (
    [
      QuestionTypeSecondLevelEnum.Completion.value,
      QuestionTypeSecondLevelEnum.ProgrammingFillInBlanks.value,
    ].indexOf(item) >= 0
  )
}

export function matchingQuestionsSupported(questionType: string) {
  return [QuestionTypeSecondLevelEnum.Matching.value].indexOf(questionType) >= 0
}

// 将后端设置的题目类型转成前端识别的题目类型
// 小题类型1单选 2多选 3填空 4排序 5连线 6简答 7判断 8编程
function formatQuestionTypeToFrontEndString(type: number | string) {
  const tmp: { [key: string]: string } = {
    '1': QuestionTypeSecondLevelEnum.SingleChoiceQuestion.value,
    '2': QuestionTypeSecondLevelEnum.MultipleChoiceQuestion.value,
    '3': QuestionTypeSecondLevelEnum.Completion.value,
    '4': QuestionTypeSecondLevelEnum.Sorting.value,
    '5': QuestionTypeSecondLevelEnum.Matching.value,
    '6': QuestionTypeSecondLevelEnum.SimpleQuestion.value,
    '7': QuestionTypeSecondLevelEnum.TrueOrFalse.value,
    '8': QuestionTypeSecondLevelEnum.ProgrammingFillInBlanks.value,
  }
  return tmp[type]
}

// 题目类型是否支持单选题、多选题
export function isOptionSelectQuestionType(questionType: number) {
  return (
    [
      QuestionTypeSecondLevelEnum.SingleChoiceQuestion.value,
      QuestionTypeSecondLevelEnum.MultipleChoiceQuestion.value,
    ].indexOf(formatQuestionTypeToFrontEndString(`${questionType}`)) >= 0
  )
}
export function isSingleOptionSelectQuetionType(questionType: number) {
  return (
    [QuestionTypeSecondLevelEnum.SingleChoiceQuestion.value].indexOf(
      formatQuestionTypeToFrontEndString(`${questionType}`),
    ) >= 0
  )
}
export function isMultipleOptionSelectQuetionType(questionType: number) {
  return (
    [QuestionTypeSecondLevelEnum.MultipleChoiceQuestion.value].indexOf(
      formatQuestionTypeToFrontEndString(`${questionType}`),
    ) >= 0
  )
}
// 题目类型是否支持判断题
export function isTrueOrFalseQuestion(questionType: number) {
  return (
    [QuestionTypeSecondLevelEnum.TrueOrFalse.value].indexOf(
      formatQuestionTypeToFrontEndString(`${questionType}`),
    ) >= 0
  )
}

// 题目类型是否支持排序
export function isSortingQuestion(questionType: number) {
  return (
    [QuestionTypeSecondLevelEnum.Sorting.value].indexOf(
      formatQuestionTypeToFrontEndString(`${questionType}`),
    ) >= 0
  )
}

// 题目类型是否支持简答题
export function isDescriptiveAnswerQuestion(questionType: number) {
  return (
    [QuestionTypeSecondLevelEnum.SimpleQuestion.value].indexOf(
      formatQuestionTypeToFrontEndString(`${questionType}`),
    ) >= 0
  )
}

// 题目类型是否支持填空题、编程填空
export function isFillinBlanksQuestion(questionType: number) {
  return (
    [QuestionTypeSecondLevelEnum.Completion.value].indexOf(
      formatQuestionTypeToFrontEndString(`${questionType}`),
    ) >= 0
  )
}

export function isMatchingQuestions(questionType: number) {
  return (
    [QuestionTypeSecondLevelEnum.Matching.value].indexOf(
      formatQuestionTypeToFrontEndString(`${questionType}`),
    ) >= 0
  )
}

export function isProgrammingQuestion(questionType: number) {
  return (
    [QuestionTypeSecondLevelEnum.ProgrammingFillInBlanks.value].indexOf(
      formatQuestionTypeToFrontEndString(`${questionType}`),
    ) >= 0
  )
}

export function initTrueOrFalseQuestionOptions(): Array<any> {
  return [
    {
      optionContent: '正确',
      optionId: uuid(),
      rightFlag: 0,
    },
    {
      optionContent: '错误',
      optionId: uuid(),
      rightFlag: 0,
    },
  ]
}

export function deriveUserQuestionObjectWithMinimumProperty(question) {
  return {
    questionId: question.questionId,
    questionType: question.questionType,
    questionRemark: question.questionRemark,
    questionContent: question.questionContent,
    questionTypeShow: '1',
    rightAnswer: question.rightAnswer,
    analysis: question.analysis,
    disorder: question.disorder,
    sort: question.sort,
    folderId: question.folderId,
    options:
      question.options?.map((item) => {
        return {
          optionId: item.optionId,
          optionContent: item.optionContent,
          questionId: item.questionId,
          rightFlag: item.rightFlag,
          optionPosition: item.optionPosition,
          sort: item.sort,
        }
      }) ?? [],
    codeContent: question.codeContent,
    createSource: question.createSource,
  }
}

export function deriveBookQuestionObjectWithMinimumProperty(question) {
  return {
    bookId: question.bookId,
    bookQuestionId: question.bookQuestionId,
    chapterId: question.chapterId,
    folderId: question.folderId,
    questionType: question.questionType,
    userQuestionId: question.userQuestionId,
    questionTypeShow: '1',
  }
}

export function deriverChapterInfoObjectWithMinimumProperty(chapterModel) {
  return {
    bookId: chapterModel.bookId,
    chapterId: chapterModel.chapterId,
    domId: chapterModel.domId,
    name: chapterModel.name,
    catalogId: chapterModel.catalogId,
  }
}
