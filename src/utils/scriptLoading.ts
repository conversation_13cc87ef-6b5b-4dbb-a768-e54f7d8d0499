import { shortId } from "./short-id";

/**
 * 动态加载外部 JavaScript 文件
 * @param {string} src - 外部脚本的 URL
 * @param {Function} [callback] - 脚本加载完成后的回调函数
 */
export function loadScript(scriptOpts: {
  src: string,
  id?: string,
  attachTo?: Element
}, callback?) {
  return new Promise((resolve, reject) => {
    if (scriptOpts?.id) {
      const scriptEle: HTMLScriptElement | null = document.querySelector(`#script_${scriptOpts.id}`)
      if(scriptEle) {
        return resolve({
          existed: true,
          src: scriptEle.src,
          id: scriptEle.id
        })
      }
    }
    // 创建一个 script 元素
    const scriptEle: HTMLScriptElement = document.createElement('script');
    scriptEle.src = scriptOpts.src; // 设置脚本的 URL
    scriptEle.type = 'text/javascript'; // 设置脚本类型
    if (scriptOpts?.id) {
      scriptEle.id = `script_${scriptOpts.id}`
    }
    // 当脚本加载完成时执行回调函数
    scriptEle.onload = () => {
      callback?.({
        existed: true,
        src: scriptEle.src,
        id: scriptEle.id
      })
      resolve({
        existed: true,
        src: scriptEle.src,
        id: scriptEle.id
      })
    };
    // 当脚本加载失败时处理错误
    scriptEle.onerror = () => {
      callback?.(null)
      // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors
      reject(`Failed to load script: ${scriptEle.src}`)
    };
    // 将 script 元素插入到 head 中
    const attachToELe = scriptOpts.attachTo ?? document.head
    attachToELe.append(scriptEle);
  })
}
