/**
 * 格式化科目名称(支持四级科目)
 * @param {Object} book 包含科目ID的书籍对象(topSubjectId/secondSubjectId/thirdSubjectId/forthSubjectId)
 * @param {Array} subjects 科目列表
 * @returns {string} 格式化后的科目名称(以 / 分隔)
 */
export function formatSubjectName(book, subjects) {


  // 参数校验
  if (!book || !subjects || !Array.isArray(subjects)) {
    return '暂无分类';
  }

  // 递归查找父级科目ID
  const findParentIds = (targetId, subjectList, parentIds = []) => {
    for (const subject of subjectList) {
      const currentId = subject.id || subject.subjectId;
      if (currentId === targetId) {
        return parentIds;
      }
      if (subject.children && subject.children.length > 0) {
        const foundParentIds = findParentIds(targetId, subject.children, [...parentIds, currentId]);
        if (foundParentIds) return foundParentIds;
      }
    }
    return null;
  };

  // 获取所有非空的科目ID
  const subjectIds = book.subjectId 
    ? (() => {
        const parentIds = findParentIds(book.subjectId, subjects) || [];
        return [...parentIds, book.subjectId];
      })()
    : [
        book.topSubjectId,
        book.secondSubjectId,
        book.thirdSubjectId,
        book.forthSubjectId
      ].filter(id => id != null);

  // 递归查找科目名称
  const findSubjectName = (targetId, subjectList) => {


    for (const subject of subjectList) {
      const currentId = subject.id || subject.subjectId;
      if (currentId == targetId) { 
        return subject.subjectName;
      }
      if (subject.children && subject.children.length > 0) {
        const name = findSubjectName(targetId, subject.children);
        if (name) return name;
      }
    }
    return null;
  };

  // 获取所有科目名称
  const subjectNames = subjectIds
    .map(id => findSubjectName(id, subjects))
    .filter(Boolean);

  return subjectNames.length > 0 ? subjectNames.join(' / ') : '未知分类';
}