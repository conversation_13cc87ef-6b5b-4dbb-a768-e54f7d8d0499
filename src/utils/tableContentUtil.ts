const { container, editor } = useStore()

export function scrollHeadingInEditorIntoView(id: string) {
  const element = editor.value?.view.dom.querySelector(`[data-toc-id="${id}"`)
  if (element) {
    const pageContainer = document.querySelector(
      `${container} .umo-zoomable-content`
    )
    pageContainer?.scrollTo({
      top: ((element as HTMLElement)?.offsetTop ?? 0) - 6,
    })
  }
}

// export function treelizeTableOfContents(tableContent: Array<{[key: string]: any}>) {
//   // 从根节点开始构建树
//   const { children } = buildTree(tableContent, 1, 0);
//   return children;
// }

// // 辅助函数，用于递归构建树形结构
// function buildTree(items, currentLevel, index) {
//   const tree: any = [];
//   while (index < items.length) {
//     const item = items[index];
//     // 如果当前项的 level 小于 currentLevel，说明已经跳出当前层级，返回
//     if (item.level < currentLevel) {
//       break;
//     }
//     // 如果当前项的 level 等于 currentLevel，说明是当前层级的兄弟节点
//     if (item.level === currentLevel) {
//       tree.push({
//         ...item,
//         children: [],
//       });
//       index++;
//     }
//     // 如果当前项的 level 大于 currentLevel，说明是子节点，递归处理
//     else if (item.level > currentLevel) {
//       if (tree.length === 0) {
//         throw new Error('Invalid structure: child node without parent');
//       }
//       const lastNode = tree[tree.length - 1];
//       const { children, newIndex } = buildTree(items, item.level, index);
//       lastNode.children = children;
//       index = newIndex;
//     }
//   }
//   return { children: tree, newIndex: index };
// }

// // 示例数据
// const jsonData = [
//   { itemIndex: 1, id: '', originalLevel: 1, level: 1, textContent: '标题一' },
//   { itemIndex: 1, id: '', originalLevel: 2, level: 2, textContent: '标题一.1' },
//   { itemIndex: 2, id: '', originalLevel: 2, level: 2, textContent: '标题一.2' },
//   { itemIndex: 1, id: '', originalLevel: 3, level: 3, textContent: '标题一.2.1)' },
//   { itemIndex: 2, id: '', originalLevel: 3, level: 3, textContent: '标题一.2.2)' },
//   { itemIndex: 2, id: '', originalLevel: 1, level: 1, textContent: '标题二' },
// ];

// // 生成树形结构
// const titleTree = buildTitleTree(jsonData);

// // 打印结果
// console.log(JSON.stringify(titleTree, null, 2));