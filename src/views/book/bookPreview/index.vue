<!-- 非编辑内预览 -->
<template>
  <div class="book-preview">
    <div class="book-preview-header">
      <div class="back-icon" @click="back"><ChevronLeftIcon /> 返回</div>
      <div class="my-header">
        <div
          v-for="tab in tabs"
          :key="tab.icon"
          :class="{ selected: checkTabs === tab.icon }"
          class="tab-item"
          @click="changeTab(tab.icon)"
        >
          <i :class="tab.icon"></i>{{ tab.value }}
        </div>
      </div>
    </div>

    <div class="preview-menus" :style="{ width: menuIsShow ? '300px' : '0' }">
      <div v-if="menuIsShow" class="preview-menus-content">
        <t-tabs
          :default-value="1"
          style="width: 300px; height: 100vh; overflow-y: auto"
        >
          <t-tab-panel :value="1" label="目录" :destroy-on-hide="false">
            <div
              v-if="chapterList && chapterList.length > 0"
              class="chapter-list"
            >
              <div v-for="item in chapterList" :key="item.chapterId">
                <div class="chapter-item" @click="changeChapter(item)">
                  <i class="book-icon"></i>{{ item.chapterName }}
                </div>
                <t-menu
                  v-if="
                    curChapterId == item.chapterId && chapterLogList.length > 0
                  "
                  width="300px"
                  :expanded="expanded"
                >
                  <toc-item
                    :list="chapterLogList"
                    @on-click="(e, item) => headingClick(e, item)"
                  />
                </t-menu>
              </div>
            </div>

            <t-menu v-else width="300px" :expanded="expanded">
              <toc-item
                :list="chapterLogList"
                @on-click="(e, item) => headingClick(e, item)"
              />
            </t-menu>
          </t-tab-panel>
          <t-tab-panel :value="2" label="知识图谱" :destroy-on-hide="false">
            <p style="margin: 20px">选项卡2内容区</p>
          </t-tab-panel>
          <t-tab-panel :value="3" label="搜索" :destroy-on-hide="false">
            <!-- <container-search-preview :allDomList="allDomList" /> -->
          </t-tab-panel>
        </t-tabs>
      </div>
      <div
        v-if="!menuIsShow"
        class="preview-menu-open"
        @click="menuIsShow = true"
      >
        <MenuFoldIcon />
      </div>

      <div v-else class="preview-menu-cloce" @click="menuIsShow = false">
        <MenuUnfoldIcon />
      </div>
    </div>

    <div class="content-container">
      <div v-if="checkTabs === 'phone'" class="phonebg-phone">
        <div class="phoneMain">
          <div class="phoneHeader">
            <div>{{ formattedTime }}</div>
            <div>
              <img
                v-if="bookPreviewVisible"
                src="@/assets/editorImg/phoneHeaderTools.png"
                alt=""
                height="12px"
              />
            </div>
          </div>

          <div
            ref="appContent"
            class="phoneContent umo-editor-container"
            contenteditable="false"
          >
            <analysisJson
              :check-tabs="checkTabs"
              :page-config-list="pageConfigList"
            />
          </div>
        </div>
      </div>

      <div v-else-if="checkTabs === 'tablet'" class="content-tablet">
        <div class="padMain">
          <div class="phoneHeader">
            <div>{{ formattedTime }}</div>
            <div>
              <img
                v-if="bookPreviewVisible"
                src="@/assets/editorImg/phoneHeaderTools.png"
                alt=""
                height="12px"
              />
            </div>
          </div>

          <div
            ref="appContent"
            class="padContent preview-box"
            contenteditable="false"
          >
            <analysisJson
              :check-tabs="checkTabs"
              :page-config-list="pageConfigList"
            />
          </div>
        </div>
      </div>

      <div v-else="checkTabs === 'desktop'" class="content-pc">
        <div class="pcbg">
          <div class="pcMain">
            <div ref="appContent" class="pcContent" contenteditable="false">
              <analysisJson
                :check-tabs="checkTabs"
                :page-config-list="pageConfigList"
              />
            </div>
          </div>
          <div class="pcFooter">
            <img
              v-if="bookPreviewVisible"
              src="@/assets/editorImg/mac-bottom.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  ChevronLeftIcon,
  MenuFoldIcon,
  MenuUnfoldIcon,
} from 'tdesign-icons-vue-next'
import { useRoute, useRouter } from 'vue-router'

import { chapterCatalogList, queryChapterList,queryChapterListLastVersion } from '@/api/book/chapter'
import { getChapterContentInfo } from '@/api/chapterContent'
import tocItem from '@/components/container/toc-item.vue'
import { removeManageToken, setManageToken } from '@/request/token'
const { pageTemplateId, getPageTemplate } = useTemplate()
const route = useRoute()
const router = useRouter()
let checkTabs = $ref('phone')
const menuIsShow: boolean = $ref(true)
let pageConfigList: any = $ref([])
const appContent: any = $ref(null)
let expanded: any = $ref([])
let newMenuList: any = $ref([])
const allDomList: any = $ref([])
const chapterLogList: any = ref([])
const bookPreviewVisible = ref(false)
const chapterList = ref([])
const curChapterId = ref(null)
const chapterId = ref(null)
const bookId = ref(null)
// 1整本书 2章节
const bookPreviewType = ref(1)
// 1编辑器后台 2管理后台
const formType = ref(1)
// 1 教材列表审批页进入 2 待办中心审批页进入 3 待办中心审批详情页进入
const funType = ref(1)
const tabs = ref([
  {
    name: 'headerTools.preview.phonePreview',
    icon: 'phone',
    value: '手机',
  },
  {
    name: 'headerTools.preview.tabletPreview',
    icon: 'tablet',
    value: '平板',
  },
  {
    name: 'headerTools.preview.desktopPreview',
    icon: 'desktop',
    value: '电脑',
  },
])
onMounted(() => {
  checkTabs = 'phone'
  newMenuList = []
  bookPreviewType.value = route.query.bookPreviewType || 1
  formType.value = route.query.formType || 1
  funType.value = route.query.funType || 1
  bookId.value = route.query.bookId || null
  chapterId.value = route.query.chapterId
  if (route.query.token) {
    setManageToken(route.query.token)
  }
  if (bookPreviewType.value == 2) {
    getChapterDetail(chapterId.value)
    chapterCatalogue(chapterId.value)
  } else if (bookPreviewType.value == 1) {
    getChapterList(bookId.value)
  }
})
window.onbeforeunload = function (e) {
  removeManageToken()
}

function back() {
  if (formType.value == 1) {
    if (funType.value == 2) {
      router.push({
        path: '/pages/bookApprove',
        query: {
          bookId: bookId.value,
          masterFlag: route.query.masterFlag,
          bookOrganize: route.query.bookOrganize,
          stepId: route.query.stepId,
          processId: route.query.processId,
          auditUserId: route.query.auditUserId,
          stepValue: 4,
          funType: funType.value,
        },
      })
    } else if (funType.value == 3) {
      router.push({
        path: '/pages/processDetail',
        query: {
          bookId: bookId.value,
          bookOrganize: route.query.bookOrganize,
          stepId: route.query.stepId,
          processId: route.query.processId,
          auditUserId: route.query.auditUserId,
          state: route.query.state,
          funType: funType.value,
        },
      })
    } else {
      router.push({
        path: '/pages/detail',
        query: { bookId: bookId.value },
      })
    }
  } else if (formType.value == 2) {
    if (funType.value == 2) {
      window.location.href = `${import.meta.env.VITE_APP_MANAGE_URL}book/approve` +
                              `?bookId=${bookId.value}`
                              + "&masterFlag=" + route.query.masterFlag
                              + "&bookOrganize=" + route.query.bookOrganize
                              + "&stepId=" + route.query.stepId
                              + "&processId=" + route.query.processId
                              + "&auditUserId=" + route.query.auditUserId
                              + "&activeTab=4"
    } else if (funType.value == 3) {
      window.location.href = `${import.meta.env.VITE_APP_MANAGE_URL}book/approveDetail` +
        `?bookId=${bookId.value}`
        + "&masterFlag=" + route.query.masterFlag
        + "&bookOrganize=" + route.query.bookOrganize
        + "&stepId=" + route.query.stepId
        + "&processId=" + route.query.processId
        + "&state=" + route.query.state
    } else {
      window.location.href =
        `${import.meta.env.VITE_APP_MANAGE_URL}book/bookDetail` +
        `?bookId=${bookId.value}`
    }
  }
}
// 获取章节详情
function getChapterDetail(chapterId) {
  expanded = []
  getChapterContentInfo(chapterId).then((response) => {
    const { data } = response
    if (data) {
      pageConfigList = JSON.parse(data.content).content || []
      pageConfigList?.forEach((page: any) => {
        page?.content.forEach((item: any) => {
          if (item.type === 'heading') {
            expanded.push(item?.attrs?.id)
          }
        })
      })
    } else {
      pageConfigList = []
    }
  })
}

// 更换章节
function changeChapter(item) {
  if (item.chapterId == curChapterId.value) {
    return
  }
  getPageTemplate(item.chapterId)
  curChapterId.value = item.chapterId
  getChapterDetail(item.chapterId)
  chapterCatalogue(item.chapterId)
}

// 获取章节目录
async function chapterCatalogue(chapterId) {
  if (chapterId) {
    const res = await chapterCatalogList(chapterId)
    chapterLogList.value = res.data
  }
}

// 获取章节列表
function getChapterList(bookId) {
  if (funType.value == 3) {
    // 获取章节列表 待办中心审批详情页进入 需要查询最新版本的章节
    queryChapterListLastVersion(bookId).then((res) => {
      chapterList.value = res.data || []
      if (chapterList.value.length > 0) {
        changeChapter(chapterList.value[0])
      }
    })
  }else{
    // 其他入口获取章节列表
    queryChapterList(bookId).then((res) => {
      chapterList.value = res.data || []
      if (chapterList.value.length > 0) {
        changeChapter(chapterList.value[0])
      }
    })
  }
}


// 点击菜单跳转或者展开
const headingClick = (e: any, heading: any) => {
  if (e.target?.className !== 'menuItems-maskLayer') {
    if (expanded.includes(heading?.id)) {
      expanded = expanded.filter((ele: string) => ele !== heading?.id)
    } else {
      expanded.push(heading?.id)
    }
  }

  //把元素滚动到可视区
  const targetDiv = document.getElementById(heading?.id)
  if (targetDiv) {
    // 平滑滚动到目标位置
    targetDiv.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    })
  }
}

const changeTab = (index: string) => {
  checkTabs = index
}

const formattedTime = computed(() => {
  const currentTime = new Date()
  const hours = currentTime.getHours().toString().padStart(2, '0')
  const minutes = currentTime.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
})
</script>
<style lang="less" scoped>
.book-preview {
  background-color: #fff;
  height: 100vh;
  .book-preview-header {
    width: 100%;
    background-color: var(--umo-container-background);
    height: 60px;
    position: fixed;
    top: 0;
    left: 0;
    .back-icon {
      position: fixed;
      top: 20px;
      left: 20px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
    .my-header {
      width: 380px;
      margin: 0 auto;
      display: flex;
      padding: 0 20px;
      align-items: center;
      background-color: var(--umo-container-background);
      height: 60px;

      .tab-item {
        display: flex;
        padding: 10px 20px;
        font-size: 14px;
        cursor: pointer;
        align-items: center;

        .phone {
          display: inline-flex;
          margin-top: 3px;
          margin-right: 5px;
          width: 16px;
          height: 16px;
          background: url(@/assets/editorImg/phone.svg) no-repeat;
          background-size: contain;
        }

        .tablet {
          display: inline-flex;
          align-items: center;

          width: 16px;
          height: 16px;
          margin-top: 5px;
          margin-right: 5px;
          background: url(@/assets/editorImg/header-pad.svg) no-repeat;
          background-size: contain;
        }

        .desktop {
          display: inline-flex;
          width: 24px;
          height: 24px;

          margin-right: 5px;
          background: url(@/assets/editorImg/header-pc.svg) no-repeat;
          background-size: contain;
        }
      }

      .selected {
        background: var(--umo-primary-color);
        color: var(--umo-color-white);
        border-radius: var(--umo-radius-medium);
      }
    }
  }
  // 预览菜单目录内容
  .preview-menus {
    position: fixed;
    top: 80px;
    .preview-menus-content {
      .chapter-list {
        .chapter-item {
          padding: 0 10px;
          height: 54px;
          line-height: 54px;
          cursor: pointer;
          color: #333;
          font-weight: bold;
          width: 200px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          .book-icon {
            background: url('@/assets/icons/bookIcon.svg') no-repeat;
            width: 14px;
            height: 14px;
            background-size: 100% 100%;
            display: inline-flex;
            align-items: center;
            position: relative;
            top: 3px;
            margin-right: 5px;
          }
        }
      }
    }
  }
}
:global(.umo-popup__arrow) {
  bottom: -3px;
}

:global(.umo-popup) {
  max-width: 400px;
}
:global(.slotCss) {
  padding: 5px 10px;

  width: 400px !important;
  display: flex;
  align-items: center;
}

.content-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  img {
    width: 100%;
  }
}
.phonebg-phone {
  width: 100%;
  overflow: hidden;
  .phoneMain {
    width: 22vw;
    height: calc(22vw * 1.78);
    border-radius: 52px;
    border-width: 11px;
    border-style: solid;
    border-color: rgb(0, 0, 0);
    border-image-source: url('@/assets/editorImg/phoneBg.png');
    border-image-slice: 120 fill;
    border-image-width: 80px;
    border-image-outset: initial;
    border-image-repeat: initial;
    margin: 0 auto;
    overflow: hidden;
    transform-origin: 50% 0;
    background-color: #ece8e8;
    margin-top: 80px;
    .phoneHeader {
      height: 28px;
      background-color: var(--umo-primary-color);
      line-height: 28px;
      padding: 0 30px;
      display: flex;
      justify-content: space-between;
      font-size: 1em;
      color: #fff;
    }

    .phoneContent {
      width: 100%;
      height: 100%;
      overflow-y: auto;
      // background-color: #ff0;
      padding-bottom: 50px;
      box-sizing: border-box;
      overflow-y: scroll;
      scrollbar-width: none;
      cursor: pointer;

      ::-webkit-scrollbar {
        display: none;
      }
      .header {
        min-height: 71px;
      }

      p {
        margin: 0 10px;
      }

      table {
        width: 96%;
        margin: 0 auto;
        border-spacing: 0;
        border-collapse: 0;
        border-color: #333;
        margin: 0 auto;

        tr td {
          border: 1px solid;
          border-color: #333;
        }
      }
    }
  }
}
.content-tablet {
  width: 100%;
  .padMain {
    width: 50vw;
    height: calc(50vw / 1.6);
    border-radius: 28px;
    border-width: 10px;
    border-style: solid;
    border-color: rgb(0, 0, 0);
    border-image-source: url('@/assets/editorImg/ipadBg.png');
    border-image-slice: 121 fill;
    border-image-width: 60px;
    border-image-outset: initial;
    border-image-repeat: initial;
    background-color: #ece8e8;
    margin: 0 auto;
    overflow: auto;
    margin-top: 100px;
    scrollbar-width: none;
    .phoneHeader {
      height: 20px;
      background-color: var(--umo-primary-color);
      line-height: 20px;
      padding: 0 40px;
      display: flex;
      justify-content: space-between;
      font-size: 1em;
      color: #fff;
    }

    .preview-box {
      position: relative;
      overflow: hidden;
    }

    .padContent {
      overflow-y: scroll;
      scrollbar-width: none;
      cursor: pointer;
      flex: 1;

      ::-webkit-scrollbar {
        display: none;
      }

      p {
        margin: 0 10px;
      }

      table {
        width: 96%;
        margin: 0 auto;
        border-spacing: 0;
        border-collapse: 0;
        border-color: #333;
        margin: 0 auto;

        tr td {
          border: 1px solid;
          border-color: #333;
        }
      }
    }
  }
}

.content-pc {
  .pcbg {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 600px;
    margin-top: 70px;
    .pcMain {
      width: 55vw;
      height: calc(55vw / 1.6);
      border-radius: 0px;
      border-width: 28px 26px 32px;
      border-style: solid;
      border-color: rgb(0, 0, 0);
      border-image-source: url('@/assets/editorImg/mac.png');
      border-image-slice: 40 fill;
      border-image-width: 35px;
      border-image-outset: initial;
      border-image-repeat: initial;
      position: relative;
      overflow: hidden;
      margin: 0 auto;
      flex: auto;
      border-radius: 40px;
      background-color: #ece8e8;
      .pcContent {
        padding: 10px;
        height: 100%;
        overflow-y: scroll;
        scrollbar-width: none;
        cursor: pointer;

        ::-webkit-scrollbar {
          display: none;
        }

        p {
          margin: 0 10px;
        }

        table {
          width: 96%;
          margin: 0 auto;
          border-spacing: 0;
          border-collapse: 0;
          border-color: #333;
          margin: 0 auto;

          tr td {
            border: 1px solid;
            border-color: #333;
          }
        }
      }
    }

    .pcFooter {
      flex-grow: 1 !important;
      margin: 0 auto;
      position: relative;
      top: -20px;
      & > img {
        width: 69vw;
      }
    }
  }
}
.preview-menus {
  position: fixed;

  top: 0px;
  left: 0;
  width: auto;
  height: 100%;
  box-shadow: 10px 0 15px -3px rgba(88, 87, 87, 0.1);
  border-right: 1px solid var(--umo-border-color);
  .preview-menus-content {
    // background-color: #ff0;
    transition: 0.3s;
  }
  .preview-menu-open {
    position: absolute;
    left: 20px;
    top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--umo-container-background);
    border: 1px solid var(--umo-border-color-dark);
    cursor: pointer;
  }
  .preview-menu-cloce {
    position: absolute;
    right: 14px;
    top: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    cursor: pointer;
  }
}
</style>

<style lang="less">
.phoneMain-page-item {
  background-color: #fff;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px;
  box-sizing: border-box;
}
</style>
