<template>
  <div class="book-preview">
    <div class="header">
      <div class="headerMain">
        <div class="back-icon" @click="back"><ChevronLeftIcon /> 返回</div>
        <div v-if="optType == 1" class="restore">
          <t-button
            variant="outline"
            theme="primary"
            ghost
            @click="handleRecycleChapter"
            >恢复</t-button
          >
        </div>
        <div v-if="optType == 2" class="title" @click="handlePrint">
          <t-button theme="primary">
            <template #icon><PrintIcon /></template>
            打印
          </t-button>
        </div>
      </div>
    </div>
    <div class="content-container">
      <div v-if="optType == 1" class="left-menu">
        <t-tabs :default-value="1">
          <t-tab-panel :value="1" label="目录">
            <t-menu
              v-if="chapterLogList.length > 0"
              theme="light"
              default-value="2-1"
              :expanded="expanded"
            >
              <tocItem
                :list="chapterLogList"
                @on-click="(e, item) => headingClick(e, item)"
              />
            </t-menu>
            <div v-else class="nodata">
              <t-empty />
            </div>
          </t-tab-panel>
        </t-tabs>
      </div>
      <div class="chapterDetailContent">
        <div id="appContent" ref="appContent" class="chapterDetailContentMain">
<!--          <analysisJson :page-config-list="pageConfigList" />-->
          <iframe
            :src="href"
            frameborder="0"
            class="iframePage"
          ></iframe>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup name="chapterDetail">
import { ChevronLeftIcon, PrintIcon } from 'tdesign-icons-vue-next'
import { useRoute, useRouter } from 'vue-router'
import print from 'print-js'
import { chapterCatalogList, recycleChapter } from '@/api/book/chapter.js'
import { getChapterContentInfo } from '@/api/chapterContent'
import tocItem from '@/components/container/toc-item.vue'
import { removeManageToken, setManageToken } from '@/request/token'
const route = useRoute()
const router = useRouter()
let pageConfigList = $ref([])
const appContent = $ref(null)
const href = ref('')

const chapterId = ref(null)
const bookId = ref(null)
const chapterLogList = ref([])
const expanded = ref([])
// 1编辑器后台 2管理后台
const formType = ref(1)
// 1回收站 2打印
const optType = ref(1)

onMounted(() => {
  const boxCss = document.querySelectorAll('.box')

  boxCss.forEach((element) => {
    element.style.minHeight = '100vh'
    element.style.overflowY = 'auto'
  })
  formType.value = route.query.formType || 1
  optType.value = route.query.optType || 1
  bookId.value = route.query.bookId
  chapterId.value = route.query.chapterId
  if (route.query.token) {
    setManageToken(route.query.token)
  }
  if (chapterId.value) {
    getChapterDetail(chapterId.value)
    chapterCatalogue()
  }
  href.value = `${import.meta.env.VITE_READER_PREVIEW_URL + '?k='+ bookId.value + '&cid=' + chapterId.value + '&fromType=2' + '&operationFlag=false'}`;
})
window.onbeforeunload = function (e) {

  removeManageToken()
}

onBeforeMount(() => {

})

// 返回
function back() {
  if (formType.value == 1) {

    router.push({
      path: '/pages/detail',
      query: { bookId: bookId.value },
    })
  } else if (formType.value == 2) {
 
    window.location.href =
      `${import.meta.env.VITE_APP_MANAGE_URL}book/bookDetail` +
      `?bookId=${bookId.value}`
  }
}

// 获取章节详情
function getChapterDetail(chapterId) {
  getChapterContentInfo(chapterId).then((response) => {
    const { data } = response
    if (data) {
      pageConfigList = JSON.parse(data.content).content || []
      expanded.value = []
      pageConfigList?.forEach((page) => {
        page?.content.forEach((item) => {
          if (item.type === 'heading') {
            expanded.value.push(item?.attrs?.id)
          }
        })
      })
    } else {
      pageConfigList = []
    }
  })
}

// 获取章节目录
async function chapterCatalogue() {
  const res = await chapterCatalogList(chapterId.value)
  chapterLogList.value = res.data
}

// 点击事件
function headingClick(e, item) {
  // if (e.target?.className !== 'menuItems-maskLayer') {
  //   if (expanded.value.includes(item?.id)) {
  //     expanded.value = expanded.value.filter((ele) => ele !== item?.id)
  //   } else {
  //     expanded.value.push(item?.id)
  //   }
  // }
  // // 获取目标元素
  // const targetDiv = document.getElementById(item.domId)
  // if (targetDiv) {
  //   // 平滑滚动到目标位置
  //   targetDiv.scrollIntoView({
  //     behavior: 'smooth',
  //     block: 'center',
  //   })
  // }

  const iframe = document.querySelector('.iframePage');
  if (iframe && iframe.contentWindow) {
    iframe.contentWindow.postMessage(
      {
        type: 'SCROLL_TO_ELEMENT',
        payload: {
          domId: item.domId,
        },
      },
      '*' // 注意：建议指定为 iframe 的 origin
    );
  }
}
// 打印
function handlePrint() {
  // printJS('appContent', 'html')
  const iframe = document.querySelector('.iframePage');
  if (iframe && iframe.contentWindow) {
    iframe.contentWindow.postMessage({ type: 'PRINT' }, '*');
  } else {
    alert('iframe 未加载完成或跨域，无法发送打印指令');
  }
}
// 恢复章节
function handleRecycleChapter() {
  recycleChapter({
    chapterId: chapterId.value,
  }).then((res) => {
    MessagePlugin.success('恢复成功')
    router.back()
  })
}
</script>
<style lang="less" scoped>
.book-preview {
  background-color: #f4f5f7;
  position: absolute;
  width: 100%;
  min-height: 100%;
  .header {
    position: fixed;
    background-color: #fff;
    width: 100%;
    border-bottom: 1px solid var(--umo-border-color);
    z-index: 3;
    .headerMain {
      padding: 20px;
      display: flex;
      justify-content: space-between;

      .back-icon {
        // position: fixed;
        // top: 20px;
        // left: 20px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      }
    }
    .restore {
      cursor: pointer;
      border-radius: 10px;
    }
  }
  .content-container {
    position: relative;
    margin-top: 90px;

    .left-menu {
      width: 300px !important;
      position: absolute;
      left: 0;
      top: 5px;
      box-shadow: -5px 0 10px -3px rgba(0, 0, 0, 0.71);
      min-height: 100%;
      background-color: #fff;
      border-right: 1px solid var(--umo-border-color);
      ::v-deep(.t-default-menu) {
        width: 300px !important;
      }
      .nodata {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 500px;
      }
    }
    .chapterDetailContent {
      .chapterDetailContentMain {
        width: 25cm;
        min-height: 29cm;
        word-wrap: break-word;
        background-color: #fff;
        margin: 20px auto;

        overflow-y: auto;
        scrollbar-width: none; /* 针对 Firefox */
        -ms-overflow-style: none; /* 针对 Internet Explorer 和 Edge */
        border: 1px solid var(--umo-border-color);
        position: relative;
        .iframePage {
          width: 100%;
          //height: 100%;
          min-height: 29cm;
        }
        ::v-deep(.analysisJson-page) {
          width: 21cm;
          min-height: 29cm;
          padding: 20px;
        }
        ::v-deep(.analysisJson-page-tablet) {
          width: auto;
          margin: 0 auto;
          background-color: #fff;
        }

        // p {
        //   margin: 0 10px;
        // }

        // table {
        //   width: 96%;
        //   margin: 0 auto;
        //   border-spacing: 0;
        //   border-collapse: 0;
        //   border-color: #333;
        //   margin: 0 auto;

        //   tr td {
        //     border: 1px solid;
        //     border-color: #333;
        //   }
        // }
      }
    }
  }
}
</style>
