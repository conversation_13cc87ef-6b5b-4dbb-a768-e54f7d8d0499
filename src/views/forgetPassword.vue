<template>
  <div class="main">
    <div class="header">
      <div class="header-main">
        <div class="header-main-left">
          <div class="header-logo">
            <img src="@/assets/images/logo.png" alt="" />
          </div>
          <div class="header-title">数智云平台编辑器账号安全中心</div>
          <div class="header-desc">密码管理</div>
        </div>
        <div class="header-main-right">
          <t-button theme="default" variant="text" @click="goLogin">
            <template #icon>
              <HomeIcon />
            </template>
            返回登录</t-button>
        </div>
      </div>
    </div>

    <div class="content">
      <div class="content-header">输入账号</div>

      <div class="content-main">
        <div class="content-main-box-title">
          请输入需要找回密码的数智云平台编辑器账号
        </div>
        <t-form ref="forgetFormRef" :data="forgetForm" label-align="top" @submit="submit">
          <div class="input">
            <t-form-item label="手机号码" name="phonenumber" :rules="[{ required: true, message: '请输入手机号码' }]">
              <t-input v-model="forgetForm.phonenumber" placeholder="请输入手机号码" clearable input-class="input-class"
                size="large"></t-input>
            </t-form-item>
            <t-form-item label="验证码" :disabled="countdown > 0" name="code"
              :rules="[{ required: true, message: '请输入验证码' }]">
              <t-input v-model="forgetForm.code" placeholder="请输入验证码" clearable input-class="input-class"
                size="large"></t-input>
              <t-button size="large" click="fetchVerificationCode" :disabled="countdown > 0"
                @click="fetchVerificationCode">
                {{ countdown > 0 ? `${countdown}秒后再获取` : '获取验证码' }}
              </t-button>
            </t-form-item>
            <t-form-item label="输入新密码" name="password" :rules="[{ required: true, message: '请输入输入新密码' }]">
              <t-input v-model="forgetForm.password" placeholder="请输入输入新密码" clearable type="password"
                input-class="input-class" size="large"></t-input>
            </t-form-item>
            <t-form-item label="再次输入新密码" name="confirmPassword" :rules="[{ required: true, message: '请输入确认新密码' }]">
              <t-input v-model="forgetForm.confirmPassword" placeholder="请输入输入新密码" clearable type="password"
                input-class="input-class" size="large"></t-input>
            </t-form-item>
          </div>
          <div class="button">
            <t-button size="large" variant="base" theme="primary" style="width: 100%; margin-top: 30px"
              :loading="loading" type="submit">重置密码</t-button>
          </div>
        </t-form>
      </div>
    </div>
  </div>
</template>
<script setup>
import { HomeIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { useRouter } from 'vue-router'

import { forgetPwd, getSmsCode } from '@/api/login'
const forgetForm = ref({})
const loading = ref(false)
const countdown = ref(null)
const timer = ref(null)
const forgetFormRef = ref(null)
const router = useRouter()
const fetchVerificationCode = () => {
  if (timer.value) {
    clearInterval(timer.value)
  }
  const tel = forgetForm.value.phonenumber
  if (!tel) {
    MessagePlugin.error('请输入电话号码')
    return
  }

  // 电话号码格式校验
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(tel)) {
    MessagePlugin.error('请输入正确的电话号码格式')
    return
  }

  getSmsCode(forgetForm.value.phonenumber).then(() => {
    MessagePlugin.success('验证码已发送，请查收')
    countdown.value = 60 // 设置倒计时
    timer.value = setInterval(() => {
      if (countdown.value > 0) {
        countdown.value--
      } else {
        clearInterval(timer.value)
        countdown.value = 0
      }
    }, 1000)
  })
}

const goLogin = () => {
  router.push('/login')
}
const submit = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    console.log(forgetForm.value)
    if (forgetForm.value.password !== forgetForm.value.confirmPassword) {
      MessagePlugin.warning('两次输入的密码不一致')
      return
    }
    loading.value = true

    forgetPwd(forgetForm.value)
      .then((res) => {
        loading.value = false
        // 关闭模态框
        MessagePlugin.success('密码重置成功')
        setTimeout(() => {
          router.push('/login')
        }, 1000)
      })
      .catch((error) => {
        loading.value = false
      })
  } else {
    MessagePlugin.warning(firstError)
  }
}
</script>
<style lang="less" scoped>
.main {
  .header {
    width: 100%;
    height: 65px;
    background-color: #fff;

    .header-main {
      width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      line-height: 65px;

      .header-main-left {
        display: flex;

        .header-logo {
          display: flex;
          align-items: center;
          height: 65px;
        }

        .header-title {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin-left: 20px;

          &::after {
            content: '';
            display: inline-block;
            width: 1px;
            height: 22px;
            background-color: #eee;
            position: relative;
            top: 5px;
            margin-left: 20px;
          }
        }

        .header-desc {
          font-size: 14px;
          color: #666;
          margin-left: 20px;
        }
      }

      .header-main-right {}
    }
  }

  .content {
    width: 1200px;
    margin: 30px auto;
    background-color: #fff;
    height: 700px;

    .content-header {
      display: block;
      height: 40px;
      padding: 40px;
      font-size: 30px;
      line-height: 40px;
      text-align: center;
      border-bottom: 1px solid #eee;
      overflow: hidden;
      background: #fff;
    }

    .content-main {
      min-height: 440px;
      padding: 60px 0;
      background: #fff;
      width: 610px;
      margin: 0 auto;

      .content-main-box {
        max-width: 610px;
        margin: 0 auto;

        .content-main-box-title {
          color: #222;
          font-size: 14px;
          margin-bottom: 10px;
        }

        .input {
          width: 100%;
          height: 46px;
          line-height: 46px;

          .input-class {
            height: 46px;
            line-height: 46px;
          }
        }

        .button {
          width: 100%;
        }
      }
    }
  }
}
</style>
