<template>
  <t-layout>
    <Header @toggle-menu="getMenuVisible" />
    <t-layout>
      <t-layout style="max-height: calc(100vh - 110px)">
        <t-aside
          :style="{
            width: toggleMenuVisible ? '64px' : '232px',
            transition: 'all 0.1s',
            minHeight: 'calc(100vh - 40px)',
            background: '#FAFAFA',
          }"
        >
          <MenuList :is-collapsed="toggleMenuVisible" />
        </t-aside>
        <t-layout style="overflow-y: auto">
          <t-content>
            <div class="content"><router-view /></div>
          </t-content>
        </t-layout>
      </t-layout>
    </t-layout>
  </t-layout>
</template>
<script setup>
import { ref } from 'vue'

import Header from '@/components/template/header.vue'
import MenuList from '@/components/template/menu.vue'
const toggleMenuVisible = ref(false)
const getMenuVisible = (data) => {
  toggleMenuVisible.value = data
}
const visible = ref(true)
</script>
<style lang="less" scoped>
.content {
  padding: 30px;
}

.t-layout {
  background: #fff;
}

.t-layout__sider {
  background: #fafafa;
  border-right: 1px solid #e5e5e5;
}
</style>
