<template>
  <umo-editor ref="editorRef" v-bind="options" />
  <t-dialog
    v-model:visible="warningVisible"
    theme="warning"
    header="警示"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    :on-close="onClickCancel"
    :body="
      confirmLoading
        ? '正在保存，请稍后...'
        : '当前章节已有人员正在进行编写，是否要继续进入编写?如进入编写，则前编写人员会被强制退出编辑器，且保存编写的内容。'
    "
    confirm-btn="继续编写"
    :confirm-loading="confirmLoading"
    @cancel="onClickCancel"
    @confirm="onClickConfirm"
  />
  <t-dialog
    v-model:visible="stopOptVisible"
    theme="warning"
    header="警示"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    body="连接服务器错误，请刷新后重试"
    :confirm-btn="null"
    cancel-btn="取消"
    :on-close="back"
    @cancel="back"
  />
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'

import { updateChapterContentInfo } from '@/api/chapterContent'
import { getChapterContentInfo } from '@/api/chapterContent'
import { removeManageToken, setManageToken } from '@/request/token'
import { getToken } from '@/request/token.js'
import { OssService } from '@/utils/aliOss.js'
import ChapterEditWebSocket from '@/utils/chapterEditWebSocket.js'
const editorRef = $ref(null)
const warningVisible = ref(false)
const stopOptVisible = ref(false)
const { setContentText, chapterId, loading, editor } = useStore()
const { pageTemplateId, getPageTemplate } = useTemplate()
const websocket: any = ref(null)
const route = useRoute()
const router = useRouter()
const confirmLoading = ref(false)
let heartbeatInterval: any = null
const isRe = ref(true)
// 1编辑器后台 2管理后台
const formType = ref(1)
const bookId = ref(null)
const templates = [
  {
    title: '工作任务',
    description: '工作任务模板',
    content:
      '<h1>工作任务</h1><h3>任务名称：</h3><p>[任务的简短描述]</p><h3>负责人：</h3><p>[执行任务的个人姓名]</p><h3>截止日期：</h3><p>[任务需要完成的日期]</p><h3>任务详情：</h3><ol><li>[任务步骤1]</li><li>[任务步骤2]</li><li>[任务步骤3]...</li></ol><h3>目标：</h3><p>[任务需要达成的具体目标或结果]</p><h3>备注：</h3><p>[任何额外信息或注意事项]</p>',
  },
  {
    title: '工作周报',
    description: '工作周报模板',
    content:
      '<h1>工作周报</h1><h2>本周工作总结</h2><hr /><h3>已完成工作：</h3><ul><li>[任务1名称]：[简要描述任务内容及完成情况]</li><li>[任务2名称]：[简要描述任务内容及完成情况]</li><li>...</li></ul><h3>进行中工作：</h3><ul><li>[任务1名称]：[简要描述任务当前进度和下一步计划]</li><li>[任务2名称]：[简要描述任务当前进度和下一步计划]</li><li>...</li></ul><h3>问题与挑战：</h3><ul><li>[问题1]：[描述遇到的问题及当前解决方案或需要的支持]</li><li>[问题2]：[描述遇到的问题及当前解决方案或需要的支持]</li><li>...</li></ul><hr /><h2>下周工作计划</h2><h3>计划开展工作：</h3><ul><li>[任务1名称]：[简要描述下周计划开始的任务内容]</li><li>[任务2名称]：[简要描述下周计划开始的任务内容]</li><li>...</li></ul><h3>需要支持与资源：</h3><ul><li>[资源1]：[描述需要的资源或支持]</li><li>[资源2]：[描述需要的资源或支持]</li><li>...</li></ul>',
  },
]

const options = $ref({
  toolbar: {
    // defaultMode: 'classic',
    // menus: ['base'],
    enableSourceEditor: false,
  },
  document: {
    // title: '测试文档',
    content: localStorage.getItem('document.content') ?? '<p></p>',
  },
  templates,
  cdnUrl: 'https://cdn.umodoc.com',
  shareUrl: 'https://dutp.cn',
  file: {
    // allowedMimeTypes: [
    //   'application/pdf',
    //   'image/svg+xml',
    //   'video/mp4',
    //   'audio/*',
    // ],
  },
  assistant: {
    enabled: true,
  },
  user: {
    userId: 'dutpeditor',
    nickName: 'Dutp Editor3',
    avatarUrl: 'https://tdesign.gtimg.com/site/avatar.jpg',
  },
  async onSave(
    content: {
      html: string
      json: Record<string, any>
      text: string
    },
    page: number,
    document: { content: string },
  ) {
    // localStorage.setItem('document.content', document.content)
    // return new Promise((resolve, reject) => {
    //   setTimeout(() => {
    //     const success = true
    //     if (success) {
    //       console.log('onSave', { content, page, document })

    //       setContentText(content)
    //       resolve('操作成功')
    //     } else {
    //       reject(new Error('操作失败'))
    //     }
    //   }, 2000)
    // })
    if (chapterId.value) {
      return updateChapterContentInfo({
        chapterId: chapterId.value,
        content: JSON.stringify(content.json || '{}'),
      })
    }
  },

  async onFileUpload(file: File & { url?: string }) {
    if (!file) {
      throw new Error('没有找到要上传的文件')
    }
    loading.value = true
    const res: any = await OssService(file)
    loading.value = false
    // console.log(res)
    // await new Promise((resolve) => setTimeout(resolve, 3000))
    return {
      // id: shortId(),
      // url: file.url ?? URL.createObjectURL(file),
      url: res.url,
      name: file.name,
      type: file.type,
      size: file.size,
    }
  },
  async onAssistant() {
    return await Promise.resolve('<p>AI助手测试</p>')
  },
  async onCustomImportWordMethod() {
    return await Promise.resolve({
      value: '<p>测试导入word</p>',
    })
  },

  resourceCover: {
    fileSize: 1024 * 1024 * 200, // 200M
    imgSize: 1024 * 1024 * 10, // 10M
  },
})

window.onbeforeunload = function (e) {
  // 在保存数据时，页面刷新，释放编辑器
  if (confirmLoading.value) {
    saveSuccess()
  }
  removeManageToken()
}

onMounted(() => {
  formType.value = route.query.formType || 1
  bookId.value = route.query.bookId
  isRe.value = true
  const { chapterId } = route.query
  if (route.query.token) {
    setManageToken(route.query.token)
  }
  if (!getToken()) {
    router.push('/login')
  }
  websocket.value = new ChapterEditWebSocket()
  websocket.value.connect()
  websocket.value.on('message', handleMessage)
  websocket.value.on('open', () => {
    stopOptVisible.value = false
    if (chapterId) {
      applyEdit()
    }
    heartbeatInterval = setInterval(sendHeartbeat, 5000)
  })
  websocket.value.on('close', () => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
    }
    stopOptVisible.value = true

    // 延迟 5 秒后重试连接
    setTimeout(reConnect, 5000)
  })
  // websocket.value.on('error', () => {
  //   if (heartbeatInterval) {
  //     clearInterval(heartbeatInterval)
  //   }
  //   stopOptVisible.value = true
  //   console.log('websocket error, try to reconnect')
  //   // 延迟 5 秒后重试连接
  //   setTimeout(reConnect, 5000)
  // })

  function preventScroll(event: any) {
    const HTML: any = document.querySelector('html')
    HTML.scrollTop = 0
    event.preventDefault()
  }
  // 禁止滚动
  document.addEventListener('scroll', preventScroll)

  getPageTemplate(chapterId)
})

onBeforeUnmount(() => {
  isRe.value = false
  // 返回时，数据保存，释放编辑器
  if (confirmLoading.value) {
    saveSuccess()
  }

  websocket.value.disconnect()
})

// 重连
const reConnect = () => {
  if (isRe.value && stopOptVisible.value) {
    websocket.value.connect()
  }
}

// 心跳
const sendHeartbeat = () => {
  let isEditing = true
  if (warningVisible.value) {
    isEditing = false
  }
  websocket.value.sendMessage({
    optCode: 0,
    chapterId: chapterId.value,
    isEditing,
  })
}

const onClickCancel = () => {
  warningVisible.value = false
  back()
}

const back = () => {
  if (formType.value == 1) {
    router.push({
      path: '/pages/detail',
      query: { bookId: bookId.value },
    })
  } else if (formType.value == 2) {
    window.location.href =
      `${import.meta.env.VITE_APP_MANAGE_URL}book/bookDetail` +
      `?bookId=${bookId.value}`
  }
}

const onClickConfirm = () => {
  websocket.value.sendMessage({
    optCode: 2,
    chapterId: chapterId.value,
  })
  confirmLoading.value = true
}
async function handleMessage(message: any) {
  // 0成功响应心跳 1当前章节正在编辑中 2当前章节被强制关闭 3同意申请编辑章节 4挤退当前编辑人成功
  if (message.resultCode == 0) {
    // 心跳响应
  } else if (message.resultCode == 1) {
    // 当前章节正在编辑中
    warningVisible.value = true
    confirmLoading.value = false
  } else if (message.resultCode == 2) {
    // 当前章节被强制关闭
    confirmLoading.value = true
    warningVisible.value = true
    // 发送保存请求
    await saveContent()
    // 发送消息
    saveSuccess()
    confirmLoading.value = false
  } else if (message.resultCode == 3) {
    // 同意申请编辑章节
    warningVisible.value = false
  } else if (message.resultCode == 4) {
    // 挤退当前编辑人成功
    confirmLoading.value = true
  } else if (message.resultCode == 6) {
    warningVisible.value = false
    confirmLoading.value = false
    applyEdit()
    getChapterContent()
  }
}

// 申请编辑
function applyEdit() {
  websocket.value.sendMessage({
    optCode: 1,
    chapterId: chapterId.value,
  })
}

// 成功保存消息
function saveSuccess() {
  websocket.value.sendMessage({
    optCode: 5,
    chapterId: chapterId.value,
  })
}
async function saveContent() {
  await updateChapterContentInfo({
    chapterId: chapterId.value,
    content: JSON.stringify(editor.value?.getJSON() || '{}'),
  })
}

// 获取当前章节内容
function getChapterContent() {
  if (chapterId.value) {
    getChapterContentInfo(chapterId.value).then((response) => {
      const { data } = response
      if (data) {
        editor.value?.commands.setContent(
          JSON.parse(data.content) || null,
          true,
        )
      }
    })
  }
}
</script>
