<template>
  <div class="login">
    <div class="main-bg">
      <div class="main-bg-left"></div>
      <div class="main-bg-right">
        <t-tabs :default-value="1" style="width: 70%">
          <t-tab-panel :key="1" :value="1" label="验证码登录">
            <div class="main-bg-right-content">
              <t-form
                ref="codeLoginRef"
                :data="codeLoginForm"
                :colon="true"
                :label-width="0"
                @submit="handleCodeLogin"
              >
                <t-form-item name="phoneNumber">
                  <t-input
                    v-model="codeLoginForm.phoneNumber"
                    clearable
                    placeholder="请输入账户名"
                  >
                    <template #prefix-icon>
                      <desktop-icon />
                    </template>
                  </t-input>
                </t-form-item>
                <t-form-item name="code">
                  <t-input
                    v-model="codeLoginForm.code"
                    placeholder="请输入验证码"
                    style="width: 60%; margin-right: 8px"
                  ></t-input>
                  <t-button
                    :disabled="countdown > 0"
                    style="width: 90px"
                    @click="fetchLoginVerificationCode"
                  >
                    {{
                      countdown > 0 ? `${countdown}秒后再获取` : '获取验证码'
                    }}
                  </t-button>
                </t-form-item>
                <t-form-item name="checkcode">
                  <div class="form-code">
                    <t-input
                      v-model="codeLoginForm.checkcode"
                      auto-complete="off"
                      placeholder="验证码"
                      style="width: 60%; margin-right: 8px"
                      class="ipt"
                    >
                    </t-input>
                    <div class="login-code">
                      <img
                        :src="codeUrl"
                        class="login-code-img"
                        @click="getCode"
                      />
                    </div>
                  </div>
                </t-form-item>
                <t-form-item>
                  <div class="form-pass">
                    <div>
                      <t-checkbox v-model="codeLoginForm.rememberMe"
                        >记住密码</t-checkbox
                      >
                    </div>
                    <div
                      style="color: #999; cursor: pointer; font-size: 12px"
                      @click="showForgetPassword"
                    >
                      忘记密码
                    </div>
                  </div>
                </t-form-item>
                <t-form-item>
                  <t-button
                    :loading="loading"
                    theme="primary"
                    style="width: 100%; height: 42px"
                    type="submit"
                  >
                    <span v-if="!loading">登 录</span>
                    <span v-else>登 录 中...</span>
                  </t-button>
                </t-form-item>
              </t-form>
            </div>
          </t-tab-panel>
          <t-tab-panel :key="2" :value="2" label="密码登录">
            <div class="main-bg-right-content">
              <t-form
                ref="loginRef"
                :data="loginForm"
                :colon="true"
                :label-width="0"
                :rules="loginRules"
                @submit="handleLogin"
              >
                <t-form-item name="username">
                  <t-input
                    v-model="loginForm.username"
                    clearable
                    placeholder="请输入账户名"
                  >
                    <template #prefix-icon>
                      <desktop-icon />
                    </template>
                  </t-input>
                </t-form-item>
                <t-form-item name="password">
                  <t-input
                    v-model="loginForm.password"
                    type="password"
                    clearable
                    placeholder="请输入密码"
                  >
                    <template #prefix-icon>
                      <lock-on-icon />
                    </template>
                  </t-input>
                </t-form-item>
                <t-form-item name="code">
                  <t-input
                    v-model="loginForm.code"
                    auto-complete="off"
                    placeholder="验证码"
                    style="width: 60%; margin-right: 8px"
                  >
                  </t-input>
                  <div class="login-code">
                    <img
                      :src="codeUrl"
                      style="height: 32px; margin-top: 2px"
                      @click="getCode"
                    />
                  </div>
                </t-form-item>
                <t-form-item>
                  <div class="form-pass">
                    <div>
                      <t-checkbox v-model="loginForm.rememberMe"
                        >记住密码</t-checkbox
                      >
                    </div>
                    <div
                      style="color: #999; cursor: pointer; font-size: 12px"
                      @click="showForgetPassword"
                    >
                      忘记密码
                    </div>
                  </div>
                </t-form-item>
                <t-form-item>
                  <t-button
                    theme="primary"
                    type="submit"
                    style="width: 100%; height: 42px"
                    >登录</t-button
                  >
                </t-form-item>
              </t-form>
            </div>
          </t-tab-panel>
        </t-tabs>
      </div>
    </div>

    <div class="el-login-footer">
      <span
        >Copyright © 2024 大连理工大学出版社有限公司 All Rights Reserved.</span
      >
    </div>
  </div>
</template>

<script setup>
import Cookies from 'js-cookie'
import { DesktopIcon, LockOnIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { useRoute, useRouter } from 'vue-router'

import { getCodeImg, getLoginSmsCode, getSmsCode } from '@/api/login'
import { decrypt, encrypt } from '@/utils/jsencrypt'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const { LoginCode, CodeLogin } = useStore()

const loginForm = ref({
  username: '',
  password: '',
  code: '',
  rememberMe: false,
})

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入您的账号' }],
  password: [{ required: true, trigger: 'blur', message: '请输入您的密码' }],
  code: [{ required: true, trigger: 'change', message: '请输入验证码' }],
}

const codeUrl = ref('')
const loading = ref(false)
// 验证码开关
const captchaEnabled = ref(true)
// 注册开关
const register = ref(false)
const redirect = ref(undefined)

// ============登录方式部分==============

// 当前登录方式：'password' 或 'code'
const loginMethod = ref('password')

// ============忘记密码部分===============
// 定义模态框的显示状态
const isForgetPasswordModalVisible = ref(false)

// 显示忘记密码模态框的方法
function showForgetPassword() {
  router.push('/forgetPassword')
}

// 隐藏忘记密码模态框的方法
function hideForgetPasswordModal() {
  isForgetPasswordModalVisible.value = false
}

function submitForgetPassword() {
  // 验证表单
  proxy.$refs.forgetPasswordRef.validate((valid) => {
    if (valid) {
      // 发送重置密码请求

      forgetPwd(forgetPasswordForm.value).then((res) => {
        // 关闭模态框

        ElMessage({
          message: '密码重置成功',
          type: 'success',
        })
        isForgetPasswordModalVisible.value = false
      })
    } else {

      return false
    }
  })
}

const forgetPasswordForm = ref({
  phonenumber: '',
  code: '',
  password: '',
  confirmPassword: '',
})

const countdown = ref(0)
const timer = ref(null)

function fetchVerificationCode() {
  if (timer.value) {
    clearInterval(timer.value)
  }
  const tel = forgetPasswordForm.value.phonenumber
  if (!tel) {
    MessagePlugin.error({
      content: '请先输入电话号码',
    })
    return
  }

  // 电话号码格式校验
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(tel)) {
    MessagePlugin.error({
      content: '请输入正确的电话号码格式',
    })
    return
  }

  getSmsCode(forgetPasswordForm.value.phonenumber).then(() => {
    MessagePlugin.success({
      content: '验证码已发送，请查收',
    })
    countdown.value = 60 // 设置倒计时
    timer.value = setInterval(() => {
      if (countdown.value > 0) {
        countdown.value--
      } else {
        clearInterval(timer.value)
        countdown.value = 0
      }
    }, 1000)
  })
}

const forgetPasswordRules = {
  phonenumber: [
    { required: true, message: '请输入电话号码', trigger: 'blur' },
    {
      type: 'string',
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的电话号码格式',
      trigger: ['blur', 'change'],
    },
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    {
      type: 'string',
      min: 4,
      message: '验证码长度不能小于4位',
      trigger: 'blur',
    },
  ],
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== forgetPasswordForm.value.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// ==================验证码登录部分==============
const codeLoginForm = ref({
  phoneNumber: '',
  code: '',
  checkcode: '',
  rememberMe: '',
})

// 登录验证码和忘记密码分开俩个方法处理，但是倒计时共用
function fetchLoginVerificationCode() {
  if (timer.value) {
    clearInterval(timer.value)
  }
  const tel = codeLoginForm.value.phoneNumber
  if (!tel) {
    MessagePlugin.error({
      content: '请先输入电话号码',
    })
    return
  }

  // 电话号码格式校验
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(tel)) {
    MessagePlugin.error({
      content: '请输入正确的电话号码格式',
    })
    return
  }

  getLoginSmsCode(codeLoginForm.value.phoneNumber).then(() => {
    MessagePlugin.success({
      content: '验证码已发送，请查收',
    })
    countdown.value = 60 // 设置倒计时
    timer.value = setInterval(() => {
      if (countdown.value > 0) {
        countdown.value--
      } else {
        clearInterval(timer.value)
        countdown.value = 0
      }
    }, 1000)
  })
}

const codeLoginRules = {
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号格式',
      trigger: ['blur', 'change'],
    },
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, message: '验证码长度不能小于4位', trigger: 'blur' },
  ],
}

function handleCodeLogin({ validateResult, firstError }) {
  if (validateResult === true) {
    loading.value = true
    CodeLogin(codeLoginForm.value)
      .then((res) => {
        loading.value = false

        router.push({ path: redirect.value || '/pages/list' })
        MessagePlugin.success('提交成功')
      })
      .catch((err) => {
        loading.value = false
        getCode()
      })
  } else {
    MessagePlugin.error({
      content: firstError,
    })
  }
}

// ===============忘记密码部分=============

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect
  },
  { immediate: true },
)

const handleLogin = async ({ validateResult, firstError }) => {
  if (validateResult === true) {
    if (loginForm.value.rememberMe) {
      Cookies.set('username', loginForm.value.username, { expires: 30 })
      Cookies.set('password', encrypt(loginForm.value.password), {
        expires: 30,
      })
      Cookies.set('rememberMe', loginForm.value.rememberMe, { expires: 30 })
    } else {
      // 否则移除
      Cookies.remove('username')
      Cookies.remove('password')
      Cookies.remove('rememberMe')
    }

    const obj = {
      username: loginForm.value.username,
      password: loginForm.value.password,
      code: loginForm.value.code,
      uuid: loginForm.value.uuid,
    }

    LoginCode(obj)
      .then((res) => {
        router.push({ path: redirect.value || '/pages/list' })
        MessagePlugin.success('提交成功')
      })
      .catch((err) => {
        getCode()
      })
  } else {
    MessagePlugin.error({
      content: firstError,
    })
  }
}

function getCode() {
  getCodeImg().then((res) => {
    captchaEnabled.value =
      res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = `data:image/gif;base64,${res.img}`
      loginForm.value.uuid = res.uuid
    }
  })
}

function getCookie() {
  const username = Cookies.get('username')
  const password = Cookies.get('password')
  const rememberMe = Cookies.get('rememberMe')
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  }
}
function openDev() {

  const dev = route.query.dev9527;
  if (dev) {
    sessionStorage.setItem('OPEN_DEV', "1");
  }
}
openDev();
getCode()
getCookie()
</script>
<style lang="less" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background: #fff;
  background-size: cover;
  position: relative;
  overflow: hidden;

  .main-bg {
    width: 720px;
    height: 512px;

    display: flex;
    box-shadow: 0px 2px 16px 2px rgba(138, 139, 139, 0.08);
    border-radius: 8px 8px 8px 8px;

    .main-bg-left {
      background: url('@/assets/images/login_bg.jpg') no-repeat;
      width: 331px;
      border-radius: 8px 0 0 8px;
    }

    .main-bg-right {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      padding: 20px;
      flex: 1;

      .main-bg-right-content {
        margin: 20px auto;
      }

      .form-pass {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .form-code {
        display: flex;
        height: 32px;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .lodin-right {
    .lodin-right-image {
      width: 100%;
      height: 100%;
    }
  }

  .login-left {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .login-form .t-form-item {
      margin-bottom: 15px; // 您可以根据需要调整这个值
    }

    .formLogin {
      width: 303px;
      height: 303px;
      background: #ffffff;
      box-shadow: 0px 0px 13px 0px rgba(33, 148, 255, 0.37);
      border-radius: 8px;
      padding: 27px 41px 41px 41px;
    }

    .login-code {
      width: 81px;
      height: 30px;
      margin-left: 7px;
      border-radius: 4px;
      border: 1px solid #6a9564;
      overflow: hidden;
    }

    .login-logo {
      width: 316px;
      margin-bottom: 17px;
      display: flex;
      justify-content: space-around;
    }

    .login-code-img {
      width: 100%;
      height: 100%;
    }
  }

  .el-login-footer {
    position: absolute;
    bottom: 10px;
  }
}
</style>
