<template>
  <div class="app-container">
    <t-card style="margin-bottom: 20px">
      <div class="tool">
        <t-form
          :data="queryParams"
          ref="queryRef"
          label-width="100px"
          layout="inline"
          @reset="resetQuery"
        >
          <t-form-item name="title" label="标题：">
            <t-input
              v-model="queryParams.title"
              placeholder="请输入"
              clearable
              @keyup.enter="handleQuery"
            />
          </t-form-item>
          <t-form-item name="readFlag" label="阅读状态：">
            <t-select
              v-model="queryParams.readFlag"
              placeholder="全部"
              clearable
              style="width: 200px"
            >
              <t-option label="未读" value="0" />
              <t-option label="已读" value="1" />
            </t-select>
          </t-form-item>
          <t-form-item>
            <t-button
              theme="primary"
              type="submit"
              style="margin-right: 20px"
              @click="handleQuery"
            >查询
            </t-button
            >
            <t-button theme="default" type="reset">重置</t-button>
          </t-form-item>
        </t-form>

        <t-row :gutter="10" class="mb8">
          <t-col :span="1.5">
            <t-button theme="default" plain @click="handleAllRead"
            >全部已读
            </t-button
            >
          </t-col>
        </t-row>
      </div>
    </t-card>

    <t-card class="top20">
      <t-enhanced-table
        row-key="messageId"
        :columns="columnList"
        :data="messageList"
        :pagination="queryParams"
        @page-change="onPageChange"
      >
        <template #readFlag="scope">
          <t-tag>
            {{ scope.row.readFlag == 1 ? '已读' : '未读' }}
          </t-tag>
        </template>

        <template #opt="scope">
          <t-button
            @click="handleLink(scope.row)"
            v-if="scope.row.showReadLink == 1"
            style="margin-right: 20px"
          >去处理
          </t-button
          >
          <t-button
            @click="handleRead(scope.row)"
            v-if="scope.row.readFlag == 0"
          >设为已读
          </t-button
          >
        </template>
      </t-enhanced-table>
    </t-card>
  </div>
</template>
<script setup name="myMessage">
import {
  listMessage,
  updateMessage,
  updateAllMessage
} from '@/api/message/message'
import { getProcessInfoLink } from '@/api/book/process.js'

const { proxy } = getCurrentInstance()

const messageList = ref([])
const total = ref(0)
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    total: 0
  }
})

const { queryParams } = toRefs(data)

const columnList = [
  {
    colKey: 'readFlag',
    title: '阅读状态',
    width: 90
  },
  {
    colKey: 'title',
    title: '标题',
    width: 250
  },
  {
    colKey: 'content',
    title: '消息内容'
  },
  { colKey: 'createTime', width: 250, ellipsis: true, title: '发送时间' },
  {
    title: '操作',
    colKey: 'opt',
    fixed: 'right',
    foot: '-',
    width: 240
  }
]

// 全部已读
function handleAllRead() {
  const dialog = useConfirm({
    theme: 'danger',
    header: '提示',
    body: '确定全部已读吗？',
    attach: 'body',
    confirmBtn: {
      theme: 'danger',
      content: '确认'
    },
    onConfirm: () => {
      updateAllMessage({
        toUserType: 1
      })
      getList()
      MessagePlugin.success('全部已读成功')
      dialogInstance.destroy()
    }
  })
}

// 查询投稿邮息列表
function getList() {
  queryParams.value.toUserType = 1
  listMessage(queryParams.value).then((response) => {
    messageList.value = response.rows
    queryParams.value.total = response.total
    getShowReadLink()
  })
}

// 设为已读
function handleRead(item) {
  updateMessage({
    readFlag: 1,
    messageId: item.messageId
  }).then((res) => {
    getList()
  })
}

// 去处理
function handleLink(row) {
  handleRead(row)
  if (row.title == '教材审核提醒') {
    if (row.content.indexOf('请尽快处理') > -1) {
      // 根据流程id获取跳转到审批详情页面所需的参数
      getProcessInfoLink(row.businessId).then((response) => {
        proxy.$router.push({
          path: '/pages/processDetail',
          query: {
            bookId: response.data.bookId,
            masterFlag: response.data.masterFlag,
            bookOrganize: response.data.bookOrganize,
            stepId: response.data.stepId,
            processId: response.data.processId,
            auditUserId: response.data.auditUserId,
            funType: 2
          }
        })
      })
    }
  } else if (row.title == '任务提醒') {
    if (row.content.indexOf('教材导出') > -1) {
      proxy.$router.push({
        path: '/pages/taskCenter'
      })
    }
  } else if (row.title == '读者反馈提醒') {
    if (row.content.indexOf('读者反馈信息') > -1) {
      proxy.$router.push({
        path: '/pages/detail',
        query: { bookId: row.businessId }
      })
    }
  } else if (row.title == '章节审核提醒') {
    if (row.content.indexOf('待处理') > -1) {
      // 根据流程id获取跳转到审批详情页面所需的参数
      getProcessInfoLink(row.businessId).then((response) => {
        proxy.$router.push({
          path: '/pages/processDetail',
          query: {
            bookId: response.data.bookId,
            masterFlag: response.data.masterFlag,
            bookOrganize: response.data.bookOrganize,
            stepId: response.data.stepId,
            processId: response.data.processId,
            auditUserId: response.data.auditUserId,
            funType: 2
          }
        })
      })
    }
    if (row.content.indexOf('已驳回') > -1 || row.content.indexOf('撤销申请') > -1) {
      proxy.$router.push({
        path: '/pages/detail',
        query: { bookId: row.businessId }
      })
    }
  }

}

function getShowReadLink() {
  messageList.value.forEach((item) => {
    if (item.title == '教材审核提醒') {
      if (item.content.indexOf('请尽快处理') > -1) {
        item.showReadLink = 1
      }
    } else if (item.title == '任务提醒') {
      if (item.content.indexOf('教材导出') > -1) {
        item.showReadLink = 1
      }
    } else if (item.title == '读者反馈提醒') {
      if (item.content.indexOf('读者反馈信息') > -1) {
        item.showReadLink = 1
      }
    } else if (item.title == '章节审核提醒') {
      if (item.content.indexOf('待处理') > -1) {
        item.showReadLink = 1
      }
      if (item.content.indexOf('已驳回') > -1 || item.content.indexOf('撤销申请') > -1) {
        item.showReadLink = 1
      }
    }
  })
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

// 重置按钮操作
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

function onPageChange(pageInfo) {
  queryParams.value.pageSize = pageInfo.pageSize
  queryParams.value.pageNum = pageInfo.current
  queryParams.value.current = pageInfo.current
  getList()
}

getList()
</script>

<style lang="less" scoped>
.t-radio-group.t-radio-group--filled {
  border-color: var(--td-bg-color-component);
  padding: 6px;
  border-radius: 8px;
  background-color: #e8f4fe;
  position: relative;
}

.t-radio-group.t-radio-group--primary-filled .t-radio-button.t-is-checked {
  color: #0966b4;
  background-color: #fff;
}

.search-form {
  margin: 20px 0;
}

:global(.dialog-footer) {
  display: flex;
  justify-content: flex-end;
}

:deep(.tdesign-demo-image-viewer__ui-image--img) {
  width: 60px;
  height: 80px;
}
</style>
