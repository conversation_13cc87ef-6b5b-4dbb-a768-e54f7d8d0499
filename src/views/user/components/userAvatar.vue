<template>
  <div class="user-info-head" @click="editCropper()">
    <img
      :src="options.img || ''"
      title="点击上传头像"
      class="img-circle img-lg"
    />
    <modal
      v-model:visible="open"
      header="修改头像"
      width="550"
      :on-before-open="beforeOpen"
    >
      <t-row>
        <t-col :xs="12" :md="12" :style="{ height: '350px' }">
          <vue-cropper
            v-if="visible"
            ref="cropper"
            :img="options.img"
            :info="true"
            :auto-crop="options.autoCrop"
            :auto-crop-width="options.autoCropWidth"
            :auto-crop-height="options.autoCropHeight"
            :fixed-box="options.fixedBox"
            :output-type="options.outputType"
            @real-time="realTime"
          />
        </t-col>
        <t-col :xs="12" :md="6" :style="{ height: '350px' }">
          <div class="avatar-upload-preview">
            <img :src="options.previews.url" :style="options.previews.img" />
          </div>
        </t-col>
      </t-row>
      <br />
      <t-row>
        <t-col :lg="2" :md="2">
          <t-upload
            action="#"
            :http-request="requestUpload"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeUpload"
          >
            <t-button> 选择 </t-button>
          </t-upload>
        </t-col>
        <t-col :lg="{ span: 1, offset: 2 }" :md="2">
          <t-button @click="changeScale(1)"></t-button>
        </t-col>
        <t-col :lg="{ span: 1, offset: 1 }" :md="2">
          <t-button @click="changeScale(-1)"></t-button>
        </t-col>
        <t-col :lg="{ span: 1, offset: 1 }" :md="2">
          <t-button @click="rotateLeft()"></t-button>
        </t-col>
        <t-col :lg="{ span: 1, offset: 1 }" :md="2">
          <t-button @click="rotateRight()"> </t-button>
        </t-col>

        <t-col :lg="{ span: 2, offset: 6 }" :md="2">
          <t-button theme="primary" @click="uploadImg()">提 交</t-button>
        </t-col>
      </t-row>
    </modal>
  </div>
</template>

<script setup>
import 'vue-cropper/dist/index.css'

import { MessagePlugin } from 'tdesign-vue-next'
import { VueCropper } from 'vue-cropper'

const options = ref({
  previews: {
    url: '',
    img: '',
  },
})
const open = ref(false)
const loading = ref(false)
const visible = ref(false)
const editCropper = () => {
  open.value = true
}
const realTime = (data) => {
  realTime.value = data
}

/** 覆盖默认上传行为 */
function requestUpload(file) {
  change(file)
}

const handleAvatarSuccess = (res, file) => {}

/** 上传预处理 */
function beforeUpload(file) {
  loading.value = true
  if (file.type.indexOf('image/') == -1) {
    MessagePlugin.error('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。')
    loading.value = false
  } else {
    const reader = new FileReader()

    // reader.readAsDataURL(file)
    reader.onload = () => {
      options.value.img = reader.result
      options.value.filename = file.name
    }
    loading.value = false
  }
}

const beforeOpen = () => {
  visible.value = true
}

function uploadImg() {
  proxy.$refs.cropper.getCropBlob((data) => {
    emit('upload-success', fileInput.value)
    const param = {}
    param.avatar = fileInput.value
    updateAvatar(param).then((response) => {
      open.value = false
      options.value.img = fileInput.value
      // userStore.avatar = options.value.img
      //  proxy.$modal.msgSuccess('头像修改成功')
      visible.value = false
    })
  })
}
</script>

<style lang="less" scoped>
.user-info-head {
  position: relative;
  display: inline-block;
  width: 120px;
  height: 120px;
}

.user-info-head:hover:after {
  content: '+';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  color: #eee;
  background: rgba(0, 0, 0, 0.5);
  font-size: 24px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
  line-height: 110px;
  border-radius: 50%;
}
</style>
