<template>
  <div class="main">
    <div class="header">
      <div class="avatar" @click="handleImage">
        <div class="avatar-img">
          <img
            :src="
              defaultUserInfo.avatar ||
              'https://dutp-test.oss-cn-beijing.aliyuncs.com/1743412473588.png'
            "
            class="avatar-img"
          />

          <!-- <img
            src="https://dutp-test.oss-cn-beijing.aliyuncs.com/1743412473588.png"
            class="avatar-img"
          /> -->
        </div>
        <div class="avatar-mask">更换头像</div>
      </div>
      <div class="info">
        <div class="name">
          <div class="nickname">{{ defaultUserInfo.nickName }}</div>
          <div class="role">
            <t-tag theme="success">{{ roleGroup }}</t-tag>
          </div>
        </div>
        <div class="desc">
          {{ defaultUserInfo.deptName || '-' }}/{{ postGroup || '-' }}
        </div>
        <div class="bottom">
          <div class="bottom-item">
            <span class="label">手机号码</span
            ><span class="labelValue">{{
              defaultUserInfo.phonenumber || '-'
            }}</span>
          </div>
          <div class="bottom-item">
            <span class="label">用户邮箱</span
            ><span class="labelValue">{{ defaultUserInfo.email || '-' }}</span>
          </div>

          <div class="bottom-item">
            <span class="label">创建时间</span
            ><span class="labelValue">{{
              defaultUserInfo.createTime || '-'
            }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="content">
      <t-tabs :default-value="1">
        <t-tab-panel :value="1" label="基本资料" :destroy-on-hide="false">
          <t-form
            ref="formDataRef"
            :data="formData"
            :rules="rules"
            class="form-userinfo"
            @submit="onSubmit"
          >
            <t-form-item label="用户昵称" name="nickName">
              <t-input
                v-model="formData.nickName"
                placeholder="请输入用户昵称"
                size="large"
              ></t-input>
            </t-form-item>

            <t-form-item label="手机号码" name="phonenumber">
              <t-input
                v-model="formData.phonenumber"
                placeholder="请输入手机号码"
                size="large"
                maxlength="11"
              ></t-input>
            </t-form-item>

            <t-form-item label="邮箱" name="email">
              <t-auto-complete
                v-model="formData.email"
                :options="emailOptions"
                filterable
              ></t-auto-complete>
            </t-form-item>
            <t-form-item label="性别" name="sex">
              <t-radio-group v-model="formData.sex" size="large">
                <t-radio value="0">男</t-radio>
                <t-radio value="1">女</t-radio>
              </t-radio-group>
            </t-form-item>
            <t-button
              type="submit"
              style="width: 100%; margin: 30px 0"
              size="large"
              >修改基本资料</t-button
            >
          </t-form>
        </t-tab-panel>
        <t-tab-panel :value="2" label="修改密码" :destroy-on-hide="false">
          <t-form
            ref="pwdRef"
            :data="user"
            :rules="rulesPassword"
            label-width="80px"
            style="margin-top: 20px"
            @submit="submit"
          >
            <t-form-item label="旧密码" name="oldPassword">
              <t-input
                v-model="user.oldPassword"
                placeholder="请输入旧密码"
                type="password"
                maxlength="18"
                show-password
                size="large"
              />
            </t-form-item>
            <t-form-item label="新密码" name="newPassword">
              <t-input
                v-model="user.newPassword"
                placeholder="请输入新密码"
                type="password"
                maxlength="18"
                show-password
                size="large"
              />
            </t-form-item>
            <t-form-item label="确认密码" name="confirmPassword">
              <t-input
                v-model="user.confirmPassword"
                placeholder="请确认新密码"
                type="password"
                show-password
                maxlength="18"
                size="large"
              />
            </t-form-item>
            <div class="form-btn">
              <t-button
                type="submit"
                theme="primary"
                size="large"
                class="form-btn"
                >保存</t-button
              >
            </div>
          </t-form>
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>
<script setup>
import { MessagePlugin } from 'tdesign-vue-next'
import { computed, onMounted, ref } from 'vue'

import {
  getUserProfile,
  updateAvatar,
  updateUserProfile,
  updateUserPwd,
} from '@/api/system/user'
import { OssService } from '@/utils/aliOss'
const { getUserInfo: updateUserInfo } = useStore()
const pwdRef = ref(null)
const user = ref({})
const roleGroup = ref('')
const postGroup = ref('')
const formData = ref({})
const formDataRef = ref(null)
const acceptList = ['jpg', 'jpeg', 'png']
const defaultUserInfo = ref({})
const rules = ref({
  nickName: [
    { required: true, message: '请输入用户昵称', trigger: 'blur' },
    {
      min: 3,
      max: 20,
      message: '用户昵称长度在 3 到 20 个字符',
      trigger: 'blur',
    },
  ],
  phonenumber: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: '请输入正确的手机号码',
      trigger: 'blur',
    },
  ],
  email: [{ required: true, message: '格式必须为邮箱', type: 'warning' }],
})

const rulesPassword = ref({
  oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
  newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
  ],
})

const emailSuffix = ['@qq.com', '@163.com', '@gmail.com']

const emailOptions = computed(() => {
  const emailPrefix = formData.value.email?.split('@')[0]
  if (!emailPrefix) return []

  return emailSuffix.map((suffix) => emailPrefix + suffix)
})

onMounted(() => {
  getUserInfo()
})
const getUserInfo = () => {
  getUserProfile().then((response) => {
    if (response.code === 200) {
      roleGroup.value = response.roleGroup
      postGroup.value = response.postGroup
      defaultUserInfo.value = response.data
      formData.value = response.data
    }
  })
}

const onSubmit = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    updateUserProfile(formData.value).then((response) => {
      MessagePlugin.success('提交成功')
    })
  } else {
    MessagePlugin.warning(firstError)
  }
}

const submit = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    if (user.value.newPassword !== user.value.confirmPassword) {
      MessagePlugin.warning('两次输入的密码不一致')
      return
    }
    updateUserPwd(user.value.oldPassword, user.value.newPassword).then(
      (response) => {
        if (response.code === 200) {
          MessagePlugin.success('提交成功')
          user.value = {}
          updateUserInfo()
        }
      },
    )
  } else {
    MessagePlugin.warning(firstError)
  }
}

const handleImage = () => {
  const InputFile = document.createElement('input')
  InputFile.type = 'file'
  InputFile.click()
  InputFile.addEventListener('change', async (e) => {
    const { files } = e.target
    const fileArr = []
    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      let fileExtension = ''
      if (file.name.lastIndexOf('.') > -1) {
        fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
      }
      if (fileExtension && !acceptList.includes(fileExtension)) {
        MessagePlugin.error(`不允许上传${fileExtension}类型的文件`)
        break
      }
      if (file.size / 1024 / 1024 > 20) {
        MessagePlugin.error(`请上传不大于20MB的文件`)
        break
      }
      MessagePlugin.loading('正在上传文件，请稍候...')
      const res = await OssService(file)
      fileArr.push({
        fileName: file.name,
        fileSize: file.size,
        fileUrl: res.url,
      })
      MessagePlugin.closeAll()
    }
    fileArr.forEach((e) => {
      const param = {}
      param.avatar = e.fileUrl
      updateAvatar(param).then((response) => {
        if (response.code === 200) {
          MessagePlugin.success('上传成功')
          getUserInfo()
          updateUserInfo()
        }
      })
    })
    InputFile.remove && InputFile.remove()
  })
}
</script>
<style lang="less" scoped>
.main {
  .header {
    height: 180px;
    display: flex;
    width: 430px;
    margin: 0 auto;
    .avatar {
      display: flex;
      text-align: center;
      align-content: center;
      flex-direction: column;
      cursor: pointer;
      .avatar-img {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background-color: #333;
      }
      .avatar-mask {
        padding: 10px 0;
        color: #999;
      }
    }

    .info {
      margin-left: 20px;
      padding: 0 20px;

      .name {
        display: flex;
        padding: 10px 0;
        .nickname {
          font-size: 24px;
          font-weight: bold;
          color: #333;
        }
        .role {
          margin-left: 10px;
        }
      }
      .desc {
        font-size: 16px;
        color: #666;
      }

      .bottom {
        height: 80px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end; /* 关键点：将内容推向底部 */
        .bottom-item {
          .label {
            color: #999;
            margin-right: 5px;
          }
          .labelValue {
            color: #333;
          }
        }
      }
    }
  }

  .content {
    width: 500px;
    margin: 40px auto;
    .form-userinfo {
      width: 500px;
      margin: 40px auto;
    }
  }
}

.form-btn {
  width: 100%;
}
</style>
