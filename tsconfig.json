{"compilerOptions": {"noImplicitAny": false, "removeComments": true, "disableSizeLimit": true, "target": "ESNext", "module": "ESNext", "strict": true, "allowJs": true, "noEmit": true, "jsx": "preserve", "moduleResolution": "bundler", "experimentalDecorators": true, "resolveJsonModule": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "verbatimModuleSyntax": true, "sourceMap": true, "baseUrl": ".", "types": ["vue", "unplugin-vue-macros/macros-global", "@vue-macros/reactivity-transform/macros-global", "vite/client", "vitest/globals", "vitest", "./types/imports.d.ts", "./types/components.d.ts", "./types/i18n.d.ts", "./types/declarations.d.ts"], "paths": {"@/*": ["./src/*"], "@/types": ["./types"]}, "lib": ["ESNext", "DOM", "DOM.Iterable", "ScriptHost"]}, "vueCompilerOptions": {"plugins": ["unplugin-vue-macros/volar"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/components/menus/toolbar/base/mixins.js", "src/request/token.js", "types/**/*.d.ts"], "exclude": ["node_modules", "dist"]}