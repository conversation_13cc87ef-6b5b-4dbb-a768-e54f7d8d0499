/*
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-12 11:31:46
 * @LastEditTime: 2025-02-17 09:09:30
 * @FilePath: \dutp-editor\vite.config.ts
 * @Description:
 *
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
 */
import Vue from '@vitejs/plugin-vue'
import ReactivityTransform from '@vue-macros/reactivity-transform/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { TDesignResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import VueMacros from 'unplugin-vue-macros/vite'
import { defineConfig } from 'vite'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import tsConfigPaths from 'vite-tsconfig-paths'
import path from 'path'
import { createRequire } from 'module'
const require = createRequire(import.meta.url)
// monacoEditor 插件
const monacoEditorPlugin = require('vite-plugin-monaco-editor').default
// Plugin configurations
const vuePlugins = {
  VueMacros: VueMacros({
    plugins: {
      vue: Vue(),
    },
  }),
  AutoImport: AutoImport({
    dirs: ['./src/composables'],
    imports: ['vue', '@vueuse/core'],
    resolvers: [TDesignResolver({ library: 'vue-next', esm: true })],
    dts: './types/imports.d.ts',
  }),
  Components: Components({
    directoryAsNamespace: true,
    dirs: ['./src/components'],
    resolvers: [TDesignResolver({ library: 'vue-next', esm: true })],
    dts: './types/components.d.ts',
  }),
  SvgIcons: createSvgIconsPlugin({
    iconDirs: [`${process.cwd()}/src/assets/icons`],
    symbolId: 'umo-icon-[name]',
    customDomId: 'umo-icons',
  }),
}

const cssConfig = {
  preprocessorOptions: {
    less: {
      modifyVars: { '@prefix': 'umo' },
      javascriptEnabled: true,
    },
  },
}

export default defineConfig({
  // base: '/editor',
  plugins: [
    tsConfigPaths(),
    ReactivityTransform(),
    ...Object.values(vuePlugins),
    // ========== Monaco Editor 插件 ==========
    monacoEditorPlugin({
      publicPath: 'monaco',
    }),
  ],
  css: cssConfig,
  server: {
    host: '0.0.0.0',
    port: 9000, // 启动端口号
    open: '/', // 启动后是否自动打开网页
    cors: true, // 允许跨域
    hmr: true, // 开启热更新
    proxy: {
      '/dev-api': {
       target: 'http://*************:8080',
          // target: 'http://api.realstonia.com',
        changeOrigin: true, //是否允许跨域
        rewrite: (p) => p.replace(/^\/dev-api/, ''),
      },
    },
  },
  build: {
    minify: 'esbuild' as const,
    cssMinify: true,
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
  resolve: {
    alias: {
      '@': `${process.cwd()}/src`,
    },
  },
})
